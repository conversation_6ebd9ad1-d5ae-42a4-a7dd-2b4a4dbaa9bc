---
title: 项目经验
categories:
  - 个人介绍
tags:
  - 个人
cover: 'https://bu.dusays.com/2025/07/27/6885a30490f87.jpg'
swiper_index: 4
description: 展示我参与过的主要项目经验，包括技术栈、项目亮点和个人贡献
abbrlink: 11472
date: 2025-01-14 09:00:00
---

# 项目总结

{% p center logo large, 项目实践 %}
{% p center small, 将想法变为现实，用代码构建价值 %}

{% note modern 'anzhiyufont anzhiyu-icon-project' %}
欢迎来到我的项目实践展厅。这里汇集了我从学习到实战中，将理论付诸实践的成果。每一个项目不仅是技术的堆砌，更是我对 {% span green, 业务场景的理解 %}、对 {% span blue, 用户体验的打磨 %} 和对 {% span red, 工程化标准的追求 %}。点击下方的卡片，探索每个项目背后的故事与思考。
{% endnote %}

{% tabs '项目分类' %}

<!-- tab 全栈应用与解决方案 -->

<div class="gallery-group-main">
{% galleryGroup '若依(Ruo-Yi)深度定制化开发' '不止于用：深入源码，整合MyBatis-Plus、重构前端架构，打造企业级高效后台解决方案。' '/posts/ruoyi-deep-dive/' https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/projects/ruoyi-cover.jpg %}
{% galleryGroup 'AI 赋能的双轨物流平台 (概念)' '结合提示词工程与Vue3/Java后端，构建的智能调度与路线规划物流管理系统原型。' '/posts/ai-logistics-platform/' https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/projects/ai-logistics-cover.jpg %}
{% galleryGroup 'Electron 桌面应用实践' '基于 Vue3 和 Electron，将 Web 应用打包成功能完善、体验流畅的跨平台桌面客户端。' '/posts/electron-vue-app/' https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/projects/electron-cover.jpg %}
</div>
<!-- endtab -->

<!-- tab 前端精品与可视化 -->
<div class="gallery-group-main">
{% galleryGroup 'GSAP & Tailwind CSS 交互动效实践' '基于 GSAP 动画库，结合原子化 CSS，实现复杂、高性能的 Web 交互动画与数据可视化看板。' '/posts/gsap-tailwind-showcase/' https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/projects/gsap-cover.jpg %}
{% galleryGroup 'Uni-app 跨端小程序' '一套代码，多端发布。利用 Uni-app 打通若依后端，实现原生体验的移动端应用。' '/posts/ruoyi-uniapp-app/' https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/projects/uniapp-cover.jpg %}
{% galleryGroup 'ECharts 数据可视化大屏' '使用 ECharts 深度定制，构建满足商业需求的实时监控、数据分析的可视化大屏。' '/posts/echarts-dashboard/' https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/projects/echarts-cover.jpg %}
</div>
<!-- endtab -->

<!-- tab AI & 效率工具 -->
<div class="gallery-group-main">
{% galleryGroup 'Cursor AI 编程提示词(Prompt)库' '沉淀个人在 AI 辅助编程中的最佳实践，形成一套高效、精准的 Cursor 提示词规则与模板。' '/posts/cursor-prompts-library/' https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/projects/cursor-ai-cover.jpg %}
{% galleryGroup '个人全栈笔记知识库' '本项目本身！基于 Hexo 深度定制，结合自动化部署，打造的个人知识管理与分享平台。' '/about/' https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/projects/blog-cover.jpg %}
</div>
<!-- endtab -->

{% endtabs %}

{% note info flat %}
这个页面会随着我的学习和探索不断更新。更多项目正在构思与开发中，敬请期待！如果你对某个项目感兴趣或有合作意向，欢迎随时与我 {% btn 'mailto:<EMAIL>', '取得联系', 'fas fa-mail' %}。
{% endnote %}