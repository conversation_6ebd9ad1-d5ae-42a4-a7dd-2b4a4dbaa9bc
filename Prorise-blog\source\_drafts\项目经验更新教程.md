
### 📝 使用指南

1.  **创建新页面**:
    在你的博客根目录下，执行命令来创建一个名为 "projects" 的新页面。
    ```bash
    hexo new page projects
    ```
    这会在 `source/projects/` 目录下创建一个 `index.md` 文件。

2.  **粘贴内容**:
    将上面提供的模板代码**完整地**复制并粘贴到 `source/projects/index.md` 文件中。

3.  **修改核心内容 (重要！)**:
    你需要替换掉模板中的**占位符**：
    *   **项目链接**:
        *   `'/posts/ruoyi-deep-dive/'` 是指向你具体项目介绍文章的链接。
        *   **操作流程**:
            1.  为你每个项目创建一篇新文章，例如：`hexo new post "若依(Ruo-Yi)深度定制化开发"`。
            2.  写完文章后，获取到这篇文章的访问链接 (URL)。
            3.  回到 `projects` 页面，将模板中的占位符链接替换成你文章的真实链接。
    *   **项目封面图**:
        *   `https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/projects/ruoyi-cover.jpg` 是项目卡片的封面图。
        *   **强烈建议**为你每个项目制作或寻找一张有代表性的图片（如项目截图、Logo、架构图等），上传到你的图床或博客静态资源文件夹，然后替换掉这里的链接。一张好的封面图能极大提升吸引力。
    *   **联系邮箱**:
        *   在页面底部，将 `<EMAIL>` 替换成你的真实电子邮箱。

4.  **添加新项目**:
    未来当你完成一个新项目时，只需：
    1.  写一篇介绍它的新文章。
    2.  在 `projects` 页面对应的分类 `<!-- tab ... -->` 下，仿照现有格式，添加一行新的 `{% galleryGroup ... %}` 即可。

这个模板为你提供了一个既美观又易于维护的项目展示方案，希望能帮助你更好地展示你的才华！