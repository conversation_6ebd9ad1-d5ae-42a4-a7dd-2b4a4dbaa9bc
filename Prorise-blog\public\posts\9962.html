<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>Python（十四）：第十三章： 高级数据处理 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="Python基础知识总汇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="Python（十四）：第十三章： 高级数据处理"><meta name="application-name" content="Python（十四）：第十三章： 高级数据处理"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="Python（十四）：第十三章： 高级数据处理"><meta property="og:url" content="https://prorise666.site/posts/9962.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="第十三章： 高级数据处理Python 提供了多种处理不同类型数据的工具和库，能够轻松处理结构化和非结构化数据。本章将深入探讨 Python 中常用的数据格式处理技术，包括 JSON、CSV、XML 和配置文件等。 13.1 JSON 处理JSON (JavaScript Object Notatio"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta name="description" content="第十三章： 高级数据处理Python 提供了多种处理不同类型数据的工具和库，能够轻松处理结构化和非结构化数据。本章将深入探讨 Python 中常用的数据格式处理技术，包括 JSON、CSV、XML 和配置文件等。 13.1 JSON 处理JSON (JavaScript Object Notatio"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/9962.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"Python（十四）：第十三章： 高级数据处理",postAI:"true",pageFillDescription:"第十三章： 高级数据处理, 13.1 JSON 处理, 13.1.1 基本操作, 13.1.2 重要参数说明, 13.1.3 自定义对象序列化, 13.1.4 JSON 解码为自定义对象, 13.1.5 处理复杂 JSON 数据, 13.1.6 性能优化, 13.1.7 JSON Schema 验证, 13.2 CSV 处理, 13.2.1 基本读写操作, 13.2.2 使用字典处理 CSV 文件, 13.2.3 CSV 方言与格式化选项, 13.2.4 处理特殊情况, 13.2.5 CSV 文件的高级操作, 13.3 XML 处理, 13.3.1 创建和写入 XML, 13.3.2 解析和读取 XML, 13.3.3 修改 XML, 13.3.4 命名空间处理, 13.4 配置文件处理, 13.4.1 INI 配置文件处理, 13.4.2 YAML 配置文件处理, 13.4.3 使用环境变量作为配置, 13.4.4 JSON 作为配置文件, 13.5 正则表达式, 13.5.1 常用元字符和语法, 13.5.2 特殊序列 (预定义字符集), 13.5.3 贪婪模式 vs. 非贪婪模式, 13.5.4 分组与捕获, 13.5.5 re 模块核心函数, 13.5.6 Match 对象详解, 13.5.7 正则表达式标志 (Flags), 13.5.8 实际应用场景示例第十三章高级数据处理提供了多种处理不同类型数据的工具和库能够轻松处理结构化和非结构化数据本章将深入探讨中常用的数据格式处理技术包括和配置文件等处理是一种轻量级的数据交换格式易于人阅读和编写也易于机器解析和生成通过内置的模块提供了的序列化和反序列化功能方法描述将对象编码为格式并写入文件将对象编码为格式并返回字符串从文件读取数据并解码为对象将字符串解码为对象基本操作对象转张三数据分析机器学习数据分析转换为字符串写入文件从字符串解析张三从文件读取重要参数说明参数说明用法示例是否转义非字符时保留原始字符缩进格式美化输出指定分隔符用于紧凑输出是否按键排序指定序列化函数处理不可序列化对象自定义对象序列化的模块默认无法直接序列化自定义类对象但提供了多种方式解决方法一提供参数将对象转换为字典示例使用参数序列化自定义对象李四李四方法二通过自定义编码器自定义编码器处理类示例使用自定义编码器序列化对象李四方法三添加方法返回可序列化的字典示例使用对象的方法序列化小明小红小明小红解码为自定义对象使用的参数将字符串直接转换为自定义对象的用途自动将解析出的字典转换为自定义类的实例在解析时进行数据转换和验证简化从到对象模型的映射过程避免手动创建对象的繁琐步骤工作原理首先将字符串解析为字典然后对每个解析出的字典调用函数函数返回的对象将替代原始字典实际应用场景响应数据转换为应用程序对象模型配置文件解析为配置对象数据导入时的格式转换处理复杂数据处理嵌套结构张三技术李四市场营销策划北京上海广州深圳访问嵌套数据张三广州修改嵌套数据成都保存修改后的数据性能优化处理大型文件时可以使用流式解析来提高性能需安装流式解析大型文件只提取特定字段处理一项后继续不必载入整个文件验证验证数据是否符合预期格式需安装定义验证数据张三李四有效数据验证失败有效数据验证失败会因小于而失败处理是一种常见的表格数据格式的模块提供了读写文件的功能适用于处理电子表格和数据库导出数据在我们写入中文数据时尽量将编码更换为否则写入会导致一些乱码问题基本读写操作写入文件姓名年龄城市张三北京李四上海王五广州一次写入多行逐行写入一次写入一行读取文件使用字典处理文件使用字典写入姓名张三年龄城市北京姓名李四年龄城市上海姓名王五年龄城市广州姓名年龄城市写入表头写入多行数据使用字典读取姓名年龄岁来自城市方言与格式化选项自定义方言使用制表符作为分隔符引号字符转义字符不使用双引号转义最小引用策略使用自定义方言常见格式化选项分隔符引号字符为非数值字段添加引号转义字符行终止符处理特殊情况处理含有引号和逗号的数据产品描述价格笔记本高配处理器手机屏幕双卡双待所有字段加引号跳过特定行跳过表头处理缺失值将空字符串转换为文件的高级操作过滤行筛选年龄大于的记录年龄计算统计值计算平均分分数平均分合并多个文件获取所有匹配的文件假设所有文件结构相同第一个文件保留表头跳过后续文件的表头使用示例处理是一种用于存储和传输数据的标记语言提供多种处理的方法最常用的是模块创建和写入创建根元素添加子元素添加多个项目设置属性第项设置文本内容添加嵌套元素项目的详情创建用户信息部分添加用户张三北京李四上海生成字符串写入文件解析和读取从文件解析从字符串解析测试获取元素标签和属性根元素标签遍历子元素子元素属性查找特定元素查找第一个匹配元素使用查找所有匹配的子元素项目内容获取嵌套元素详情使用查询查找所有用户名称用户年龄城市更复杂的查询查找北京的用户北京北京用户修改修改元素属性张三添加新属性修改子元素文本修改年龄添加新元素删除元素李四保存修改命名空间处理创建带命名空间的添加带命名空间前缀的元素带命名空间的元素生成字符串解析带命名空间的使用带命名空间的查询找到命名空间元素类型属性配置文件处理配置文件是应用程序保存设置和首选项的常用方式提供了多种处理不同格式配置文件的方法配置文件处理文件是一种结构简单的配置文件格式通过模块提供支持是标准库中用于处理配置文件的模块它可以读取写入和修改类似格式的配置文件配置文件通常包含节如和每个节下的键值对如中文默认创建一个新的配置解析器添加默认节和配置项中文默认添加应用设置节应用设置应用设置添加用户信息节用户信息用户信息创建一个引用方便添加多个配置项张三修改为标准布尔值字符串添加数据库连接节数据库数据库数据库数据库数据库将配置写入文件读取配置文件获取所有节名称所有配置节应用设置用户信息数据库获取节中的所有键用户信息节中的所有键用户信息获取特定配置值用户名用户信息张三获取默认节中的值默认语言应用设置使用中的值类型转换方法应用设置将转换为字体大小类型字体大小类型自动保存类型自动保存类型修改配置用户信息李四添加新配置日志设置日志设置日志设置日志设置日志设置保存修改后的配置配置文件处理是一种人类友好的数据序列化格式需要安装库需要安装创建数据张三李四写入文件读取文件访问配置服务器地址第一个用户张三修改配置王五保存修改使用环境变量作为配置环境变量是一种灵活的配置方式尤其适用于容器化应用需安装从文件加载环境变量默认加载当前目录下的文件读取环境变量提供默认值数据库调试模式端口创建文件示例数据库设置应用设置作为配置文件也是一种常用的配置文件格式尤其适合需要与应用共享配置的场景默认配置配置文件路径加载配置如果配置文件存在则加载它否则使用默认配置并创建配置文件保存配置更新配置处理嵌套键如使用示例应用名称数据库主机更新配置重新加载配置更新后的数据库主机更新后的缓存正则表达式正则表达式通常缩写为或是一种强大的文本处理工具它使用一种专门的语法来定义搜索模式然后可以用这个模式在文本中进行查找匹配提取或替换操作正则表达式在各种编程任务中都极为有用例如数据验证检查用户输入是否符合特定格式如邮箱手机号日期数据提取从大量非结构化文本如日志文件网页内容中精确地抽取所需信息如地址错误代码特定标签内容文本替换对文本进行复杂的查找和替换操作例如格式化代码屏蔽敏感信息文本分割根据复杂的模式分割字符串通过内置的模块提供了对正则表达式的全面支持核心概念正则表达式的核心在于使用元字符和普通字符组合来定义模式元字符是具有特殊含义的字符而普通字符则匹配它们自身常用元字符和语法以下是一些最常用的正则表达式元字符及其含义元字符描述示例模式示例匹配匹配除换行符之外的任何单个字符使用标志可匹配换行符但不匹配匹配字符串的开头在多行模式下也匹配每行的开头但不匹配匹配字符串的结尾在多行模式下也匹配每行的结尾但不匹配匹配前面的元素零次或多次贪婪模式匹配前面的元素一次或多次贪婪模式但不匹配匹配前面的元素零次或一次贪婪模式也用于将贪婪量词变为非贪婪见后文匹配前面的元素恰好次但不匹配或匹配前面的元素至少次贪婪模式匹配前面的元素至少次但不超过次贪婪模式但不匹配或字符集匹配方括号中包含的任意一个字符或或否定字符集匹配不在方括号中包含的任何字符任何非数字字符转义符用于转义元字符使其匹配其字面含义如匹配句点或用于引入特殊序列如字符本身或运算符匹配左边或右边的表达式分组将括号内的表达式视为一个整体用于应用量词限制的范围或捕获匹配的子字符串踩坑提示转义当需要匹配元字符本身时如必须在前面加上反斜杠进行转义例如要匹配地址中的点应使用原始字符串在中定义正则表达式模式时强烈建议使用原始字符串在字符串前加如这可以避免解释器对反斜杠进行自身的转义从而简化正则表达式的书写尤其是包含很多的模式特殊序列预定义字符集模块提供了一些方便的特殊序列来代表常见的字符集特殊序列描述等价于示例匹配任何数字字符包括和其他语言的数字匹配任何非数字字符匹配任何空白字符包括等匹配任何非空白字符匹配任何词语字符字母数字和下划线匹配任何非词语字符匹配词语边界这是一个零宽度断言匹配词语字符和非词语字符之间或词语字符和字符串开头结尾之间的位置匹配非词语边界贪婪模式非贪婪模式默认情况下量词都是贪婪的它们会尽可能多地匹配字符场景从标签中提取贪婪模式默认匹配任何字符匹配零次或多次会一直匹配到字符串的最后一个贪婪匹配结果输出贪婪匹配结果非贪婪模式在量词后加匹配零次或多次但尽可能少地匹配遇到第一个就停止匹配非贪婪匹配结果输出非贪婪匹配结果查找所有非贪婪匹配所有非贪婪匹配输出所有非贪婪匹配何时使用非贪婪模式当需要匹配从某个开始标记到最近的结束标记之间的内容时通常需要使用非贪婪量词分组与捕获使用圆括号可以将模式的一部分组合起来形成一个分组分组有几个重要作用应用量词将量词作用于整个分组如匹配等限制范围如匹配或捕获内容默认情况下每个分组会捕获其匹配到的子字符串以便后续引用或提取场景从中提取姓名和年龄定义带有捕获组的模式第一个组捕获姓名第二个组捕获年龄使用查找所有匹配项返回一个列表如果模式中有捕获组列表元素是包含所有捕获组内容的元组使用提取分组输出使用获取对象可以更灵活地访问分组使用访问分组或获取整个匹配整个匹配获取第一个捕获组的内容姓名姓名组获取第二个捕获组的内容年龄年龄组获取所有捕获组组成的元组所有分组非捕获组如果只想分组而不捕获内容可以使用非捕获组第一个组不捕获使用非捕获组的输出只包含捕获组的内容反向引用可以在模式内部或替换字符串中使用来引用前面捕获组匹配到的文本场景查找重复的单词如确保是完整的单词捕获第一个单词匹配中间的空白引用第一个捕获组匹配的内容查找重复单词找到的重复单词输出使用进行替换将重复的单词替换为单个单词使用引用捕获组修正后的文本输出模块核心函数的模块提供了以下核心函数来执行正则表达式操作函数描述返回值主要用途从字符串的开头尝试匹配模式匹配成功返回对象失败返回验证字符串是否以特定模式开始在整个字符串中搜索模式的第一个匹配项匹配成功返回对象失败返回在字符串中查找模式是否存在并获取第一个匹配项的信息在字符串中查找模式的所有非重叠匹配项返回一个列表如果模式无捕获组列表元素是匹配的字符串如果有捕获组列表元素是包含各捕获组内容的元组提取字符串中所有符合模式的子串或捕获组内容与类似但返回一个迭代器迭代器中的每个元素都是一个对象返回一个迭代器每个元素是对象处理大量匹配结果时更内存高效因为不需要一次性存储所有结果可以方便地访问每个匹配的详细信息如位置在字符串中查找模式的所有匹配项并用替换它们可以是字符串支持或等反向引用或函数指定最大替换次数返回替换后的新字符串执行查找和替换操作可以是函数实现更复杂的替换逻辑使用模式作为分隔符来分割字符串指定最大分割次数返回一个列表包含分割后的子字符串如果模式中有捕获组捕获的内容也会包含在列表中根据复杂的模式分割字符串编译正则表达式模式为一个模式对象返回一个对象当一个模式需要被多次使用时预先编译可以提高性能模式对象拥有与模块函数同名的方法如代码示例检查开头字符串以开头匹配内容字符串不以开头不从开头匹配所以失败失败示例查找第一个匹配找到单词起始位置结束位置未找到单词查找所有匹配查找所有数字序列找到的所有数字序列查找邮箱并捕获用户名和域名捕获组迭代查找匹配对象查找所有包含字母的单词查找包含的单词使用标志找到替换将电话号码替换为替换电话号码使用函数进行替换用户名只显示第一个字符使用函数替换邮箱分割按标点符号和后面的空格分割按标点分割编译模式编译查找以开头结尾的词多次使用编译后的模式找到找到对象详解当或中的一项成功匹配时它们会返回一个对象这个对象包含了关于匹配结果的详细信息对象方法属性描述示例假设或返回整个匹配的字符串返回第个捕获组匹配的字符串从开始计数返回返回返回一个包含所有捕获组匹配内容的元组如果模式中使用了命名捕获组返回一个包含组名和匹配内容的字典需要命名组如下例返回整个匹配或指定的起始索引包含返回返回返回返回整个匹配或指定的结束索引不包含返回返回返回返回一个包含索引的元组返回返回传递给或的原始字符串匹配时使用的已编译的模式对象命名捕获组示例使用定义命名捕获组使用命名捕获组通过组名访问捕获的内容产品数量返回包含所有命名组的字典捕获字典正则表达式标志标志可以修改正则表达式的匹配行为可以在函数的参数中指定或在编译时指定多个标志可以使用按位或组合标志简写描述进行不区分大小写的匹配使和匹配每行的开头和结尾而不仅仅是整个字符串的开头和结尾使元字符能够匹配包括换行符在内的任何字符详细模式允许在模式字符串中添加空白和注释以提高可读性此时模式中的空白会被忽略后到行尾的内容视为注释使只匹配字符而不是完整的字符集默认匹配默认使匹配完整的字符集这是的默认行为示例忽略大小写示例多行模式匹配成功匹配失败一个复杂的邮箱模式使用模式添加注释和空格匹配字符串开头用户名部分字母数字下划线点连字符符号域名部分允许子域名如顶级域名如匹配字符串结尾匹配成功匹配失败匹配成功实际应用场景示例场景验证中国大陆手机号简单示例简单验证中国大陆手机号码位数字常见号段模式解释匹配字符串开头非捕获组第一位是第二位是到后面跟位数字匹配字符串结尾手机号验证号段不对位数不够位数太多注意实际手机号验证可能需要更复杂的规则或查询号段数据库场景从日志中提取地址和请求路径模式解释捕获开头的地址数字和点的组合匹配中间的部分匹配并忽略方括号内的时间戳非贪婪匹配时间戳后的空格和双引号捕获请求方法等和空格捕获请求路径非空格非双引号的字符捕获版本部分匹配剩余部分日志解析地址请求方法请求路径日志格式不匹配场景将样式的链接转换为标签这是一个链接和另一个官网的例子模式解释匹配字面量捕获链接文本不是的任意字符一次或多次匹配字面量匹配字面量捕获不是的任意字符一次或多次匹配字面量使用和反向引用进行替换转链接原始转换后输出这是一个链接和另一个官网的例子",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-13 22:13:01",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E4%B8%89%E7%AB%A0%EF%BC%9A-%E9%AB%98%E7%BA%A7%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86"><span class="toc-text">第十三章： 高级数据处理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#13-1-JSON-%E5%A4%84%E7%90%86"><span class="toc-text">13.1 JSON 处理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-1-%E5%9F%BA%E6%9C%AC%E6%93%8D%E4%BD%9C"><span class="toc-text">13.1.1 基本操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-2-%E9%87%8D%E8%A6%81%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E"><span class="toc-text">13.1.2 重要参数说明</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-3-%E8%87%AA%E5%AE%9A%E4%B9%89%E5%AF%B9%E8%B1%A1%E5%BA%8F%E5%88%97%E5%8C%96"><span class="toc-text">13.1.3 自定义对象序列化</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-4-JSON-%E8%A7%A3%E7%A0%81%E4%B8%BA%E8%87%AA%E5%AE%9A%E4%B9%89%E5%AF%B9%E8%B1%A1"><span class="toc-text">13.1.4 JSON 解码为自定义对象</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-5-%E5%A4%84%E7%90%86%E5%A4%8D%E6%9D%82-JSON-%E6%95%B0%E6%8D%AE"><span class="toc-text">13.1.5 处理复杂 JSON 数据</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-6-%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96"><span class="toc-text">13.1.6 性能优化</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-7-JSON-Schema-%E9%AA%8C%E8%AF%81"><span class="toc-text">13.1.7 JSON Schema 验证</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#13-2-CSV-%E5%A4%84%E7%90%86"><span class="toc-text">13.2 CSV 处理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#13-2-1-%E5%9F%BA%E6%9C%AC%E8%AF%BB%E5%86%99%E6%93%8D%E4%BD%9C"><span class="toc-text">13.2.1 基本读写操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-2-2-%E4%BD%BF%E7%94%A8%E5%AD%97%E5%85%B8%E5%A4%84%E7%90%86-CSV-%E6%96%87%E4%BB%B6"><span class="toc-text">13.2.2 使用字典处理 CSV 文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-2-3-CSV-%E6%96%B9%E8%A8%80%E4%B8%8E%E6%A0%BC%E5%BC%8F%E5%8C%96%E9%80%89%E9%A1%B9"><span class="toc-text">13.2.3 CSV 方言与格式化选项</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-2-4-%E5%A4%84%E7%90%86%E7%89%B9%E6%AE%8A%E6%83%85%E5%86%B5"><span class="toc-text">13.2.4 处理特殊情况</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-2-5-CSV-%E6%96%87%E4%BB%B6%E7%9A%84%E9%AB%98%E7%BA%A7%E6%93%8D%E4%BD%9C"><span class="toc-text">13.2.5 CSV 文件的高级操作</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#13-3-XML-%E5%A4%84%E7%90%86"><span class="toc-text">13.3 XML 处理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#13-3-1-%E5%88%9B%E5%BB%BA%E5%92%8C%E5%86%99%E5%85%A5-XML"><span class="toc-text">13.3.1 创建和写入 XML</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-3-2-%E8%A7%A3%E6%9E%90%E5%92%8C%E8%AF%BB%E5%8F%96-XML"><span class="toc-text">13.3.2 解析和读取 XML</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-3-3-%E4%BF%AE%E6%94%B9-XML"><span class="toc-text">13.3.3 修改 XML</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-3-4-%E5%91%BD%E5%90%8D%E7%A9%BA%E9%97%B4%E5%A4%84%E7%90%86"><span class="toc-text">13.3.4 命名空间处理</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#13-4-%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%E5%A4%84%E7%90%86"><span class="toc-text">13.4 配置文件处理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#13-4-1-INI-%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%E5%A4%84%E7%90%86"><span class="toc-text">13.4.1 INI 配置文件处理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-4-2-YAML-%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%E5%A4%84%E7%90%86"><span class="toc-text">13.4.2 YAML 配置文件处理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-4-3-%E4%BD%BF%E7%94%A8%E7%8E%AF%E5%A2%83%E5%8F%98%E9%87%8F%E4%BD%9C%E4%B8%BA%E9%85%8D%E7%BD%AE"><span class="toc-text">13.4.3 使用环境变量作为配置</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-4-4-JSON-%E4%BD%9C%E4%B8%BA%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6"><span class="toc-text">13.4.4 JSON 作为配置文件</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#13-5-%E6%AD%A3%E5%88%99%E8%A1%A8%E8%BE%BE%E5%BC%8F"><span class="toc-text">13.5 正则表达式</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-1-%E5%B8%B8%E7%94%A8%E5%85%83%E5%AD%97%E7%AC%A6%E5%92%8C%E8%AF%AD%E6%B3%95"><span class="toc-text">13.5.1 常用元字符和语法</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-2-%E7%89%B9%E6%AE%8A%E5%BA%8F%E5%88%97-%E9%A2%84%E5%AE%9A%E4%B9%89%E5%AD%97%E7%AC%A6%E9%9B%86"><span class="toc-text">13.5.2 特殊序列 (预定义字符集)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-3-%E8%B4%AA%E5%A9%AA%E6%A8%A1%E5%BC%8F-vs-%E9%9D%9E%E8%B4%AA%E5%A9%AA%E6%A8%A1%E5%BC%8F"><span class="toc-text">13.5.3 贪婪模式 vs. 非贪婪模式</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-4-%E5%88%86%E7%BB%84%E4%B8%8E%E6%8D%95%E8%8E%B7"><span class="toc-text">13.5.4 分组与捕获</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-5-re-%E6%A8%A1%E5%9D%97%E6%A0%B8%E5%BF%83%E5%87%BD%E6%95%B0"><span class="toc-text">13.5.5 re 模块核心函数</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-6-Match-%E5%AF%B9%E8%B1%A1%E8%AF%A6%E8%A7%A3"><span class="toc-text">13.5.6 Match 对象详解</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-7-%E6%AD%A3%E5%88%99%E8%A1%A8%E8%BE%BE%E5%BC%8F%E6%A0%87%E5%BF%97-Flags"><span class="toc-text">13.5.7 正则表达式标志 (Flags)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-8-%E5%AE%9E%E9%99%85%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E7%A4%BA%E4%BE%8B"><span class="toc-text">13.5.8 实际应用场景示例</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Python基础知识总汇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">Python（十四）：第十三章： 高级数据处理</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-04-18T21:13:45.000Z" title="发表于 2025-04-19 05:13:45">2025-04-19</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.544Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">10.4k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>46分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="Python（十四）：第十三章： 高级数据处理"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/9962.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/9962.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url">Python基础知识总汇</a><h1 id="CrawlerTitle" itemprop="name headline">Python（十四）：第十三章： 高级数据处理</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-04-18T21:13:45.000Z" title="发表于 2025-04-19 05:13:45">2025-04-19</time><time itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.544Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></header><div id="postchat_postcontent"><h2 id="第十三章：-高级数据处理"><a href="#第十三章：-高级数据处理" class="headerlink" title="第十三章： 高级数据处理"></a>第十三章： 高级数据处理</h2><p>Python 提供了多种处理不同类型数据的工具和库，能够轻松处理结构化和非结构化数据。本章将深入探讨 Python 中常用的数据格式处理技术，包括 JSON、CSV、XML 和配置文件等。</p><h3 id="13-1-JSON-处理"><a href="#13-1-JSON-处理" class="headerlink" title="13.1 JSON 处理"></a>13.1 JSON 处理</h3><p>JSON (JavaScript Object Notation) 是一种轻量级的数据交换格式，易于人阅读和编写，也易于机器解析和生成。Python 通过内置的 <code>json</code> 模块提供了 JSON 的序列化和反序列化功能。</p><table><thead><tr><th>方法</th><th>描述</th></tr></thead><tbody><tr><td><code>json.dump(obj, fp)</code></td><td>将 Python 对象 <code>obj</code> 编码为 JSON 格式并写入文件 <code>fp</code>。</td></tr><tr><td><code>json.dumps(obj)</code></td><td>将 Python 对象 <code>obj</code> 编码为 JSON 格式并返回字符串。</td></tr><tr><td><code>json.load(fp)</code></td><td>从文件 <code>fp</code> 读取 JSON 数据并解码为 Python 对象。</td></tr><tr><td><code>json.loads(s)</code></td><td>将字符串 <code>s</code> 解码为 Python 对象。</td></tr></tbody></table><h4 id="13-1-1-基本操作"><a href="#13-1-1-基本操作" class="headerlink" title="13.1.1 基本操作"></a>13.1.1 基本操作</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> json</span><br><span class="line"></span><br><span class="line"><span class="comment"># Python对象转JSON</span></span><br><span class="line">data = {</span><br><span class="line">    <span class="string">"name"</span>: <span class="string">"张三"</span>,</span><br><span class="line">    <span class="string">"age"</span>: <span class="number">30</span>,</span><br><span class="line">    <span class="string">"is_student"</span>: <span class="literal">False</span>,</span><br><span class="line">    <span class="string">"courses"</span>: [<span class="string">"Python"</span>, <span class="string">"数据分析"</span>, <span class="string">"机器学习"</span>],</span><br><span class="line">    <span class="string">"scores"</span>: {<span class="string">"Python"</span>: <span class="number">95</span>, <span class="string">"数据分析"</span>: <span class="number">88</span>}</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment"># 转换为JSON字符串</span></span><br><span class="line">json_str = json.dumps(data, ensure_ascii=<span class="literal">False</span>, indent=<span class="number">4</span>)</span><br><span class="line"><span class="built_in">print</span>(json_str)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 写入JSON文件</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"data.json"</span>, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    json.dump(data, f, ensure_ascii=<span class="literal">False</span>, indent=<span class="number">4</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 从JSON字符串解析</span></span><br><span class="line">parsed_data = json.loads(json_str)</span><br><span class="line"><span class="built_in">print</span>(parsed_data[<span class="string">"name"</span>])  <span class="comment"># 张三</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 从JSON文件读取</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"data.json"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    loaded_data = json.load(f)</span><br><span class="line">    <span class="built_in">print</span>(loaded_data[<span class="string">"scores"</span>][<span class="string">"Python"</span>])  <span class="comment"># 95</span></span><br></pre></td></tr></tbody></table></figure><h4 id="13-1-2-重要参数说明"><a href="#13-1-2-重要参数说明" class="headerlink" title="13.1.2 重要参数说明"></a>13.1.2 重要参数说明</h4><table><thead><tr><th>参数</th><th>说明</th><th>用法示例</th></tr></thead><tbody><tr><td><code>ensure_ascii</code></td><td>是否转义非 ASCII 字符，False 时保留原始字符</td><td><code>json.dumps(data, ensure_ascii=False)</code></td></tr><tr><td><code>indent</code></td><td>缩进格式，美化输出</td><td><code>json.dumps(data, indent=4)</code></td></tr><tr><td><code>separators</code></td><td>指定分隔符，用于紧凑输出</td><td><code>json.dumps(data, separators=(',', ':'))</code></td></tr><tr><td><code>sort_keys</code></td><td>是否按键排序</td><td><code>json.dumps(data, sort_keys=True)</code></td></tr><tr><td><code>default</code></td><td>指定序列化函数，处理不可序列化对象</td><td><code>json.dumps(obj, default=lambda o: o.__dict__)</code></td></tr></tbody></table><h4 id="13-1-3-自定义对象序列化"><a href="#13-1-3-自定义对象序列化" class="headerlink" title="13.1.3 自定义对象序列化"></a>13.1.3 自定义对象序列化</h4><p>Python 的 <code>json</code> 模块默认无法直接序列化自定义类对象，但提供了多种方式解决：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> json</span><br><span class="line"></span><br><span class="line"><span class="comment"># ========== 方法一：提供default参数 ==========</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Person</span>:</span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__init__</span>(<span class="params">self, name, age</span>):</span><br><span class="line">        <span class="variable language_">self</span>.name = name</span><br><span class="line">        <span class="variable language_">self</span>.age = age</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">person_to_dict</span>(<span class="params">person</span>):</span><br><span class="line">    <span class="string">"""将Person对象转换为字典"""</span></span><br><span class="line">    <span class="keyword">return</span> {</span><br><span class="line">        <span class="string">"name"</span>: person.name,</span><br><span class="line">        <span class="string">"age"</span>: person.age</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 示例：使用default参数序列化自定义对象</span></span><br><span class="line">person = Person(<span class="string">"李四"</span>, <span class="number">25</span>)</span><br><span class="line">json_str = json.dumps(person, default=person_to_dict, ensure_ascii=<span class="literal">False</span>)</span><br><span class="line"><span class="built_in">print</span>(json_str)  <span class="comment"># {"name": "李四", "age": 25}</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># ========== 方法二：通过自定义编码器 ==========</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">PersonEncoder</span>(json.JSONEncoder):</span><br><span class="line">    <span class="string">"""自定义JSON编码器处理Person类"""</span></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">default</span>(<span class="params">self, obj</span>):</span><br><span class="line">        <span class="keyword">if</span> <span class="built_in">isinstance</span>(obj, Person):</span><br><span class="line">            <span class="keyword">return</span> {<span class="string">"name"</span>: obj.name, <span class="string">"age"</span>: obj.age}</span><br><span class="line">        <span class="keyword">return</span> <span class="built_in">super</span>().default(obj)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 示例：使用自定义编码器序列化对象</span></span><br><span class="line">json_str = json.dumps(person, cls=PersonEncoder, ensure_ascii=<span class="literal">False</span>)</span><br><span class="line"><span class="built_in">print</span>(json_str)  <span class="comment"># {"name": "李四", "age": 25}</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># ========== 方法三：添加to_json方法 ==========</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Student</span>:</span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__init__</span>(<span class="params">self, name, grade</span>):</span><br><span class="line">        <span class="variable language_">self</span>.name = name</span><br><span class="line">        <span class="variable language_">self</span>.grade = grade</span><br><span class="line"></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__repr__</span>(<span class="params">self</span>):</span><br><span class="line">        <span class="keyword">return</span> <span class="string">f"Student('<span class="subst">{self.name}</span>', <span class="subst">{self.grade}</span>)"</span></span><br><span class="line"></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">to_json</span>(<span class="params">self</span>):</span><br><span class="line">        <span class="string">"""返回可JSON序列化的字典"""</span></span><br><span class="line">        <span class="keyword">return</span> {</span><br><span class="line">            <span class="string">"name"</span>: <span class="variable language_">self</span>.name,</span><br><span class="line">            <span class="string">"grade"</span>: <span class="variable language_">self</span>.grade</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 示例：使用对象的to_json方法序列化</span></span><br><span class="line">students = [Student(<span class="string">"小明"</span>, <span class="number">90</span>), Student(<span class="string">"小红"</span>, <span class="number">88</span>)]</span><br><span class="line">json_str = json.dumps([s.to_json() <span class="keyword">for</span> s <span class="keyword">in</span> students], ensure_ascii=<span class="literal">False</span>)</span><br><span class="line"><span class="built_in">print</span>(json_str)  <span class="comment"># [{"name": "小明", "grade": 90}, {"name": "小红", "grade": 88}]</span></span><br></pre></td></tr></tbody></table></figure><h4 id="13-1-4-JSON-解码为自定义对象"><a href="#13-1-4-JSON-解码为自定义对象" class="headerlink" title="13.1.4 JSON 解码为自定义对象"></a>13.1.4 JSON 解码为自定义对象</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> json</span><br><span class="line"><span class="keyword">from</span> typing <span class="keyword">import</span> <span class="type">Dict</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Person</span>:</span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__init__</span>(<span class="params">self, name: <span class="built_in">str</span>, age: <span class="built_in">int</span></span>):</span><br><span class="line">        <span class="variable language_">self</span>.name = name</span><br><span class="line">        <span class="variable language_">self</span>.age = age</span><br><span class="line"></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__str__</span>(<span class="params">self</span>):</span><br><span class="line">        <span class="keyword">return</span> <span class="string">f"<span class="subst">{self.name}</span>(<span class="subst">{self.age}</span>)"</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">dict_to_person</span>(<span class="params">data: <span class="type">Dict</span></span>) -&gt; Person:</span><br><span class="line">    <span class="keyword">return</span> Person(data[<span class="string">"name"</span>], data[<span class="string">"age"</span>])</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用 json.loads() 的 object_hook 参数将 JSON 字符串直接转换为自定义对象</span></span><br><span class="line"><span class="comment"># object_hook 的用途:</span></span><br><span class="line"><span class="comment"># 1. 自动将 JSON 解析出的字典转换为自定义类的实例</span></span><br><span class="line"><span class="comment"># 2. 在解析 JSON 时进行数据转换和验证</span></span><br><span class="line"><span class="comment"># 3. 简化从 JSON 到对象模型的映射过程</span></span><br><span class="line"><span class="comment"># 4. 避免手动创建对象的繁琐步骤</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 工作原理:</span></span><br><span class="line"><span class="comment"># - json.loads() 首先将 JSON 字符串解析为 Python 字典</span></span><br><span class="line"><span class="comment"># - 然后对每个解析出的字典调用 object_hook 函数</span></span><br><span class="line"><span class="comment"># - object_hook 函数返回的对象将替代原始字典</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 实际应用场景:</span></span><br><span class="line"><span class="comment"># - API 响应数据转换为应用程序对象模型</span></span><br><span class="line"><span class="comment"># - 配置文件解析为配置对象</span></span><br><span class="line"><span class="comment"># - 数据导入时的格式转换</span></span><br><span class="line"></span><br><span class="line">person_data = <span class="string">'{"name": "Alice", "age": 25}'</span></span><br><span class="line">person = json.loads(person_data, object_hook=dict_to_person)</span><br><span class="line"><span class="built_in">print</span>(<span class="built_in">type</span>(person))  <span class="comment"># &lt;class '__main__.Person'&gt;</span></span><br><span class="line"><span class="built_in">print</span>([person.name, person.age])  <span class="comment"># ['Alice', 25]</span></span><br><span class="line"><span class="built_in">print</span>(person)  <span class="comment"># Alice(25)</span></span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><h4 id="13-1-5-处理复杂-JSON-数据"><a href="#13-1-5-处理复杂-JSON-数据" class="headerlink" title="13.1.5 处理复杂 JSON 数据"></a>13.1.5 处理复杂 JSON 数据</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 处理嵌套结构</span></span><br><span class="line">nested_json = <span class="string">'''</span></span><br><span class="line"><span class="string">{</span></span><br><span class="line"><span class="string">    "company": "ABC Corp",</span></span><br><span class="line"><span class="string">    "employees": [</span></span><br><span class="line"><span class="string">        {"name": "张三", "department": "技术", "skills": ["Python", "Java"]},</span></span><br><span class="line"><span class="string">        {"name": "李四", "department": "市场", "skills": ["营销", "策划"]}</span></span><br><span class="line"><span class="string">    ],</span></span><br><span class="line"><span class="string">    "locations": {</span></span><br><span class="line"><span class="string">        "headquarters": "北京",</span></span><br><span class="line"><span class="string">        "branches": ["上海", "广州", "深圳"]</span></span><br><span class="line"><span class="string">    }</span></span><br><span class="line"><span class="string">}</span></span><br><span class="line"><span class="string">'''</span></span><br><span class="line"></span><br><span class="line">data = json.loads(nested_json)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 访问嵌套数据</span></span><br><span class="line"><span class="built_in">print</span>(data[<span class="string">"employees"</span>][<span class="number">0</span>][<span class="string">"name"</span>])        <span class="comment"># 张三</span></span><br><span class="line"><span class="built_in">print</span>(data[<span class="string">"employees"</span>][<span class="number">0</span>][<span class="string">"skills"</span>][<span class="number">0</span>])   <span class="comment"># Python</span></span><br><span class="line"><span class="built_in">print</span>(data[<span class="string">"locations"</span>][<span class="string">"branches"</span>][<span class="number">1</span>])    <span class="comment"># 广州</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 修改嵌套数据</span></span><br><span class="line">data[<span class="string">"employees"</span>][<span class="number">0</span>][<span class="string">"skills"</span>].append(<span class="string">"C++"</span>)</span><br><span class="line">data[<span class="string">"locations"</span>][<span class="string">"branches"</span>].append(<span class="string">"成都"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 保存修改后的数据</span></span><br><span class="line">updated_json = json.dumps(data, ensure_ascii=<span class="literal">False</span>, indent=<span class="number">2</span>)</span><br><span class="line"><span class="built_in">print</span>(updated_json)</span><br></pre></td></tr></tbody></table></figure><h4 id="13-1-6-性能优化"><a href="#13-1-6-性能优化" class="headerlink" title="13.1.6 性能优化"></a>13.1.6 性能优化</h4><p>处理大型 JSON 文件时，可以使用流式解析来提高性能：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> ijson  <span class="comment"># 需安装: pip install ijson</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 流式解析大型JSON文件</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"large_file.json"</span>, <span class="string">"rb"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    <span class="comment"># 只提取特定字段</span></span><br><span class="line">    <span class="keyword">for</span> item <span class="keyword">in</span> ijson.items(f, <span class="string">"items.item"</span>):</span><br><span class="line">        <span class="built_in">print</span>(item[<span class="string">"id"</span>], item[<span class="string">"name"</span>])</span><br><span class="line">        <span class="comment"># 处理一项后继续，不必载入整个文件</span></span><br></pre></td></tr></tbody></table></figure><h4 id="13-1-7-JSON-Schema-验证"><a href="#13-1-7-JSON-Schema-验证" class="headerlink" title="13.1.7 JSON Schema 验证"></a>13.1.7 JSON Schema 验证</h4><p>验证 JSON 数据是否符合预期格式：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> jsonschema <span class="keyword">import</span> validate  <span class="comment"># 需安装: pip install jsonschema</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 定义JSON Schema</span></span><br><span class="line">schema = {</span><br><span class="line">    <span class="string">"type"</span>: <span class="string">"object"</span>,</span><br><span class="line">    <span class="string">"properties"</span>: {</span><br><span class="line">        <span class="string">"name"</span>: {<span class="string">"type"</span>: <span class="string">"string"</span>},</span><br><span class="line">        <span class="string">"age"</span>: {<span class="string">"type"</span>: <span class="string">"integer"</span>, <span class="string">"minimum"</span>: <span class="number">0</span>},</span><br><span class="line">        <span class="string">"email"</span>: {<span class="string">"type"</span>: <span class="string">"string"</span>, <span class="string">"format"</span>: <span class="string">"email"</span>}</span><br><span class="line">    },</span><br><span class="line">    <span class="string">"required"</span>: [<span class="string">"name"</span>, <span class="string">"age"</span>]</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment"># 验证数据</span></span><br><span class="line">valid_data = {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"age"</span>: <span class="number">30</span>, <span class="string">"email"</span>: <span class="string">"<EMAIL>"</span>}</span><br><span class="line">invalid_data = {<span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"age"</span>: -<span class="number">5</span>}</span><br><span class="line"></span><br><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    validate(instance=valid_data, schema=schema)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"有效数据"</span>)</span><br><span class="line"><span class="keyword">except</span> Exception <span class="keyword">as</span> e:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"验证失败: <span class="subst">{e}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    validate(instance=invalid_data, schema=schema)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"有效数据"</span>)</span><br><span class="line"><span class="keyword">except</span> Exception <span class="keyword">as</span> e:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"验证失败: <span class="subst">{e}</span>"</span>)  <span class="comment"># 会因age小于0而失败</span></span><br></pre></td></tr></tbody></table></figure><h3 id="13-2-CSV-处理"><a href="#13-2-CSV-处理" class="headerlink" title="13.2 CSV 处理"></a>13.2 CSV 处理</h3><p>CSV (Comma-Separated Values) 是一种常见的表格数据格式。Python 的 <code>csv</code> 模块提供了读写 CSV 文件的功能，适用于处理电子表格和数据库导出数据。</p><blockquote><p>在我们写入中文数据时，尽量将编码更换为 <code>GBK</code> 否则写入 CSV 会导致一些乱码问题</p></blockquote><h4 id="13-2-1-基本读写操作"><a href="#13-2-1-基本读写操作" class="headerlink" title="13.2.1 基本读写操作"></a>13.2.1 基本读写操作</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> csv</span><br><span class="line"></span><br><span class="line"><span class="comment"># 写入CSV文件</span></span><br><span class="line">data = [</span><br><span class="line">    [<span class="string">"姓名"</span>, <span class="string">"年龄"</span>, <span class="string">"城市"</span>],</span><br><span class="line">    [<span class="string">"张三"</span>, <span class="number">30</span>, <span class="string">"北京"</span>],</span><br><span class="line">    [<span class="string">"李四"</span>, <span class="number">25</span>, <span class="string">"上海"</span>],</span><br><span class="line">    [<span class="string">"王五"</span>, <span class="number">28</span>, <span class="string">"广州"</span>]</span><br><span class="line">]</span><br><span class="line"></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"people.csv"</span>, <span class="string">"w"</span>, newline=<span class="string">""</span>, encoding=<span class="string">"gbk"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    writer = csv.writer(f)</span><br><span class="line">    writer.writerows(data)  <span class="comment"># 一次写入多行</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 逐行写入</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"people_row.csv"</span>, <span class="string">"w"</span>, newline=<span class="string">""</span>, encoding=<span class="string">"gbk"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    writer = csv.writer(f)</span><br><span class="line">    <span class="keyword">for</span> row <span class="keyword">in</span> data:</span><br><span class="line">        writer.writerow(row)  <span class="comment"># 一次写入一行</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 读取CSV文件</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"people.csv"</span>, <span class="string">"r"</span>, encoding=<span class="string">"gbk"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    reader = csv.reader(f)</span><br><span class="line">    <span class="keyword">for</span> row <span class="keyword">in</span> reader:</span><br><span class="line">        <span class="built_in">print</span>(row)</span><br></pre></td></tr></tbody></table></figure><h4 id="13-2-2-使用字典处理-CSV-文件"><a href="#13-2-2-使用字典处理-CSV-文件" class="headerlink" title="13.2.2 使用字典处理 CSV 文件"></a>13.2.2 使用字典处理 CSV 文件</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 使用字典写入CSV</span></span><br><span class="line"><span class="keyword">import</span> csv</span><br><span class="line"></span><br><span class="line">dict_data = [</span><br><span class="line">    {<span class="string">"姓名"</span>: <span class="string">"张三"</span>, <span class="string">"年龄"</span>: <span class="number">30</span>, <span class="string">"城市"</span>: <span class="string">"北京"</span>},</span><br><span class="line">    {<span class="string">"姓名"</span>: <span class="string">"李四"</span>, <span class="string">"年龄"</span>: <span class="number">25</span>, <span class="string">"城市"</span>: <span class="string">"上海"</span>},</span><br><span class="line">    {<span class="string">"姓名"</span>: <span class="string">"王五"</span>, <span class="string">"年龄"</span>: <span class="number">28</span>, <span class="string">"城市"</span>: <span class="string">"广州"</span>}</span><br><span class="line">]</span><br><span class="line"></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"people_dict.csv"</span>, <span class="string">"w"</span>, newline=<span class="string">""</span>, encoding=<span class="string">"gbk"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    fieldnames = [<span class="string">"姓名"</span>, <span class="string">"年龄"</span>, <span class="string">"城市"</span>]</span><br><span class="line">    writer = csv.DictWriter(f, fieldnames=fieldnames)</span><br><span class="line">    writer.writeheader()  <span class="comment"># 写入表头</span></span><br><span class="line">    writer.writerows(dict_data)  <span class="comment"># 写入多行数据</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用字典读取CSV</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"people_dict.csv"</span>, <span class="string">"r"</span>, encoding=<span class="string">"gbk"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    reader = csv.DictReader(f)</span><br><span class="line">    <span class="keyword">for</span> row <span class="keyword">in</span> reader:</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"<span class="subst">{row[<span class="string">'姓名'</span>]}</span> (<span class="subst">{row[<span class="string">'年龄'</span>]}</span>岁) 来自 <span class="subst">{row[<span class="string">'城市'</span>]}</span>"</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="13-2-3-CSV-方言与格式化选项"><a href="#13-2-3-CSV-方言与格式化选项" class="headerlink" title="13.2.3 CSV 方言与格式化选项"></a>13.2.3 CSV 方言与格式化选项</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 自定义CSV方言</span></span><br><span class="line">csv.register_dialect(</span><br><span class="line">    <span class="string">'tab_dialect'</span>,</span><br><span class="line">    delimiter=<span class="string">'\t'</span>,       <span class="comment"># 使用制表符作为分隔符</span></span><br><span class="line">    quotechar=<span class="string">'"'</span>,        <span class="comment"># 引号字符</span></span><br><span class="line">    escapechar=<span class="string">'\\'</span>,      <span class="comment"># 转义字符</span></span><br><span class="line">    doublequote=<span class="literal">False</span>,    <span class="comment"># 不使用双引号转义</span></span><br><span class="line">    quoting=csv.QUOTE_MINIMAL  <span class="comment"># 最小引用策略</span></span><br><span class="line">)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用自定义方言</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"tab_data.csv"</span>, <span class="string">"w"</span>, newline=<span class="string">""</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    writer = csv.writer(f, dialect=<span class="string">'tab_dialect'</span>)</span><br><span class="line">    writer.writerows(data)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 常见格式化选项</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"formatted.csv"</span>, <span class="string">"w"</span>, newline=<span class="string">""</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    writer = csv.writer(</span><br><span class="line">        f,</span><br><span class="line">        delimiter=<span class="string">','</span>,          <span class="comment"># 分隔符</span></span><br><span class="line">        quotechar=<span class="string">'"'</span>,          <span class="comment"># 引号字符</span></span><br><span class="line">        quoting=csv.QUOTE_NONNUMERIC,  <span class="comment"># 为非数值字段添加引号</span></span><br><span class="line">        escapechar=<span class="string">'\\'</span>,        <span class="comment"># 转义字符</span></span><br><span class="line">        lineterminator=<span class="string">'\n'</span>     <span class="comment"># 行终止符</span></span><br><span class="line">    )</span><br><span class="line">    writer.writerows(data)</span><br></pre></td></tr></tbody></table></figure><h4 id="13-2-4-处理特殊情况"><a href="#13-2-4-处理特殊情况" class="headerlink" title="13.2.4 处理特殊情况"></a>13.2.4 处理特殊情况</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 处理含有引号和逗号的数据</span></span><br><span class="line">complex_data = [</span><br><span class="line">    [<span class="string">"产品"</span>, <span class="string">"描述"</span>, <span class="string">"价格"</span>],</span><br><span class="line">    [<span class="string">"笔记本"</span>, <span class="string">"14\" 高配, i7处理器"</span>, <span class="number">5999.99</span>],</span><br><span class="line">    [<span class="string">"手机"</span>, <span class="string">"5.5\" 屏幕, 双卡双待"</span>, <span class="number">2999.50</span>]</span><br><span class="line">]</span><br><span class="line"></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"complex.csv"</span>, <span class="string">"w"</span>, newline=<span class="string">""</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    writer = csv.writer(f, quoting=csv.QUOTE_ALL)  <span class="comment"># 所有字段加引号</span></span><br><span class="line">    writer.writerows(complex_data)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 跳过特定行</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"complex.csv"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    reader = csv.reader(f)</span><br><span class="line">    <span class="built_in">next</span>(reader)  <span class="comment"># 跳过表头</span></span><br><span class="line">    <span class="keyword">for</span> row <span class="keyword">in</span> reader:</span><br><span class="line">        <span class="built_in">print</span>(row)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 处理缺失值</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"missing.csv"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    reader = csv.reader(f)</span><br><span class="line">    <span class="keyword">for</span> row <span class="keyword">in</span> reader:</span><br><span class="line">        <span class="comment"># 将空字符串转换为None</span></span><br><span class="line">        processed_row = [<span class="literal">None</span> <span class="keyword">if</span> cell == <span class="string">''</span> <span class="keyword">else</span> cell <span class="keyword">for</span> cell <span class="keyword">in</span> row]</span><br><span class="line">        <span class="built_in">print</span>(processed_row)</span><br></pre></td></tr></tbody></table></figure><h4 id="13-2-5-CSV-文件的高级操作"><a href="#13-2-5-CSV-文件的高级操作" class="headerlink" title="13.2.5 CSV 文件的高级操作"></a>13.2.5 CSV 文件的高级操作</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 过滤行</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"people.csv"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    reader = csv.DictReader(f)</span><br><span class="line">    <span class="comment"># 筛选年龄大于25的记录</span></span><br><span class="line">    filtered_data = [row <span class="keyword">for</span> row <span class="keyword">in</span> reader <span class="keyword">if</span> <span class="built_in">int</span>(row[<span class="string">"年龄"</span>]) &gt; <span class="number">25</span>]</span><br><span class="line"></span><br><span class="line"><span class="comment"># 计算统计值</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"grades.csv"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    reader = csv.DictReader(f)</span><br><span class="line">    <span class="comment"># 计算平均分</span></span><br><span class="line">    scores = [<span class="built_in">float</span>(row[<span class="string">"分数"</span>]) <span class="keyword">for</span> row <span class="keyword">in</span> reader]</span><br><span class="line">    avg_score = <span class="built_in">sum</span>(scores) / <span class="built_in">len</span>(scores)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"平均分: <span class="subst">{avg_score:<span class="number">.2</span>f}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 合并多个CSV文件</span></span><br><span class="line"><span class="keyword">import</span> glob</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">merge_csv_files</span>(<span class="params">file_pattern, output_file</span>):</span><br><span class="line">    <span class="comment"># 获取所有匹配的文件</span></span><br><span class="line">    all_files = glob.glob(file_pattern)</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(output_file, <span class="string">"w"</span>, newline=<span class="string">""</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> outfile:</span><br><span class="line">        <span class="comment"># 假设所有文件结构相同</span></span><br><span class="line">        <span class="keyword">for</span> i, filename <span class="keyword">in</span> <span class="built_in">enumerate</span>(all_files):</span><br><span class="line">            <span class="keyword">with</span> <span class="built_in">open</span>(filename, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> infile:</span><br><span class="line">                reader = csv.reader(infile)</span><br><span class="line">                <span class="keyword">if</span> i == <span class="number">0</span>:</span><br><span class="line">                    <span class="comment"># 第一个文件，保留表头</span></span><br><span class="line">                    <span class="keyword">for</span> row <span class="keyword">in</span> reader:</span><br><span class="line">                        csv.writer(outfile).writerow(row)</span><br><span class="line">                <span class="keyword">else</span>:</span><br><span class="line">                    <span class="comment"># 跳过后续文件的表头</span></span><br><span class="line">                    <span class="built_in">next</span>(reader, <span class="literal">None</span>)</span><br><span class="line">                    <span class="keyword">for</span> row <span class="keyword">in</span> reader:</span><br><span class="line">                        csv.writer(outfile).writerow(row)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用示例</span></span><br><span class="line"><span class="comment"># merge_csv_files("data_*.csv", "merged_data.csv")</span></span><br></pre></td></tr></tbody></table></figure><h3 id="13-3-XML-处理"><a href="#13-3-XML-处理" class="headerlink" title="13.3 XML 处理"></a>13.3 XML 处理</h3><p>XML (eXtensible Markup Language) 是一种用于存储和传输数据的标记语言。Python 提供多种处理 XML 的方法，最常用的是 <code>xml.etree.ElementTree</code> 模块。</p><h4 id="13-3-1-创建和写入-XML"><a href="#13-3-1-创建和写入-XML" class="headerlink" title="13.3.1 创建和写入 XML"></a>13.3.1 创建和写入 XML</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> xml.etree.ElementTree <span class="keyword">as</span> ET</span><br><span class="line"></span><br><span class="line"><span class="comment"># 创建XML根元素</span></span><br><span class="line">root = ET.Element(<span class="string">"data"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 添加子元素</span></span><br><span class="line">items = ET.SubElement(root, <span class="string">"items"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 添加多个项目</span></span><br><span class="line"><span class="keyword">for</span> i <span class="keyword">in</span> <span class="built_in">range</span>(<span class="number">1</span>, <span class="number">4</span>):</span><br><span class="line">    item = ET.SubElement(items, <span class="string">"item"</span>)</span><br><span class="line">    item.<span class="built_in">set</span>(<span class="string">"id"</span>, <span class="built_in">str</span>(i))  <span class="comment"># 设置属性</span></span><br><span class="line">    item.text = <span class="string">f"第<span class="subst">{i}</span>项"</span>  <span class="comment"># 设置文本内容</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 添加嵌套元素</span></span><br><span class="line">    detail = ET.SubElement(item, <span class="string">"detail"</span>)</span><br><span class="line">    detail.text = <span class="string">f"项目<span class="subst">{i}</span>的详情"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 创建用户信息部分</span></span><br><span class="line">users = ET.SubElement(root, <span class="string">"users"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 添加用户</span></span><br><span class="line">user = ET.SubElement(users, <span class="string">"user"</span>)</span><br><span class="line">user.<span class="built_in">set</span>(<span class="string">"name"</span>, <span class="string">"张三"</span>)</span><br><span class="line">ET.SubElement(user, <span class="string">"age"</span>).text = <span class="string">"30"</span></span><br><span class="line">ET.SubElement(user, <span class="string">"city"</span>).text = <span class="string">"北京"</span></span><br><span class="line"></span><br><span class="line">user2 = ET.SubElement(users, <span class="string">"user"</span>)</span><br><span class="line">user2.<span class="built_in">set</span>(<span class="string">"name"</span>, <span class="string">"李四"</span>)</span><br><span class="line">ET.SubElement(user2, <span class="string">"age"</span>).text = <span class="string">"25"</span></span><br><span class="line">ET.SubElement(user2, <span class="string">"city"</span>).text = <span class="string">"上海"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 生成XML字符串</span></span><br><span class="line">xml_str = ET.tostring(root, encoding=<span class="string">"utf-8"</span>).decode(<span class="string">"utf-8"</span>)</span><br><span class="line"><span class="built_in">print</span>(xml_str)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 写入XML文件</span></span><br><span class="line">tree = ET.ElementTree(root)</span><br><span class="line">tree.write(<span class="string">"data.xml"</span>, encoding=<span class="string">"utf-8"</span>, xml_declaration=<span class="literal">True</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="13-3-2-解析和读取-XML"><a href="#13-3-2-解析和读取-XML" class="headerlink" title="13.3.2 解析和读取 XML"></a>13.3.2 解析和读取 XML</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 从文件解析XML</span></span><br><span class="line">tree = ET.parse(<span class="string">"data.xml"</span>)</span><br><span class="line">root = tree.getroot()</span><br><span class="line"></span><br><span class="line"><span class="comment"># 从字符串解析XML</span></span><br><span class="line">xml_string = <span class="string">'&lt;data&gt;&lt;item id="1"&gt;测试&lt;/item&gt;&lt;/data&gt;'</span></span><br><span class="line">root = ET.fromstring(xml_string)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 获取元素标签和属性</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"根元素标签: <span class="subst">{root.tag}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 遍历子元素</span></span><br><span class="line"><span class="keyword">for</span> child <span class="keyword">in</span> root:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"子元素: <span class="subst">{child.tag}</span>, 属性: <span class="subst">{child.attrib}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 查找特定元素 - find()查找第一个匹配元素</span></span><br><span class="line">items = root.find(<span class="string">"items"</span>)</span><br><span class="line"><span class="keyword">if</span> items <span class="keyword">is</span> <span class="keyword">not</span> <span class="literal">None</span>:</span><br><span class="line">    <span class="comment"># 使用findall()查找所有匹配的子元素</span></span><br><span class="line">    <span class="keyword">for</span> item <span class="keyword">in</span> items.findall(<span class="string">"item"</span>):</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"项目ID: <span class="subst">{item.get(<span class="string">'id'</span>)}</span>, 内容: <span class="subst">{item.text}</span>"</span>)</span><br><span class="line">        <span class="comment"># 获取嵌套元素</span></span><br><span class="line">        detail = item.find(<span class="string">"detail"</span>)</span><br><span class="line">        <span class="keyword">if</span> detail <span class="keyword">is</span> <span class="keyword">not</span> <span class="literal">None</span>:</span><br><span class="line">            <span class="built_in">print</span>(<span class="string">f"  详情: <span class="subst">{detail.text}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用XPath查询</span></span><br><span class="line"><span class="comment"># 查找所有用户名称</span></span><br><span class="line">users = root.findall(<span class="string">".//user"</span>)</span><br><span class="line"><span class="keyword">for</span> user <span class="keyword">in</span> users:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"用户: <span class="subst">{user.get(<span class="string">'name'</span>)}</span>"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"  年龄: <span class="subst">{user.find(<span class="string">'age'</span>).text}</span>"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"  城市: <span class="subst">{user.find(<span class="string">'city'</span>).text}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 更复杂的XPath查询 - 查找北京的用户</span></span><br><span class="line">beijing_users = root.findall(<span class="string">".//user[city='北京']"</span>)</span><br><span class="line"><span class="keyword">for</span> user <span class="keyword">in</span> beijing_users:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"北京用户: <span class="subst">{user.get(<span class="string">'name'</span>)}</span>"</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="13-3-3-修改-XML"><a href="#13-3-3-修改-XML" class="headerlink" title="13.3.3 修改 XML"></a>13.3.3 修改 XML</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 修改元素属性</span></span><br><span class="line">user = root.find(<span class="string">".//user[@name='张三']"</span>)</span><br><span class="line"><span class="keyword">if</span> user <span class="keyword">is</span> <span class="keyword">not</span> <span class="literal">None</span>:</span><br><span class="line">    user.<span class="built_in">set</span>(<span class="string">"status"</span>, <span class="string">"active"</span>)  <span class="comment"># 添加新属性</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 修改子元素文本</span></span><br><span class="line">    age_elem = user.find(<span class="string">"age"</span>)</span><br><span class="line">    <span class="keyword">if</span> age_elem <span class="keyword">is</span> <span class="keyword">not</span> <span class="literal">None</span>:</span><br><span class="line">        age_elem.text = <span class="string">"31"</span>  <span class="comment"># 修改年龄</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 添加新元素</span></span><br><span class="line">    ET.SubElement(user, <span class="string">"email"</span>).text = <span class="string">"<EMAIL>"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 删除元素</span></span><br><span class="line">users = root.find(<span class="string">"users"</span>)</span><br><span class="line"><span class="keyword">if</span> users <span class="keyword">is</span> <span class="keyword">not</span> <span class="literal">None</span>:</span><br><span class="line">    <span class="keyword">for</span> user <span class="keyword">in</span> users.findall(<span class="string">"user"</span>):</span><br><span class="line">        <span class="keyword">if</span> user.get(<span class="string">"name"</span>) == <span class="string">"李四"</span>:</span><br><span class="line">            users.remove(user)</span><br><span class="line">            <span class="keyword">break</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 保存修改</span></span><br><span class="line">tree.write(<span class="string">"updated_data.xml"</span>, encoding=<span class="string">"utf-8"</span>, xml_declaration=<span class="literal">True</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="13-3-4-命名空间处理"><a href="#13-3-4-命名空间处理" class="headerlink" title="13.3.4 命名空间处理"></a>13.3.4 命名空间处理</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 创建带命名空间的XML</span></span><br><span class="line">root = ET.Element(<span class="string">"data"</span>, {<span class="string">"xmlns:dt"</span>: <span class="string">"http://example.org/datatypes"</span>})</span><br><span class="line"></span><br><span class="line"><span class="comment"># 添加带命名空间前缀的元素</span></span><br><span class="line">item = ET.SubElement(root, <span class="string">"dt:item"</span>)</span><br><span class="line">item.<span class="built_in">set</span>(<span class="string">"dt:type"</span>, <span class="string">"special"</span>)</span><br><span class="line">item.text = <span class="string">"带命名空间的元素"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 生成XML字符串</span></span><br><span class="line">ns_xml = ET.tostring(root, encoding=<span class="string">"utf-8"</span>).decode(<span class="string">"utf-8"</span>)</span><br><span class="line"><span class="built_in">print</span>(ns_xml)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 解析带命名空间的XML</span></span><br><span class="line">ns_root = ET.fromstring(ns_xml)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用带命名空间的XPath查询</span></span><br><span class="line">namespaces = {<span class="string">"dt"</span>: <span class="string">"http://example.org/datatypes"</span>}</span><br><span class="line">ns_items = ns_root.findall(<span class="string">".//dt:item"</span>, namespaces)</span><br><span class="line"></span><br><span class="line"><span class="keyword">for</span> item <span class="keyword">in</span> ns_items:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"找到命名空间元素: <span class="subst">{item.text}</span>"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"类型属性: <span class="subst">{item.get(<span class="string">'{http://example.org/datatypes}type'</span>)}</span>"</span>)</span><br></pre></td></tr></tbody></table></figure><h3 id="13-4-配置文件处理"><a href="#13-4-配置文件处理" class="headerlink" title="13.4 配置文件处理"></a>13.4 配置文件处理</h3><p>配置文件是应用程序保存设置和首选项的常用方式。Python 提供了多种处理不同格式配置文件的方法。</p><h4 id="13-4-1-INI-配置文件处理"><a href="#13-4-1-INI-配置文件处理" class="headerlink" title="13.4.1 INI 配置文件处理"></a>13.4.1 INI 配置文件处理</h4><p>INI 文件是一种结构简单的配置文件格式，Python 通过 <code>configparser</code> 模块提供支持。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> configparser</span><br><span class="line"><span class="comment"># configparser是Python标准库中用于处理配置文件的模块</span></span><br><span class="line"><span class="comment"># 它可以读取、写入和修改类似INI格式的配置文件</span></span><br><span class="line"><span class="comment"># 配置文件通常包含节(sections)</span></span><br><span class="line"><span class="comment"># 如:[DEFAULT]</span></span><br><span class="line"><span class="comment"># 和每个节下的键值对(key-value pairs)</span></span><br><span class="line"><span class="comment"># 如:</span></span><br><span class="line"><span class="comment"># language = 中文</span></span><br><span class="line"><span class="comment"># theme = 默认</span></span><br><span class="line"><span class="comment"># auto_save = true</span></span><br><span class="line"><span class="comment"># save_interval = 10</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 创建一个新的配置解析器</span></span><br><span class="line">config = configparser.ConfigParser()</span><br><span class="line"></span><br><span class="line"><span class="comment"># 添加默认节和配置项</span></span><br><span class="line">config[<span class="string">"DEFAULT"</span>] = {</span><br><span class="line">    <span class="string">"language"</span>: <span class="string">"中文"</span>,</span><br><span class="line">    <span class="string">"theme"</span>: <span class="string">"默认"</span>,</span><br><span class="line">    <span class="string">"auto_save"</span>: <span class="string">"true"</span>,  </span><br><span class="line">    <span class="string">"save_interval"</span>: <span class="string">"10"</span></span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment"># 添加应用设置节</span></span><br><span class="line">config[<span class="string">"应用设置"</span>] = {}</span><br><span class="line">config[<span class="string">"应用设置"</span>][<span class="string">"font_size"</span>] = <span class="string">"14"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 添加用户信息节</span></span><br><span class="line">config[<span class="string">"用户信息"</span>] = {}</span><br><span class="line">user_info = config[<span class="string">"用户信息"</span>]  <span class="comment"># 创建一个引用，方便添加多个配置项</span></span><br><span class="line">user_info[<span class="string">"username"</span>] = <span class="string">"张三"</span></span><br><span class="line">user_info[<span class="string">"email"</span>] = <span class="string">"<EMAIL>"</span></span><br><span class="line">user_info[<span class="string">"remember_password"</span>] = <span class="string">"false"</span>  <span class="comment"># 修改为标准布尔值字符串</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 添加数据库连接节</span></span><br><span class="line">config[<span class="string">"数据库"</span>] = {}</span><br><span class="line">config[<span class="string">"数据库"</span>][<span class="string">"host"</span>] = <span class="string">"localhost"</span></span><br><span class="line">config[<span class="string">"数据库"</span>][<span class="string">"port"</span>] = <span class="string">"3306"</span></span><br><span class="line">config[<span class="string">"数据库"</span>][<span class="string">"username"</span>] = <span class="string">"root"</span></span><br><span class="line">config[<span class="string">"数据库"</span>][<span class="string">"password"</span>] = <span class="string">"123456"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 将配置写入文件</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"config.ini"</span>, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    config.write(f)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 读取配置文件</span></span><br><span class="line">config = configparser.ConfigParser()</span><br><span class="line">config.read(<span class="string">"config.ini"</span>, encoding=<span class="string">"utf-8"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 获取所有节名称</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"所有配置节:"</span>, config.sections())  <span class="comment"># ['应用设置', '用户信息', '数据库']</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 获取节中的所有键</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"用户信息节中的所有键:"</span>, <span class="built_in">list</span>(config[<span class="string">"用户信息"</span>].keys()))</span><br><span class="line"></span><br><span class="line"><span class="comment"># 获取特定配置值</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"用户名:"</span>, config[<span class="string">"用户信息"</span>][<span class="string">"username"</span>])  <span class="comment"># 张三</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 获取默认节中的值</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"默认语言:"</span>, config.get(<span class="string">"应用设置"</span>, <span class="string">"language"</span>))  <span class="comment"># 使用DEFAULT中的值</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 类型转换方法</span></span><br><span class="line">font_size = config.getint(<span class="string">"应用设置"</span>, <span class="string">"font_size"</span>)</span><br><span class="line">auto_save = config.getboolean(<span class="string">"DEFAULT"</span>, <span class="string">"auto_save"</span>, fallback=<span class="literal">True</span>)  <span class="comment"># 将"true"转换为True</span></span><br><span class="line">save_interval = config.getint(<span class="string">"DEFAULT"</span>, <span class="string">"save_interval"</span>)</span><br><span class="line"></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"字体大小: <span class="subst">{font_size}</span>, 类型: <span class="subst">{<span class="built_in">type</span>(font_size)}</span>"</span>)  <span class="comment"># 字体大小: 14, 类型: &lt;class 'int'&gt;</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"自动保存: <span class="subst">{auto_save}</span>, 类型: <span class="subst">{<span class="built_in">type</span>(auto_save)}</span>"</span>)  <span class="comment"># 自动保存: True, 类型: &lt;class 'bool'&gt;</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 修改配置</span></span><br><span class="line">config[<span class="string">"用户信息"</span>][<span class="string">"username"</span>] = <span class="string">"李四"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 添加新配置</span></span><br><span class="line"><span class="keyword">if</span> <span class="string">"日志设置"</span> <span class="keyword">not</span> <span class="keyword">in</span> config:</span><br><span class="line">    config[<span class="string">"日志设置"</span>] = {}</span><br><span class="line">config[<span class="string">"日志设置"</span>][<span class="string">"log_level"</span>] = <span class="string">"INFO"</span></span><br><span class="line">config[<span class="string">"日志设置"</span>][<span class="string">"log_file"</span>] = <span class="string">"app.log"</span></span><br><span class="line">config[<span class="string">"日志设置"</span>][<span class="string">"max_size"</span>] = <span class="string">"10MB"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 保存修改后的配置</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"updated_config.ini"</span>, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    config.write(f)</span><br></pre></td></tr></tbody></table></figure><h4 id="13-4-2-YAML-配置文件处理"><a href="#13-4-2-YAML-配置文件处理" class="headerlink" title="13.4.2 YAML 配置文件处理"></a>13.4.2 YAML 配置文件处理</h4><p>YAML 是一种人类友好的数据序列化格式，需要安装 <code>PyYAML</code> 库。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 需要安装PyYAML: pip install pyyaml</span></span><br><span class="line"><span class="keyword">import</span> yaml</span><br><span class="line"></span><br><span class="line"><span class="comment"># 创建YAML数据</span></span><br><span class="line">data = {</span><br><span class="line">    <span class="string">"server"</span>: {</span><br><span class="line">        <span class="string">"host"</span>: <span class="string">"example.com"</span>,</span><br><span class="line">        <span class="string">"port"</span>: <span class="number">8080</span></span><br><span class="line">    },</span><br><span class="line">    <span class="string">"database"</span>: {</span><br><span class="line">        <span class="string">"host"</span>: <span class="string">"localhost"</span>,</span><br><span class="line">        <span class="string">"port"</span>: <span class="number">5432</span>,</span><br><span class="line">        <span class="string">"username"</span>: <span class="string">"admin"</span>,</span><br><span class="line">        <span class="string">"password"</span>: <span class="string">"secret"</span></span><br><span class="line">    },</span><br><span class="line">    <span class="string">"logging"</span>: {</span><br><span class="line">        <span class="string">"level"</span>: <span class="string">"INFO"</span>,</span><br><span class="line">        <span class="string">"file"</span>: <span class="string">"/var/log/app.log"</span></span><br><span class="line">    },</span><br><span class="line">    <span class="string">"users"</span>: [</span><br><span class="line">        {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"role"</span>: <span class="string">"admin"</span>},</span><br><span class="line">        {<span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"role"</span>: <span class="string">"user"</span>}</span><br><span class="line">    ]</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment"># 写入YAML文件</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"config.yaml"</span>, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    yaml.dump(data, f, default_flow_style=<span class="literal">False</span>, allow_unicode=<span class="literal">True</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 读取YAML文件</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"config.yaml"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    config = yaml.safe_load(f)</span><br><span class="line">    </span><br><span class="line"><span class="comment"># 访问配置</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"服务器地址: <span class="subst">{config[<span class="string">'server'</span>][<span class="string">'host'</span>]}</span>"</span>)  <span class="comment"># example.com</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"第一个用户: <span class="subst">{config[<span class="string">'users'</span>][<span class="number">0</span>][<span class="string">'name'</span>]}</span>"</span>)  <span class="comment"># 张三</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 修改配置</span></span><br><span class="line">config[<span class="string">"server"</span>][<span class="string">"port"</span>] = <span class="number">9090</span></span><br><span class="line">config[<span class="string">"users"</span>].append({<span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"role"</span>: <span class="string">"user"</span>})</span><br><span class="line"></span><br><span class="line"><span class="comment"># 保存修改</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"updated_config.yaml"</span>, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    yaml.dump(config, f, default_flow_style=<span class="literal">False</span>, allow_unicode=<span class="literal">True</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="13-4-3-使用环境变量作为配置"><a href="#13-4-3-使用环境变量作为配置" class="headerlink" title="13.4.3 使用环境变量作为配置"></a>13.4.3 使用环境变量作为配置</h4><p>环境变量是一种灵活的配置方式，尤其适用于容器化应用。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> os</span><br><span class="line"><span class="keyword">from</span> dotenv <span class="keyword">import</span> load_dotenv  <span class="comment"># 需安装: pip install python-dotenv</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 从.env文件加载环境变量</span></span><br><span class="line">load_dotenv()  <span class="comment"># 默认加载当前目录下的.env文件</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 读取环境变量，提供默认值</span></span><br><span class="line">database_url = os.environ.get(<span class="string">"DATABASE_URL"</span>, <span class="string">"sqlite:///default.db"</span>)</span><br><span class="line">debug_mode = os.environ.get(<span class="string">"DEBUG"</span>, <span class="string">"False"</span>).lower() <span class="keyword">in</span> (<span class="string">"true"</span>, <span class="string">"1"</span>, <span class="string">"yes"</span>)</span><br><span class="line">port = <span class="built_in">int</span>(os.environ.get(<span class="string">"PORT"</span>, <span class="string">"8000"</span>))</span><br><span class="line"></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"数据库URL: <span class="subst">{database_url}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"调试模式: <span class="subst">{debug_mode}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"端口: <span class="subst">{port}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 创建.env文件示例</span></span><br><span class="line">env_content = <span class="string">"""</span></span><br><span class="line"><span class="string"># 数据库设置</span></span><br><span class="line"><span class="string">DATABASE_URL=postgresql://user:pass@localhost/dbname</span></span><br><span class="line"><span class="string"># 应用设置</span></span><br><span class="line"><span class="string">DEBUG=True</span></span><br><span class="line"><span class="string">PORT=5000</span></span><br><span class="line"><span class="string">"""</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">".env.example"</span>, <span class="string">"w"</span>) <span class="keyword">as</span> f:</span><br><span class="line">    f.write(env_content)</span><br></pre></td></tr></tbody></table></figure><h4 id="13-4-4-JSON-作为配置文件"><a href="#13-4-4-JSON-作为配置文件" class="headerlink" title="13.4.4 JSON 作为配置文件"></a>13.4.4 JSON 作为配置文件</h4><p>JSON 也是一种常用的配置文件格式，尤其适合需要与 Web 应用共享配置的场景。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> json</span><br><span class="line"><span class="keyword">import</span> os</span><br><span class="line"></span><br><span class="line"><span class="comment"># 默认配置</span></span><br><span class="line">default_config = {</span><br><span class="line">    <span class="string">"app_name"</span>: <span class="string">"MyApp"</span>,</span><br><span class="line">    <span class="string">"version"</span>: <span class="string">"1.0.0"</span>,</span><br><span class="line">    <span class="string">"debug"</span>: <span class="literal">False</span>,</span><br><span class="line">    <span class="string">"database"</span>: {</span><br><span class="line">        <span class="string">"host"</span>: <span class="string">"localhost"</span>,</span><br><span class="line">        <span class="string">"port"</span>: <span class="number">5432</span>,</span><br><span class="line">        <span class="string">"name"</span>: <span class="string">"app_db"</span></span><br><span class="line">    },</span><br><span class="line">    <span class="string">"cache"</span>: {</span><br><span class="line">        <span class="string">"enabled"</span>: <span class="literal">True</span>,</span><br><span class="line">        <span class="string">"ttl"</span>: <span class="number">3600</span></span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment"># 配置文件路径</span></span><br><span class="line">config_path = <span class="string">"app_config.json"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 加载配置</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">load_config</span>():</span><br><span class="line">    <span class="comment"># 如果配置文件存在，则加载它</span></span><br><span class="line">    <span class="keyword">if</span> os.path.exists(config_path):</span><br><span class="line">        <span class="keyword">with</span> <span class="built_in">open</span>(config_path, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">            <span class="keyword">return</span> json.load(f)</span><br><span class="line">    <span class="comment"># 否则使用默认配置并创建配置文件</span></span><br><span class="line">    <span class="keyword">else</span>:</span><br><span class="line">        save_config(default_config)</span><br><span class="line">        <span class="keyword">return</span> default_config</span><br><span class="line"></span><br><span class="line"><span class="comment"># 保存配置</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">save_config</span>(<span class="params">config</span>):</span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(config_path, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">        json.dump(config, f, indent=<span class="number">4</span>, ensure_ascii=<span class="literal">False</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 更新配置</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">update_config</span>(<span class="params">key, value</span>):</span><br><span class="line">    config = load_config()</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 处理嵌套键 (如 "database.host")</span></span><br><span class="line">    <span class="keyword">if</span> <span class="string">"."</span> <span class="keyword">in</span> key:</span><br><span class="line">        parts = key.split(<span class="string">"."</span>)</span><br><span class="line">        current = config</span><br><span class="line">        <span class="keyword">for</span> part <span class="keyword">in</span> parts[:-<span class="number">1</span>]:</span><br><span class="line">            <span class="keyword">if</span> part <span class="keyword">not</span> <span class="keyword">in</span> current:</span><br><span class="line">                current[part] = {}</span><br><span class="line">            current = current[part]</span><br><span class="line">        current[parts[-<span class="number">1</span>]] = value</span><br><span class="line">    <span class="keyword">else</span>:</span><br><span class="line">        config[key] = value</span><br><span class="line">    </span><br><span class="line">    save_config(config)</span><br><span class="line">    <span class="keyword">return</span> config</span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用示例</span></span><br><span class="line">config = load_config()</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"应用名称: <span class="subst">{config[<span class="string">'app_name'</span>]}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"数据库主机: <span class="subst">{config[<span class="string">'database'</span>][<span class="string">'host'</span>]}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 更新配置</span></span><br><span class="line">update_config(<span class="string">"database.host"</span>, <span class="string">"db.example.com"</span>)</span><br><span class="line">update_config(<span class="string">"cache.ttl"</span>, <span class="number">7200</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 重新加载配置</span></span><br><span class="line">config = load_config()</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"更新后的数据库主机: <span class="subst">{config[<span class="string">'database'</span>][<span class="string">'host'</span>]}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"更新后的缓存TTL: <span class="subst">{config[<span class="string">'cache'</span>][<span class="string">'ttl'</span>]}</span>"</span>)</span><br></pre></td></tr></tbody></table></figure><h3 id="13-5-正则表达式"><a href="#13-5-正则表达式" class="headerlink" title="13.5 正则表达式"></a>13.5 正则表达式</h3><p><strong>正则表达式</strong>（通常缩写为 regex 或 regexp）是一种强大的文本处理工具。它使用一种专门的语法来定义 <strong>搜索模式 (pattern)</strong>，然后可以用这个模式在文本中进行查找、匹配、提取或替换操作。正则表达式在各种编程任务中都极为有用，例如：</p><ul><li><strong>数据验证</strong>: 检查用户输入是否符合特定格式（如邮箱、手机号、日期）。</li><li><strong>数据提取</strong>: 从大量非结构化文本（如日志文件、网页内容）中精确地抽取所需信息（如 IP 地址、错误代码、特定标签内容）。</li><li><strong>文本替换</strong>: 对文本进行复杂的查找和替换操作，例如格式化代码、屏蔽敏感信息。</li><li><strong>文本分割</strong>: 根据复杂的模式分割字符串。</li></ul><p>Python 通过内置的 <code>re</code> 模块提供了对正则表达式的全面支持。</p><p><strong>核心概念</strong>: 正则表达式的核心在于使用 <strong>元字符 (metacharacters)</strong> 和普通字符组合来定义模式。元字符是具有特殊含义的字符，而普通字符则匹配它们自身。</p><h4 id="13-5-1-常用元字符和语法"><a href="#13-5-1-常用元字符和语法" class="headerlink" title="13.5.1 常用元字符和语法"></a>13.5.1 常用元字符和语法</h4><p>以下是一些最常用的正则表达式元字符及其含义：</p><table><thead><tr><th align="left">元字符</th><th align="left">描述</th><th align="left">示例模式</th><th align="left">示例匹配</th></tr></thead><tbody><tr><td align="left"><code>.</code></td><td align="left">匹配 <strong>除换行符 <code>\n</code> 之外</strong> 的任何单个字符 (使用 <code>re.DOTALL</code> 标志可匹配换行符)。</td><td align="left"><code>a.c</code></td><td align="left"><code>abc</code>, <code>a_c</code>, <code>a&amp;c</code> (但不匹配 <code>ac</code>)</td></tr><tr><td align="left"><code>^</code></td><td align="left">匹配字符串的 <strong>开头</strong>。在多行模式 (<code>re.MULTILINE</code>) 下，也匹配每行的开头。</td><td align="left"><code>^Hello</code></td><td align="left"><code>Hello world</code> (但不匹配 <code>Say Hello</code>)</td></tr><tr><td align="left"><code>$</code></td><td align="left">匹配字符串的 <strong>结尾</strong>。在多行模式 (<code>re.MULTILINE</code>) 下，也匹配每行的结尾。</td><td align="left"><code>world$</code></td><td align="left"><code>Hello world</code> (但不匹配 <code>world say</code>)</td></tr><tr><td align="left"><code>*</code></td><td align="left">匹配前面的元素 <strong>零次或多次</strong> (贪婪模式)。</td><td align="left"><code>go*d</code></td><td align="left"><code>gd</code>, <code>god</code>, <code>good</code>, <code>goooood</code></td></tr><tr><td align="left"><code>+</code></td><td align="left">匹配前面的元素 <strong>一次或多次</strong> (贪婪模式)。</td><td align="left"><code>go+d</code></td><td align="left"><code>god</code>, <code>good</code>, <code>goooood</code> (但不匹配 <code>gd</code>)</td></tr><tr><td align="left"><code>?</code></td><td align="left">匹配前面的元素 <strong>零次或一次</strong> (贪婪模式)。也用于将贪婪量词变为 <strong>非贪婪</strong> (见后文)。</td><td align="left"><code>colou?r</code></td><td align="left"><code>color</code>, <code>colour</code></td></tr><tr><td align="left"><code>{n}</code></td><td align="left">匹配前面的元素 <strong>恰好 <code>n</code> 次</strong>。</td><td align="left"><code>\d{3}</code></td><td align="left"><code>123</code> (但不匹配 <code>12</code> 或 <code>1234</code>)</td></tr><tr><td align="left"><code>{n,}</code></td><td align="left">匹配前面的元素 <strong>至少 <code>n</code> 次</strong> (贪婪模式)。</td><td align="left"><code>\d{2,}</code></td><td align="left"><code>12</code>, <code>123</code>, <code>12345</code></td></tr><tr><td align="left"><code>{n,m}</code></td><td align="left">匹配前面的元素 <strong>至少 <code>n</code> 次，但不超过 <code>m</code> 次</strong> (贪婪模式)。</td><td align="left"><code>\d{2,4}</code></td><td align="left"><code>12</code>, <code>123</code>, <code>1234</code> (但不匹配 <code>1</code> 或 <code>12345</code>)</td></tr><tr><td align="left"><code>[]</code></td><td align="left"><strong>字符集</strong>。匹配方括号中包含的 <strong>任意一个</strong> 字符。</td><td align="left"><code>[abc]</code></td><td align="left"><code>a</code> 或 <code>b</code> 或 <code>c</code></td></tr><tr><td align="left"><code>[^...]</code></td><td align="left"><strong>否定字符集</strong>。匹配 <strong>不在</strong> 方括号中包含的任何字符。</td><td align="left"><code>[^0-9]</code></td><td align="left">任何非数字字符</td></tr><tr><td align="left"><code>\</code></td><td align="left"><strong>转义符</strong>。用于转义元字符，使其匹配其字面含义 (如 <code>\.</code> 匹配句点 <code>.</code>)，或用于引入特殊序列 (如 <code>\d</code>)。</td><td align="left"><code>\$</code></td><td align="left"><code>$</code> 字符本身</td></tr><tr><td align="left">`</td><td align="left">`</td><td align="left"><strong>或 (OR)</strong> 运算符。匹配 `</td><td align="left">` 左边或右边的表达式。</td></tr><tr><td align="left"><code>()</code></td><td align="left"><strong>分组</strong>。将括号内的表达式视为一个整体，用于应用量词、限制 `</td><td align="left">` 的范围，或 <strong>捕获</strong> 匹配的子字符串。</td><td align="left"><code>(ab)+</code></td></tr></tbody></table><p><strong>踩坑提示</strong>:</p><ul><li><strong>转义</strong>: 当需要匹配元字符本身时（如 <code>.</code>、<code>*</code>、<code>?</code>），必须在前面加上反斜杠 <code>\</code> 进行转义。例如，要匹配 IP 地址中的点，应使用 <code>\.</code>。</li><li><strong>原始字符串 (Raw Strings)</strong>: 在 Python 中定义正则表达式模式时，<strong>强烈建议</strong> 使用原始字符串（在字符串前加 <code>r</code>），如 <code>r"\d+"</code>。这可以避免 Python 解释器对反斜杠进行自身的转义，从而简化正则表达式的书写，尤其是包含很多 <code>\</code> 的模式。</li></ul><h4 id="13-5-2-特殊序列-预定义字符集"><a href="#13-5-2-特殊序列-预定义字符集" class="headerlink" title="13.5.2 特殊序列 (预定义字符集)"></a>13.5.2 特殊序列 (预定义字符集)</h4><p><code>re</code> 模块提供了一些方便的特殊序列来代表常见的字符集：</p><table><thead><tr><th align="left">特殊序列</th><th align="left">描述</th><th align="left">等价于</th><th align="left">示例</th></tr></thead><tbody><tr><td align="left"><code>\d</code></td><td align="left">匹配任何 <strong>Unicode 数字</strong> 字符 (包括 [0-9] 和其他语言的数字)。</td><td align="left"><code>[0-9]</code> (ASCII)</td><td align="left"><code>1</code>, <code>5</code></td></tr><tr><td align="left"><code>\D</code></td><td align="left">匹配任何 <strong>非数字</strong> 字符。</td><td align="left"><code>[^0-9]</code> (ASCII)</td><td align="left"><code>a</code>, <code>_</code>, <code></code></td></tr><tr><td align="left"><code>\s</code></td><td align="left">匹配任何 <strong>Unicode 空白</strong> 字符 (包括 <code></code>、<code>\t</code>、<code>\n</code>、<code>\r</code>、<code>\f</code>、<code>\v</code> 等)。</td><td align="left"></td><td align="left"><code></code>, <code>\t</code></td></tr><tr><td align="left"><code>\S</code></td><td align="left">匹配任何 <strong>非空白</strong> 字符。</td><td align="left"></td><td align="left"><code>a</code>, <code>1</code>, <code>.</code></td></tr><tr><td align="left"><code>\w</code></td><td align="left">匹配任何 <strong>Unicode 词语</strong> 字符 (字母、数字和下划线 <code>_</code>)。</td><td align="left"><code>[a-zA-Z0-9_]</code> (ASCII)</td><td align="left"><code>a</code>, <code>B</code>, <code>5</code>, <code>_</code></td></tr><tr><td align="left"><code>\W</code></td><td align="left">匹配任何 <strong>非词语</strong> 字符。</td><td align="left"><code>[^a-zA-Z0-9_]</code>(ASCII)</td><td align="left"><code>!</code>, <code></code>, <code>@</code></td></tr><tr><td align="left"><code>\b</code></td><td align="left">匹配 <strong>词语边界</strong> (word boundary)。这是一个零宽度断言，匹配词语字符 (<code>\w</code>) 和非词语字符 (<code>\W</code>) 之间，或词语字符和字符串开头/结尾之间的位置。</td><td align="left"></td><td align="left"><code>\bword\b</code></td></tr><tr><td align="left"><code>\B</code></td><td align="left">匹配 <strong>非词语边界</strong>。</td><td align="left"></td><td align="left"><code>\Bword\B</code></td></tr></tbody></table><h4 id="13-5-3-贪婪模式-vs-非贪婪模式"><a href="#13-5-3-贪婪模式-vs-非贪婪模式" class="headerlink" title="13.5.3 贪婪模式 vs. 非贪婪模式"></a>13.5.3 贪婪模式 vs. 非贪婪模式</h4><p>默认情况下，量词 (<code>*</code>, <code>+</code>, <code>?</code>, <code>{n,}</code>, <code>{n,m}</code>) 都是 <strong>贪婪 (Greedy)</strong> 的，它们会尽可能多地匹配字符。</p><p><strong>场景</strong>: 从 HTML 标签 <code>&lt;b&gt;Bold Text&lt;/b&gt;</code> 中提取 <code>&lt;b&gt;</code>。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> re</span><br><span class="line"></span><br><span class="line">text = <span class="string">"&lt;b&gt;Bold Text&lt;/b&gt; Regular Text &lt;b&gt;Another Bold&lt;/b&gt;"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 贪婪模式 (默认)</span></span><br><span class="line">greedy_pattern = <span class="string">r"&lt;.*&gt;"</span> <span class="comment"># . 匹配任何字符，* 匹配零次或多次</span></span><br><span class="line">match_greedy = re.search(greedy_pattern, text)</span><br><span class="line"><span class="keyword">if</span> match_greedy:</span><br><span class="line">    <span class="comment"># * 会一直匹配到字符串的最后一个 &gt;</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"贪婪匹配结果: <span class="subst">{match_greedy.group(<span class="number">0</span>)}</span>"</span>)</span><br><span class="line">    <span class="comment"># 输出: 贪婪匹配结果: &lt;b&gt;Bold Text&lt;/b&gt; Regular Text &lt;b&gt;Another Bold&lt;/b&gt;</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 非贪婪模式 (在量词后加 ?)</span></span><br><span class="line">non_greedy_pattern = <span class="string">r"&lt;.*?&gt;"</span> <span class="comment"># *? 匹配零次或多次，但尽可能少地匹配</span></span><br><span class="line">match_non_greedy = re.search(non_greedy_pattern, text)</span><br><span class="line"><span class="keyword">if</span> match_non_greedy:</span><br><span class="line">    <span class="comment"># *? 遇到第一个 &gt; 就停止匹配</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"非贪婪匹配结果: <span class="subst">{match_non_greedy.group(<span class="number">0</span>)}</span>"</span>)</span><br><span class="line">    <span class="comment"># 输出: 非贪婪匹配结果: &lt;b&gt;</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 查找所有非贪婪匹配</span></span><br><span class="line">all_matches_non_greedy = re.findall(non_greedy_pattern, text)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"所有非贪婪匹配: <span class="subst">{all_matches_non_greedy}</span>"</span>)</span><br><span class="line"><span class="comment"># 输出: 所有非贪婪匹配: ['&lt;b&gt;', '&lt;/b&gt;', '&lt;b&gt;', '&lt;/b&gt;']</span></span><br></pre></td></tr></tbody></table></figure><p><strong>何时使用非贪婪模式？</strong></p><p>当需要匹配从某个开始标记到 <strong>最近的</strong> 结束标记之间的内容时，通常需要使用非贪婪量词 (<code>*?</code>, <code>+?</code>, <code>??</code>, <code>{n,}?</code>, <code>{n,m}?</code>)。</p><h4 id="13-5-4-分组与捕获"><a href="#13-5-4-分组与捕获" class="headerlink" title="13.5.4 分组与捕获"></a>13.5.4 分组与捕获</h4><p>使用圆括号 <code>()</code> 可以将模式的一部分组合起来，形成一个 <strong>分组 (Group)</strong>。分组有几个重要作用：</p><ol><li><strong>应用量词</strong>: 将量词作用于整个分组，如 <code>(abc)+</code> 匹配 <code>abc</code>, <code>abcabc</code> 等。</li><li><strong>限制 <code>|</code> 范围</strong>: 如 <code>gr(a|e)y</code> 匹配 <code>gray</code> 或 <code>grey</code>。</li><li><strong>捕获内容</strong>: 默认情况下，每个分组会 <strong>捕获 (Capture)</strong> 其匹配到的子字符串，以便后续引用或提取。</li></ol><p><strong>场景</strong>: 从 “Name: John Doe, Age: 30” 中提取姓名和年龄。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> re</span><br><span class="line"></span><br><span class="line">text = <span class="string">"Name: John Doe, Age: 30; Name: Jane Smith, Age: 25"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 定义带有捕获组的模式</span></span><br><span class="line"><span class="comment"># 第一个组 (\w+\s+\w+) 捕获姓名</span></span><br><span class="line"><span class="comment"># 第二个组 (\d+) 捕获年龄</span></span><br><span class="line">pattern_capture = <span class="string">r"Name: (\w+\s+\w+), Age: (\d+)"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用 findall 查找所有匹配项</span></span><br><span class="line"><span class="comment"># findall 返回一个列表，如果模式中有捕获组，列表元素是包含所有捕获组内容的元组</span></span><br><span class="line">matches = re.findall(pattern_capture, text)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"\n--- 使用 findall 提取分组 ---"</span>)</span><br><span class="line"><span class="built_in">print</span>(matches) <span class="comment"># 输出: [('John Doe', '30'), ('Jane Smith', '25')]</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用 finditer 获取 Match 对象，可以更灵活地访问分组</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"\n--- 使用 finditer 访问分组 ---"</span>)</span><br><span class="line"><span class="keyword">for</span> match_obj <span class="keyword">in</span> re.finditer(pattern_capture, text):</span><br><span class="line">    <span class="comment"># match_obj.group(0) 或 group() 获取整个匹配</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"整个匹配: <span class="subst">{match_obj.group(<span class="number">0</span>)}</span>"</span>)</span><br><span class="line">    <span class="comment"># match_obj.group(1) 获取第一个捕获组的内容 (姓名)</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"  姓名 (组 1): <span class="subst">{match_obj.group(<span class="number">1</span>)}</span>"</span>)</span><br><span class="line">    <span class="comment"># match_obj.group(2) 获取第二个捕获组的内容 (年龄)</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"  年龄 (组 2): <span class="subst">{match_obj.group(<span class="number">2</span>)}</span>"</span>)</span><br><span class="line">    <span class="comment"># match_obj.groups() 获取所有捕获组组成的元组</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"  所有分组: <span class="subst">{match_obj.groups()}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 非捕获组 (?:...)</span></span><br><span class="line"><span class="comment"># 如果只想分组而不捕获内容，可以使用非捕获组</span></span><br><span class="line">pattern_non_capture = <span class="string">r"Name: (?:\w+\s+\w+), Age: (\d+)"</span> <span class="comment"># 第一个组不捕获</span></span><br><span class="line">matches_nc = re.findall(pattern_non_capture, text)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"\n--- 使用非捕获组的 findall ---"</span>)</span><br><span class="line"><span class="built_in">print</span>(matches_nc) <span class="comment"># 输出: ['30', '25'] (只包含捕获组的内容)</span></span><br></pre></td></tr></tbody></table></figure><p><strong>反向引用 (Backreferences)</strong>: 可以在模式内部或替换字符串中使用 <code>\1</code>, <code>\2</code>, … 来引用前面捕获组匹配到的文本。</p><p><strong>场景</strong>: 查找重复的单词，如 “the the”。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br></pre></td><td class="code"><pre><span class="line">text_repeat = <span class="string">"This is the the test sentence with repeated repeated words."</span></span><br><span class="line"><span class="comment"># \b 确保是完整的单词</span></span><br><span class="line"><span class="comment"># (\w+) 捕获第一个单词</span></span><br><span class="line"><span class="comment"># \s+ 匹配中间的空白</span></span><br><span class="line"><span class="comment"># \1 引用第一个捕获组匹配的内容</span></span><br><span class="line">pattern_repeat = <span class="string">r"\b(\w+)\s+\1\b"</span></span><br><span class="line">repeated_words = re.findall(pattern_repeat, text_repeat)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"\n--- 查找重复单词 ---"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"找到的重复单词: <span class="subst">{repeated_words}</span>"</span>) <span class="comment"># 输出: ['the', 'repeated']</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用 sub 进行替换</span></span><br><span class="line"><span class="comment"># 将重复的单词替换为单个单词</span></span><br><span class="line">corrected_text = re.sub(pattern_repeat, <span class="string">r"\1"</span>, text_repeat) <span class="comment"># 使用 \1 引用捕获组</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"修正后的文本: <span class="subst">{corrected_text}</span>"</span>)</span><br><span class="line"><span class="comment"># 输出: This is the test sentence with repeated words.</span></span><br></pre></td></tr></tbody></table></figure><h4 id="13-5-5-re-模块核心函数"><a href="#13-5-5-re-模块核心函数" class="headerlink" title="13.5.5 re 模块核心函数"></a>13.5.5 <code>re</code> 模块核心函数</h4><p>Python 的 <code>re</code> 模块提供了以下核心函数来执行正则表达式操作：</p><table><thead><tr><th align="left">函数</th><th align="left">描述</th><th align="left">返回值</th><th align="left">主要用途</th></tr></thead><tbody><tr><td align="left"><code>re.match(p, s, flags=0)</code></td><td align="left">从字符串 <code>s</code> 的 <strong>开头</strong> 尝试匹配模式 <code>p</code>。</td><td align="left">匹配成功返回 <code>Match</code> 对象，失败返回 <code>None</code>。</td><td align="left">验证字符串是否以特定模式开始。</td></tr><tr><td align="left"><code>re.search(p, s, flags=0)</code></td><td align="left">在 <strong>整个</strong> 字符串 <code>s</code> 中搜索模式 <code>p</code> 的 <strong>第一个</strong> 匹配项。</td><td align="left">匹配成功返回 <code>Match</code> 对象，失败返回 <code>None</code>。</td><td align="left">在字符串中查找模式是否存在，并获取第一个匹配项的信息。</td></tr><tr><td align="left"><code>re.findall(p, s, flags=0)</code></td><td align="left">在字符串 <code>s</code> 中查找模式 <code>p</code> 的 <strong>所有非重叠</strong> 匹配项。</td><td align="left">返回一个 <strong>列表</strong>。如果模式无捕获组，列表元素是匹配的字符串；如果有捕获组，列表元素是包含各捕获组内容的元组。</td><td align="left">提取字符串中所有符合模式的子串或捕获组内容。</td></tr><tr><td align="left"><code>re.finditer(p, s, flags=0)</code></td><td align="left">与 <code>findall</code> 类似，但返回一个 <strong>迭代器 (iterator)</strong>，迭代器中的每个元素都是一个 <code>Match</code> 对象。</td><td align="left">返回一个迭代器，每个元素是 <code>Match</code> 对象。</td><td align="left">处理大量匹配结果时更 <strong>内存高效</strong>，因为不需要一次性存储所有结果。可以方便地访问每个匹配的详细信息（如位置）。</td></tr><tr><td align="left"><code>re.sub(p, repl, s, count=0, flags=0)</code></td><td align="left">在字符串 <code>s</code> 中查找模式 <code>p</code> 的所有匹配项，并用 <code>repl</code> 替换它们。<code>repl</code> 可以是字符串（支持 <code>\g&lt;name&gt;</code> 或 <code>\1</code> 等反向引用）或函数。<code>count</code> 指定最大替换次数。</td><td align="left">返回替换后的 <strong>新字符串</strong>。</td><td align="left">执行查找和替换操作。<code>repl</code> 可以是函数，实现更复杂的替换逻辑。</td></tr><tr><td align="left"><code>re.split(p, s, maxsplit=0, flags=0)</code></td><td align="left">使用模式 <code>p</code> 作为分隔符来 <strong>分割</strong> 字符串 <code>s</code>。<code>maxsplit</code> 指定最大分割次数。</td><td align="left">返回一个 <strong>列表</strong>，包含分割后的子字符串。如果模式中有捕获组，捕获的内容也会包含在列表中。</td><td align="left">根据复杂的模式分割字符串。</td></tr><tr><td align="left"><code>re.compile(p, flags=0)</code></td><td align="left"><strong>编译</strong> 正则表达式模式 <code>p</code> 为一个 <strong>模式对象 (Pattern Object)</strong>。</td><td align="left">返回一个 <code>Pattern</code> 对象。</td><td align="left">当一个模式需要被 <strong>多次</strong> 使用时，预先编译可以 <strong>提高性能</strong>。模式对象拥有与 <code>re</code> 模块函数同名的方法（如 <code>pattern.search(s)</code>）。</td></tr></tbody></table><p><strong>代码示例</strong>:</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> re</span><br><span class="line"></span><br><span class="line">text = <span class="string">"The quick brown fox jumps over the lazy dog. Phone: ************. Email: <EMAIL>."</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 1. re.match() - 检查开头</span></span><br><span class="line">pattern_start = <span class="string">r"The"</span></span><br><span class="line">match_result = re.<span class="keyword">match</span>(pattern_start, text)</span><br><span class="line"><span class="keyword">if</span> match_result:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"match(): 字符串以 '<span class="subst">{pattern_start}</span>' 开头。匹配内容: '<span class="subst">{match_result.group(<span class="number">0</span>)}</span>'"</span>)</span><br><span class="line"><span class="keyword">else</span>:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"match(): 字符串不以 '<span class="subst">{pattern_start}</span>' 开头。"</span>)</span><br><span class="line"></span><br><span class="line">match_fail = re.<span class="keyword">match</span>(<span class="string">r"quick"</span>, text) <span class="comment"># 不从开头匹配，所以失败</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"match() 失败示例: <span class="subst">{match_fail}</span>"</span>) <span class="comment"># None</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 2. re.search() - 查找第一个匹配</span></span><br><span class="line">pattern_word = <span class="string">r"fox"</span></span><br><span class="line">search_result = re.search(pattern_word, text)</span><br><span class="line"><span class="keyword">if</span> search_result:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"search(): 找到单词 '<span class="subst">{pattern_word}</span>'。 起始位置: <span class="subst">{search_result.start()}</span>, 结束位置: <span class="subst">{search_result.end()}</span>"</span>)</span><br><span class="line"><span class="keyword">else</span>:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"search(): 未找到单词 '<span class="subst">{pattern_word}</span>'。"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 3. re.findall() - 查找所有匹配</span></span><br><span class="line">pattern_digits = <span class="string">r"\d+"</span> <span class="comment"># 查找所有数字序列</span></span><br><span class="line">all_digits = re.findall(pattern_digits, text)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"findall(): 找到的所有数字序列: <span class="subst">{all_digits}</span>"</span>) <span class="comment"># ['123', '456', '7890']</span></span><br><span class="line"></span><br><span class="line">pattern_email = <span class="string">r"(\w+)@(\w+\.\w+)"</span> <span class="comment"># 查找邮箱并捕获用户名和域名</span></span><br><span class="line">email_parts = re.findall(pattern_email, text)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"findall() 捕获组: <span class="subst">{email_parts}</span>"</span>) <span class="comment"># [('test', 'example.com')]</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 4. re.finditer() - 迭代查找匹配对象</span></span><br><span class="line">pattern_words_o = <span class="string">r"\b\w*o\w*\b"</span> <span class="comment"># 查找所有包含字母'o'的单词</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"finditer(): 查找包含 'o' 的单词:"</span>)</span><br><span class="line"><span class="keyword">for</span> <span class="keyword">match</span> <span class="keyword">in</span> re.finditer(pattern_words_o, text, re.IGNORECASE): <span class="comment"># 使用 IGNORECASE 标志</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"  找到: '<span class="subst">{<span class="keyword">match</span>.group(<span class="number">0</span>)}</span>' at position <span class="subst">{<span class="keyword">match</span>.span()}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 5. re.sub() - 替换</span></span><br><span class="line">pattern_phone = <span class="string">r"\d{3}-\d{3}-\d{4}"</span></span><br><span class="line"><span class="comment"># 将电话号码替换为 [REDACTED]</span></span><br><span class="line">censored_text = re.sub(pattern_phone, <span class="string">"[REDACTED]"</span>, text)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"sub() 替换电话号码: <span class="subst">{censored_text}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用函数进行替换</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">mask_email</span>(<span class="params">match_obj</span>):</span><br><span class="line">    username = match_obj.group(<span class="number">1</span>)</span><br><span class="line">    domain = match_obj.group(<span class="number">2</span>)</span><br><span class="line">    <span class="keyword">return</span> <span class="string">f"<span class="subst">{username[<span class="number">0</span>]}</span>***@<span class="subst">{domain}</span>"</span> <span class="comment"># 用户名只显示第一个字符</span></span><br><span class="line"></span><br><span class="line">censored_email_text = re.sub(pattern_email, mask_email, text)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"sub() 使用函数替换邮箱: <span class="subst">{censored_email_text}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 6. re.split() - 分割</span></span><br><span class="line">pattern_punct = <span class="string">r"[.,:;]\s*"</span> <span class="comment"># 按标点符号和后面的空格分割</span></span><br><span class="line">parts = re.split(pattern_punct, text)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"split(): 按标点分割: <span class="subst">{parts}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 7. re.compile() - 编译模式</span></span><br><span class="line">compiled_pattern = re.<span class="built_in">compile</span>(<span class="string">r"l\w*y"</span>, re.IGNORECASE) <span class="comment"># 编译查找以l开头y结尾的词</span></span><br><span class="line"><span class="comment"># 多次使用编译后的模式</span></span><br><span class="line">match1 = compiled_pattern.search(text)</span><br><span class="line"><span class="keyword">if</span> match1:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"compile() &amp; search(): 找到 '<span class="subst">{match1.group(<span class="number">0</span>)}</span>'"</span>)</span><br><span class="line">match2 = compiled_pattern.findall(<span class="string">"Actually, Lily is lovely."</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"compile() &amp; findall(): 找到 <span class="subst">{match2}</span>"</span>) <span class="comment"># ['Lily', 'lovely']</span></span><br></pre></td></tr></tbody></table></figure><h4 id="13-5-6-Match-对象详解"><a href="#13-5-6-Match-对象详解" class="headerlink" title="13.5.6 Match 对象详解"></a>13.5.6 Match 对象详解</h4><p>当 <code>re.match()</code>, <code>re.search()</code> 或 <code>re.finditer()</code> 中的一项成功匹配时，它们会返回一个 <strong><code>Match</code> 对象</strong>。这个对象包含了关于匹配结果的详细信息。</p><table><thead><tr><th align="left">Match 对象方法/属性</th><th align="left">描述</th><th align="left">示例 (假设 <code>m = re.search(r"(\w+) (\d+)", "Order P123 45")</code>)</th></tr></thead><tbody><tr><td align="left"><code>m.group(0)</code> 或 <code>m.group()</code></td><td align="left">返回整个匹配的字符串。</td><td align="left"><code>'P123 45'</code></td></tr><tr><td align="left"><code>m.group(n)</code></td><td align="left">返回第 <code>n</code> 个捕获组匹配的字符串 (从 1 开始计数)。</td><td align="left"><code>m.group(1)</code> 返回 <code>'P123'</code>, <code>m.group(2)</code> 返回 <code>'45'</code></td></tr><tr><td align="left"><code>m.groups()</code></td><td align="left">返回一个包含所有捕获组匹配内容的 <strong>元组</strong>。</td><td align="left"><code>('P123', '45')</code></td></tr><tr><td align="left"><code>m.groupdict()</code></td><td align="left">如果模式中使用了 <strong>命名捕获组</strong> <code>(?P&lt;name&gt;...)</code>，返回一个包含组名和匹配内容的字典。</td><td align="left">(需要命名组，如下例)</td></tr><tr><td align="left"><code>m.start([group])</code></td><td align="left">返回整个匹配或指定 <code>group</code> 的 <strong>起始索引</strong> (包含)。</td><td align="left"><code>m.start()</code> 返回 6, <code>m.start(1)</code> 返回 6, <code>m.start(2)</code> 返回 11</td></tr><tr><td align="left"><code>m.end([group])</code></td><td align="left">返回整个匹配或指定 <code>group</code> 的 <strong>结束索引</strong> (不包含)。</td><td align="left"><code>m.end()</code> 返回 13, <code>m.end(1)</code> 返回 10, <code>m.end(2)</code> 返回 13</td></tr><tr><td align="left"><code>m.span([group])</code></td><td align="left">返回一个包含 <code>(start, end)</code> 索引的 <strong>元组</strong>。</td><td align="left"><code>m.span()</code> 返回 <code>(6, 13)</code>, <code>m.span(1)</code> 返回 <code>(6, 10)</code></td></tr><tr><td align="left"><code>m.string</code></td><td align="left">传递给 <code>match()</code> 或 <code>search()</code> 的原始字符串。</td><td align="left"><code>'Order P123 45'</code></td></tr><tr><td align="left"><code>m.re</code></td><td align="left">匹配时使用的已编译的模式对象 (<code>Pattern</code> object)。</td><td align="left"></td></tr></tbody></table><p><strong>命名捕获组示例</strong>:</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> re</span><br><span class="line"></span><br><span class="line">text = <span class="string">"Product ID: ABC-987, Quantity: 50"</span></span><br><span class="line"><span class="comment"># 使用 ?P&lt;name&gt; 定义命名捕获组</span></span><br><span class="line">pattern_named = <span class="string">r"Product ID: (?P&lt;product_id&gt;[A-Z]+-\d+), Quantity: (?P&lt;quantity&gt;\d+)"</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">match</span> = re.search(pattern_named, text)</span><br><span class="line"><span class="keyword">if</span> <span class="keyword">match</span>:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n--- 使用命名捕获组 ---"</span>)</span><br><span class="line">    <span class="comment"># 通过组名访问捕获的内容</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"产品 ID: <span class="subst">{<span class="keyword">match</span>.group(<span class="string">'product_id'</span>)}</span>"</span>) <span class="comment"># ABC-987</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"数量: <span class="subst">{<span class="keyword">match</span>.group(<span class="string">'quantity'</span>)}</span>"</span>)   <span class="comment"># 50</span></span><br><span class="line">    <span class="comment"># groupdict() 返回包含所有命名组的字典</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"捕获字典: <span class="subst">{<span class="keyword">match</span>.groupdict()}</span>"</span>) <span class="comment"># {'product_id': 'ABC-987', 'quantity': '50'}</span></span><br></pre></td></tr></tbody></table></figure><h4 id="13-5-7-正则表达式标志-Flags"><a href="#13-5-7-正则表达式标志-Flags" class="headerlink" title="13.5.7 正则表达式标志 (Flags)"></a>13.5.7 正则表达式标志 (Flags)</h4><p>标志可以修改正则表达式的匹配行为。可以在 <code>re</code> 函数的 <code>flags</code> 参数中指定，或在编译时指定。多个标志可以使用 <code>|</code> (按位或) 组合。</p><table><thead><tr><th align="left">标志</th><th align="left">简写</th><th align="left">描述</th></tr></thead><tbody><tr><td align="left"><code>re.IGNORECASE</code></td><td align="left"><code>re.I</code></td><td align="left">进行 <strong>不区分大小写</strong> 的匹配。</td></tr><tr><td align="left"><code>re.MULTILINE</code></td><td align="left"><code>re.M</code></td><td align="left">使 <code>^</code> 和 <code>$</code> 匹配 <strong>每行的开头和结尾</strong>，而不仅仅是整个字符串的开头和结尾。</td></tr><tr><td align="left"><code>re.DOTALL</code></td><td align="left"><code>re.S</code></td><td align="left">使元字符 <code>.</code> 能够匹配 <strong>包括换行符 <code>\n</code> 在内</strong> 的任何字符。</td></tr><tr><td align="left"><code>re.VERBOSE</code></td><td align="left"><code>re.X</code></td><td align="left"><strong>详细模式</strong>。允许在模式字符串中添加 <strong>空白和注释</strong> 以提高可读性，此时模式中的空白会被忽略，<code>#</code> 后到行尾的内容视为注释。</td></tr><tr><td align="left"><code>re.ASCII</code></td><td align="left"><code>re.A</code></td><td align="left">使 <code>\w</code>, <code>\W</code>, <code>\b</code>, <code>\B</code>, <code>\s</code>, <code>\S</code> 只匹配 ASCII 字符，而不是完整的 Unicode 字符集 (Python 3 默认匹配 Unicode)。</td></tr><tr><td align="left"><code>re.UNICODE</code> (默认)</td><td align="left"><code>re.U</code></td><td align="left">使 <code>\w</code>, <code>\W</code>, <code>\b</code>, <code>\B</code>, <code>\s</code>, <code>\S</code>, <code>\d</code>, <code>\D</code> 匹配完整的 Unicode 字符集。这是 Python 3 的默认行为。</td></tr></tbody></table><p><strong>示例</strong>:</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> re</span><br><span class="line"></span><br><span class="line">text_multi = <span class="string">"""first line</span></span><br><span class="line"><span class="string">second line</span></span><br><span class="line"><span class="string">THIRD line"""</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># re.I (忽略大小写)</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"\n--- Flags 示例 ---"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"re.I: <span class="subst">{re.findall(<span class="string">r'line'</span>, text_multi, re.IGNORECASE)}</span>"</span>) <span class="comment"># ['line', 'line', 'line']</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># re.M (多行模式)</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"re.M (^): <span class="subst">{re.findall(<span class="string">r'^s.*'</span>, text_multi, re.MULTILINE | re.IGNORECASE)}</span>"</span>) <span class="comment"># ['second line']</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"re.M ($): <span class="subst">{re.findall(<span class="string">r'line$'</span>, text_multi, re.MULTILINE | re.IGNORECASE)}</span>"</span>) <span class="comment"># ['line', 'line', 'line']</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># re.S (DOTALL)</span></span><br><span class="line">text_dot = <span class="string">"Hello\nWorld"</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"re.S (.): <span class="subst">{re.search(<span class="string">r'Hello.World'</span>, text_dot, re.DOTALL)}</span>"</span>) <span class="comment"># 匹配成功</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"No re.S (.): <span class="subst">{re.search(<span class="string">r'Hello.World'</span>, text_dot)}</span>"</span>)      <span class="comment"># 匹配失败 (None)</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># re.X (VERBOSE)</span></span><br><span class="line"><span class="comment"># 一个复杂的邮箱模式，使用 VERBOSE 模式添加注释和空格</span></span><br><span class="line">pattern_verbose = <span class="string">r"""</span></span><br><span class="line"><span class="string">  ^                  # 匹配字符串开头</span></span><br><span class="line"><span class="string">  [\w\.\-]+          # 用户名部分 (字母、数字、下划线、点、连字符)</span></span><br><span class="line"><span class="string">  @                  # @ 符号</span></span><br><span class="line"><span class="string">  ([\w\-]+\.)+       # 域名部分 (允许子域名，如 mail.example.)</span></span><br><span class="line"><span class="string">  [a-zA-Z]{2,7}      # 顶级域名 (如 .com, .org)</span></span><br><span class="line"><span class="string">  $                  # 匹配字符串结尾</span></span><br><span class="line"><span class="string">"""</span></span><br><span class="line">email = <span class="string">"<EMAIL>"</span></span><br><span class="line">match_verbose = re.<span class="keyword">match</span>(pattern_verbose, email, re.VERBOSE)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"re.X (VERBOSE): <span class="subst">{<span class="string">'匹配成功'</span> <span class="keyword">if</span> match_verbose <span class="keyword">else</span> <span class="string">'匹配失败'</span>}</span>"</span>) <span class="comment"># 匹配成功</span></span><br></pre></td></tr></tbody></table></figure><h4 id="13-5-8-实际应用场景示例"><a href="#13-5-8-实际应用场景示例" class="headerlink" title="13.5.8 实际应用场景示例"></a>13.5.8 实际应用场景示例</h4><p><strong>场景 1: 验证中国大陆手机号 (简单示例)</strong></p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> re</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">is_valid_china_mobile</span>(<span class="params">phone_number: <span class="built_in">str</span></span>) -&gt; <span class="built_in">bool</span>:</span><br><span class="line">    <span class="string">"""简单验证中国大陆手机号码 (11位数字，常见号段)"""</span></span><br><span class="line">    <span class="comment"># 模式解释:</span></span><br><span class="line">    <span class="comment"># ^            匹配字符串开头</span></span><br><span class="line">    <span class="comment"># (?:...)      非捕获组</span></span><br><span class="line">    <span class="comment"># 1[3-9]       第一位是1，第二位是3到9</span></span><br><span class="line">    <span class="comment"># \d{9}        后面跟9位数字</span></span><br><span class="line">    <span class="comment"># $            匹配字符串结尾</span></span><br><span class="line">    pattern = <span class="string">r"^(?:1[3-9])\d{9}$"</span></span><br><span class="line">    <span class="keyword">if</span> re.<span class="keyword">match</span>(pattern, phone_number):</span><br><span class="line">        <span class="keyword">return</span> <span class="literal">True</span></span><br><span class="line">    <span class="keyword">else</span>:</span><br><span class="line">        <span class="keyword">return</span> <span class="literal">False</span></span><br><span class="line"></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"\n--- 手机号验证 ---"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"13812345678: <span class="subst">{is_valid_china_mobile(<span class="string">'13812345678'</span>)}</span>"</span>) <span class="comment"># True</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"12012345678: <span class="subst">{is_valid_china_mobile(<span class="string">'12012345678'</span>)}</span>"</span>) <span class="comment"># False (号段不对)</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"1381234567: <span class="subst">{is_valid_china_mobile(<span class="string">'1381234567'</span>)}</span>"</span>)  <span class="comment"># False (位数不够)</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"138123456789: <span class="subst">{is_valid_china_mobile(<span class="string">'138123456789'</span>)}</span>"</span>)<span class="comment"># False (位数太多)</span></span><br></pre></td></tr></tbody></table></figure><p><em>注意</em>: 实际手机号验证可能需要更复杂的规则或查询号段数据库。</p><p><strong>场景 2: 从 Apache/Nginx 日志中提取 IP 地址和请求路径</strong></p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> re</span><br><span class="line"></span><br><span class="line">log_line = <span class="string">'************* - - [03/May/2025:17:20:01 +0900] "GET /index.html HTTP/1.1" 200 1542 "-" "Mozilla/5.0..."'</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 模式解释:</span></span><br><span class="line"><span class="comment"># ^([\d\.]+)      捕获开头的 IP 地址 (数字和点的组合)</span></span><br><span class="line"><span class="comment"># \s+-\s+-\s+      匹配中间的 ' - - ' 部分</span></span><br><span class="line"><span class="comment"># \[.*?\]        匹配并忽略方括号内的时间戳 (非贪婪)</span></span><br><span class="line"><span class="comment"># \s+"           匹配时间戳后的空格和双引号</span></span><br><span class="line"><span class="comment"># (GET|POST|PUT|DELETE|HEAD) \s+  捕获请求方法 (GET, POST 等) 和空格</span></span><br><span class="line"><span class="comment"># ([^\s"]+)      捕获请求路径 (非空格、非双引号的字符)</span></span><br><span class="line"><span class="comment"># \s+HTTP/[\d\.]+" 捕获 HTTP 版本部分</span></span><br><span class="line"><span class="comment"># .* 匹配剩余部分</span></span><br><span class="line">pattern_log = <span class="string">r'^([\d\.]+) \s+-\s+-\s+ \[.*?\] \s+"(GET|POST|PUT|DELETE|HEAD)\s+([^\s"]+)\s+HTTP/[\d\.]+" .*'</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">match</span> = re.<span class="keyword">match</span>(pattern_log, log_line)</span><br><span class="line"><span class="keyword">if</span> <span class="keyword">match</span>:</span><br><span class="line">    ip_address = <span class="keyword">match</span>.group(<span class="number">1</span>)</span><br><span class="line">    method = <span class="keyword">match</span>.group(<span class="number">2</span>)</span><br><span class="line">    path = <span class="keyword">match</span>.group(<span class="number">3</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"\n--- 日志解析 ---"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"IP 地址: <span class="subst">{ip_address}</span>"</span>) <span class="comment"># *************</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"请求方法: <span class="subst">{method}</span>"</span>)   <span class="comment"># GET</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"请求路径: <span class="subst">{path}</span>"</span>)     <span class="comment"># /index.html</span></span><br><span class="line"><span class="keyword">else</span>:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"日志格式不匹配"</span>)</span><br></pre></td></tr></tbody></table></figure><p><strong>场景 3: 将 Markdown 样式的链接 <code>[text](url)</code> 转换为 HTML <code>&lt;a&gt;</code> 标签</strong></p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> re</span><br><span class="line"></span><br><span class="line">markdown_text = <span class="string">"这是一个链接 [Google](https://www.google.com) 和另一个 [Python 官网](http://python.org) 的例子。"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 模式解释:</span></span><br><span class="line"><span class="comment"># \[        匹配字面量 '['</span></span><br><span class="line"><span class="comment"># ([^\]]+)  捕获链接文本 (不是 ']' 的任意字符一次或多次)</span></span><br><span class="line"><span class="comment"># \]        匹配字面量 ']'</span></span><br><span class="line"><span class="comment"># \(        匹配字面量 '('</span></span><br><span class="line"><span class="comment"># ([^\)]+)  捕获 URL (不是 ')' 的任意字符一次或多次)</span></span><br><span class="line"><span class="comment"># \)        匹配字面量 ')'</span></span><br><span class="line">pattern_md_link = <span class="string">r'\[([^\]]+)\]\(([^\)]+)\)'</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用 re.sub 和反向引用 \1, \2 进行替换</span></span><br><span class="line">html_text = re.sub(pattern_md_link, <span class="string">r'&lt;a href="\2"&gt;\1&lt;/a&gt;'</span>, markdown_text)</span><br><span class="line"></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"\n--- Markdown 转 HTML 链接 ---"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"原始 Markdown: <span class="subst">{markdown_text}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"转换后 HTML: <span class="subst">{html_text}</span>"</span>)</span><br><span class="line"><span class="comment"># 输出: 这是一个链接 &lt;a href="https://www.google.com"&gt;Google&lt;/a&gt; 和另一个 &lt;a href="http://python.org"&gt;Python 官网&lt;/a&gt; 的例子。</span></span><br></pre></td></tr></tbody></table></figure></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/9962.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/9962.html&quot;)">Python（十四）：第十三章： 高级数据处理</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/9962.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/9962.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Python<span class="categoryesPageCount">22</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Python基础知识总汇<span class="tagsPageCount">22</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/62937.html"><img class="prev-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Python（十三）：第十二章： 异常处理</div></div></a></div><div class="next-post pull-right"><a href="/posts/13212.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Python（十五）：第十四章：深入解析并发编程</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/45310.html" title="Python（七）：第六章：条件循环分支"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（七）：第六章：条件循环分支</div></div></a></div><div><a href="/posts/8019.html" title="Python（三）：第二章：转义字符"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（三）：第二章：转义字符</div></div></a></div><div><a href="/posts/56572.html" title="Python（九）：第八章： 函数知识总结"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（九）：第八章： 函数知识总结</div></div></a></div><div><a href="/posts/55902.html" title="Python（二十一）：第二十章：Python 语法新特性总结"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（二十一）：第二十章：Python 语法新特性总结</div></div></a></div><div><a href="/posts/2501.html" title="Python（二）：第一章：字符串打印格式化与PyCharm模板变量"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（二）：第一章：字符串打印格式化与PyCharm模板变量</div></div></a></div><div><a href="/posts/43091.html" title="Python（二十二）：第二十一章：项目结构规范与最佳实践"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（二十二）：第二十一章：项目结构规范与最佳实践</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"Python（十四）：第十三章： 高级数据处理",date:"2025-04-19 05:13:45",updated:"2025-07-13 22:13:01",tags:["Python基础知识总汇"],categories:["后端技术","Python"],content:'\n## 第十三章： 高级数据处理\n\nPython 提供了多种处理不同类型数据的工具和库，能够轻松处理结构化和非结构化数据。本章将深入探讨 Python 中常用的数据格式处理技术，包括 JSON、CSV、XML 和配置文件等。\n\n### 13.1 JSON 处理\n\nJSON (JavaScript Object Notation) 是一种轻量级的数据交换格式，易于人阅读和编写，也易于机器解析和生成。Python 通过内置的 `json` 模块提供了 JSON 的序列化和反序列化功能。\n\n| 方法                 | 描述                                                   |\n| -------------------- | ------------------------------------------------------ |\n| `json.dump(obj, fp)` | 将 Python 对象 `obj` 编码为 JSON 格式并写入文件 `fp`。 |\n| `json.dumps(obj)`    | 将 Python 对象 `obj` 编码为 JSON 格式并返回字符串。    |\n| `json.load(fp)`      | 从文件 `fp` 读取 JSON 数据并解码为 Python 对象。       |\n| `json.loads(s)`      | 将字符串 `s` 解码为 Python 对象。                      |\n\n#### 13.1.1 基本操作\n\n```python\nimport json\n\n# Python对象转JSON\ndata = {\n    "name": "张三",\n    "age": 30,\n    "is_student": False,\n    "courses": ["Python", "数据分析", "机器学习"],\n    "scores": {"Python": 95, "数据分析": 88}\n}\n\n# 转换为JSON字符串\njson_str = json.dumps(data, ensure_ascii=False, indent=4)\nprint(json_str)\n\n# 写入JSON文件\nwith open("data.json", "w", encoding="utf-8") as f:\n    json.dump(data, f, ensure_ascii=False, indent=4)\n\n# 从JSON字符串解析\nparsed_data = json.loads(json_str)\nprint(parsed_data["name"])  # 张三\n\n# 从JSON文件读取\nwith open("data.json", "r", encoding="utf-8") as f:\n    loaded_data = json.load(f)\n    print(loaded_data["scores"]["Python"])  # 95\n```\n\n#### 13.1.2 重要参数说明\n\n| 参数           | 说明                                        | 用法示例                                        |\n| -------------- | ------------------------------------------- | ----------------------------------------------- |\n| `ensure_ascii` | 是否转义非 ASCII 字符，False 时保留原始字符 | `json.dumps(data, ensure_ascii=False)`          |\n| `indent`       | 缩进格式，美化输出                          | `json.dumps(data, indent=4)`                    |\n| `separators`   | 指定分隔符，用于紧凑输出                    | `json.dumps(data, separators=(\',\', \':\'))`       |\n| `sort_keys`    | 是否按键排序                                | `json.dumps(data, sort_keys=True)`              |\n| `default`      | 指定序列化函数，处理不可序列化对象          | `json.dumps(obj, default=lambda o: o.__dict__)` |\n\n#### 13.1.3 自定义对象序列化\n\nPython 的 `json` 模块默认无法直接序列化自定义类对象，但提供了多种方式解决：\n\n```python\nimport json\n\n# ========== 方法一：提供default参数 ==========\n\nclass Person:\n    def __init__(self, name, age):\n        self.name = name\n        self.age = age\n\n\ndef person_to_dict(person):\n    """将Person对象转换为字典"""\n    return {\n        "name": person.name,\n        "age": person.age\n    }\n\n\n# 示例：使用default参数序列化自定义对象\nperson = Person("李四", 25)\njson_str = json.dumps(person, default=person_to_dict, ensure_ascii=False)\nprint(json_str)  # {"name": "李四", "age": 25}\n\n\n# ========== 方法二：通过自定义编码器 ==========\n\nclass PersonEncoder(json.JSONEncoder):\n    """自定义JSON编码器处理Person类"""\n    def default(self, obj):\n        if isinstance(obj, Person):\n            return {"name": obj.name, "age": obj.age}\n        return super().default(obj)\n\n\n# 示例：使用自定义编码器序列化对象\njson_str = json.dumps(person, cls=PersonEncoder, ensure_ascii=False)\nprint(json_str)  # {"name": "李四", "age": 25}\n\n\n# ========== 方法三：添加to_json方法 ==========\n\nclass Student:\n    def __init__(self, name, grade):\n        self.name = name\n        self.grade = grade\n\n    def __repr__(self):\n        return f"Student(\'{self.name}\', {self.grade})"\n\n    def to_json(self):\n        """返回可JSON序列化的字典"""\n        return {\n            "name": self.name,\n            "grade": self.grade\n        }\n\n\n# 示例：使用对象的to_json方法序列化\nstudents = [Student("小明", 90), Student("小红", 88)]\njson_str = json.dumps([s.to_json() for s in students], ensure_ascii=False)\nprint(json_str)  # [{"name": "小明", "grade": 90}, {"name": "小红", "grade": 88}]\n```\n\n#### 13.1.4 JSON 解码为自定义对象\n\n```python\nimport json\nfrom typing import Dict\n\n\nclass Person:\n    def __init__(self, name: str, age: int):\n        self.name = name\n        self.age = age\n\n    def __str__(self):\n        return f"{self.name}({self.age})"\n\n\ndef dict_to_person(data: Dict) -> Person:\n    return Person(data["name"], data["age"])\n\n\n# 使用 json.loads() 的 object_hook 参数将 JSON 字符串直接转换为自定义对象\n# object_hook 的用途:\n# 1. 自动将 JSON 解析出的字典转换为自定义类的实例\n# 2. 在解析 JSON 时进行数据转换和验证\n# 3. 简化从 JSON 到对象模型的映射过程\n# 4. 避免手动创建对象的繁琐步骤\n\n# 工作原理:\n# - json.loads() 首先将 JSON 字符串解析为 Python 字典\n# - 然后对每个解析出的字典调用 object_hook 函数\n# - object_hook 函数返回的对象将替代原始字典\n\n# 实际应用场景:\n# - API 响应数据转换为应用程序对象模型\n# - 配置文件解析为配置对象\n# - 数据导入时的格式转换\n\nperson_data = \'{"name": "Alice", "age": 25}\'\nperson = json.loads(person_data, object_hook=dict_to_person)\nprint(type(person))  # <class \'__main__.Person\'>\nprint([person.name, person.age])  # [\'Alice\', 25]\nprint(person)  # Alice(25)\n\n```\n\n#### 13.1.5 处理复杂 JSON 数据\n\n```python\n# 处理嵌套结构\nnested_json = \'\'\'\n{\n    "company": "ABC Corp",\n    "employees": [\n        {"name": "张三", "department": "技术", "skills": ["Python", "Java"]},\n        {"name": "李四", "department": "市场", "skills": ["营销", "策划"]}\n    ],\n    "locations": {\n        "headquarters": "北京",\n        "branches": ["上海", "广州", "深圳"]\n    }\n}\n\'\'\'\n\ndata = json.loads(nested_json)\n\n# 访问嵌套数据\nprint(data["employees"][0]["name"])        # 张三\nprint(data["employees"][0]["skills"][0])   # Python\nprint(data["locations"]["branches"][1])    # 广州\n\n# 修改嵌套数据\ndata["employees"][0]["skills"].append("C++")\ndata["locations"]["branches"].append("成都")\n\n# 保存修改后的数据\nupdated_json = json.dumps(data, ensure_ascii=False, indent=2)\nprint(updated_json)\n```\n\n#### 13.1.6 性能优化\n\n处理大型 JSON 文件时，可以使用流式解析来提高性能：\n\n```python\nimport ijson  # 需安装: pip install ijson\n\n# 流式解析大型JSON文件\nwith open("large_file.json", "rb") as f:\n    # 只提取特定字段\n    for item in ijson.items(f, "items.item"):\n        print(item["id"], item["name"])\n        # 处理一项后继续，不必载入整个文件\n```\n\n#### 13.1.7 JSON Schema 验证\n\n验证 JSON 数据是否符合预期格式：\n\n```python\nfrom jsonschema import validate  # 需安装: pip install jsonschema\n\n# 定义JSON Schema\nschema = {\n    "type": "object",\n    "properties": {\n        "name": {"type": "string"},\n        "age": {"type": "integer", "minimum": 0},\n        "email": {"type": "string", "format": "email"}\n    },\n    "required": ["name", "age"]\n}\n\n# 验证数据\nvalid_data = {"name": "张三", "age": 30, "email": "<EMAIL>"}\ninvalid_data = {"name": "李四", "age": -5}\n\ntry:\n    validate(instance=valid_data, schema=schema)\n    print("有效数据")\nexcept Exception as e:\n    print(f"验证失败: {e}")\n\ntry:\n    validate(instance=invalid_data, schema=schema)\n    print("有效数据")\nexcept Exception as e:\n    print(f"验证失败: {e}")  # 会因age小于0而失败\n```\n\n### 13.2 CSV 处理\n\nCSV (Comma-Separated Values) 是一种常见的表格数据格式。Python 的 `csv` 模块提供了读写 CSV 文件的功能，适用于处理电子表格和数据库导出数据。\n\n> 在我们写入中文数据时，尽量将编码更换为 `GBK` 否则写入 CSV 会导致一些乱码问题\n\n\n\n#### 13.2.1 基本读写操作\n\n```python\nimport csv\n\n# 写入CSV文件\ndata = [\n    ["姓名", "年龄", "城市"],\n    ["张三", 30, "北京"],\n    ["李四", 25, "上海"],\n    ["王五", 28, "广州"]\n]\n\nwith open("people.csv", "w", newline="", encoding="gbk") as f:\n    writer = csv.writer(f)\n    writer.writerows(data)  # 一次写入多行\n\n# 逐行写入\nwith open("people_row.csv", "w", newline="", encoding="gbk") as f:\n    writer = csv.writer(f)\n    for row in data:\n        writer.writerow(row)  # 一次写入一行\n\n# 读取CSV文件\nwith open("people.csv", "r", encoding="gbk") as f:\n    reader = csv.reader(f)\n    for row in reader:\n        print(row)\n```\n\n#### 13.2.2 使用字典处理 CSV 文件\n\n```python\n# 使用字典写入CSV\nimport csv\n\ndict_data = [\n    {"姓名": "张三", "年龄": 30, "城市": "北京"},\n    {"姓名": "李四", "年龄": 25, "城市": "上海"},\n    {"姓名": "王五", "年龄": 28, "城市": "广州"}\n]\n\nwith open("people_dict.csv", "w", newline="", encoding="gbk") as f:\n    fieldnames = ["姓名", "年龄", "城市"]\n    writer = csv.DictWriter(f, fieldnames=fieldnames)\n    writer.writeheader()  # 写入表头\n    writer.writerows(dict_data)  # 写入多行数据\n\n# 使用字典读取CSV\nwith open("people_dict.csv", "r", encoding="gbk") as f:\n    reader = csv.DictReader(f)\n    for row in reader:\n        print(f"{row[\'姓名\']} ({row[\'年龄\']}岁) 来自 {row[\'城市\']}")\n```\n\n#### 13.2.3 CSV 方言与格式化选项\n\n```python\n# 自定义CSV方言\ncsv.register_dialect(\n    \'tab_dialect\',\n    delimiter=\'\\t\',       # 使用制表符作为分隔符\n    quotechar=\'"\',        # 引号字符\n    escapechar=\'\\\\\',      # 转义字符\n    doublequote=False,    # 不使用双引号转义\n    quoting=csv.QUOTE_MINIMAL  # 最小引用策略\n)\n\n# 使用自定义方言\nwith open("tab_data.csv", "w", newline="", encoding="utf-8") as f:\n    writer = csv.writer(f, dialect=\'tab_dialect\')\n    writer.writerows(data)\n\n# 常见格式化选项\nwith open("formatted.csv", "w", newline="", encoding="utf-8") as f:\n    writer = csv.writer(\n        f,\n        delimiter=\',\',          # 分隔符\n        quotechar=\'"\',          # 引号字符\n        quoting=csv.QUOTE_NONNUMERIC,  # 为非数值字段添加引号\n        escapechar=\'\\\\\',        # 转义字符\n        lineterminator=\'\\n\'     # 行终止符\n    )\n    writer.writerows(data)\n```\n\n#### 13.2.4 处理特殊情况\n\n```python\n# 处理含有引号和逗号的数据\ncomplex_data = [\n    ["产品", "描述", "价格"],\n    ["笔记本", "14\\" 高配, i7处理器", 5999.99],\n    ["手机", "5.5\\" 屏幕, 双卡双待", 2999.50]\n]\n\nwith open("complex.csv", "w", newline="", encoding="utf-8") as f:\n    writer = csv.writer(f, quoting=csv.QUOTE_ALL)  # 所有字段加引号\n    writer.writerows(complex_data)\n\n# 跳过特定行\nwith open("complex.csv", "r", encoding="utf-8") as f:\n    reader = csv.reader(f)\n    next(reader)  # 跳过表头\n    for row in reader:\n        print(row)\n\n# 处理缺失值\nwith open("missing.csv", "r", encoding="utf-8") as f:\n    reader = csv.reader(f)\n    for row in reader:\n        # 将空字符串转换为None\n        processed_row = [None if cell == \'\' else cell for cell in row]\n        print(processed_row)\n```\n\n#### 13.2.5 CSV 文件的高级操作\n\n```python\n# 过滤行\nwith open("people.csv", "r", encoding="utf-8") as f:\n    reader = csv.DictReader(f)\n    # 筛选年龄大于25的记录\n    filtered_data = [row for row in reader if int(row["年龄"]) > 25]\n\n# 计算统计值\nwith open("grades.csv", "r", encoding="utf-8") as f:\n    reader = csv.DictReader(f)\n    # 计算平均分\n    scores = [float(row["分数"]) for row in reader]\n    avg_score = sum(scores) / len(scores)\n    print(f"平均分: {avg_score:.2f}")\n\n# 合并多个CSV文件\nimport glob\n\ndef merge_csv_files(file_pattern, output_file):\n    # 获取所有匹配的文件\n    all_files = glob.glob(file_pattern)\n    \n    with open(output_file, "w", newline="", encoding="utf-8") as outfile:\n        # 假设所有文件结构相同\n        for i, filename in enumerate(all_files):\n            with open(filename, "r", encoding="utf-8") as infile:\n                reader = csv.reader(infile)\n                if i == 0:\n                    # 第一个文件，保留表头\n                    for row in reader:\n                        csv.writer(outfile).writerow(row)\n                else:\n                    # 跳过后续文件的表头\n                    next(reader, None)\n                    for row in reader:\n                        csv.writer(outfile).writerow(row)\n\n# 使用示例\n# merge_csv_files("data_*.csv", "merged_data.csv")\n```\n\n### 13.3 XML 处理\n\nXML (eXtensible Markup Language) 是一种用于存储和传输数据的标记语言。Python 提供多种处理 XML 的方法，最常用的是 `xml.etree.ElementTree` 模块。\n\n#### 13.3.1 创建和写入 XML\n\n```python\nimport xml.etree.ElementTree as ET\n\n# 创建XML根元素\nroot = ET.Element("data")\n\n# 添加子元素\nitems = ET.SubElement(root, "items")\n\n# 添加多个项目\nfor i in range(1, 4):\n    item = ET.SubElement(items, "item")\n    item.set("id", str(i))  # 设置属性\n    item.text = f"第{i}项"  # 设置文本内容\n    \n    # 添加嵌套元素\n    detail = ET.SubElement(item, "detail")\n    detail.text = f"项目{i}的详情"\n\n# 创建用户信息部分\nusers = ET.SubElement(root, "users")\n\n# 添加用户\nuser = ET.SubElement(users, "user")\nuser.set("name", "张三")\nET.SubElement(user, "age").text = "30"\nET.SubElement(user, "city").text = "北京"\n\nuser2 = ET.SubElement(users, "user")\nuser2.set("name", "李四")\nET.SubElement(user2, "age").text = "25"\nET.SubElement(user2, "city").text = "上海"\n\n# 生成XML字符串\nxml_str = ET.tostring(root, encoding="utf-8").decode("utf-8")\nprint(xml_str)\n\n# 写入XML文件\ntree = ET.ElementTree(root)\ntree.write("data.xml", encoding="utf-8", xml_declaration=True)\n```\n\n#### 13.3.2 解析和读取 XML\n\n```python\n# 从文件解析XML\ntree = ET.parse("data.xml")\nroot = tree.getroot()\n\n# 从字符串解析XML\nxml_string = \'<data><item id="1">测试</item></data>\'\nroot = ET.fromstring(xml_string)\n\n# 获取元素标签和属性\nprint(f"根元素标签: {root.tag}")\n\n# 遍历子元素\nfor child in root:\n    print(f"子元素: {child.tag}, 属性: {child.attrib}")\n\n# 查找特定元素 - find()查找第一个匹配元素\nitems = root.find("items")\nif items is not None:\n    # 使用findall()查找所有匹配的子元素\n    for item in items.findall("item"):\n        print(f"项目ID: {item.get(\'id\')}, 内容: {item.text}")\n        # 获取嵌套元素\n        detail = item.find("detail")\n        if detail is not None:\n            print(f"  详情: {detail.text}")\n\n# 使用XPath查询\n# 查找所有用户名称\nusers = root.findall(".//user")\nfor user in users:\n    print(f"用户: {user.get(\'name\')}")\n    print(f"  年龄: {user.find(\'age\').text}")\n    print(f"  城市: {user.find(\'city\').text}")\n\n# 更复杂的XPath查询 - 查找北京的用户\nbeijing_users = root.findall(".//user[city=\'北京\']")\nfor user in beijing_users:\n    print(f"北京用户: {user.get(\'name\')}")\n```\n\n#### 13.3.3 修改 XML\n\n```python\n# 修改元素属性\nuser = root.find(".//user[@name=\'张三\']")\nif user is not None:\n    user.set("status", "active")  # 添加新属性\n    \n    # 修改子元素文本\n    age_elem = user.find("age")\n    if age_elem is not None:\n        age_elem.text = "31"  # 修改年龄\n    \n    # 添加新元素\n    ET.SubElement(user, "email").text = "<EMAIL>"\n\n# 删除元素\nusers = root.find("users")\nif users is not None:\n    for user in users.findall("user"):\n        if user.get("name") == "李四":\n            users.remove(user)\n            break\n\n# 保存修改\ntree.write("updated_data.xml", encoding="utf-8", xml_declaration=True)\n```\n\n#### 13.3.4 命名空间处理\n\n```python\n# 创建带命名空间的XML\nroot = ET.Element("data", {"xmlns:dt": "http://example.org/datatypes"})\n\n# 添加带命名空间前缀的元素\nitem = ET.SubElement(root, "dt:item")\nitem.set("dt:type", "special")\nitem.text = "带命名空间的元素"\n\n# 生成XML字符串\nns_xml = ET.tostring(root, encoding="utf-8").decode("utf-8")\nprint(ns_xml)\n\n# 解析带命名空间的XML\nns_root = ET.fromstring(ns_xml)\n\n# 使用带命名空间的XPath查询\nnamespaces = {"dt": "http://example.org/datatypes"}\nns_items = ns_root.findall(".//dt:item", namespaces)\n\nfor item in ns_items:\n    print(f"找到命名空间元素: {item.text}")\n    print(f"类型属性: {item.get(\'{http://example.org/datatypes}type\')}")\n```\n\n### 13.4 配置文件处理\n\n配置文件是应用程序保存设置和首选项的常用方式。Python 提供了多种处理不同格式配置文件的方法。\n\n#### 13.4.1 INI 配置文件处理\n\nINI 文件是一种结构简单的配置文件格式，Python 通过 `configparser` 模块提供支持。\n\n```python\nimport configparser\n# configparser是Python标准库中用于处理配置文件的模块\n# 它可以读取、写入和修改类似INI格式的配置文件\n# 配置文件通常包含节(sections)\n# 如:[DEFAULT]\n# 和每个节下的键值对(key-value pairs)\n# 如:\n# language = 中文\n# theme = 默认\n# auto_save = true\n# save_interval = 10\n\n\n# 创建一个新的配置解析器\nconfig = configparser.ConfigParser()\n\n# 添加默认节和配置项\nconfig["DEFAULT"] = {\n    "language": "中文",\n    "theme": "默认",\n    "auto_save": "true",  \n    "save_interval": "10"\n}\n\n# 添加应用设置节\nconfig["应用设置"] = {}\nconfig["应用设置"]["font_size"] = "14"\n\n# 添加用户信息节\nconfig["用户信息"] = {}\nuser_info = config["用户信息"]  # 创建一个引用，方便添加多个配置项\nuser_info["username"] = "张三"\nuser_info["email"] = "<EMAIL>"\nuser_info["remember_password"] = "false"  # 修改为标准布尔值字符串\n\n# 添加数据库连接节\nconfig["数据库"] = {}\nconfig["数据库"]["host"] = "localhost"\nconfig["数据库"]["port"] = "3306"\nconfig["数据库"]["username"] = "root"\nconfig["数据库"]["password"] = "123456"\n\n# 将配置写入文件\nwith open("config.ini", "w", encoding="utf-8") as f:\n    config.write(f)\n\n# 读取配置文件\nconfig = configparser.ConfigParser()\nconfig.read("config.ini", encoding="utf-8")\n\n# 获取所有节名称\nprint("所有配置节:", config.sections())  # [\'应用设置\', \'用户信息\', \'数据库\']\n\n# 获取节中的所有键\nprint("用户信息节中的所有键:", list(config["用户信息"].keys()))\n\n# 获取特定配置值\nprint("用户名:", config["用户信息"]["username"])  # 张三\n\n# 获取默认节中的值\nprint("默认语言:", config.get("应用设置", "language"))  # 使用DEFAULT中的值\n\n# 类型转换方法\nfont_size = config.getint("应用设置", "font_size")\nauto_save = config.getboolean("DEFAULT", "auto_save", fallback=True)  # 将"true"转换为True\nsave_interval = config.getint("DEFAULT", "save_interval")\n\nprint(f"字体大小: {font_size}, 类型: {type(font_size)}")  # 字体大小: 14, 类型: <class \'int\'>\nprint(f"自动保存: {auto_save}, 类型: {type(auto_save)}")  # 自动保存: True, 类型: <class \'bool\'>\n\n# 修改配置\nconfig["用户信息"]["username"] = "李四"\n\n# 添加新配置\nif "日志设置" not in config:\n    config["日志设置"] = {}\nconfig["日志设置"]["log_level"] = "INFO"\nconfig["日志设置"]["log_file"] = "app.log"\nconfig["日志设置"]["max_size"] = "10MB"\n\n# 保存修改后的配置\nwith open("updated_config.ini", "w", encoding="utf-8") as f:\n    config.write(f)\n```\n\n#### 13.4.2 YAML 配置文件处理\n\nYAML 是一种人类友好的数据序列化格式，需要安装 `PyYAML` 库。\n\n```python\n# 需要安装PyYAML: pip install pyyaml\nimport yaml\n\n# 创建YAML数据\ndata = {\n    "server": {\n        "host": "example.com",\n        "port": 8080\n    },\n    "database": {\n        "host": "localhost",\n        "port": 5432,\n        "username": "admin",\n        "password": "secret"\n    },\n    "logging": {\n        "level": "INFO",\n        "file": "/var/log/app.log"\n    },\n    "users": [\n        {"name": "张三", "role": "admin"},\n        {"name": "李四", "role": "user"}\n    ]\n}\n\n# 写入YAML文件\nwith open("config.yaml", "w", encoding="utf-8") as f:\n    yaml.dump(data, f, default_flow_style=False, allow_unicode=True)\n\n# 读取YAML文件\nwith open("config.yaml", "r", encoding="utf-8") as f:\n    config = yaml.safe_load(f)\n    \n# 访问配置\nprint(f"服务器地址: {config[\'server\'][\'host\']}")  # example.com\nprint(f"第一个用户: {config[\'users\'][0][\'name\']}")  # 张三\n\n# 修改配置\nconfig["server"]["port"] = 9090\nconfig["users"].append({"name": "王五", "role": "user"})\n\n# 保存修改\nwith open("updated_config.yaml", "w", encoding="utf-8") as f:\n    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)\n```\n\n#### 13.4.3 使用环境变量作为配置\n\n环境变量是一种灵活的配置方式，尤其适用于容器化应用。\n\n```python\nimport os\nfrom dotenv import load_dotenv  # 需安装: pip install python-dotenv\n\n# 从.env文件加载环境变量\nload_dotenv()  # 默认加载当前目录下的.env文件\n\n# 读取环境变量，提供默认值\ndatabase_url = os.environ.get("DATABASE_URL", "sqlite:///default.db")\ndebug_mode = os.environ.get("DEBUG", "False").lower() in ("true", "1", "yes")\nport = int(os.environ.get("PORT", "8000"))\n\nprint(f"数据库URL: {database_url}")\nprint(f"调试模式: {debug_mode}")\nprint(f"端口: {port}")\n\n# 创建.env文件示例\nenv_content = """\n# 数据库设置\nDATABASE_URL=postgresql://user:pass@localhost/dbname\n# 应用设置\nDEBUG=True\nPORT=5000\n"""\n\nwith open(".env.example", "w") as f:\n    f.write(env_content)\n```\n\n#### 13.4.4 JSON 作为配置文件\n\nJSON 也是一种常用的配置文件格式，尤其适合需要与 Web 应用共享配置的场景。\n\n```python\nimport json\nimport os\n\n# 默认配置\ndefault_config = {\n    "app_name": "MyApp",\n    "version": "1.0.0",\n    "debug": False,\n    "database": {\n        "host": "localhost",\n        "port": 5432,\n        "name": "app_db"\n    },\n    "cache": {\n        "enabled": True,\n        "ttl": 3600\n    }\n}\n\n# 配置文件路径\nconfig_path = "app_config.json"\n\n# 加载配置\ndef load_config():\n    # 如果配置文件存在，则加载它\n    if os.path.exists(config_path):\n        with open(config_path, "r", encoding="utf-8") as f:\n            return json.load(f)\n    # 否则使用默认配置并创建配置文件\n    else:\n        save_config(default_config)\n        return default_config\n\n# 保存配置\ndef save_config(config):\n    with open(config_path, "w", encoding="utf-8") as f:\n        json.dump(config, f, indent=4, ensure_ascii=False)\n\n# 更新配置\ndef update_config(key, value):\n    config = load_config()\n    \n    # 处理嵌套键 (如 "database.host")\n    if "." in key:\n        parts = key.split(".")\n        current = config\n        for part in parts[:-1]:\n            if part not in current:\n                current[part] = {}\n            current = current[part]\n        current[parts[-1]] = value\n    else:\n        config[key] = value\n    \n    save_config(config)\n    return config\n\n# 使用示例\nconfig = load_config()\nprint(f"应用名称: {config[\'app_name\']}")\nprint(f"数据库主机: {config[\'database\'][\'host\']}")\n\n# 更新配置\nupdate_config("database.host", "db.example.com")\nupdate_config("cache.ttl", 7200)\n\n# 重新加载配置\nconfig = load_config()\nprint(f"更新后的数据库主机: {config[\'database\'][\'host\']}")\nprint(f"更新后的缓存TTL: {config[\'cache\'][\'ttl\']}")\n```\n\n### 13.5 正则表达式\n\n**正则表达式**（通常缩写为 regex 或 regexp）是一种强大的文本处理工具。它使用一种专门的语法来定义 **搜索模式 (pattern)**，然后可以用这个模式在文本中进行查找、匹配、提取或替换操作。正则表达式在各种编程任务中都极为有用，例如：\n\n* **数据验证**: 检查用户输入是否符合特定格式（如邮箱、手机号、日期）。\n* **数据提取**: 从大量非结构化文本（如日志文件、网页内容）中精确地抽取所需信息（如 IP 地址、错误代码、特定标签内容）。\n* **文本替换**: 对文本进行复杂的查找和替换操作，例如格式化代码、屏蔽敏感信息。\n* **文本分割**: 根据复杂的模式分割字符串。\n\nPython 通过内置的 `re` 模块提供了对正则表达式的全面支持。\n\n**核心概念**: 正则表达式的核心在于使用 **元字符 (metacharacters)** 和普通字符组合来定义模式。元字符是具有特殊含义的字符，而普通字符则匹配它们自身。\n\n#### 13.5.1 常用元字符和语法\n\n以下是一些最常用的正则表达式元字符及其含义：\n\n| 元字符     | 描述                                                                                              | 示例模式        | 示例匹配                               |\n| :--------- | :------------------------------------------------------------------------------------------------ | :-------------- | :------------------------------------- |\n| `.`        | 匹配 **除换行符 `\\n` 之外** 的任何单个字符 (使用 `re.DOTALL` 标志可匹配换行符)。                       | `a.c`           | `abc`, `a_c`, `a&c` (但不匹配 `ac`)    |\n| `^`        | 匹配字符串的 **开头**。在多行模式 (`re.MULTILINE`) 下，也匹配每行的开头。                              | `^Hello`        | `Hello world` (但不匹配 `Say Hello`)   |\n| `$`        | 匹配字符串的 **结尾**。在多行模式 (`re.MULTILINE`) 下，也匹配每行的结尾。                              | `world$`        | `Hello world` (但不匹配 `world say`)   |\n| `*`        | 匹配前面的元素 **零次或多次** (贪婪模式)。                                                          | `go*d`          | `gd`, `god`, `good`, `goooood`         |\n| `+`        | 匹配前面的元素 **一次或多次** (贪婪模式)。                                                          | `go+d`          | `god`, `good`, `goooood` (但不匹配 `gd`) |\n| `?`        | 匹配前面的元素 **零次或一次** (贪婪模式)。也用于将贪婪量词变为 **非贪婪** (见后文)。                    | `colou?r`       | `color`, `colour`                      |\n| `{n}`      | 匹配前面的元素 **恰好 `n` 次**。                                                                   | `\\d{3}`         | `123` (但不匹配 `12` 或 `1234`)        |\n| `{n,}`     | 匹配前面的元素 **至少 `n` 次** (贪婪模式)。                                                         | `\\d{2,}`        | `12`, `123`, `12345`                   |\n| `{n,m}`    | 匹配前面的元素 **至少 `n` 次，但不超过 `m` 次** (贪婪模式)。                                          | `\\d{2,4}`       | `12`, `123`, `1234` (但不匹配 `1` 或 `12345`) |\n| `[]`       | **字符集**。匹配方括号中包含的 **任意一个** 字符。                                                      | `[abc]`         | `a` 或 `b` 或 `c`                    |\n| `[^...]`   | **否定字符集**。匹配 **不在** 方括号中包含的任何字符。                                                  | `[^0-9]`        | 任何非数字字符                         |\n| `\\`        | **转义符**。用于转义元字符，使其匹配其字面含义 (如 `\\.` 匹配句点 `.`)，或用于引入特殊序列 (如 `\\d`)。 | `\\$`            | `$` 字符本身                           |\n| `|`        | **或 (OR)** 运算符。匹配 `|` 左边或右边的表达式。                                                 | `cat|dog`       | `cat` 或 `dog`                         |\n| `()`       | **分组**。将括号内的表达式视为一个整体，用于应用量词、限制 `|` 的范围，或 **捕获** 匹配的子字符串。    | `(ab)+`         | `ab`, `abab`, `ababab`                 |\n\n**踩坑提示**:\n\n* **转义**: 当需要匹配元字符本身时（如 `.`、`*`、`?`），必须在前面加上反斜杠 `\\` 进行转义。例如，要匹配 IP 地址中的点，应使用 `\\.`。\n* **原始字符串 (Raw Strings)**: 在 Python 中定义正则表达式模式时，**强烈建议** 使用原始字符串（在字符串前加 `r`），如 `r"\\d+"`。这可以避免 Python 解释器对反斜杠进行自身的转义，从而简化正则表达式的书写，尤其是包含很多 `\\` 的模式。\n\n#### 13.5.2 特殊序列 (预定义字符集)\n\n`re` 模块提供了一些方便的特殊序列来代表常见的字符集：\n\n| 特殊序列 | 描述                                                         | 等价于        | 示例      |\n| :------- | :----------------------------------------------------------- | :------------ | :-------- |\n| `\\d`     | 匹配任何 **Unicode 数字** 字符 (包括 [0-9] 和其他语言的数字)。 | `[0-9]` (ASCII) | `1`, `5`    |\n| `\\D`     | 匹配任何 **非数字** 字符。                                     | `[^0-9]` (ASCII)| `a`, `_`, ` ` |\n| `\\s`     | 匹配任何 **Unicode 空白** 字符 (包括 ` `、`\\t`、`\\n`、`\\r`、`\\f`、`\\v` 等)。 |               | ` `, `\\t`   |\n| `\\S`     | 匹配任何 **非空白** 字符。                                     |               | `a`, `1`, `.` |\n| `\\w`     | 匹配任何 **Unicode 词语** 字符 (字母、数字和下划线 `_`)。      | `[a-zA-Z0-9_]` (ASCII) | `a`, `B`, `5`, `_` |\n| `\\W`     | 匹配任何 **非词语** 字符。                                     | `[^a-zA-Z0-9_]`(ASCII) | `!`, ` `, `@` |\n| `\\b`     | 匹配 **词语边界** (word boundary)。这是一个零宽度断言，匹配词语字符 (`\\w`) 和非词语字符 (`\\W`) 之间，或词语字符和字符串开头/结尾之间的位置。 |               | `\\bword\\b` |\n| `\\B`     | 匹配 **非词语边界**。                                          |               | `\\Bword\\B` |\n\n#### 13.5.3 贪婪模式 vs. 非贪婪模式\n\n默认情况下，量词 (`*`, `+`, `?`, `{n,}`, `{n,m}`) 都是 **贪婪 (Greedy)** 的，它们会尽可能多地匹配字符。\n\n**场景**: 从 HTML 标签 `<b>Bold Text</b>` 中提取 `<b>`。\n\n```python\nimport re\n\ntext = "<b>Bold Text</b> Regular Text <b>Another Bold</b>"\n\n# 贪婪模式 (默认)\ngreedy_pattern = r"<.*>" # . 匹配任何字符，* 匹配零次或多次\nmatch_greedy = re.search(greedy_pattern, text)\nif match_greedy:\n    # * 会一直匹配到字符串的最后一个 >\n    print(f"贪婪匹配结果: {match_greedy.group(0)}")\n    # 输出: 贪婪匹配结果: <b>Bold Text</b> Regular Text <b>Another Bold</b>\n\n# 非贪婪模式 (在量词后加 ?)\nnon_greedy_pattern = r"<.*?>" # *? 匹配零次或多次，但尽可能少地匹配\nmatch_non_greedy = re.search(non_greedy_pattern, text)\nif match_non_greedy:\n    # *? 遇到第一个 > 就停止匹配\n    print(f"非贪婪匹配结果: {match_non_greedy.group(0)}")\n    # 输出: 非贪婪匹配结果: <b>\n\n# 查找所有非贪婪匹配\nall_matches_non_greedy = re.findall(non_greedy_pattern, text)\nprint(f"所有非贪婪匹配: {all_matches_non_greedy}")\n# 输出: 所有非贪婪匹配: [\'<b>\', \'</b>\', \'<b>\', \'</b>\']\n```\n\n**何时使用非贪婪模式？**\n\n当需要匹配从某个开始标记到 **最近的** 结束标记之间的内容时，通常需要使用非贪婪量词 (`*?`, `+?`, `??`, `{n,}?`, `{n,m}?`)。\n\n#### 13.5.4 分组与捕获\n\n使用圆括号 `()` 可以将模式的一部分组合起来，形成一个 **分组 (Group)**。分组有几个重要作用：\n\n1.  **应用量词**: 将量词作用于整个分组，如 `(abc)+` 匹配 `abc`, `abcabc` 等。\n2.  **限制 `|` 范围**: 如 `gr(a|e)y` 匹配 `gray` 或 `grey`。\n3.  **捕获内容**: 默认情况下，每个分组会 **捕获 (Capture)** 其匹配到的子字符串，以便后续引用或提取。\n\n**场景**: 从 "Name: John Doe, Age: 30" 中提取姓名和年龄。\n\n```python\nimport re\n\ntext = "Name: John Doe, Age: 30; Name: Jane Smith, Age: 25"\n\n# 定义带有捕获组的模式\n# 第一个组 (\\w+\\s+\\w+) 捕获姓名\n# 第二个组 (\\d+) 捕获年龄\npattern_capture = r"Name: (\\w+\\s+\\w+), Age: (\\d+)"\n\n# 使用 findall 查找所有匹配项\n# findall 返回一个列表，如果模式中有捕获组，列表元素是包含所有捕获组内容的元组\nmatches = re.findall(pattern_capture, text)\nprint(f"\\n--- 使用 findall 提取分组 ---")\nprint(matches) # 输出: [(\'John Doe\', \'30\'), (\'Jane Smith\', \'25\')]\n\n# 使用 finditer 获取 Match 对象，可以更灵活地访问分组\nprint("\\n--- 使用 finditer 访问分组 ---")\nfor match_obj in re.finditer(pattern_capture, text):\n    # match_obj.group(0) 或 group() 获取整个匹配\n    print(f"整个匹配: {match_obj.group(0)}")\n    # match_obj.group(1) 获取第一个捕获组的内容 (姓名)\n    print(f"  姓名 (组 1): {match_obj.group(1)}")\n    # match_obj.group(2) 获取第二个捕获组的内容 (年龄)\n    print(f"  年龄 (组 2): {match_obj.group(2)}")\n    # match_obj.groups() 获取所有捕获组组成的元组\n    print(f"  所有分组: {match_obj.groups()}")\n\n# 非捕获组 (?:...)\n# 如果只想分组而不捕获内容，可以使用非捕获组\npattern_non_capture = r"Name: (?:\\w+\\s+\\w+), Age: (\\d+)" # 第一个组不捕获\nmatches_nc = re.findall(pattern_non_capture, text)\nprint(f"\\n--- 使用非捕获组的 findall ---")\nprint(matches_nc) # 输出: [\'30\', \'25\'] (只包含捕获组的内容)\n```\n\n**反向引用 (Backreferences)**: 可以在模式内部或替换字符串中使用 `\\1`, `\\2`, ... 来引用前面捕获组匹配到的文本。\n\n**场景**: 查找重复的单词，如 "the the"。\n\n```python\ntext_repeat = "This is the the test sentence with repeated repeated words."\n# \\b 确保是完整的单词\n# (\\w+) 捕获第一个单词\n# \\s+ 匹配中间的空白\n# \\1 引用第一个捕获组匹配的内容\npattern_repeat = r"\\b(\\w+)\\s+\\1\\b"\nrepeated_words = re.findall(pattern_repeat, text_repeat)\nprint(f"\\n--- 查找重复单词 ---")\nprint(f"找到的重复单词: {repeated_words}") # 输出: [\'the\', \'repeated\']\n\n# 使用 sub 进行替换\n# 将重复的单词替换为单个单词\ncorrected_text = re.sub(pattern_repeat, r"\\1", text_repeat) # 使用 \\1 引用捕获组\nprint(f"修正后的文本: {corrected_text}")\n# 输出: This is the test sentence with repeated words.\n```\n\n#### 13.5.5 `re` 模块核心函数\n\nPython 的 `re` 模块提供了以下核心函数来执行正则表达式操作：\n\n| 函数                               | 描述                                                                                                                               | 返回值                                                              | 主要用途                                                                                                       |\n| :--------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------ | :----------------------------------------------------------------------------------------------------------- |\n| `re.match(p, s, flags=0)`        | 从字符串 `s` 的 **开头** 尝试匹配模式 `p`。                                                                                            | 匹配成功返回 `Match` 对象，失败返回 `None`。                          | 验证字符串是否以特定模式开始。                                                                                 |\n| `re.search(p, s, flags=0)`       | 在 **整个** 字符串 `s` 中搜索模式 `p` 的 **第一个** 匹配项。                                                                            | 匹配成功返回 `Match` 对象，失败返回 `None`。                          | 在字符串中查找模式是否存在，并获取第一个匹配项的信息。                                                           |\n| `re.findall(p, s, flags=0)`      | 在字符串 `s` 中查找模式 `p` 的 **所有非重叠** 匹配项。                                                                                 | 返回一个 **列表**。如果模式无捕获组，列表元素是匹配的字符串；如果有捕获组，列表元素是包含各捕获组内容的元组。 | 提取字符串中所有符合模式的子串或捕获组内容。                                                                   |\n| `re.finditer(p, s, flags=0)`     | 与 `findall` 类似，但返回一个 **迭代器 (iterator)**，迭代器中的每个元素都是一个 `Match` 对象。                                            | 返回一个迭代器，每个元素是 `Match` 对象。                             | 处理大量匹配结果时更 **内存高效**，因为不需要一次性存储所有结果。可以方便地访问每个匹配的详细信息（如位置）。 |\n| `re.sub(p, repl, s, count=0, flags=0)` | 在字符串 `s` 中查找模式 `p` 的所有匹配项，并用 `repl` 替换它们。`repl` 可以是字符串（支持 `\\g<name>` 或 `\\1` 等反向引用）或函数。`count` 指定最大替换次数。 | 返回替换后的 **新字符串**。                                              | 执行查找和替换操作。`repl` 可以是函数，实现更复杂的替换逻辑。                                                  |\n| `re.split(p, s, maxsplit=0, flags=0)` | 使用模式 `p` 作为分隔符来 **分割** 字符串 `s`。`maxsplit` 指定最大分割次数。                                                          | 返回一个 **列表**，包含分割后的子字符串。如果模式中有捕获组，捕获的内容也会包含在列表中。                         | 根据复杂的模式分割字符串。                                                                                 |\n| `re.compile(p, flags=0)`         | **编译** 正则表达式模式 `p` 为一个 **模式对象 (Pattern Object)**。                                                                      | 返回一个 `Pattern` 对象。                                           | 当一个模式需要被 **多次** 使用时，预先编译可以 **提高性能**。模式对象拥有与 `re` 模块函数同名的方法（如 `pattern.search(s)`）。 |\n\n**代码示例**:\n\n```python\nimport re\n\ntext = "The quick brown fox jumps over the lazy dog. Phone: ************. Email: <EMAIL>."\n\n# 1. re.match() - 检查开头\npattern_start = r"The"\nmatch_result = re.match(pattern_start, text)\nif match_result:\n    print(f"match(): 字符串以 \'{pattern_start}\' 开头。匹配内容: \'{match_result.group(0)}\'")\nelse:\n    print(f"match(): 字符串不以 \'{pattern_start}\' 开头。")\n\nmatch_fail = re.match(r"quick", text) # 不从开头匹配，所以失败\nprint(f"match() 失败示例: {match_fail}") # None\n\n# 2. re.search() - 查找第一个匹配\npattern_word = r"fox"\nsearch_result = re.search(pattern_word, text)\nif search_result:\n    print(f"search(): 找到单词 \'{pattern_word}\'。 起始位置: {search_result.start()}, 结束位置: {search_result.end()}")\nelse:\n    print(f"search(): 未找到单词 \'{pattern_word}\'。")\n\n# 3. re.findall() - 查找所有匹配\npattern_digits = r"\\d+" # 查找所有数字序列\nall_digits = re.findall(pattern_digits, text)\nprint(f"findall(): 找到的所有数字序列: {all_digits}") # [\'123\', \'456\', \'7890\']\n\npattern_email = r"(\\w+)@(\\w+\\.\\w+)" # 查找邮箱并捕获用户名和域名\nemail_parts = re.findall(pattern_email, text)\nprint(f"findall() 捕获组: {email_parts}") # [(\'test\', \'example.com\')]\n\n# 4. re.finditer() - 迭代查找匹配对象\npattern_words_o = r"\\b\\w*o\\w*\\b" # 查找所有包含字母\'o\'的单词\nprint("finditer(): 查找包含 \'o\' 的单词:")\nfor match in re.finditer(pattern_words_o, text, re.IGNORECASE): # 使用 IGNORECASE 标志\n    print(f"  找到: \'{match.group(0)}\' at position {match.span()}")\n\n# 5. re.sub() - 替换\npattern_phone = r"\\d{3}-\\d{3}-\\d{4}"\n# 将电话号码替换为 [REDACTED]\ncensored_text = re.sub(pattern_phone, "[REDACTED]", text)\nprint(f"sub() 替换电话号码: {censored_text}")\n\n# 使用函数进行替换\ndef mask_email(match_obj):\n    username = match_obj.group(1)\n    domain = match_obj.group(2)\n    return f"{username[0]}***@{domain}" # 用户名只显示第一个字符\n\ncensored_email_text = re.sub(pattern_email, mask_email, text)\nprint(f"sub() 使用函数替换邮箱: {censored_email_text}")\n\n# 6. re.split() - 分割\npattern_punct = r"[.,:;]\\s*" # 按标点符号和后面的空格分割\nparts = re.split(pattern_punct, text)\nprint(f"split(): 按标点分割: {parts}")\n\n# 7. re.compile() - 编译模式\ncompiled_pattern = re.compile(r"l\\w*y", re.IGNORECASE) # 编译查找以l开头y结尾的词\n# 多次使用编译后的模式\nmatch1 = compiled_pattern.search(text)\nif match1:\n    print(f"compile() & search(): 找到 \'{match1.group(0)}\'")\nmatch2 = compiled_pattern.findall("Actually, Lily is lovely.")\nprint(f"compile() & findall(): 找到 {match2}") # [\'Lily\', \'lovely\']\n```\n\n#### 13.5.6 Match 对象详解\n\n当 `re.match()`, `re.search()` 或 `re.finditer()` 中的一项成功匹配时，它们会返回一个 **`Match` 对象**。这个对象包含了关于匹配结果的详细信息。\n\n| Match 对象方法/属性 | 描述                                                     | 示例 (假设 `m = re.search(r"(\\w+) (\\d+)", "Order P123 45")`) |\n| :---------------- | :------------------------------------------------------- | :----------------------------------------------------------- |\n| `m.group(0)` 或 `m.group()` | 返回整个匹配的字符串。                                     | `\'P123 45\'`                                                  |\n| `m.group(n)`        | 返回第 `n` 个捕获组匹配的字符串 (从 1 开始计数)。          | `m.group(1)` 返回 `\'P123\'`, `m.group(2)` 返回 `\'45\'`          |\n| `m.groups()`        | 返回一个包含所有捕获组匹配内容的 **元组**。                   | `(\'P123\', \'45\')`                                             |\n| `m.groupdict()`     | 如果模式中使用了 **命名捕获组** `(?P<name>...)`，返回一个包含组名和匹配内容的字典。 | (需要命名组，如下例)                                         |\n| `m.start([group])`  | 返回整个匹配或指定 `group` 的 **起始索引** (包含)。         | `m.start()` 返回 6, `m.start(1)` 返回 6, `m.start(2)` 返回 11 |\n| `m.end([group])`    | 返回整个匹配或指定 `group` 的 **结束索引** (不包含)。       | `m.end()` 返回 13, `m.end(1)` 返回 10, `m.end(2)` 返回 13    |\n| `m.span([group])`   | 返回一个包含 `(start, end)` 索引的 **元组**。             | `m.span()` 返回 `(6, 13)`, `m.span(1)` 返回 `(6, 10)`         |\n| `m.string`          | 传递给 `match()` 或 `search()` 的原始字符串。              | `\'Order P123 45\'`                                            |\n| `m.re`              | 匹配时使用的已编译的模式对象 (`Pattern` object)。        |                                                              |\n\n**命名捕获组示例**:\n\n```python\nimport re\n\ntext = "Product ID: ABC-987, Quantity: 50"\n# 使用 ?P<name> 定义命名捕获组\npattern_named = r"Product ID: (?P<product_id>[A-Z]+-\\d+), Quantity: (?P<quantity>\\d+)"\n\nmatch = re.search(pattern_named, text)\nif match:\n    print("\\n--- 使用命名捕获组 ---")\n    # 通过组名访问捕获的内容\n    print(f"产品 ID: {match.group(\'product_id\')}") # ABC-987\n    print(f"数量: {match.group(\'quantity\')}")   # 50\n    # groupdict() 返回包含所有命名组的字典\n    print(f"捕获字典: {match.groupdict()}") # {\'product_id\': \'ABC-987\', \'quantity\': \'50\'}\n```\n\n#### 13.5.7 正则表达式标志 (Flags)\n\n标志可以修改正则表达式的匹配行为。可以在 `re` 函数的 `flags` 参数中指定，或在编译时指定。多个标志可以使用 `|` (按位或) 组合。\n\n| 标志                   | 简写   | 描述                                                                         |\n| :--------------------- | :----- | :--------------------------------------------------------------------------- |\n| `re.IGNORECASE`        | `re.I` | 进行 **不区分大小写** 的匹配。                                                 |\n| `re.MULTILINE`         | `re.M` | 使 `^` 和 `$` 匹配 **每行的开头和结尾**，而不仅仅是整个字符串的开头和结尾。     |\n| `re.DOTALL`            | `re.S` | 使元字符 `.` 能够匹配 **包括换行符 `\\n` 在内** 的任何字符。                      |\n| `re.VERBOSE`           | `re.X` | **详细模式**。允许在模式字符串中添加 **空白和注释** 以提高可读性，此时模式中的空白会被忽略，`#` 后到行尾的内容视为注释。 |\n| `re.ASCII`             | `re.A` | 使 `\\w`, `\\W`, `\\b`, `\\B`, `\\s`, `\\S` 只匹配 ASCII 字符，而不是完整的 Unicode 字符集 (Python 3 默认匹配 Unicode)。 |\n| `re.UNICODE` (默认)    | `re.U` | 使 `\\w`, `\\W`, `\\b`, `\\B`, `\\s`, `\\S`, `\\d`, `\\D` 匹配完整的 Unicode 字符集。这是 Python 3 的默认行为。 |\n\n**示例**:\n\n```python\nimport re\n\ntext_multi = """first line\nsecond line\nTHIRD line"""\n\n# re.I (忽略大小写)\nprint(f"\\n--- Flags 示例 ---")\nprint(f"re.I: {re.findall(r\'line\', text_multi, re.IGNORECASE)}") # [\'line\', \'line\', \'line\']\n\n# re.M (多行模式)\nprint(f"re.M (^): {re.findall(r\'^s.*\', text_multi, re.MULTILINE | re.IGNORECASE)}") # [\'second line\']\nprint(f"re.M ($): {re.findall(r\'line$\', text_multi, re.MULTILINE | re.IGNORECASE)}") # [\'line\', \'line\', \'line\']\n\n# re.S (DOTALL)\ntext_dot = "Hello\\nWorld"\nprint(f"re.S (.): {re.search(r\'Hello.World\', text_dot, re.DOTALL)}") # 匹配成功\nprint(f"No re.S (.): {re.search(r\'Hello.World\', text_dot)}")      # 匹配失败 (None)\n\n# re.X (VERBOSE)\n# 一个复杂的邮箱模式，使用 VERBOSE 模式添加注释和空格\npattern_verbose = r"""\n  ^                  # 匹配字符串开头\n  [\\w\\.\\-]+          # 用户名部分 (字母、数字、下划线、点、连字符)\n  @                  # @ 符号\n  ([\\w\\-]+\\.)+       # 域名部分 (允许子域名，如 mail.example.)\n  [a-zA-Z]{2,7}      # 顶级域名 (如 .com, .org)\n  $                  # 匹配字符串结尾\n"""\nemail = "<EMAIL>"\nmatch_verbose = re.match(pattern_verbose, email, re.VERBOSE)\nprint(f"re.X (VERBOSE): {\'匹配成功\' if match_verbose else \'匹配失败\'}") # 匹配成功\n```\n\n#### 13.5.8 实际应用场景示例\n\n**场景 1: 验证中国大陆手机号 (简单示例)**\n\n```python\nimport re\n\ndef is_valid_china_mobile(phone_number: str) -> bool:\n    """简单验证中国大陆手机号码 (11位数字，常见号段)"""\n    # 模式解释:\n    # ^            匹配字符串开头\n    # (?:...)      非捕获组\n    # 1[3-9]       第一位是1，第二位是3到9\n    # \\d{9}        后面跟9位数字\n    # $            匹配字符串结尾\n    pattern = r"^(?:1[3-9])\\d{9}$"\n    if re.match(pattern, phone_number):\n        return True\n    else:\n        return False\n\nprint("\\n--- 手机号验证 ---")\nprint(f"13812345678: {is_valid_china_mobile(\'13812345678\')}") # True\nprint(f"12012345678: {is_valid_china_mobile(\'12012345678\')}") # False (号段不对)\nprint(f"1381234567: {is_valid_china_mobile(\'1381234567\')}")  # False (位数不够)\nprint(f"138123456789: {is_valid_china_mobile(\'138123456789\')}")# False (位数太多)\n```\n*注意*: 实际手机号验证可能需要更复杂的规则或查询号段数据库。\n\n**场景 2: 从 Apache/Nginx 日志中提取 IP 地址和请求路径**\n\n```python\nimport re\n\nlog_line = \'************* - - [03/May/2025:17:20:01 +0900] "GET /index.html HTTP/1.1" 200 1542 "-" "Mozilla/5.0..."\'\n\n# 模式解释:\n# ^([\\d\\.]+)      捕获开头的 IP 地址 (数字和点的组合)\n# \\s+-\\s+-\\s+      匹配中间的 \' - - \' 部分\n# \\[.*?\\]        匹配并忽略方括号内的时间戳 (非贪婪)\n# \\s+"           匹配时间戳后的空格和双引号\n# (GET|POST|PUT|DELETE|HEAD) \\s+  捕获请求方法 (GET, POST 等) 和空格\n# ([^\\s"]+)      捕获请求路径 (非空格、非双引号的字符)\n# \\s+HTTP/[\\d\\.]+" 捕获 HTTP 版本部分\n# .* 匹配剩余部分\npattern_log = r\'^([\\d\\.]+) \\s+-\\s+-\\s+ \\[.*?\\] \\s+"(GET|POST|PUT|DELETE|HEAD)\\s+([^\\s"]+)\\s+HTTP/[\\d\\.]+" .*\'\n\nmatch = re.match(pattern_log, log_line)\nif match:\n    ip_address = match.group(1)\n    method = match.group(2)\n    path = match.group(3)\n    print("\\n--- 日志解析 ---")\n    print(f"IP 地址: {ip_address}") # *************\n    print(f"请求方法: {method}")   # GET\n    print(f"请求路径: {path}")     # /index.html\nelse:\n    print("日志格式不匹配")\n```\n\n**场景 3: 将 Markdown 样式的链接 `[text](url)` 转换为 HTML `<a>` 标签**\n\n```python\nimport re\n\nmarkdown_text = "这是一个链接 [Google](https://www.google.com) 和另一个 [Python 官网](http://python.org) 的例子。"\n\n# 模式解释:\n# \\[        匹配字面量 \'[\'\n# ([^\\]]+)  捕获链接文本 (不是 \']\' 的任意字符一次或多次)\n# \\]        匹配字面量 \']\'\n# \\(        匹配字面量 \'(\'\n# ([^\\)]+)  捕获 URL (不是 \')\' 的任意字符一次或多次)\n# \\)        匹配字面量 \')\'\npattern_md_link = r\'\\[([^\\]]+)\\]\\(([^\\)]+)\\)\'\n\n# 使用 re.sub 和反向引用 \\1, \\2 进行替换\nhtml_text = re.sub(pattern_md_link, r\'<a href="\\2">\\1</a>\', markdown_text)\n\nprint("\\n--- Markdown 转 HTML 链接 ---")\nprint(f"原始 Markdown: {markdown_text}")\nprint(f"转换后 HTML: {html_text}")\n# 输出: 这是一个链接 <a href="https://www.google.com">Google</a> 和另一个 <a href="http://python.org">Python 官网</a> 的例子。\n```'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E4%B8%89%E7%AB%A0%EF%BC%9A-%E9%AB%98%E7%BA%A7%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86"><span class="toc-number">1.</span> <span class="toc-text">第十三章： 高级数据处理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#13-1-JSON-%E5%A4%84%E7%90%86"><span class="toc-number">1.1.</span> <span class="toc-text">13.1 JSON 处理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-1-%E5%9F%BA%E6%9C%AC%E6%93%8D%E4%BD%9C"><span class="toc-number">1.1.1.</span> <span class="toc-text">13.1.1 基本操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-2-%E9%87%8D%E8%A6%81%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E"><span class="toc-number">1.1.2.</span> <span class="toc-text">13.1.2 重要参数说明</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-3-%E8%87%AA%E5%AE%9A%E4%B9%89%E5%AF%B9%E8%B1%A1%E5%BA%8F%E5%88%97%E5%8C%96"><span class="toc-number">1.1.3.</span> <span class="toc-text">13.1.3 自定义对象序列化</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-4-JSON-%E8%A7%A3%E7%A0%81%E4%B8%BA%E8%87%AA%E5%AE%9A%E4%B9%89%E5%AF%B9%E8%B1%A1"><span class="toc-number">1.1.4.</span> <span class="toc-text">13.1.4 JSON 解码为自定义对象</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-5-%E5%A4%84%E7%90%86%E5%A4%8D%E6%9D%82-JSON-%E6%95%B0%E6%8D%AE"><span class="toc-number">1.1.5.</span> <span class="toc-text">13.1.5 处理复杂 JSON 数据</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-6-%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96"><span class="toc-number">1.1.6.</span> <span class="toc-text">13.1.6 性能优化</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-1-7-JSON-Schema-%E9%AA%8C%E8%AF%81"><span class="toc-number">1.1.7.</span> <span class="toc-text">13.1.7 JSON Schema 验证</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#13-2-CSV-%E5%A4%84%E7%90%86"><span class="toc-number">1.2.</span> <span class="toc-text">13.2 CSV 处理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#13-2-1-%E5%9F%BA%E6%9C%AC%E8%AF%BB%E5%86%99%E6%93%8D%E4%BD%9C"><span class="toc-number">1.2.1.</span> <span class="toc-text">13.2.1 基本读写操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-2-2-%E4%BD%BF%E7%94%A8%E5%AD%97%E5%85%B8%E5%A4%84%E7%90%86-CSV-%E6%96%87%E4%BB%B6"><span class="toc-number">1.2.2.</span> <span class="toc-text">13.2.2 使用字典处理 CSV 文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-2-3-CSV-%E6%96%B9%E8%A8%80%E4%B8%8E%E6%A0%BC%E5%BC%8F%E5%8C%96%E9%80%89%E9%A1%B9"><span class="toc-number">1.2.3.</span> <span class="toc-text">13.2.3 CSV 方言与格式化选项</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-2-4-%E5%A4%84%E7%90%86%E7%89%B9%E6%AE%8A%E6%83%85%E5%86%B5"><span class="toc-number">1.2.4.</span> <span class="toc-text">13.2.4 处理特殊情况</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-2-5-CSV-%E6%96%87%E4%BB%B6%E7%9A%84%E9%AB%98%E7%BA%A7%E6%93%8D%E4%BD%9C"><span class="toc-number">1.2.5.</span> <span class="toc-text">13.2.5 CSV 文件的高级操作</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#13-3-XML-%E5%A4%84%E7%90%86"><span class="toc-number">1.3.</span> <span class="toc-text">13.3 XML 处理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#13-3-1-%E5%88%9B%E5%BB%BA%E5%92%8C%E5%86%99%E5%85%A5-XML"><span class="toc-number">1.3.1.</span> <span class="toc-text">13.3.1 创建和写入 XML</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-3-2-%E8%A7%A3%E6%9E%90%E5%92%8C%E8%AF%BB%E5%8F%96-XML"><span class="toc-number">1.3.2.</span> <span class="toc-text">13.3.2 解析和读取 XML</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-3-3-%E4%BF%AE%E6%94%B9-XML"><span class="toc-number">1.3.3.</span> <span class="toc-text">13.3.3 修改 XML</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-3-4-%E5%91%BD%E5%90%8D%E7%A9%BA%E9%97%B4%E5%A4%84%E7%90%86"><span class="toc-number">1.3.4.</span> <span class="toc-text">13.3.4 命名空间处理</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#13-4-%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%E5%A4%84%E7%90%86"><span class="toc-number">1.4.</span> <span class="toc-text">13.4 配置文件处理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#13-4-1-INI-%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%E5%A4%84%E7%90%86"><span class="toc-number">1.4.1.</span> <span class="toc-text">13.4.1 INI 配置文件处理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-4-2-YAML-%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%E5%A4%84%E7%90%86"><span class="toc-number">1.4.2.</span> <span class="toc-text">13.4.2 YAML 配置文件处理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-4-3-%E4%BD%BF%E7%94%A8%E7%8E%AF%E5%A2%83%E5%8F%98%E9%87%8F%E4%BD%9C%E4%B8%BA%E9%85%8D%E7%BD%AE"><span class="toc-number">1.4.3.</span> <span class="toc-text">13.4.3 使用环境变量作为配置</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-4-4-JSON-%E4%BD%9C%E4%B8%BA%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6"><span class="toc-number">1.4.4.</span> <span class="toc-text">13.4.4 JSON 作为配置文件</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#13-5-%E6%AD%A3%E5%88%99%E8%A1%A8%E8%BE%BE%E5%BC%8F"><span class="toc-number">1.5.</span> <span class="toc-text">13.5 正则表达式</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-1-%E5%B8%B8%E7%94%A8%E5%85%83%E5%AD%97%E7%AC%A6%E5%92%8C%E8%AF%AD%E6%B3%95"><span class="toc-number">1.5.1.</span> <span class="toc-text">13.5.1 常用元字符和语法</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-2-%E7%89%B9%E6%AE%8A%E5%BA%8F%E5%88%97-%E9%A2%84%E5%AE%9A%E4%B9%89%E5%AD%97%E7%AC%A6%E9%9B%86"><span class="toc-number">1.5.2.</span> <span class="toc-text">13.5.2 特殊序列 (预定义字符集)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-3-%E8%B4%AA%E5%A9%AA%E6%A8%A1%E5%BC%8F-vs-%E9%9D%9E%E8%B4%AA%E5%A9%AA%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.5.3.</span> <span class="toc-text">13.5.3 贪婪模式 vs. 非贪婪模式</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-4-%E5%88%86%E7%BB%84%E4%B8%8E%E6%8D%95%E8%8E%B7"><span class="toc-number">1.5.4.</span> <span class="toc-text">13.5.4 分组与捕获</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-5-re-%E6%A8%A1%E5%9D%97%E6%A0%B8%E5%BF%83%E5%87%BD%E6%95%B0"><span class="toc-number">1.5.5.</span> <span class="toc-text">13.5.5 re 模块核心函数</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-6-Match-%E5%AF%B9%E8%B1%A1%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.5.6.</span> <span class="toc-text">13.5.6 Match 对象详解</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-7-%E6%AD%A3%E5%88%99%E8%A1%A8%E8%BE%BE%E5%BC%8F%E6%A0%87%E5%BF%97-Flags"><span class="toc-number">1.5.7.</span> <span class="toc-text">13.5.7 正则表达式标志 (Flags)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#13-5-8-%E5%AE%9E%E9%99%85%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E7%A4%BA%E4%BE%8B"><span class="toc-number">1.5.8.</span> <span class="toc-text">13.5.8 实际应用场景示例</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>