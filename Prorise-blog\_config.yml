# Hexo Configuration
## Docs: https://hexo.io/docs/configuration.html
## Source: https://github.com/hexojs/hexo/

# --- 网站信息 (Site) ---
# 这部分定义了您博客的基础信息，会显示在网站的各个位置。
# -----------------------------------------------------------

# 网站主标题，会显示在浏览器标签页和主题的显眼位置。
title: "Prorise - 分享技术与实战经验"
# 网站副标题，通常显示在主标题下方。
subtitle: ""
# 网站描述，主要用于SEO，告诉搜索引擎您的网站是关于什么内容的。
# (优化建议) 建议写一句完整的话来描述您的博客。
description: "Prorise的个人博客，一位超全栈工程师，在这里分享前后端开发、架构设计、运维部署等技术领域的学习笔记与实战经验。"
# 网站关键词，用于SEO，多个关键词用英文逗号隔开。
keywords: 全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享
# 您的名字或昵称。
author: Prorise
# 网站语言。对于中文博客，'zh-CN' 是正确的设置。
language: zh-CN
# 网站时区。建议设置为您所在的时区，以确保文章发布时间的准确性。
# 'Asia/Shanghai' 设置正确，代表中国时区。
timezone: "Asia/Shanghai"

# --- 网址 (URL) ---
# 这部分配置与您网站的链接结构（URL）密切相关，非常重要。
# -----------------------------------------------------------
# 【重要】请务必修改为您的网站最终的访问网址！
# 例如，如果您使用 GitHub Pages，它可能是 'https://yourname.github.io'。
# 这个配置会影响网站所有资源的绝对路径，如果错误，可能导致CSS、JS、图片加载失败。
url: https://prorise666.site/ # 暂时使用测试地址
# 文章的永久链接格式。
# :year, :month, :day, :i_month, :i_day, :hour, :minute, :second, :title, :name, :post_title, :id, :category
# 示例:
#   :year/:month/:day/:title/  (默认值，例如 2025/06/08/hello-world/)
#   :title.html               (例如 hello-world.html，非常简洁)
#   :category/:title/          (例如 tech/hello-world/)
# 推荐使用 hexo-abbrlink 插件生成短链接，对SEO友好且不会因修改标题而改变： permalink: posts/:abbrlink.html
permalink: posts/:abbrlink.html
# 永久链接中各部分的默认值。
permalink_defaults:
# URL 美化选项。
pretty_urls:
  # 是否移除永久链接末尾的 'index.html'。通常保持默认。
  trailing_index: true
  # 是否移除永久链接末尾的 '.html'。通常保持默认。
  trailing_html: true

# --- 目录 (Directory) ---
# 这部分定义了您项目中的各个核心文件夹的名称，通常无需修改。
# -----------------------------------------------------------
# 源文件夹，您创作内容的地方（文章、图片等）。
source_dir: source
# 公共文件夹，存放最终生成的静态网站文件（最终部署到服务器上的内容）。
public_dir: public
# 标签页面的目录名。例如: yoursite.com/tags/
tag_dir: tags
# 归档页面的目录名。例如: yoursite.com/archives/
archive_dir: archives
# 分类页面的目录名。例如: yoursite.com/categories/
category_dir: categories
# 代码下载目录（如果您使用代码下载功能）。
code_dir: downloads/code
# 国际化（i18n）语言文件的目录。
i18n_dir: :lang
# 跳过渲染指定的文件或文件夹。您可以在这里列出不希望被Hexo处理的文件路径。
skip_render:

# --- 写作 (Writing) ---
# 这部分配置与您撰写文章时的行为相关。
# -----------------------------------------------------------
# 新文章的文件名格式。:title 是文章标题。
new_post_name: :title.md
# 新建文件的默认布局，通常是 'post' (文章) 或 'draft' (草稿)。
default_layout: post
# 是否将文章标题转换为 "Title Case" (首字母大写)。建议 'false'，保持原文案。
titlecase: false
# 外部链接设置。
external_link:
  # 是否在新标签页中打开外部链接。建议 'true'，以保留用户在您的网站上。
  enable: true
  # 应用范围。'site' 表示全站，'post' 表示仅文章内容。
  field: site
  # 在这里列出的域名将不会被当作外部链接处理。例如: 'exclude: yoursite.com'
  exclude: ""
# 文件名大小写转换。0: 无变化; 1: 小写; 2: 大写。
filename_case: 0
# 是否渲染 'source/_drafts' 文件夹中的草稿。'false' 表示默认不渲染。
render_drafts: false
# 是否启用文章资源文件夹。
# 如果设为 'true'，当您用 `hexo new post "xxx"` 创建文章时，
# 会在 `source/_posts` 目录下同时创建一个名为 "xxx" 的文件夹，方便您存放该文章专属的图片等资源。
post_asset_folder: false
# 是否将链接转换为与根目录的相对路径。通常保持 'false'。
relative_link: false
# 是否渲染发布日期在未来的文章。'true' 表示会渲染。
future: true
# 代码高亮引擎。可选值: 'highlight.js' 或 'prismjs'。
# Butterfly 等现代主题通常有自己的高亮方案，可能会覆盖此设置。
syntax_highlighter: highlight.js
# highlight.js 的具体配置。
highlight:
  # 是否显示行号。
  line_number: true
  # 是否自动检测语言。建议 'false' 以获得更好的性能和准确性。
  auto_detect: false
  # 用什么字符替换 Tab。
  tab_replace: ""
  # 是否用 `<table>` 包裹代码块以实现复杂的行号显示。
  wrap: true
  # 是否启用 highlight.js 内置的样式。通常主题会有自己的样式，所以设为 'false'。
  hljs: false
# prismjs 的具体配置。
prismjs:
  # 是否在预处理阶段进行语法高亮。
  preprocess: true
  # 是否显示行号。
  line_number: true
  # 用什么字符替换 Tab。
  tab_replace: ""

# --- 主页设置 (Home page setting) ---
# 这部分控制您博客首页的文章列表行为。
# -----------------------------------------------------------
index_generator:
  # 首页的路径。空字符串 '' 表示网站根目录。
  path: ""
  # 每页显示的文章数量。0 表示禁用分页。
  per_page: 10
  # 文章排序方式。'-date' 表示按日期降序（最新的在最前），'date' 表示升序。
  order_by: -date

# --- 分类与标签 (Category & Tag) ---
# -----------------------------------------------------------
# 默认分类。当文章没有指定分类时，会使用此分类。
default_category: uncategorized
# 分类别名。例如: 'cate_alias: my-cate'
category_map:
# 标签别名。例如: 'tag_alias: my-tag'
tag_map:

# --- 元数据 (Metadata elements) ---
# -----------------------------------------------------------
# 是否在HTML头部注入 Hexo 的 meta generator 标签。有助于进行网站技术栈统计，建议保留。
meta_generator: true

# --- 日期与时间格式 (Date / Time format) ---
# Hexo 使用 Moment.js 库来处理时间格式。
# 格式定义: http://momentjs.com/docs/#/displaying/format/
# -----------------------------------------------------------
# 日期显示格式。
date_format: YYYY-MM-DD
# 时间显示格式。
time_format: HH:mm:ss
# 文章更新时间的选项。
# 'mtime': 使用文件的最后修改时间作为更新时间 (推荐)。
# 'date': 使用 Front-matter 中的 'date' 字段作为更新时间。
# 'empty': 不使用更新时间。
updated_option: "mtime"

# --- 分页 (Pagination) ---
# 归档页（如分类页、标签页）的分页设置。
# -----------------------------------------------------------
# 每页显示的文章数量。0 表示禁用分页。
per_page: 10
# 分页的目录。例如: yoursite.com/page/2/
pagination_dir: page

# --- 包含与排除文件 (Include / Exclude file(s)) ---
# 这些选项仅对 'source/' 文件夹生效。
# -----------------------------------------------------------
# Hexo 默认会忽略隐藏文件和以 '_' 或 '#' 开头的文件/文件夹。
# include: [.well-known] # 如果您需要 Hexo 处理某些被忽略的文件，可以在这里列出。
include:
# exclude: [temp/] # 如果您希望 Hexo 忽略 'source/' 下的某些文件或文件夹，可以在这里列出。
exclude:
# ignore: [*.log] # 全局忽略规则。
ignore:

# --- 扩展 (Extensions) ---
# -----------------------------------------------------------
## 插件: https://hexo.io/plugins/
## 主题: https://hexo.io/themes/
# [重要] 当前使用的主题名称。请确保 'themes' 文件夹下有对应名称的主题文件夹。
# 例如，要使用 Butterfly 主题，请修改为: 'theme: butterfly'
theme: anzhiyu

plugins:
  - hexo-plugin-postchat

# --- Algolia Search (Final Configuration for AnZhiYu Theme) ---
algolia:
  appId: "K8Y7M0RMXQ"

  # 前端搜索用的"只读搜索Key"
  apiKey: "********************************" # <--- 这里放 Search API Key

  # 运行 hexo algolia 命令用的"管理员Key"
  adminApiKey: "********************************" # <--- 这里放 Write API Key

  indexName: "prorise_blog"
  chunkSize: 5000

  # fields 字段必须存在，并且至少包含一些基本项
  fields:
    - content:strip:truncate,0,500
    - excerpt:strip
    - permalink
    - tags
    - title

# 信笺样式留言板 (Envelope Comment Page)
# 插件主页: https://akilar.top/posts/e2d3c450/
envelope_comment:
  # --- 功能总开关 ---
  enable: true

  # --- 自定义图片资源 ---
  # 您可以将这些图片下载后放到您自己的图床，然后替换链接
  custom_pic:
    cover: https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250612/tyCN/0X0/ezgif.com-video-to-webp-converter.webp # 信纸顶部的图片
    line: https://npm.elemecdn.com/hexo-butterfly-envelope/lib/line.png # 信纸底部的装饰线
    beforeimg: https://npm.elemecdn.com/hexo-butterfly-envelope/lib/before.png # 信封的上半部分
    afterimg: https://npm.elemecdn.com/hexo-butterfly-envelope/lib/after.png # 信封的下半部分

  # --- 信纸上的引导性问题 ---
  # 插件会从下面的列表中随机选择一条显示在信纸上
  message:
    - 有什么想对我说的吗？
    - 对我的博客有什么建议或想法？
    - 留下你的足迹，我们交个朋友吧！
    - 哪怕是有什么想吐槽的，都可以告诉我哦~

  # --- 信纸底部的署名 ---
  bottom: Prorise 竭诚为您服务！ # 仅支持单行文本，已为您替换

  # --- （可选）信纸划出的高度 ---
  # 如果您的 message 内容很长，可以适当增加这个值
  height: # 1024px

  # --- （可选）页面的访问路径 ---
  # 默认为 /comments/，生成的页面地址就是 yoursite.com/comments/
  path: comments

  # --- （可选）为这个自动生成的页面设置 Front-matter ---
  # 这是最关键的部分，它定义了留言板页面的属性
  front_matter:
    title: 留言板 # 页面标题
    comments: true # 允许评论（必须为true）
    top_img: false # 不显示页面顶部的横幅大图
    type: envelope # 页面类型，主题会根据这个来应用特殊样式

# APlayer 播放器配置
aplayer:
  meting: true # 必须开启，以支持从音乐平台获取歌单
  asset_inject: false # 主题会自行处理资源注入，这里建议设为false

# --- 部署 (Deployment) ---
# `hexo deploy` 命令的配置。
# Docs: https://hexo.io/docs/one-command-deployment
# -----------------------------------------------------------
deploy:
  type: git
  repo: "**************:Prorise-cool/Prorise-cool.github.io.git" # 推荐使用SSH地址
  branch: main

# --- PostChat 配置 (PostChat Configuration) ---
# PostChat 是一个集成了 AI 文章摘要和智能对话功能的插件
# 插件主页: https://postchat.zhheo.com/
# -----------------------------------------------------------
postchat:
  # --- 账户配置 ---
  account:
    # PostChat API Key，请前往 https://ai.tianli0.top/ 获取
    key: "S-DNA1JM95BX2F9L0N"

  # --- 文章摘要配置 ---
  summary:
    # 是否启用文章摘要功能
    enableSummary: true
    # 文章内容选择器，用于选择要进行摘要的内容
    postSelector: "#article-container"
    # 摘要标题
    title: "✨ AI 文章摘要"
    # 摘要样式 CSS 地址
    summaryStyle: "https://ai.tianli0.top/static/public/postChatUser_summary.min.css"
    # URL 匹配规则，符合此规则的页面才会显示摘要
    # 你的文章格式是 /:year/:month/:day/:title/，对应的正则表达式：
    postURL: ""
    # 黑名单地址（可选，填写 JSON 地址）
    blacklist: ""
    # 提交内容的字数限制，默认 1000 字
    wordLimit: "1000"
    # 智能打字效果，模拟流式处理
    typingAnimate: true

  # --- 智能对话配置 ---
  chat:
    # 是否启用 PostChat 智能对话功能
    enableAI: true
    userIcon: ""
    # 对话按钮背景颜色
    backgroundColor: "#1e2022"
    # 按钮图标填充颜色
    fill: "#FFFFFF"
    # 按钮距离底部的距离
    bottom: "76px"
    # 按钮距离左边的距离
    left: "16px"
    # 按钮宽度
    width: "50px"
    # 聊天窗口宽度
    frameWidth: "380px"
    # 聊天窗口高度
    frameHeight: "600px"
    # 是否启用默认输入提示
    defaultInput: true
    # 是否上传网站内容用于对话
    upLoadWeb: true
    # 是否显示邀请链接
    showInviteLink: false
    # 对话界面标题
    userTitle: "💬 Prorise"
    # 对话界面描述
    userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～"
    # 是否显示对话按钮
    addButton: true

# Hexo SafeGo 安全跳转插件配置
# --------------------------------------
hexo_safego:
  # --- 基本功能设置 ---
  general:
    enable: true # 【必填】插件总开关
    enable_base64_encode: true # 【推荐】是否对链接进行Base64编码，让URL更干净，也能轻微防止爬虫
    enable_target_blank: true # 【推荐】是否在新标签页中打开外链

  # --- 安全与路径设置 ---
  security:
    url_param_name: "u" # URL中的参数名, 例如 /go.html?u=...
    html_file_name: "go.html" # 跳转页面的文件名，可自定义
    ignore_attrs: # 忽略带有这些属性的链接，例如相册的链接
      - "data-fancybox"

  # --- 生效范围设置 ---
  scope:
    apply_containers: # 指定插件在哪些HTML容器内生效
      - "#article-container" # 默认为文章内容区，您可以添加其他选择器，如 '#aside-content'
  # apply_pages:                      # 【可选】只在特定路径下的页面生效，例如 ['/posts/']
  # exclude_pages:                    # 【可选】在哪些页面完全禁用此插件

  # --- 域名白名单 ---
  whitelist:
    domain_whitelist: # 【重要】白名单内的域名不会被跳转，请务必加入您自己的所有域名
      - "prorise.com" # <--- 请替换为您自己的主域名
      - "prorise666.site" # <--- 您绑定的Waline/Twikoo域名
      - "waline.prorise666.site" # <--- 所有您自己的、不希望跳转的域名

  # --- 跳转页面外观设置 ---
  appearance:
    avatar: "/img/user/avatar.webp" # 【可选】跳转页面上显示的头像
    title: "Prorise 的博客" # 【可选】跳转页面大标题
    subtitle: "安全中心" # 【可选】跳转页面副标题
    darkmode: true # 【可选】是否为跳转页启用深色模式
    countdowntime: 5 # 【可选】自动跳转的倒计时秒数，设置为 -1 则不自动跳转，需要用户手动点击

  # --- 调试设置 ---
  debug:
    enable: false # 调试模式，一般保持false。遇到问题时可开启，会输出详细处理日志

# hexo-neat 压缩配置
neat_enable: true
# 压缩HTML
neat_html:
  enable: true
  exclude:
# 压缩CSS
neat_css:
  enable: true
  exclude:
    - "**/*.min.css" # 排除已经压缩过的.min.css文件
# 压缩JS
neat_js:
  enable: true
  exclude:
    - "**/*.min.js" # 排除已经压缩过的.min.js文件

# RSS订阅
feed:
  enable: true
  type: atom
  path: atom.xml
  limit: 20
  hub:
  content: true
  content_limit: 140
  content_limit_delim: " "
