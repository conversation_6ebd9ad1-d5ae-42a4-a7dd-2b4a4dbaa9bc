<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理进阶（十）：第十章：分销电商 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理进阶（十）：第十章：分销电商"><meta name="application-name" content="产品经理进阶（十）：第十章：分销电商"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="产品经理进阶（十）：第十章：分销电商"><meta property="og:url" content="https://prorise666.site/posts/27703.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="第十章：分销电商欢迎来到第十章。在前面的章节中，我们已经完整地学习了，如何设计一个“人、货、场”模型下的平台型电商。现在，我们将探讨一种能为平台带来强大“裂变增长”能力的、建立在社交关系链之上的高级模式——分销电商。 10.1 学习目标在本章中，我的核心目标是，带大家系统性地掌握分销电商的业务模式与"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta name="description" content="第十章：分销电商欢迎来到第十章。在前面的章节中，我们已经完整地学习了，如何设计一个“人、货、场”模型下的平台型电商。现在，我们将探讨一种能为平台带来强大“裂变增长”能力的、建立在社交关系链之上的高级模式——分销电商。 10.1 学习目标在本章中，我的核心目标是，带大家系统性地掌握分销电商的业务模式与"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/27703.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"产品经理进阶（十）：第十章：分销电商",postAI:"true",pageFillDescription:"第十章：分销电商, 10.1 学习目标, 10.2 分销电商项目背景, 1. 为什么要做分销电商？, 2. 分销电商的核心需求, 10.2.1 什么是分销电商, 10.2.2 核心角色定义（供货商、分销商、消费者）, 10.3 分销电商的优势, 10.3.1 低成本快速裂变, 10.3.2 强信任关系转化, 10.3.3 轻资产运营, 10.4 分销电商搭建思路, 10.4.1 分销员体系设计, 10.4.2 商品与供应链管理, 10.4.3 佣金与结算体系, 10.5 分销电商产品设计, 10.5.1 平台端核心功能, 10.5.2 商家端核心功能, 10.5.3 分销商端核心功能, 10.6 本章总结第十章分销电商欢迎来到第十章在前面的章节中我们已经完整地学习了如何设计一个人货场模型下的平台型电商现在我们将探讨一种能为平台带来强大裂变增长能力的建立在社交关系链之上的高级模式分销电商学习目标在本章中我的核心目标是带大家系统性地掌握分销电商的业务模式与产品设计我们将从项目背景出发理解分销电商的定义和核心角色并最终学会如何为这个模式设计其独特的产品功能分销电商项目背景为什么要做分销电商我之所以要考虑在我们的电商产品中融入分销模式其核心的驱动力是为了解决传统电商模式获客成本越来越高的瓶颈分销电商本质上是一种的模式它通过一种利益共享的机制将我们平台上的海量端用户转化为成千上万的小分销商让他们利用自己的私域流量和社交信任去为我们获取更多的新用户分销电商的核心需求基于这个背景我提炼出的搭建分销系统的核心产品需求如下用户可以申请成为平台的分销商商家有权利自定义自己店铺的商品是否允许分销分销商可以发展自己的下线但为了确保业务合规层级不能超过两级什么是分销电商我给分销电商的定义是一个通过设置销售提成作为激励驱动平台用户即分销商利用其自有的社交关系进行商品分享和销售裂变并最终达成自购省钱分享赚钱目的的商业模式正如案例所示分销最常见的形态就是用户将一个带有自己专属二维码或链接的商品海报分享到微信群或朋友圈当他的好友通过这个链接完成购买后他就能获得平台支付的相应比例的佣金在这个模式中我们平台需要为分销商解决好除了销售以外的一切后顾之忧即统一提供货源仓储配送和售后服务核心角色定义供货商分销商消费者我设计分销系统需要清晰地定义出这个新生态中的三个核心角色核心角色我的定义与解读供货商这是货的来源他们可以是我们平台自营的商品也可以是我们平台上参与了分销活动的第三方商家他们的核心诉求是提升商品销量分销商这是我们这个模式中新增的核心角色他们是平台的普通用户在申请成为分销商后就拥有了带货的资格他们不拥有商品不处理订单不负责发货他们唯一的工作就是分享和推广他们的核心诉求是赚取佣金消费者这是最终完成购买的终端用户他们通常是某个分销商的好友或粉丝他们的购买决策很大程度上是建立在对分销商的信任之上分销电商的优势我们已经清楚了分销电商的定义和核心角色现在我需要回答一个关键的商业问题作为一个产品或业务的决策者我为什么要选择分销这种模式答案在于一个设计良好的分销体系能为我们带来传统电商模式难以企及的三大核心优势低成本快速裂变在我看来分销模式最强大最核心的优势就是它解决了现代电商最头痛的问题高昂的获客成本传统模式的困境传统的电商平台需要花费巨额的市场预算去购买流量投放广告来吸引用户分销模式的破局分销模式本质上是将我们的营销预算从购买流量变为了奖励用户我不再花钱给广告平台而是把这部分钱以销售佣金的形式直接分给了帮我们带来客户的分销商这相当于我们将每一个分销商都发展成了我们行走的广告牌和销售渠道他们利用自己的社交关系链进行一带十十带百的裂变式传播正如云集的案例数据显示其单个用户维系成本显著低于阿里京东等传统流量驱动的电商平台这就是裂变带来的低成本优势强信任关系转化分销模式的第二个巨大优势是它能带来极高的销售转化率和用户忠诚度传统模式的挑战用户面对一个冰冷的平台推送的广告内心天然是带有防备和不信任的分销模式的破解分销模式的传播是建立在社交信任的基础之上的朋友的推荐远比平台的广告更具说服力当一个消费者看到他朋友圈里一位他所信任的好友或在真实地分享一款产品的使用心得时他的购买决策链路会变得极短这种基于信任的转化效果是惊人的云集案例中提到的复购率达到就是这种强信任关系带来高用户粘性的最好证明轻资产运营分销模式的第三个优势是它为分销商这个角色提供了一种极具吸引力的轻资产运营模式我把它总结为你只管卖其他都交给我电商环节由谁负责对分销商意味着什么供货选品平台供货商分销商无需自己找货源仓储库存平台供货商分销商无需自己租仓库压库存发货物流平台供货商分销商无需自己打包发快递售后服务平台供货商分销商无需自己处理复杂的退换货问题推广销售分销商分销商只需要专注于他最擅长最核心的一件事分享和推广正是这种轻资产的模式极大地降低了个人成为小老板的门槛使得我们的分销商队伍可以像滚雪球一样快速地发展和壮大分销电商搭建思路我们已经理解了分销电商的是什么和为什么现在我们就进入最核心的怎么做的环节要搭建一套完整的分销电商体系我作为产品经理需要从顶层设计好三大核心支柱分销员体系商品与供应链体系以及佣金与结算体系这三大支柱共同构成了我们分销业务的骨架分销员体系设计分销业务核心是人的生意因此我首先要设计好我们分销员这个核心角色的完整生命周期和组织结构角色与层级为了激励分销员为平台带来更多的下线分销员我设计的体系通常会包含分销层级核心逻辑一个高级别的一级分销商可以邀请新人成为他的二级分销商当二级分销商卖出商品时一级分销商也能获得一部分的团队奖励我的合规设计要点我必须强调为了确保业务的合法合规在国内设计分销体系时计佣计算佣金的层级绝对不能超过三级这是一个不可逾越的红线核心流程我设计的整个分销员体系必须能够支撑起以下几个核心的业务流程成为分销商普通用户可以通过一个申请入口提交申请由平台审核通过后获得分销商身份分享商品分销商可以在内选择商品生成带有自己专属推广码的海报或链接并分享出去发展下线分销商可以生成自己专属的邀请码邀请好友来注册成为自己的下线分销商购买转化当一个普通消费者通过分销商分享的链接完成购买后系统需要准确地记录下这笔订单的归属商品与供应链管理分销员只负责推广而不负责货因此我必须在后台设计好货的管理逻辑平台侧在平台运营后台我需要设计一个总开关可以一键启用或关闭整个平台的分销功能商家侧在商家后台我需要为商家提供两级控制权店铺级开关商家可以决定自己整个店铺是否参与平台的分销活动商品级开关在参与活动的前提下商家还可以进一步地去勾选指定的某些商品来参与分销佣金与结算体系这是驱动整个分销体系运转的发动机我设计的佣金结算体系必须公平透明准确佣金规则配置我需要在平台运营后台设计一个强大的佣金规则引擎它需要支持运营同事可以灵活地按不同维度来设置佣金比例按商品设置不同的商品可以有不同的佣金比例按分销商等级设置高级别的分销商可以享受更高的佣金比例团队奖励设置可以设置当下线分销商出单时其上级可以获得的奖励比例结算与提现当一笔通过分销链接产生的订单完成交易即已过售后维权期后系统需要自动地将计算好的佣金打入对应分销商的佣金账户中同时我需要在分销商的专属后台为他设计清晰的收益报表和便捷的佣金提现功能综上所述我搭建分销电商的整体思路就是围绕人分销员体系货商品管理钱佣金体系这三大核心分别为用户端商家端平台端设计出支撑其运转所必需的功能分销电商产品设计在我们明确了分销电商的搭建思路之后现在我们就进入具体的产品功能设计环节我将严格按照平台端商家端分销商端这三个不同的使用者视角来分别进行功能设计的拆解平台端核心功能这是整个分销系统的总控制器由我们平台的运营人员使用用来设定整个分销业务的游戏规则分销规则配置我设计的后台必须有一个全局的分销设置页面在这里运营可以设置是否开启分销是否开启自购分佣分销层级最多支持几级以及每一级的抽佣比例分销员等级管理为了激励分销商我还会设计一个分销等级管理后台运营可以在这里创建不同的分销商等级如初级中级高级并为每个等级配置不同的邀请奖励和销售抽成比例以及对应的升级规则分销员审核管理当有普通用户申请成为分销商时他们的申请会进入到这个后台的待审核列表中运营人员可以在这里查看申请人的信息并进行通过或驳回的操作订单与结算管理我需要设计一个分销订单列表让运营和财务可以清晰地看到每一笔通过分销产生的订单以及这笔订单需要为哪几级的分销商分别计算多少佣金同时还需要结算设置和提现管理功能来处理佣金的发放商家端核心功能这是我们设计给供货商即参与分销的商家使用的后台核心是让他们能够对自己店铺的分销业务进行自主管理分销商品管理在商家后台的商品管理模块我需要为商家提供一个分销商品设置的功能在这里商家可以勾选自己店铺中愿意拿出利润来进行分销的商品并且可以为这些商品设定一个基础的佣金比例分销业绩查看我还需要为商家提供一个查看分销业绩的报表在这里他可以看到是哪些分销商为他带来了哪些订单让他可以直观地感受到分销为店铺带来的价值分销商端核心功能这是我们设计给分销商本人使用的个人工作台它通常会内嵌在我们用户端的个人中心里申请成为分销商首先我需要在用户端的个人中心等位置为普通用户提供一个清晰的申请成为分销商的入口选品中心与推广当用户成为分销商后他的个人中心就会出现分销中心的模块在分销中心里他可以浏览所有可供分销的商品在商品详情页上会有专属于他的自购省钱和分享赚钱按钮点击分享赚钱系统会自动为他生成带有专属推广二维码的精美海报收益与提现这是分销商最关心的模块我设计的这个页面必须清晰地展示他的今日收益累计收益等核心数据并提供一个醒目的提现入口团队管理为了鼓励裂变我还需要为分销商设计一个简单的我的团队管理功能在这里他可以获取专属的邀请链接海报用于发展自己的下线团队并查看团队的业绩概况本章总结在本章我们系统性地学习了分销电商这种独特的商业模式背景与优势我们理解了它通过社交裂变来降低获客成本提升转化率的核心价值搭建思路我们明确了搭建分销体系需要从分销员商品佣金这三大支柱入手产品设计我们分别为平台商家分销商这三方设计了支撑其业务运转所必需的核心功能",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-25 11:05:48",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E7%AB%A0%EF%BC%9A%E5%88%86%E9%94%80%E7%94%B5%E5%95%86"><span class="toc-text">第十章：分销电商</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#10-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">10.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-2-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E9%A1%B9%E7%9B%AE%E8%83%8C%E6%99%AF"><span class="toc-text">10.2 分销电商项目背景</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E5%81%9A%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%EF%BC%9F"><span class="toc-text">1. 为什么要做分销电商？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E7%9A%84%E6%A0%B8%E5%BF%83%E9%9C%80%E6%B1%82"><span class="toc-text">2. 分销电商的核心需求</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-1-%E4%BB%80%E4%B9%88%E6%98%AF%E5%88%86%E9%94%80%E7%94%B5%E5%95%86"><span class="toc-text">10.2.1 什么是分销电商</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-2-%E6%A0%B8%E5%BF%83%E8%A7%92%E8%89%B2%E5%AE%9A%E4%B9%89%EF%BC%88%E4%BE%9B%E8%B4%A7%E5%95%86%E3%80%81%E5%88%86%E9%94%80%E5%95%86%E3%80%81%E6%B6%88%E8%B4%B9%E8%80%85%EF%BC%89"><span class="toc-text">10.2.2 核心角色定义（供货商、分销商、消费者）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-3-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E7%9A%84%E4%BC%98%E5%8A%BF"><span class="toc-text">10.3 分销电商的优势</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-1-%E4%BD%8E%E6%88%90%E6%9C%AC%E5%BF%AB%E9%80%9F%E8%A3%82%E5%8F%98"><span class="toc-text">10.3.1 低成本快速裂变</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-2-%E5%BC%BA%E4%BF%A1%E4%BB%BB%E5%85%B3%E7%B3%BB%E8%BD%AC%E5%8C%96"><span class="toc-text">10.3.2 强信任关系转化</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-3-%E8%BD%BB%E8%B5%84%E4%BA%A7%E8%BF%90%E8%90%A5"><span class="toc-text">10.3.3 轻资产运营</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-4-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E6%90%AD%E5%BB%BA%E6%80%9D%E8%B7%AF"><span class="toc-text">10.4 分销电商搭建思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-1-%E5%88%86%E9%94%80%E5%91%98%E4%BD%93%E7%B3%BB%E8%AE%BE%E8%AE%A1"><span class="toc-text">10.4.1 分销员体系设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-2-%E5%95%86%E5%93%81%E4%B8%8E%E4%BE%9B%E5%BA%94%E9%93%BE%E7%AE%A1%E7%90%86"><span class="toc-text">10.4.2 商品与供应链管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-3-%E4%BD%A3%E9%87%91%E4%B8%8E%E7%BB%93%E7%AE%97%E4%BD%93%E7%B3%BB"><span class="toc-text">10.4.3 佣金与结算体系</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-5-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">10.5 分销电商产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-1-%E5%B9%B3%E5%8F%B0%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD"><span class="toc-text">10.5.1 平台端核心功能</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-2-%E5%95%86%E5%AE%B6%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD"><span class="toc-text">10.5.2 商家端核心功能</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-3-%E5%88%86%E9%94%80%E5%95%86%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD"><span class="toc-text">10.5.3 分销商端核心功能</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-6-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-text">10.6 本章总结</span></a></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理进阶（十）：第十章：分销电商</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-24T17:13:45.000Z" title="发表于 2025-07-25 01:13:45">2025-07-25</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.665Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">3.9k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>11分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理进阶（十）：第十章：分销电商"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/27703.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/27703.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理进阶（十）：第十章：分销电商</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-24T17:13:45.000Z" title="发表于 2025-07-25 01:13:45">2025-07-25</time><time itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.665Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></header><div id="postchat_postcontent"><h1 id="第十章：分销电商"><a href="#第十章：分销电商" class="headerlink" title="第十章：分销电商"></a>第十章：分销电商</h1><p>欢迎来到第十章。在前面的章节中，我们已经完整地学习了，如何设计一个“人、货、场”模型下的平台型电商。现在，我们将探讨一种能为平台带来强大“<strong>裂变增长</strong>”能力的、建立在<strong>社交关系链</strong>之上的高级模式——<strong>分销电商</strong>。</p><h2 id="10-1-学习目标"><a href="#10-1-学习目标" class="headerlink" title="10.1 学习目标"></a>10.1 学习目标</h2><p>在本章中，我的核心目标是，带大家系统性地掌握分销电商的业务模式与产品设计。我们将从项目背景出发，理解分销电商的定义和核心角色，并最终学会如何为这个模式，设计其独特的产品功能。</p><h2 id="10-2-分销电商项目背景"><a href="#10-2-分销电商项目背景" class="headerlink" title="10.2 分销电商项目背景"></a>10.2 分销电商项目背景</h2><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724104514357.png" alt="image-20250724104514357"></p><h3 id="1-为什么要做分销电商？"><a href="#1-为什么要做分销电商？" class="headerlink" title="1. 为什么要做分销电商？"></a>1. 为什么要做分销电商？</h3><p>我之所以要考虑在我们的电商产品中，融入分销模式，其核心的驱动力，是为了解决传统电商模式“<strong>获客成本越来越高</strong>”的瓶颈。</p><p>分销电商，本质上是一种**S2B2C (Supply chain to Business to Customer)**的模式。它通过一种“<strong>利益共享</strong>”的机制，将我们平台上的海量“<strong>C端用户</strong>”，转化为成千上万的“<strong>小B（分销商）</strong>”，让他们利用自己的私域流量和社交信任，去为我们获取更多的新用户。</p><h3 id="2-分销电商的核心需求"><a href="#2-分销电商的核心需求" class="headerlink" title="2. 分销电商的核心需求"></a>2. 分销电商的核心需求</h3><p>基于这个背景，我提炼出的、搭建分销系统的核心产品需求如下：</p><ol><li><strong>用户可以申请成为平台的分销商</strong>。</li><li><strong>商家有权利自定义自己店铺的商品，是否允许分销</strong>。</li><li><strong>分销商可以发展自己的下线</strong>，但为了确保业务合规，<strong>层级不能超过两级</strong>。</li></ol><h3 id="10-2-1-什么是分销电商"><a href="#10-2-1-什么是分销电商" class="headerlink" title="10.2.1 什么是分销电商"></a>10.2.1 什么是分销电商</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724113646666.png" alt="image-20250724113646666"></p><p>我给<strong>分销电商</strong>的定义是：<strong>一个通过设置“销售提成”作为激励，驱动平台用户（即分销商），利用其自有的“社交关系”进行商品分享和销售裂变，并最终达成“自购省钱，分享赚钱”目的的商业模式。</strong></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724113704425.png" alt="image-20250724113704425"></p><p>正如案例所示，分销最常见的形态，就是用户将一个带有自己专属二维码或链接的商品海报，分享到微信群或朋友圈。当他的好友通过这个链接完成购买后，他就能获得平台支付的相应比例的佣金。</p><p>在这个模式中，我们平台，需要为分销商解决好除了“销售”以外的一切后顾之忧，即<strong>统一提供货源、仓储、配送和售后服务</strong>。</p><h3 id="10-2-2-核心角色定义（供货商、分销商、消费者）"><a href="#10-2-2-核心角色定义（供货商、分销商、消费者）" class="headerlink" title="10.2.2 核心角色定义（供货商、分销商、消费者）"></a>10.2.2 核心角色定义（供货商、分销商、消费者）</h3><p>我设计分销系统，需要清晰地定义出这个新生态中的三个核心角色：</p><table><thead><tr><th align="left"><strong>核心角色</strong></th><th align="left"><strong>我的定义与解读</strong></th></tr></thead><tbody><tr><td align="left"><strong>供货商 (Supplier)</strong></td><td align="left">这是“<strong>货</strong>”的来源。他们可以是<strong>我们平台自营</strong>的商品，也可以是我们平台上<strong>参与了分销活动的第三方商家</strong>。他们的核心诉求，是<strong>提升商品销量</strong>。</td></tr><tr><td align="left"><strong>分销商 (Distributor)</strong></td><td align="left">这是我们这个模式中，<strong>新增的核心角色</strong>。他们是平台的普通用户，在申请成为分销商后，就拥有了“<strong>带货</strong>”的资格。他们<strong>不拥有商品、不处理订单、不负责发货</strong>，他们唯一的工作，就是<strong>分享和推广</strong>。他们的核心诉-求，是<strong>赚取佣金</strong>。</td></tr><tr><td align="left"><strong>消费者 (Consumer)</strong></td><td align="left">这是最终完成购买的<strong>终端用户</strong>。他们通常是某个分销商的<strong>好友或粉丝</strong>。他们的购买决策，很大程度上是建立在对分销商的<strong>信任</strong>之上。</td></tr></tbody></table><hr><h2 id="10-3-分销电商的优势"><a href="#10-3-分销电商的优势" class="headerlink" title="10.3 分销电商的优势"></a>10.3 分销电商的优势</h2><p>我们已经清楚了分销电商的定义和核心角色。现在，我需要回答一个关键的商业问题：<strong>作为一个产品或业务的决策者，我为什么要选择分销这种模式？</strong></p><p>答案在于，一个设计良好的分销体系，能为我们带来传统电商模式，难以企及的三大核心优势。</p><h3 id="10-3-1-低成本快速裂变"><a href="#10-3-1-低成本快速裂变" class="headerlink" title="10.3.1 低成本快速裂变"></a>10.3.1 低成本快速裂变</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724132513350.png" alt="image-20250724132513350"></p><p>在我看来，分销模式最强大、最核心的优势，就是它解决了现代电商最头痛的问题——<strong>高昂的获客成本</strong>。</p><ul><li><strong>传统模式的困境</strong>：传统的电商平台，需要花费巨额的市场预算，去购买流量、投放广告，来吸引用户。</li><li><strong>分销模式的破局</strong>：分销模式，本质上是将我们的营销预算，<strong>从“购买流量”，变为了“奖励用户”</strong>。我不再花钱给广告平台，而是把这部分钱，以“<strong>销售佣金</strong>”的形式，直接分给了帮我们带来客户的分销商。</li></ul><p>这相当于，我们<strong>将每一个分销商，都发展成了我们“行走的广告牌”和“销售渠道”</strong>。他们利用自己的社交关系链，进行“一带十、十带百”的<strong>裂变式传播</strong>。正如云集的案例数据显示，其“<strong>单个用户维系成本</strong>”，显著低于阿里、京东等传统流量驱动的电商平台。这就是裂变带来的低成本优势。</p><h3 id="10-3-2-强信任关系转化"><a href="#10-3-2-强信任关系转化" class="headerlink" title="10.3.2 强信任关系转化"></a>10.3.2 强信任关系转化</h3><p>分销模式的第二个巨大优势，是它能带来<strong>极高的销售转化率</strong>和<strong>用户忠诚度</strong>。</p><ul><li><strong>传统模式的挑战</strong>：用户面对一个冰冷的平台推送的广告，内心天然是带有“防备”和“不信任”的。</li><li><strong>分销模式的破解</strong>：分销模式的传播，是建立在“<strong>社交信任</strong>”的基础之上的。<strong>朋友的推荐，远比平台的广告，更具说服力。</strong></li></ul><p>当一个消费者，看到他朋友圈里，一位他所信任的好友或KOL，在真实地分享一款产品的使用心得时，他的购买决策链路会变得极短。这种基于信任的转化，效果是惊人的。云集案例中提到的“<strong>复购率达到93.6%</strong>”，就是这种强信任关系，带来高用户粘性的最好证明。</p><h3 id="10-3-3-轻资产运营"><a href="#10-3-3-轻资产运营" class="headerlink" title="10.3.3 轻资产运营"></a>10.3.3 轻资产运营</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724132621058.png" alt="image-20250724132621058"></p><p>分销模式的第三个优势，是它为“<strong>分销商</strong>”这个角色，提供了一种<strong>极具吸引力的“轻资产”运营</strong>模式。</p><p>我把它总结为“<strong>你只管卖，其他都交给我</strong>”。</p><table><thead><tr><th align="left"><strong>电商环节</strong></th><th align="left"><strong>由谁负责？</strong></th><th align="left"><strong>对分销商意味着什么？</strong></th></tr></thead><tbody><tr><td align="left"><strong>供货/选品</strong></td><td align="left"><strong>平台/供货商</strong></td><td align="left">分销商<strong>无需</strong>自己找货源</td></tr><tr><td align="left"><strong>仓储/库存</strong></td><td align="left"><strong>平台/供货商</strong></td><td align="left">分销商<strong>无需</strong>自己租仓库、压库存</td></tr><tr><td align="left"><strong>发货/物流</strong></td><td align="left"><strong>平台/供货商</strong></td><td align="left">分销商<strong>无需</strong>自己打包、发快递</td></tr><tr><td align="left"><strong>售后服务</strong></td><td align="left"><strong>平台/供货商</strong></td><td align="left">分销商<strong>无需</strong>自己处理复杂的退换货问题</td></tr><tr><td align="left"><strong>推广/销售</strong></td><td align="left"><strong>分销商</strong></td><td align="left"><strong>分销商只需要专注于他最擅长、最核心的一件事：分享和推广。</strong></td></tr></tbody></table><p>正是这种“轻资产”的模式，极大地降低了个人成为“小老板”的门槛，使得我们的分销商队伍，可以像滚雪球一样，快速地发展和壮大。</p><hr><h2 id="10-4-分销电商搭建思路"><a href="#10-4-分销电商搭建思路" class="headerlink" title="10.4 分销电商搭建思路"></a>10.4 分销电商搭建思路</h2><p>我们已经理解了分销电商的“是什么”和“为什么”。现在，我们就进入最核心的“<strong>怎么做</strong>”的环节。</p><p>要搭建一套完整的分销电商体系，我作为产品经理，需要从顶层，设计好<strong>三大核心支柱</strong>：</p><p><strong>分销员体系</strong>、<strong>商品与供应链体系</strong>、以及<strong>佣金与结算体系</strong>。这三大支柱，共同构成了我们分销业务的“骨架”。</p><h3 id="10-4-1-分销员体系设计"><a href="#10-4-1-分销员体系设计" class="headerlink" title="10.4.1 分销员体系设计"></a>10.4.1 分销员体系设计</h3><p>分销业务，核心是“<strong>人</strong>”的生意。因此，我首先要设计好，我们“<strong>分销员</strong>”这个核心角色的完整生命周期和组织结构。</p><p><strong>1. 角色与层级</strong></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724133059672.png" alt="image-20250724133059672"></p><p>为了激励分销员，为平台带来更多的“下线”分销员，我设计的体系，通常会包含“<strong>分销层级</strong>”。</p><ul><li><strong>核心逻辑</strong>：一个高级别的“一级分销商”，可以邀请新人，成为他的“二级分销商”。当“二级分销商”卖出商品时，“一级分销商”也能获得一部分的“团队奖励”。</li><li><strong>我的合规设计要点</strong>：我必须强调，为了确保业务的合法合规，在国内设计分销体系时，<strong>计佣（计算佣金）的层级，绝对不能超过三级</strong>。这是一个不可逾越的红线。</li></ul><p><strong>2. 核心流程</strong></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724133213227.png" alt="image-20250724133213227"></p><p>我设计的整个分销员体系，必须能够支撑起以下几个核心的业务流程：</p><ul><li><strong>成为分销商</strong>：普通用户，可以通过一个申请入口，提交申请，由平台审核通过后，获得“分销商”身份。</li><li><strong>分享商品</strong>：分销商可以在App内，选择商品，生成带有自己专属“推广码”的海报或链接，并分享出去。</li><li><strong>发展下线</strong>：分销商可以生成自己专属的“邀请码”，邀请好友来注册，成为自己的“下线”分销商。</li><li><strong>购买转化</strong>：当一个普通消费者，通过分销商分享的链接完成购买后，系统需要准确地记录下这笔订单的归属。</li></ul><h3 id="10-4-2-商品与供应链管理"><a href="#10-4-2-商品与供应链管理" class="headerlink" title="10.4.2 商品与供应链管理"></a>10.4.2 商品与供应链管理</h3><p>分销员只负责“推广”，而不负责“货”。因此，我必须在后台，设计好“<strong>货</strong>”的管理逻辑。</p><ul><li><strong>平台侧</strong>：在平台运营后台，我需要设计一个“<strong>总开关</strong>”，可以一键启用或关闭整个平台的分销功能。</li><li><strong>商家侧</strong>：在商家后台，我需要为商家，提供<strong>两级控制权</strong>：<ol><li><strong>店铺级开关</strong>：商家可以决定，自己整个店铺，是否参与平台的分销活动。</li><li><strong>商品级开关</strong>：在参与活动的前提下，商家还可以进一步地，去勾选“<strong>指定</strong>”的某些商品，来参与分销。</li></ol></li></ul><h3 id="10-4-3-佣金与结算体系"><a href="#10-4-3-佣金与结算体系" class="headerlink" title="10.4.3 佣金与结算体系"></a>10.4.3 佣金与结算体系</h3><p>这是驱动整个分销体系运转的“<strong>发动机</strong>”。我设计的佣金结算体系，必须<strong>公平、透明、准确</strong>。</p><ul><li><strong>佣金规则配置</strong>：我需要在平台运营后台，设计一个强大的“<strong>佣金规则引擎</strong>”。它需要支持运营同事，可以灵活地，按不同维度，来设置佣金比例。<ul><li><strong>按商品设置</strong>：不同的商品，可以有不同的佣-金比例。</li><li><strong>按分销商等级设置</strong>：高级别的分销商，可以享受更高的佣金比例。</li><li><strong>团队奖励设置</strong>：可以设置当下线分销商出单时，其上级可以获得的奖励比例。</li></ul></li><li><strong>结算与提现</strong>：当一笔通过分销链接产生的订单，<strong>完成交易</strong>（即，已过售后维权期）后，系统需要<strong>自动地</strong>，将计算好的佣金，打入对应分销商的“<strong>佣金账户</strong>”中。同时，我需要在分销商的专属后台，为他设计清晰的“<strong>收益报表</strong>”和便捷的“<strong>佣金提现</strong>”功能。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724133510027.png" alt="image-20250724133510027"></p><p>综上所述，我搭建分销电商的整体思路，就是围绕“<strong>人（分销员体系）</strong>”、“<strong>货（商品管理）</strong>”、“<strong>钱（佣金体系）</strong>”这三大核心，分别为<strong>用户端、商家端、平台端</strong>，设计出支撑其运转所必需的功能。</p><hr><h2 id="10-5-分销电商产品设计"><a href="#10-5-分销电商产品设计" class="headerlink" title="10.5 分销电商产品设计"></a>10.5 分销电商产品设计</h2><p>在我们明确了分销电商的搭建思路之后，现在，我们就进入具体的<strong>产品功能设计</strong>环节。我将严格按照<strong>平台端、商家端、分销商端</strong>这三个不同的使用者视角，来分别进行功能设计的拆解。</p><h3 id="10-5-1-平台端核心功能"><a href="#10-5-1-平台端核心功能" class="headerlink" title="10.5.1 平台端核心功能"></a>10.5.1 平台端核心功能</h3><p>这是整个分销系统的“<strong>总控制器</strong>”，由我们平台的运营人员使用，用来设定整个分销业务的“游戏规则”。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140309332.png" alt="image-20250724140309332"></p><ul><li><strong>分销规则配置</strong>：我设计的后台，必须有一个全局的“<strong>分销设置</strong>”页面。在这里，运营可以设置<code>是否开启分销</code>、<code>是否开启自购分佣</code>、<code>分销层级</code>（最多支持几级）、以及每一级的<code>抽佣比例</code>。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140324144.png" alt="image-20250724140324144"></p><ul><li><strong>分销员等级管理</strong>：为了激励分销商，我还会设计一个“<strong>分销等级</strong>”管理后台。运营可以在这里，创建不同的分销商等级（如：初级、中级、高级），并为每个等级，配置不同的<strong>邀请奖励</strong>和<strong>销售抽成</strong>比例，以及对应的<strong>升级规则</strong>。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140344850.png" alt="image-20250724140344850"></p><ul><li><strong>分销员审核管理</strong>：当有普通用户，申请成为分销商时，他们的申请会进入到这个后台的“<strong>待审核</strong>”列表中。运营人员可以在这里，查看申请人的信息，并进行“<strong>通过</strong>”或“<strong>驳回</strong>”的操作。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140428712.png" alt="image-20250724140428712"></p><ul><li><strong>订单与结算管理</strong>：我需要设计一个“<strong>分销订单</strong>”列表，让运营和财务，可以清晰地看到每一笔通过分销产生的订单，以及这笔订单需要为哪几级的分销商，分别计算多少佣金。同时，还需要“<strong>结算设置</strong>”和“<strong>提现管理</strong>”功能，来处理佣金的发放。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140527700.png" alt="image-20250724140527700"></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724141040118.png" alt="image-20250724141040118"></p><hr><h3 id="10-5-2-商家端核心功能"><a href="#10-5-2-商家端核心功能" class="headerlink" title="10.5.2 商家端核心功能"></a>10.5.2 商家端核心功能</h3><p>这是我们设计给“<strong>供货商</strong>”（即参与分销的商家）使用的后台，核心是让他们能够<strong>对自己店铺的分销业务，进行自主管理</strong>。</p><ul><li><strong>分销商品管理</strong>：在商家后台的“<strong>商品管理</strong>”模块，我需要为商家提供一个“<strong>分销商品设置</strong>”的功能。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140703094.png" alt="image-20250724140703094"><br>在这里，商家可以<strong>勾选</strong>自己店铺中，愿意拿出利润来进行分销的商品。并且，可以为这些商品，<strong>设定一个基础的佣金比例</strong>。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140729521.png" alt="image-20250724140729521"></p><ul><li><strong>分销业绩查看</strong>：我还需要为商家，提供一个查看<strong>分销业绩</strong>的报表。在这里，他可以看到是<strong>哪些分销商</strong>，为他带来了<strong>哪些订单</strong>，让他可以直观地感受到分销为店铺带来的价值。</li></ul><h3 id="10-5-3-分销商端核心功能"><a href="#10-5-3-分销商端核心功能" class="headerlink" title="10.5.3 分销商端核心功能"></a>10.5.3 分销商端核心功能</h3><p>这是我们设计给“<strong>分销商</strong>”本人使用的“<strong>个人工作台</strong>”，它通常会内嵌在我们用户端App的“个人中心”里。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140758440.png" alt="image-20250724140758440"></p><ul><li><strong>申请成为分销商</strong>：首先，我需要在用户端的“个人中心”等位置，为普通用户，提供一个清晰的“<strong>申请成为分销商</strong>”的入口。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140808421.png" alt="image-20250724140808421"></p><ul><li><strong>选品中心与推广</strong>：当用户成为分销商后，他的个人中心，就会出现“<strong>分销中心</strong>”的模块。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140842614.png" alt="image-20250724140842614"><br>在分销中心里，他可以浏览所有可供分销的商品。在商品详情页上，会有专属于他的“<strong>自购省钱</strong>”和“<strong>分享赚钱</strong>”按钮。点击“分享赚钱”，系统会自动为他生成带有<strong>专属推广二维码</strong>的精美海报。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140918643.png" alt="image-20250724140918643"></p><ul><li><strong>收益与提现</strong>：这是分销商最关心的模块。我设计的这个页面，必须清晰地展示他的<code>今日收益</code>、<code>累计收益</code>等核心数据，并提供一个醒目的“<strong>提现</strong>”入口。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140943661.png" alt="image-20250724140943661"></p><ul><li><strong>团队管理</strong>：为了鼓励“裂变”，我还需要为分销商，设计一个简单的“<strong>我的团队</strong>”管理功能。在这里，他可以获取专属的<strong>邀请链接/海报</strong>，用于发展自己的下线团队，并查看团队的业绩概况。</li></ul><h2 id="10-6-本章总结"><a href="#10-6-本章总结" class="headerlink" title="10.6 本章总结"></a>10.6 本章总结</h2><p>在本章，我们系统性地学习了“<strong>分销电商</strong>”这种独特的商业模式。</p><ul><li><strong>背景与优势</strong>：我们理解了它通过<strong>社交裂变</strong>，来<strong>降低获客成本</strong>、提升<strong>转化率</strong>的核心价值。</li><li><strong>搭建思路</strong>：我们明确了搭建分销体系，需要从<strong>分销员、商品、佣金</strong>这三大支柱入手。</li><li><strong>产品设计</strong>：我们分别为<strong>平台、商家、分销商</strong>这三方，设计了支撑其业务运转所必需的核心功能。</li></ul><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/27703.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/27703.html&quot;)">产品经理进阶（十）：第十章：分销电商</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/27703.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/27703.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/40087.html"><img class="prev-cover" src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理进阶（九）：第九章：电商后台 - 财务管理</div></div></a></div><div class="next-post pull-right"><a href="/posts/60138.html"><img class="next-cover" src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理进阶（十一）：第十一章：直播电商</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理进阶（十）：第十章：分销电商",date:"2025-07-25 01:13:45",updated:"2025-07-25 11:05:48",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第十章：分销电商\n\n欢迎来到第十章。在前面的章节中，我们已经完整地学习了，如何设计一个“人、货、场”模型下的平台型电商。现在，我们将探讨一种能为平台带来强大“**裂变增长**”能力的、建立在**社交关系链**之上的高级模式——**分销电商**。\n\n## 10.1 学习目标\n\n在本章中，我的核心目标是，带大家系统性地掌握分销电商的业务模式与产品设计。我们将从项目背景出发，理解分销电商的定义和核心角色，并最终学会如何为这个模式，设计其独特的产品功能。\n\n## 10.2 分销电商项目背景\n\n![image-20250724104514357](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724104514357.png)\n\n### 1. 为什么要做分销电商？\n\n我之所以要考虑在我们的电商产品中，融入分销模式，其核心的驱动力，是为了解决传统电商模式“**获客成本越来越高**”的瓶颈。\n\n分销电商，本质上是一种**S2B2C (Supply chain to Business to Customer)**的模式。它通过一种“**利益共享**”的机制，将我们平台上的海量“**C端用户**”，转化为成千上万的“**小B（分销商）**”，让他们利用自己的私域流量和社交信任，去为我们获取更多的新用户。\n\n### 2. 分销电商的核心需求\n\n基于这个背景，我提炼出的、搭建分销系统的核心产品需求如下：\n1.  **用户可以申请成为平台的分销商**。\n2.  **商家有权利自定义自己店铺的商品，是否允许分销**。\n3.  **分销商可以发展自己的下线**，但为了确保业务合规，**层级不能超过两级**。\n\n### 10.2.1 什么是分销电商\n\n![image-20250724113646666](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724113646666.png)\n\n我给**分销电商**的定义是：**一个通过设置“销售提成”作为激励，驱动平台用户（即分销商），利用其自有的“社交关系”进行商品分享和销售裂变，并最终达成“自购省钱，分享赚钱”目的的商业模式。**\n\n![image-20250724113704425](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724113704425.png)\n\n正如案例所示，分销最常见的形态，就是用户将一个带有自己专属二维码或链接的商品海报，分享到微信群或朋友圈。当他的好友通过这个链接完成购买后，他就能获得平台支付的相应比例的佣金。\n\n在这个模式中，我们平台，需要为分销商解决好除了“销售”以外的一切后顾之忧，即**统一提供货源、仓储、配送和售后服务**。\n\n### 10.2.2 核心角色定义（供货商、分销商、消费者）\n\n我设计分销系统，需要清晰地定义出这个新生态中的三个核心角色：\n\n| **核心角色** | **我的定义与解读** |\n| :--- | :--- |\n| **供货商 (Supplier)** | 这是“**货**”的来源。他们可以是**我们平台自营**的商品，也可以是我们平台上**参与了分销活动的第三方商家**。他们的核心诉求，是**提升商品销量**。 |\n| **分销商 (Distributor)**| 这是我们这个模式中，**新增的核心角色**。他们是平台的普通用户，在申请成为分销商后，就拥有了“**带货**”的资格。他们**不拥有商品、不处理订单、不负责发货**，他们唯一的工作，就是**分享和推广**。他们的核心诉-求，是**赚取佣金**。 |\n| **消费者 (Consumer)**| 这是最终完成购买的**终端用户**。他们通常是某个分销商的**好友或粉丝**。他们的购买决策，很大程度上是建立在对分销商的**信任**之上。 |\n\n\n\n---\n## 10.3 分销电商的优势\n\n我们已经清楚了分销电商的定义和核心角色。现在，我需要回答一个关键的商业问题：**作为一个产品或业务的决策者，我为什么要选择分销这种模式？**\n\n答案在于，一个设计良好的分销体系，能为我们带来传统电商模式，难以企及的三大核心优势。\n\n### 10.3.1 低成本快速裂变\n\n![image-20250724132513350](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724132513350.png)\n\n在我看来，分销模式最强大、最核心的优势，就是它解决了现代电商最头痛的问题——**高昂的获客成本**。\n\n* **传统模式的困境**：传统的电商平台，需要花费巨额的市场预算，去购买流量、投放广告，来吸引用户。\n* **分销模式的破局**：分销模式，本质上是将我们的营销预算，**从“购买流量”，变为了“奖励用户”**。我不再花钱给广告平台，而是把这部分钱，以“**销售佣金**”的形式，直接分给了帮我们带来客户的分销商。\n\n这相当于，我们**将每一个分销商，都发展成了我们“行走的广告牌”和“销售渠道”**。他们利用自己的社交关系链，进行“一带十、十带百”的**裂变式传播**。正如云集的案例数据显示，其“**单个用户维系成本**”，显著低于阿里、京东等传统流量驱动的电商平台。这就是裂变带来的低成本优势。\n\n### 10.3.2 强信任关系转化\n\n分销模式的第二个巨大优势，是它能带来**极高的销售转化率**和**用户忠诚度**。\n\n* **传统模式的挑战**：用户面对一个冰冷的平台推送的广告，内心天然是带有“防备”和“不信任”的。\n* **分销模式的破解**：分销模式的传播，是建立在“**社交信任**”的基础之上的。**朋友的推荐，远比平台的广告，更具说服力。**\n\n当一个消费者，看到他朋友圈里，一位他所信任的好友或KOL，在真实地分享一款产品的使用心得时，他的购买决策链路会变得极短。这种基于信任的转化，效果是惊人的。云集案例中提到的“**复购率达到93.6%**”，就是这种强信任关系，带来高用户粘性的最好证明。\n\n### 10.3.3 轻资产运营\n\n![image-20250724132621058](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724132621058.png)\n\n分销模式的第三个优势，是它为“**分销商**”这个角色，提供了一种**极具吸引力的“轻资产”运营**模式。\n\n我把它总结为“**你只管卖，其他都交给我**”。\n\n| **电商环节** | **由谁负责？** | **对分销商意味着什么？** |\n| :--- | :--- | :--- |\n| **供货/选品**| **平台/供货商** | 分销商**无需**自己找货源 |\n| **仓储/库存**| **平台/供货商** | 分销商**无需**自己租仓库、压库存 |\n| **发货/物流**| **平台/供货商** | 分销商**无需**自己打包、发快递 |\n| **售后服务**| **平台/供货商** | 分销商**无需**自己处理复杂的退换货问题 |\n| **推广/销售**| **分销商**| **分销商只需要专注于他最擅长、最核心的一件事：分享和推广。** |\n\n正是这种“轻资产”的模式，极大地降低了个人成为“小老板”的门槛，使得我们的分销商队伍，可以像滚雪球一样，快速地发展和壮大。\n\n\n\n\n---\n## 10.4 分销电商搭建思路\n\n我们已经理解了分销电商的“是什么”和“为什么”。现在，我们就进入最核心的“**怎么做**”的环节。\n\n要搭建一套完整的分销电商体系，我作为产品经理，需要从顶层，设计好**三大核心支柱**：\n\n**分销员体系**、**商品与供应链体系**、以及**佣金与结算体系**。这三大支柱，共同构成了我们分销业务的“骨架”。\n\n### 10.4.1 分销员体系设计\n\n分销业务，核心是“**人**”的生意。因此，我首先要设计好，我们“**分销员**”这个核心角色的完整生命周期和组织结构。\n\n**1. 角色与层级**\n\n![image-20250724133059672](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724133059672.png)\n\n为了激励分销员，为平台带来更多的“下线”分销员，我设计的体系，通常会包含“**分销层级**”。\n* **核心逻辑**：一个高级别的“一级分销商”，可以邀请新人，成为他的“二级分销商”。当“二级分销商”卖出商品时，“一级分销商”也能获得一部分的“团队奖励”。\n* **我的合规设计要点**：我必须强调，为了确保业务的合法合规，在国内设计分销体系时，**计佣（计算佣金）的层级，绝对不能超过三级**。这是一个不可逾越的红线。\n\n**2. 核心流程**\n\n![image-20250724133213227](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724133213227.png)\n\n我设计的整个分销员体系，必须能够支撑起以下几个核心的业务流程：\n* **成为分销商**：普通用户，可以通过一个申请入口，提交申请，由平台审核通过后，获得“分销商”身份。\n* **分享商品**：分销商可以在App内，选择商品，生成带有自己专属“推广码”的海报或链接，并分享出去。\n* **发展下线**：分销商可以生成自己专属的“邀请码”，邀请好友来注册，成为自己的“下线”分销商。\n* **购买转化**：当一个普通消费者，通过分销商分享的链接完成购买后，系统需要准确地记录下这笔订单的归属。\n\n### 10.4.2 商品与供应链管理\n\n分销员只负责“推广”，而不负责“货”。因此，我必须在后台，设计好“**货**”的管理逻辑。\n\n* **平台侧**：在平台运营后台，我需要设计一个“**总开关**”，可以一键启用或关闭整个平台的分销功能。\n* **商家侧**：在商家后台，我需要为商家，提供**两级控制权**：\n    1.  **店铺级开关**：商家可以决定，自己整个店铺，是否参与平台的分销活动。\n    2.  **商品级开关**：在参与活动的前提下，商家还可以进一步地，去勾选“**指定**”的某些商品，来参与分销。\n\n### 10.4.3 佣金与结算体系\n\n这是驱动整个分销体系运转的“**发动机**”。我设计的佣金结算体系，必须**公平、透明、准确**。\n\n* **佣金规则配置**：我需要在平台运营后台，设计一个强大的“**佣金规则引擎**”。它需要支持运营同事，可以灵活地，按不同维度，来设置佣金比例。\n    * **按商品设置**：不同的商品，可以有不同的佣-金比例。\n    * **按分销商等级设置**：高级别的分销商，可以享受更高的佣金比例。\n    * **团队奖励设置**：可以设置当下线分销商出单时，其上级可以获得的奖励比例。\n* **结算与提现**：当一笔通过分销链接产生的订单，**完成交易**（即，已过售后维权期）后，系统需要**自动地**，将计算好的佣金，打入对应分销商的“**佣金账户**”中。同时，我需要在分销商的专属后台，为他设计清晰的“**收益报表**”和便捷的“**佣金提现**”功能。\n\n![image-20250724133510027](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724133510027.png)\n\n综上所述，我搭建分销电商的整体思路，就是围绕“**人（分销员体系）**”、“**货（商品管理）**”、“**钱（佣金体系）**”这三大核心，分别为**用户端、商家端、平台端**，设计出支撑其运转所必需的功能。\n\n\n---\n## 10.5 分销电商产品设计\n\n在我们明确了分销电商的搭建思路之后，现在，我们就进入具体的**产品功能设计**环节。我将严格按照**平台端、商家端、分销商端**这三个不同的使用者视角，来分别进行功能设计的拆解。\n\n### 10.5.1 平台端核心功能\n\n这是整个分销系统的“**总控制器**”，由我们平台的运营人员使用，用来设定整个分销业务的“游戏规则”。\n\n![image-20250724140309332](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140309332.png)\n\n* **分销规则配置**：我设计的后台，必须有一个全局的“**分销设置**”页面。在这里，运营可以设置`是否开启分销`、`是否开启自购分佣`、`分销层级`（最多支持几级）、以及每一级的`抽佣比例`。\n\n![image-20250724140324144](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140324144.png)\n\n* **分销员等级管理**：为了激励分销商，我还会设计一个“**分销等级**”管理后台。运营可以在这里，创建不同的分销商等级（如：初级、中级、高级），并为每个等级，配置不同的**邀请奖励**和**销售抽成**比例，以及对应的**升级规则**。\n\n![image-20250724140344850](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140344850.png)\n\n* **分销员审核管理**：当有普通用户，申请成为分销商时，他们的申请会进入到这个后台的“**待审核**”列表中。运营人员可以在这里，查看申请人的信息，并进行“**通过**”或“**驳回**”的操作。\n\n![image-20250724140428712](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140428712.png)\n\n* **订单与结算管理**：我需要设计一个“**分销订单**”列表，让运营和财务，可以清晰地看到每一笔通过分销产生的订单，以及这笔订单需要为哪几级的分销商，分别计算多少佣金。同时，还需要“**结算设置**”和“**提现管理**”功能，来处理佣金的发放。\n\n![image-20250724140527700](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140527700.png)\n\n![image-20250724141040118](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724141040118.png)\n\n\n\n---\n\n### 10.5.2 商家端核心功能\n\n这是我们设计给“**供货商**”（即参与分销的商家）使用的后台，核心是让他们能够**对自己店铺的分销业务，进行自主管理**。\n\n* **分销商品管理**：在商家后台的“**商品管理**”模块，我需要为商家提供一个“**分销商品设置**”的功能。\n\n![image-20250724140703094](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140703094.png)\n在这里，商家可以**勾选**自己店铺中，愿意拿出利润来进行分销的商品。并且，可以为这些商品，**设定一个基础的佣金比例**。\n\n![image-20250724140729521](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140729521.png)\n\n* **分销业绩查看**：我还需要为商家，提供一个查看**分销业绩**的报表。在这里，他可以看到是**哪些分销商**，为他带来了**哪些订单**，让他可以直观地感受到分销为店铺带来的价值。\n\n### 10.5.3 分销商端核心功能\n\n这是我们设计给“**分销商**”本人使用的“**个人工作台**”，它通常会内嵌在我们用户端App的“个人中心”里。\n\n![image-20250724140758440](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140758440.png)\n\n* **申请成为分销商**：首先，我需要在用户端的“个人中心”等位置，为普通用户，提供一个清晰的“**申请成为分销商**”的入口。\n\n![image-20250724140808421](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140808421.png)\n\n* **选品中心与推广**：当用户成为分销商后，他的个人中心，就会出现“**分销中心**”的模块。\n\n![image-20250724140842614](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140842614.png)\n在分销中心里，他可以浏览所有可供分销的商品。在商品详情页上，会有专属于他的“**自购省钱**”和“**分享赚钱**”按钮。点击“分享赚钱”，系统会自动为他生成带有**专属推广二维码**的精美海报。\n\n![image-20250724140918643](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140918643.png)\n\n* **收益与提现**：这是分销商最关心的模块。我设计的这个页面，必须清晰地展示他的`今日收益`、`累计收益`等核心数据，并提供一个醒目的“**提现**”入口。\n\n![image-20250724140943661](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724140943661.png)\n\n* **团队管理**：为了鼓励“裂变”，我还需要为分销商，设计一个简单的“**我的团队**”管理功能。在这里，他可以获取专属的**邀请链接/海报**，用于发展自己的下线团队，并查看团队的业绩概况。\n\n## 10.6 本章总结\n\n在本章，我们系统性地学习了“**分销电商**”这种独特的商业模式。\n\n- **背景与优势**：我们理解了它通过**社交裂变**，来**降低获客成本**、提升**转化率**的核心价值。\n- **搭建思路**：我们明确了搭建分销体系，需要从**分销员、商品、佣金**这三大支柱入手。\n- **产品设计**：我们分别为**平台、商家、分销商**这三方，设计了支撑其业务运转所必需的核心功能。\n\n\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E7%AB%A0%EF%BC%9A%E5%88%86%E9%94%80%E7%94%B5%E5%95%86"><span class="toc-number">1.</span> <span class="toc-text">第十章：分销电商</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#10-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.1.</span> <span class="toc-text">10.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-2-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E9%A1%B9%E7%9B%AE%E8%83%8C%E6%99%AF"><span class="toc-number">1.2.</span> <span class="toc-text">10.2 分销电商项目背景</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E5%81%9A%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%EF%BC%9F"><span class="toc-number">1.2.1.</span> <span class="toc-text">1. 为什么要做分销电商？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E7%9A%84%E6%A0%B8%E5%BF%83%E9%9C%80%E6%B1%82"><span class="toc-number">1.2.2.</span> <span class="toc-text">2. 分销电商的核心需求</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-1-%E4%BB%80%E4%B9%88%E6%98%AF%E5%88%86%E9%94%80%E7%94%B5%E5%95%86"><span class="toc-number">1.2.3.</span> <span class="toc-text">10.2.1 什么是分销电商</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-2-%E6%A0%B8%E5%BF%83%E8%A7%92%E8%89%B2%E5%AE%9A%E4%B9%89%EF%BC%88%E4%BE%9B%E8%B4%A7%E5%95%86%E3%80%81%E5%88%86%E9%94%80%E5%95%86%E3%80%81%E6%B6%88%E8%B4%B9%E8%80%85%EF%BC%89"><span class="toc-number">1.2.4.</span> <span class="toc-text">10.2.2 核心角色定义（供货商、分销商、消费者）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-3-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E7%9A%84%E4%BC%98%E5%8A%BF"><span class="toc-number">1.3.</span> <span class="toc-text">10.3 分销电商的优势</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-1-%E4%BD%8E%E6%88%90%E6%9C%AC%E5%BF%AB%E9%80%9F%E8%A3%82%E5%8F%98"><span class="toc-number">1.3.1.</span> <span class="toc-text">10.3.1 低成本快速裂变</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-2-%E5%BC%BA%E4%BF%A1%E4%BB%BB%E5%85%B3%E7%B3%BB%E8%BD%AC%E5%8C%96"><span class="toc-number">1.3.2.</span> <span class="toc-text">10.3.2 强信任关系转化</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-3-%E8%BD%BB%E8%B5%84%E4%BA%A7%E8%BF%90%E8%90%A5"><span class="toc-number">1.3.3.</span> <span class="toc-text">10.3.3 轻资产运营</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-4-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E6%90%AD%E5%BB%BA%E6%80%9D%E8%B7%AF"><span class="toc-number">1.4.</span> <span class="toc-text">10.4 分销电商搭建思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-1-%E5%88%86%E9%94%80%E5%91%98%E4%BD%93%E7%B3%BB%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.4.1.</span> <span class="toc-text">10.4.1 分销员体系设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-2-%E5%95%86%E5%93%81%E4%B8%8E%E4%BE%9B%E5%BA%94%E9%93%BE%E7%AE%A1%E7%90%86"><span class="toc-number">1.4.2.</span> <span class="toc-text">10.4.2 商品与供应链管理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-3-%E4%BD%A3%E9%87%91%E4%B8%8E%E7%BB%93%E7%AE%97%E4%BD%93%E7%B3%BB"><span class="toc-number">1.4.3.</span> <span class="toc-text">10.4.3 佣金与结算体系</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-5-%E5%88%86%E9%94%80%E7%94%B5%E5%95%86%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.5.</span> <span class="toc-text">10.5 分销电商产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-1-%E5%B9%B3%E5%8F%B0%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD"><span class="toc-number">1.5.1.</span> <span class="toc-text">10.5.1 平台端核心功能</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-2-%E5%95%86%E5%AE%B6%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD"><span class="toc-number">1.5.2.</span> <span class="toc-text">10.5.2 商家端核心功能</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-3-%E5%88%86%E9%94%80%E5%95%86%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD"><span class="toc-number">1.5.3.</span> <span class="toc-text">10.5.3 分销商端核心功能</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#10-6-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-number">1.6.</span> <span class="toc-text">10.6 本章总结</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>