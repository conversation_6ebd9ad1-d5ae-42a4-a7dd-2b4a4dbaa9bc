<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理入门（四）：第四章：流程图与结构图 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理入门（四）：第四章：流程图与结构图"><meta name="application-name" content="产品经理入门（四）：第四章：流程图与结构图"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="产品经理入门（四）：第四章：流程图与结构图"><meta property="og:url" content="https://prorise666.site/posts/13237.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="第四章：流程图与结构图在我看来，如果说需求文档是用文字来描述“做什么”和“为什么做”，那么流程图和结构图就是我用来清晰、无歧义地表达“怎么做”的视觉语言。 它们是我与设计师、工程师、测试，甚至是老板和业务方进行高效沟通，确保大家对产品理解一致的最重要的工具。掌握这两种图的绘制，是我们产品经理的基本功"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta name="description" content="第四章：流程图与结构图在我看来，如果说需求文档是用文字来描述“做什么”和“为什么做”，那么流程图和结构图就是我用来清晰、无歧义地表达“怎么做”的视觉语言。 它们是我与设计师、工程师、测试，甚至是老板和业务方进行高效沟通，确保大家对产品理解一致的最重要的工具。掌握这两种图的绘制，是我们产品经理的基本功"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/13237.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"产品经理入门（四）：第四章：流程图与结构图",postAI:"true",pageFillDescription:"第四章：流程图与结构图, 4.1 认识流程图, 4.1.1 流程图的定义与分类, 1. 流程图的定义, 2. 常见流程图类型, 4.2 流程图的绘制, 4.2.1 流程图常见元素, 4.2.2 流程图常见结构, 1. 顺序结构, 2. 选择结构, 3. 循环结构, 4.2.3 流程图绘制工具, 4.2.4 流程图绘制思路与注意事项, 1. 绘制思路, 2. 绘制注意事项, 练习：绘制找工作流程图, 4.3 泳道图, 4.3.1 泳道图定义, 1. 跨职能（多角色）流程图, 2. 多角色协同与多阶段协同, 4.3.2 泳道图绘制思路, 案例解析：找工作泳道图, 4.4 结构图介绍, 4.4.1 结构图的定义与分类, 1. 结构图的定义, 2. 常见结构图类型, 4.5 结构图的绘制, 4.5.1 结构图绘制注意事项, 课习：拆解视频播放页面第四章流程图与结构图在我看来如果说需求文档是用文字来描述做什么和为什么做那么流程图和结构图就是我用来清晰无歧义地表达怎么做的视觉语言它们是我与设计师工程师测试甚至是老板和业务方进行高效沟通确保大家对产品理解一致的最重要的工具掌握这两种图的绘制是我们产品经理的基本功认识流程图我们先从流程图开始我用它来描述一个动态的过程即一系列随时间先后发生的动作和决策它回答的核心问题是接下来会发生什么流程图的定义与分类流程图的定义流程图对我而言就是一种将一个复杂的做事过程通过标准化的图形和箭头进行可视化表达的图示它的最大价值就是能把抽象的逻辑繁琐的步骤变得直观清晰让团队里的每一个人都能快速理解常见流程图类型在我的日常工作中根据我要沟通的对象和目的不同我会绘制三种不同类型的流程图混淆它们常常是新手产品经理犯的错误业务流程图我用它来描述一个完整的端到端的业务场景特别是当这个场景涉及到多个角色或系统交互时它聚焦的是业务活动本身而不是产品内部的具体功能图中的医院挂号案例就是绝佳的示范它清晰地展示了病人医院服务的流程在项目初期我会用这种图来和老板业务方统一对整个商业模式的认知功能流程图我用它来详细说明某一个具体功能内部的严谨的逻辑它的粒度比业务流程图要细得多我们来看这张在线挂号的流程图它就是一个完美的例子它描述的是挂号这单个功能内部的完整逻辑从选择科室开始到系统进行判断当天是否已约满再到用户选择具体时间确认就诊人最后系统再次判断是否符合科室要求直到最终预约成功或提示约满它把所有可能的情况和分支都严谨地表达了出来我就是用这种图来和开发测试工程师沟通一个功能的具体实现规则确保没有遗漏任何用户场景和异常情况页面流程图我用它来表达用户在产品不同界面之间的跳转路径它关注的是用户为了完成一个任务需要从哪个页面流转到哪个页面图中的从首页到搜索结果页再到商品详情页的流程就是一个典型的页面流程图我用它和设计师合作来保证整个产品的导航体验是顺畅无断点的确保用户不会在我们的产品里迷路为了方便我们记忆和区分我将这三种流程图的核心特点总结在了一张表格里流程图类型核心描述我用它来回答什么问题主要沟通对象业务流程图描述完整的商业活动涉及多角色系统我们的整体业务是如何运转的老板业务方运营功能流程图描述单个功能的内部逻辑和异常处理这个功能内部是如何工作的开发测试工程师页面流程图描述用户在不同界面间的跳转路径用户为了完成任务需要经过哪些页面设计师开发工程师流程图的绘制对我来说画流程图就像在用一种通用的视觉语言写作要写好我们得先掌握它的基本词汇元素和核心句型结构流程图常见元素为了让流程图具有通用性我始终坚持使用一套标准化的符号这些符号就是构成流程图的词汇元素样式元素名称我的使用说明开始结束我用它来明确标识一个流程的起点和所有可能的终点一个流程只有一个开始但可以有多个结束节点处理这是最常用的符号代表一个具体的操作动作或状态比如用户输入密码系统保存数据判定代表一个需要做是否或多分支判断的地方菱形必须有至少两个出口对应不同的判断结果子流程当一个流程中的某个步骤本身又是一个复杂的流程时比如支付流程我用这个符号来表示可以避免主流程图过于臃肿连接线用来连接各个元素表示流程的走向箭头方向至关重要我有时还会在连接线上标注文字比如是或否流程图常见结构掌握了基本符号后我就用它们来组合成三种最基本的句型或结构几乎所有复杂的流程都可以通过这三种基本结构的嵌套和组合来表达顺序结构这是最简单的结构表示一组操作按照时间先后从上到下地依次执行中间没有任何分支或重复图中的发布新闻评论流程就是一个典型的顺序结构用户从浏览新闻到查看新闻详情再到发布评论整个过程是一条直线走到底的其中是否已登录是一个选择结构我们下面会讲选择结构这是用来表达判断和分支的结构当流程走到某一步需要根据不同情况走向不同路径时我就用它二元选择结构这就是一个简单的二选一逻辑流程在决策点上根据条件是或否走向两条不同的道路图中校验手机号的例子很清晰系统判断手机号是否符合规范如果是流程就继续往下走到获取验证码如果否流程就走另一条路回到输入手机号这一步让用户重新输入多元选择结构当一个决策点可能产生多于两个的分支时我就使用多元选择结构图中的用户选择登录方式就是一个很好的例子用户在这里可以做出三种选择分别走向手机号登录账号密码登录第三方登录这三条完全不同的并行的路径循环结构当流程中的某一个或几个步骤需要被重复执行直到某个条件满足为止时我就使用循环结构图中发送验证码的例子非常经典系统执行发送验证码操作然后进入判断是否发送成功如果否则执行重新发送然后流程线绕回去再次进入是否发送成功的判断这个发送判断重发的过程会一直循环直到是否发送成功的判断结果为是流程才会跳出这个循环继续执行下一步输入验证码流程图绘制工具此处放置流程图绘制工具的图片工欲善其事必先利其器虽然理论上用纸笔就能画流程图但在实际工作中我一定会使用专业的工具因为它们更高效更规范也便于修改和分享市面上的工具很多我将几款主流工具的特点总结在了下面的表格里工具名称核心特点我推荐的使用场景墨刀白板国产在线一体化平台集原型设计流程图于一体协作功能强大上手快强烈推荐新手使用尤其适合移动端产品团队需要快速产出原型并进行协作评审的场景功能强大的专业原型工具同时内置了流程图功能当你需要在一个工具里同时完成高保真原型和详细流程图的绘制时无缝衔接微软出品功能全面模板库强大非常标准化环境下需要绘制非常专业复杂的企业级流程图或网络拓扑图等平台专属界面精美交互体验流畅重度用户对绘图的视觉效果和体验有较高要求国产在线协作绘图工具专注于流程图思维导图等需要多人实时共同编辑一份流程图进行头脑风暴或在线评审的场景亿图图示国产跨平台软件内置海量模板和素材库希望快速套用模板高效产出多种类型图表的用户我的建议对于新手我通常推荐从墨刀这样的在线一体化工具开始因为它免费易用并且集成了我们产品经理最高频使用的多种功能协作起来也非常方便它的确太好用了流程图绘制思路与注意事项选好了工具接下来就是最重要的部分如何思考画图只是思考结果的表达图画得好不好本质上是思路清不清晰我总结了自己的一套四步思考法和五大注意事项绘制思路明确核心目的在动笔前我一定会先用一句话说清楚我画这张图是为了给谁看想说明白一件什么事比如是为了跟开发讲清楚一个功能的逻辑还是为了跟老板讲明白一个业务模式先想后画我从不直接在软件上拖拽图形我习惯先在草稿纸或白板上把关键节点和流程大致地勾勒出来想清楚了再用工具画这样效率最高也避免了在细节上反复修改先主线后支线我总是先把一个流程最理想最通畅的主干道画出来然后再回头去补充那些异常情况判断分支等小路这样能保证我的逻辑主线是清晰的多思考边界异常一个产品经理的价值很大程度上体现在对异常情况的考虑是否周全比如用户输错密码怎么办网络断了怎么办库存不足了怎么办我会尽可能地把这些边界和异常情况都考虑到我的流程图里绘制注意事项顺序排列尽量保持从上到下从左到右的统一流向避免连接线交叉混乱开头结尾一个完整的流程必须有明确的开始和结束符号我绝不允许画一个没有终点的流程是否闭环我要确保流程的每一个分支都有一个明确的去向最终都能导向一个结束节点或回到主流程不能出现断头路善用标注当图形本身无法完全说清楚逻辑时我会毫不犹豫地使用文字标注来补充说明清晰永远是第一位的化繁为简如果一个流程图变得过于巨大和复杂我会思考是否可以把它拆分成几个子流程来表达我们的目标是用最简洁的图说明白最复杂的事练习绘制找工作流程图现在我们来做一个练习请根据我们在图片中看到的找工作流程图案例亲手绘制几张图这个案例的流程如下先在各个招聘网站投简历公司的看到你的简历后初步评估如果符合岗位需求就邀请你去公司面试接到面试通知后你就去公司参加面试先由面试再由该岗位的产品经理给你初面上面两次面试都通过后会再约你谈薪资最后确认录用你就会给你发练习任务任务一绘制业务流程图请思考一下这个流程涉及到哪些核心角色比如求职者用人部门等请你画一张业务流程图清晰地表达出这些角色以及他们在整个求职过程中的主要交互和行为在画的这张图里所有的动作都放在了一条线上但实际上找工作这个业务至少涉及到三个角色求职者用人部门这里就是产品经理这三个角色在不同的时间点做着不同的事互相配合才完成了整个流程而我们画业务流程图的核心目的就是要清晰地展现这种跨角色的协作关系那么如何优化呢我推荐使用一种最经典的业务流程图泳道图您可以想象一个游泳池我们为求职者用人部门这三个角色分别划分出一条独立的泳道然后我们把现在画的这些步骤按照这个动作是谁做的放回到对应角色的泳道里泳道图在上一节的练习中我们提到了一个关键概念泳道图用它来优化我们画的业务流程图现在我们就来系统地学习一下这个我个人非常推崇的能清晰表达多角色协作关系的强大工具泳道图定义跨职能多角色流程图正如它的名字一样泳道图我把它就看作是带泳道的流程图它的官方定义是跨职能流程图它的核心价值在于它不仅能展示要做什么流程更能清晰地展示由谁来做角色部门多角色协同与多阶段协同在我的实践中泳道的划分方式主要有两种按角色部门划分这是我最常用的一种就像我们找工作案例中的求职者产品经理我用它来理清不同的人或团队之间的工作交接关系和职责边界按阶段划分有时一个流程会经历几个大的阶段比如需求阶段设计阶段开发阶段测试阶段我也可以用泳道来划分这几个阶段清晰地展示任务在不同阶段的流转不过在日常工作中我们绝大多数时候都是按角色划分泳道图绘制思路绘制泳道图我的思路比画普通流程图会多几个规划步骤这能确保最终的图清晰准确明确目标对象和画所有图一样第一步永远是明确我画这张图的目的我要说明的是一个什么样的流程梳理角色阶段这是泳道图独有的一步我会把这个流程中涉及到的所有参与方角色部门全部罗列出来这是构建泳道的基础划分归属我会把流程中的每一个动作节点明确地分配给上一步中罗列出的角色也就是回答这件事到底该归谁管这个问题对应绘制最后一步才是动手画我先画好垂直或水平的泳道然后把上一步中划分好归属的动作节点一个一个放到各自的泳道里再用流程线将它们连接起来案例解析找工作泳道图理论说完了我们直接来看上一节练习的标准答案找工作泳道图案例这张图完美地诠释了泳道图的绘制思路和价值第一步梳理角色我们看到这张图清晰地定义了四个泳道也就是四个核心角色求职者产品经理产品总监第二步划分归属并绘制我们跟着流程线走一遍就能清晰地看到动作和角色的对应关系流程从求职者泳道的投递简历开始箭头跨越泳道流向泳道的查看简历和系统初筛如果通过流程继续在泳道里走到邀请面试然后再次跨越泳道信息流转回求职者的接收信息后续的初面由产品经理负责复面由产品总监负责最后的薪资沟通又回到了这里我的洞察通过这张图我不仅知道了找工作的完整步骤更重要的是我能一眼看清在每个环节我应该去找谁谁是负责人以及信息和任务是如何在不同角色之间流转交接的这种对职责和协作的清晰表达是普通流程图无法给予的这就是泳道图的威力所在也是为什么它在表达复杂业务流程时是我最重要的工具结构图介绍我们已经掌握了用来描述动态过程的流程图现在我们来学习与它互补的用来描述静态组成的结构图如果说流程图是产品的电影剧本那么结构图就是产品的骨骼光片或解剖图它不关心先后顺序只关心这个东西是由哪些部分构成的结构图的定义与分类结构图的定义我给结构图的定义是一种通过树状或脑图等形式来表达产品功能或信息层级关系的可视化图表它的核心作用就是帮助我把一个复杂混沌的整体拆解成一个个清晰有序有归属的部分常见结构图类型就像流程图一样根据我拆解的对象不同我主要会用到三种结构图功能结构图当我需要梳理一个产品或模块有哪些功能时我就会画功能结构图它是一个从抽象到具体的功能拆解过程帮我梳理出完整的功能清单我们来看挂号功能结构图这个案例它清晰地将挂号这个大功能拆解为返回首页搜索选择科室医院列表等子功能然后又把医院列表这个功能进一步拆解为筛选和查看医院这两个孙子级功能通过这张图我就能确保在设计时不会遗漏任何一个必要的功能点信息结构图当我需要梳理一个页面或模块要展示哪些信息时我就会画信息结构图它拆解的不是功能而是数据和信息在挂号信息结构图这个案例中我们看到它把医院列表这个模块拆解为它需要展示的封面图名称评分地区等级等信息字段这张图是我和设计师沟通界面内容以及和开发工程师沟通数据字段时的重要依据产品结构图产品结构图在我看来是功能结构图和信息结构图的集合体是产品最全面最宏观的一张鸟瞰地图我们看挂号产品结构图这个案例它既包含了搜索这样的功能模块也包含了科室金刚区查看这样的信息模块和界面元素它是我在进行原型设计之前用来组织整体产品框架的总设计图能帮我从全局视角思考产品每个部分的构成和关系结构图的绘制结构图绘制注意事项绘制结构图虽然比流程图要更自由一些但我依然会遵循一些基本原则来保证图表的清晰和易读层级数量我画结构图时会尽量把层级控制在层以内如果一个分支拆解得过深说明这个模块可能太复杂了我会考虑把它单独拎出来为它画一张新的更详细的结构图绘制方式我习惯用自顶向下逐层分解的方式来画先确定最顶层的核心主题然后拆分出第二层的主要构成再把第二层的每一项继续往下拆这样能保证逻辑的清晰和结构的完整顺序和流程图不同结构图同一层级的节点左右顺序并没有严格的规定我的原则是表达清楚即可有时我会把逻辑上更重要或更核心的模块放在左边或上边但这并不是硬性要求课习拆解视频播放页面现在我们来做一个结构图的练习请你想象一下我们正在设计一个类似于或的视频网站你的任务是对最重要的视频播放页面进行结构化拆解这个页面通常包含以下元素主视频播放器窗口视频标题主上传者信息头像昵称粉丝数订阅按钮点赞不喜欢分享下载收藏等互动按钮视频简介播放量发布日期等数据评论区包括评论输入框评论列表右侧的相关视频推荐列表练习任务任务绘制功能结构图请你画一张功能结构图来拆解这个页面上所有用户可以进行的操作从顶层的视频播放页功能开始往下拆解出例如播放器控制如播放暂停调节音量全屏视频互动如点赞收藏作者互动如订阅评论互动等几大功能模块并思考这些模块下还可以有哪些更细分的子功能",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-21 14:52:17",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%9B%9B%E7%AB%A0%EF%BC%9A%E6%B5%81%E7%A8%8B%E5%9B%BE%E4%B8%8E%E7%BB%93%E6%9E%84%E5%9B%BE"><span class="toc-text">第四章：流程图与结构图</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#4-1-%E8%AE%A4%E8%AF%86%E6%B5%81%E7%A8%8B%E5%9B%BE"><span class="toc-text">4.1 认识流程图</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-1-1-%E6%B5%81%E7%A8%8B%E5%9B%BE%E7%9A%84%E5%AE%9A%E4%B9%89%E4%B8%8E%E5%88%86%E7%B1%BB"><span class="toc-text">4.1.1 流程图的定义与分类</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%B5%81%E7%A8%8B%E5%9B%BE%E7%9A%84%E5%AE%9A%E4%B9%89"><span class="toc-text">1. 流程图的定义</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B8%B8%E8%A7%81%E6%B5%81%E7%A8%8B%E5%9B%BE%E7%B1%BB%E5%9E%8B"><span class="toc-text">2. 常见流程图类型</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-2-%E6%B5%81%E7%A8%8B%E5%9B%BE%E7%9A%84%E7%BB%98%E5%88%B6"><span class="toc-text">4.2 流程图的绘制</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-1-%E6%B5%81%E7%A8%8B%E5%9B%BE%E5%B8%B8%E8%A7%81%E5%85%83%E7%B4%A0"><span class="toc-text">4.2.1 流程图常见元素</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-2-%E6%B5%81%E7%A8%8B%E5%9B%BE%E5%B8%B8%E8%A7%81%E7%BB%93%E6%9E%84"><span class="toc-text">4.2.2 流程图常见结构</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%A1%BA%E5%BA%8F%E7%BB%93%E6%9E%84"><span class="toc-text">1. 顺序结构</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E9%80%89%E6%8B%A9%E7%BB%93%E6%9E%84"><span class="toc-text">2. 选择结构</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E5%BE%AA%E7%8E%AF%E7%BB%93%E6%9E%84"><span class="toc-text">3. 循环结构</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-3-%E6%B5%81%E7%A8%8B%E5%9B%BE%E7%BB%98%E5%88%B6%E5%B7%A5%E5%85%B7"><span class="toc-text">4.2.3 流程图绘制工具</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-4-%E6%B5%81%E7%A8%8B%E5%9B%BE%E7%BB%98%E5%88%B6%E6%80%9D%E8%B7%AF%E4%B8%8E%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9"><span class="toc-text">4.2.4 流程图绘制思路与注意事项</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%BB%98%E5%88%B6%E6%80%9D%E8%B7%AF"><span class="toc-text">1. 绘制思路</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%BB%98%E5%88%B6%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9"><span class="toc-text">2. 绘制注意事项</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%BB%83%E4%B9%A0%EF%BC%9A%E7%BB%98%E5%88%B6%E2%80%9C%E6%89%BE%E5%B7%A5%E4%BD%9C%E2%80%9D%E6%B5%81%E7%A8%8B%E5%9B%BE"><span class="toc-text">练习：绘制“找工作”流程图</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-3-%E6%B3%B3%E9%81%93%E5%9B%BE"><span class="toc-text">4.3 泳道图</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-1-%E6%B3%B3%E9%81%93%E5%9B%BE%E5%AE%9A%E4%B9%89"><span class="toc-text">4.3.1 泳道图定义</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%B7%A8%E8%81%8C%E8%83%BD%EF%BC%88%E5%A4%9A%E8%A7%92%E8%89%B2%EF%BC%89%E6%B5%81%E7%A8%8B%E5%9B%BE"><span class="toc-text">1. 跨职能（多角色）流程图</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%A4%9A%E8%A7%92%E8%89%B2%E5%8D%8F%E5%90%8C%E4%B8%8E%E5%A4%9A%E9%98%B6%E6%AE%B5%E5%8D%8F%E5%90%8C"><span class="toc-text">2. 多角色协同与多阶段协同</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-2-%E6%B3%B3%E9%81%93%E5%9B%BE%E7%BB%98%E5%88%B6%E6%80%9D%E8%B7%AF"><span class="toc-text">4.3.2 泳道图绘制思路</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%A1%88%E4%BE%8B%E8%A7%A3%E6%9E%90%EF%BC%9A%E6%89%BE%E5%B7%A5%E4%BD%9C%E6%B3%B3%E9%81%93%E5%9B%BE"><span class="toc-text">案例解析：找工作泳道图</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-4-%E7%BB%93%E6%9E%84%E5%9B%BE%E4%BB%8B%E7%BB%8D"><span class="toc-text">4.4 结构图介绍</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-1-%E7%BB%93%E6%9E%84%E5%9B%BE%E7%9A%84%E5%AE%9A%E4%B9%89%E4%B8%8E%E5%88%86%E7%B1%BB"><span class="toc-text">4.4.1 结构图的定义与分类</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%BB%93%E6%9E%84%E5%9B%BE%E7%9A%84%E5%AE%9A%E4%B9%89"><span class="toc-text">1. 结构图的定义</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B8%B8%E8%A7%81%E7%BB%93%E6%9E%84%E5%9B%BE%E7%B1%BB%E5%9E%8B"><span class="toc-text">2. 常见结构图类型</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-5-%E7%BB%93%E6%9E%84%E5%9B%BE%E7%9A%84%E7%BB%98%E5%88%B6"><span class="toc-text">4.5 结构图的绘制</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-5-1-%E7%BB%93%E6%9E%84%E5%9B%BE%E7%BB%98%E5%88%B6%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9"><span class="toc-text">4.5.1 结构图绘制注意事项</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E8%AF%BE%E4%B9%A0%EF%BC%9A%E6%8B%86%E8%A7%A3%E2%80%9C%E8%A7%86%E9%A2%91%E6%92%AD%E6%94%BE%E9%A1%B5%E9%9D%A2%E2%80%9D"><span class="toc-text">课习：拆解“视频播放页面”</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理入门（四）：第四章：流程图与结构图</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-20T11:13:45.000Z" title="发表于 2025-07-20 19:13:45">2025-07-20</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-21T06:52:17.471Z" title="更新于 2025-07-21 14:52:17">2025-07-21</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">5.9k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>17分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理入门（四）：第四章：流程图与结构图"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/13237.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/13237.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理入门（四）：第四章：流程图与结构图</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-20T11:13:45.000Z" title="发表于 2025-07-20 19:13:45">2025-07-20</time><time itemprop="dateCreated datePublished" datetime="2025-07-21T06:52:17.471Z" title="更新于 2025-07-21 14:52:17">2025-07-21</time></header><div id="postchat_postcontent"><h1 id="第四章：流程图与结构图"><a href="#第四章：流程图与结构图" class="headerlink" title="第四章：流程图与结构图"></a>第四章：流程图与结构图</h1><p>在我看来，如果说需求文档是用文字来描述“做什么”和“为什么做”，那么流程图和结构图就是我用来清晰、无歧义地表达“怎么做”的<strong>视觉语言</strong>。</p><p>它们是我与设计师、工程师、测试，甚至是老板和业务方进行高效沟通，确保大家对产品理解一致的最重要的工具。掌握这两种图的绘制，是我们产品经理的基本功。</p><h2 id="4-1-认识流程图"><a href="#4-1-认识流程图" class="headerlink" title="4.1 认识流程图"></a>4.1 认识流程图</h2><p>我们先从流程图开始。我用它来描述一个<strong>动态的过程</strong>，即一系列随时间先后发生的动作和决策。它回答的核心问题是：“接下来会发生什么？”。</p><h3 id="4-1-1-流程图的定义与分类"><a href="#4-1-1-流程图的定义与分类" class="headerlink" title="4.1.1 流程图的定义与分类"></a>4.1.1 流程图的定义与分类</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215002454.png" alt="image-20250719215002454"></p><h4 id="1-流程图的定义"><a href="#1-流程图的定义" class="headerlink" title="1. 流程图的定义"></a>1. 流程图的定义</h4><p><strong>流程图</strong>，对我而言，就是一种<strong>将一个复杂的做事过程，通过标准化的图形和箭头，进行可视化表达的图示</strong>。它的最大价值，就是能把抽象的逻辑、繁琐的步骤，变得直观、清晰，让团队里的每一个人都能快速理解。</p><h4 id="2-常见流程图类型"><a href="#2-常见流程图类型" class="headerlink" title="2. 常见流程图类型"></a>2. 常见流程图类型</h4><p>在我的日常工作中，根据我要沟通的对象和目的不同，我会绘制三种不同类型的流程图。混淆它们，常常是新手产品经理犯的错误。</p><ul><li><p><strong>业务流程图 (Business Flowchart)</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215053441.png" alt="image-20250719215053441"></p><p>我用它来描述一个<strong>完整的、端到端的业务场景</strong>，特别是当这个场景涉及到多个角色或系统交互时。它聚焦的是业务活动本身，而不是产品内部的具体功能。</p><p>图中的“医院挂号”案例就是绝佳的示范。它清晰地展示了“病人”、“医院服务”、的流程，在项目初期，我会用这种图来和老板、业务方统一对整个商业模式的认知。</p></li><li><p><strong>功能流程图 (Functional Flowchart)</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215234409.png" alt="image-20250719215234409"></p><p>我用它来详细说明<strong>某一个具体功能内部的、严谨的逻辑</strong>。它的粒度比业务流程图要细得多。</p><p>我们来看这张“在线挂号”的流程图，它就是一个完美的例子。它描述的是“挂号”这<strong>单个功能</strong>内部的完整逻辑。从“选择科室”开始，到系统进行判断“当天是否已约满”，再到用户选择具体时间、确认就诊人，最后系统再次判断“是否符合科室要求”，直到最终“预约成功”或“提示约满”。</p><p>它把所有可能的情况和分支都严谨地表达了出来。我就是用这种图，来和开发、测试工程师沟通一个功能的具体实现规则，确保没有遗漏任何用户场景和异常情况。</p></li><li><p><strong>页面流程图 (Page Flowchart)</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215727998.png" alt="image-20250719215727998"></p><p>我用它来表达<strong>用户在产品不同界面之间的跳转路径</strong>。它关注的是用户为了完成一个任务，需要“从哪个页面”流转到“哪个页面”。</p><p>图中的从“App首页”到“搜索结果页”再到“商品详情页”的流程，就是一个典型的页面流程图。我用它和UI/UX设计师合作，来保证整个产品的导航体验是顺畅、无断点的，确保用户不会在我们的产品里“迷路”。</p></li></ul><hr><p>为了方便我们记忆和区分，我将这三种流程图的核心特点总结在了一张表格里：</p><table><thead><tr><th align="left"><strong>流程图类型</strong></th><th align="left"><strong>核心描述</strong></th><th align="left"><strong>我用它来回答什么问题？</strong></th><th align="left"><strong>主要沟通对象</strong></th></tr></thead><tbody><tr><td align="left"><strong>业务流程图</strong></td><td align="left">描述完整的商业活动，涉及<strong>多角色/系统</strong>。</td><td align="left">“我们的整体业务是如何运转的？”</td><td align="left">老板、业务方、运营</td></tr><tr><td align="left"><strong>功能流程图</strong></td><td align="left">描述<strong>单个功能</strong>的内部逻辑和异常处理。</td><td align="left">“这个功能内部是如何工作的？”</td><td align="left">开发、测试工程师</td></tr><tr><td align="left"><strong>页面流程图</strong></td><td align="left">描述用户在<strong>不同界面</strong>间的跳转路径。</td><td align="left">“用户为了完成任务，需要经过哪些页面？”</td><td align="left">UI/UX设计师、开发工程师</td></tr></tbody></table><hr><h2 id="4-2-流程图的绘制"><a href="#4-2-流程图的绘制" class="headerlink" title="4.2 流程图的绘制"></a>4.2 流程图的绘制</h2><p>对我来说，画流程图就像在用一种通用的视觉语言写作。要写好，我们得先掌握它的“基本词汇”（元素）和“核心句型”（结构）。</p><h3 id="4-2-1-流程图常见元素"><a href="#4-2-1-流程图常见元素" class="headerlink" title="4.2.1 流程图常见元素"></a>4.2.1 流程图常见元素</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220137457.png" alt="image-20250719220137457"></p><p>为了让流程图具有通用性，我始终坚持使用一套标准化的符号。这些符号就是构成流程图的“词汇”。</p><table><thead><tr><th align="left"><strong>元素样式</strong></th><th align="left"><strong>元素名称</strong></th><th align="left"><strong>我的使用说明</strong></th></tr></thead><tbody><tr><td align="left"></td><td align="left"><strong>开始/结束</strong></td><td align="left">我用它来明确标识一个流程的<strong>起点</strong>和所有可能的<strong>终点</strong>。一个流程只有一个“开始”，但可以有多个“结束”。</td></tr><tr><td align="left"></td><td align="left"><strong>节点/处理</strong></td><td align="left">这是最常用的符号，代表一个具体的操作、动作或状态。比如“用户输入密码”、“系统保存数据”。</td></tr><tr><td align="left"></td><td align="left"><strong>判定</strong></td><td align="left">代表一个需要做“是/否”或多分支<strong>判断</strong>的地方。菱形必须有至少两个出口，对应不同的判断结果。</td></tr><tr><td align="left"></td><td align="left"><strong>子流程</strong></td><td align="left">当一个流程中的某个步骤本身又是一个复杂的流程时（比如“支付流程”），我用这个符号来表示，可以避免主流程图过于臃肿。</td></tr><tr><td align="left"></td><td align="left"><strong>连接线</strong></td><td align="left">用来连接各个元素，表示流程的<strong>走向</strong>。箭头方向至关重要，我有时还会在连接线上标注文字，比如“是”或“否”。</td></tr></tbody></table><h3 id="4-2-2-流程图常见结构"><a href="#4-2-2-流程图常见结构" class="headerlink" title="4.2.2 流程图常见结构"></a>4.2.2 流程图常见结构</h3><p>掌握了基本符号后，我就用它们来组合成三种最基本的“句型”或“结构”。几乎所有复杂的流程，都可以通过这三种基本结构的嵌套和组合来表达。</p><h4 id="1-顺序结构"><a href="#1-顺序结构" class="headerlink" title="1. 顺序结构"></a>1. 顺序结构</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220305242.png" alt="image-20250719220305242"></p><p>这是最简单的结构，表示一组操作<strong>按照时间先后、从上到下地依次执行</strong>，中间没有任何分支或重复。</p><p>图中的“发布新闻评论”流程就是一个典型的顺序结构。用户从<code>浏览新闻</code>，到<code>查看新闻详情</code>，再到<code>发布评论</code>，整个过程是一条直线走到底的（其中“是否已登录”是一个选择结构，我们下面会讲）。</p><h4 id="2-选择结构"><a href="#2-选择结构" class="headerlink" title="2. 选择结构"></a>2. 选择结构</h4><p>这是用来表达“判断”和“分支”的结构。当流程走到某一步需要根据不同情况，走向不同路径时，我就用它。</p><ul><li><p><strong>二元选择结构</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220457761.png" alt="image-20250719220457761"></p><p>这就是一个简单的“<strong>二选一</strong>”逻辑。流程在决策点上，根据条件“是”或“否”，走向两条不同的道路。</p><p>图中“校验手机号”的例子很清晰：系统判断<code>手机号是否符合规范？</code>。如果“是”，流程就继续往下走到<code>获取验证码</code>；如果“否”，流程就走另一条路，回到<code>输入手机号</code>这一步，让用户重新输入。</p></li><li><p><strong>多元选择结构</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220534751.png" alt="image-20250719220534751"></p><p>当一个决策点可能产生<strong>多于两个</strong>的分支时，我就使用多元选择结构。</p><p>图中的<code>用户选择登录方式</code>就是一个很好的例子。用户在这里可以做出三种选择，分别走向<code>手机号登录</code>、<code>账号密码登录</code>、<code>第三方登录</code>这三条完全不同的、并行的路径。</p></li></ul><h4 id="3-循环结构"><a href="#3-循环结构" class="headerlink" title="3. 循环结构"></a>3. 循环结构</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220609300.png" alt="image-20250719220609300"></p><p>当流程中的某一个或几个步骤，需要<strong>被重复执行</strong>，直到某个条件满足为止时，我就使用循环结构。</p><p>图中“发送验证码”的例子非常经典：</p><ol><li>系统执行<code>发送验证码</code>操作。</li><li>然后进入判断<code>是否发送成功？</code>。</li><li>如果“否”，则执行<code>重新发送</code>，然后<strong>流程线绕回去</strong>，再次进入<code>是否发送成功？</code>的判断。</li><li>这个“发送-判断-重发”的过程会一直循环，直到“是否发送成功？”的判断结果为“是”，流程才会跳出这个循环，继续执行下一步<code>输入验证码</code>。</li></ol><hr><h3 id="4-2-3-流程图绘制工具"><a href="#4-2-3-流程图绘制工具" class="headerlink" title="4.2.3 流程图绘制工具"></a>4.2.3 流程图绘制工具</h3><p>[此处放置“流程图绘制工具”的图片]</p><p>“工欲善其事，必先利其器”。虽然理论上用纸笔就能画流程图，但在实际工作中，我一定会使用专业的工具，因为它们更高效、更规范，也便于修改和分享。市面上的工具很多，我将几款主流工具的特点总结在了下面的表格里。</p><table><thead><tr><th align="left">工具名称</th><th align="left">核心特点</th><th align="left">我推荐的使用场景</th></tr></thead><tbody><tr><td align="left"><code>墨刀白板</code></td><td align="left">国产在线一体化平台，集原型、设计、流程图于一体，协作功能强大，上手快。</td><td align="left"><strong>强烈推荐新手使用</strong>。尤其适合移动端产品团队，需要快速产出原型并进行协作评审的场景。</td></tr><tr><td align="left"><strong>Axure RP 9</strong></td><td align="left">功能强大的专业原型工具，同时内置了流程图功能。</td><td align="left">当你需要在一个工具里，同时完成高保真原型和详细流程图的绘制时，无缝衔接。</td></tr><tr><td align="left"><strong>Visio</strong></td><td align="left">微软出品，功能全面，模板库强大，非常标准化。</td><td align="left">Windows环境下，需要绘制非常专业、复杂的企业级流程图或网络拓扑图等。</td></tr><tr><td align="left"><strong>OmniGraffle</strong></td><td align="left">Mac平台专属，界面精美，交互体验流畅。</td><td align="left">Mac重度用户，对绘图的视觉效果和体验有较高要求。</td></tr><tr><td align="left"><strong>ProcessOn</strong></td><td align="left">国产在线协作绘图工具，专注于流程图、思维导图等。</td><td align="left">需要多人实时共同编辑一份流程图，进行头脑风暴或在线评审的场景。</td></tr><tr><td align="left"><strong>EdrawMax (亿图图示)</strong></td><td align="left">国产跨平台软件，内置海量模板和素材库。</td><td align="left">希望快速套用模板，高效产出多种类型图表的用户。</td></tr></tbody></table><p><strong>我的建议：</strong><br>对于新手，我通常推荐从 <strong>墨刀 (MockingBot)</strong> 这样的在线一体化工具开始，因为它免费、易用，并且集成了我们产品经理最高频使用的多种功能，协作起来也非常方便。它的确太好用了。</p><h3 id="4-2-4-流程图绘制思路与注意事项"><a href="#4-2-4-流程图绘制思路与注意事项" class="headerlink" title="4.2.4 流程图绘制思路与注意事项"></a>4.2.4 流程图绘制思路与注意事项</h3><p>选好了工具，接下来就是最重要的部分——如何思考。画图只是思考结果的表达，图画得好不好，本质上是思路清不清晰。我总结了自己的一套“四步思考法”和“五大注意事项”。</p><h4 id="1-绘制思路"><a href="#1-绘制思路" class="headerlink" title="1. 绘制思路"></a>1. 绘制思路</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720080050483.png" alt="image-20250720080050483"></p><ul><li><strong>明确核心目的</strong>：在动笔前，我一定会先用一句话说清楚：我画这张图，是为了给谁看？想说明白一件什么事？比如，是为了跟开发讲清楚一个功能的逻辑，还是为了跟老板讲明白一个业务模式。</li><li><strong>先想后画</strong>：我从不直接在软件上拖拽图形。我习惯先在草稿纸或白板上，把关键节点和流程大致地勾勒出来，想清楚了再用工具画，这样效率最高，也避免了在细节上反复修改。</li><li><strong>先主线后支线</strong>：我总是先把一个流程最理想、最通畅的“主干道”画出来。然后再回头，去补充那些异常情况、判断分支等“小路”。这样能保证我的逻辑主线是清晰的。</li><li><strong>多思考边界异常</strong>：一个产品经理的价值，很大程度上体现在对异常情况的考虑是否周全。比如，用户输错密码怎么办？网络断了怎么办？库存不足了怎么办？我会尽可能地把这些边界和异常情况都考虑到我的流程图里。</li></ul><h4 id="2-绘制注意事项"><a href="#2-绘制注意事项" class="headerlink" title="2. 绘制注意事项"></a>2. 绘制注意事项</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720080153245.png" alt="image-20250720080153245"></p><ul><li><strong>顺序排列</strong>：尽量保持从上到下、从左到右的统一流向，避免连接线交叉、混乱。</li><li><strong>开头结尾</strong>：一个完整的流程必须有明确的“开始”和“结束”符号。我绝不允许画一个没有终点的流程。</li><li><strong>是否闭环</strong>：我要确保流程的每一个分支都有一个明确的去向，最终都能导向一个结束节点或回到主流程，不能出现“断头路”。</li><li><strong>善用标注</strong>：当图形本身无法完全说清楚逻辑时，我会毫不犹豫地使用文字标注来补充说明，清晰永远是第一位的。</li><li><strong>化繁为简</strong>：如果一个流程图变得过于巨大和复杂，我会思考是否可以把它拆分成几个子流程来表达。我们的目标是用最简洁的图，说明白最复杂的事。</li></ul><hr><h3 id="练习：绘制“找工作”流程图"><a href="#练习：绘制“找工作”流程图" class="headerlink" title="练习：绘制“找工作”流程图"></a>练习：绘制“找工作”流程图</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720080301030.png" alt="image-20250720080301030"></p><p>现在，我们来做一个练习。请根据我们在图片中看到的“找工作流程图”案例，亲手绘制几张图。这个案例的流程如下：<br>a. 先在各个招聘网站投简历<br>b. 公司的HR看到你的简历后，初步评估，如果符合岗位需求，就邀请你去公司面试<br>c. 接到面试通知后，你就去公司参加面试，先由HR面试，再由该岗位的产品经理给你初面<br>d. 上面两次面试都通过后，HR会再约你谈薪资，最后确认录用你，就会给你发offer</p><p><strong>【练习任务】</strong></p><ol><li><p><strong>任务一：绘制业务流程图</strong><br>请思考一下，这个流程涉及到哪些核心角色？（比如：求职者、HR、用人部门等）。</p><p>请你画一张<strong>业务流程图</strong>，清晰地表达出这些角色以及他们在整个求职过程中的主要交互和行为。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E6%9C%AA%E5%91%BD%E5%90%8D%E7%99%BD%E6%9D%BF.png" alt="未命名白板"></p></li></ol><p>在画的这张图里，所有的动作都放在了一条线上。但实际上，“找工作”这个业务，至少涉及到三个角色：</p><ul><li><strong>求职者</strong></li><li><strong>HR</strong></li><li><strong>用人部门</strong> (这里就是产品经理)</li></ul><p>这三个角色在不同的时间点，做着不同的事，互相配合才完成了整个流程。而我们画“业务流程图”的核心目的，就是要清晰地展现这种**“跨角色的协作关系”**。</p><p><strong>那么，如何优化呢？</strong></p><p>我推荐使用一种最经典的业务流程图——<strong>泳道图 (Swimlane Diagram)</strong>。</p><p>您可以想象一个游泳池，我们为“求职者”、“HR”、“用人部门”这三个角色，分别划分出一条独立的“泳道”。然后，我们把现在画的这些步骤，按照“<strong>这个动作是谁做的</strong>”，放回到对应角色的泳道里。</p><hr><h2 id="4-3-泳道图"><a href="#4-3-泳道图" class="headerlink" title="4.3 泳道图"></a>4.3 泳道图</h2><p>在上一节的练习中，我们提到了一个关键概念——<strong>泳道图 (Swimlane Diagram)</strong>，用它来优化我们画的业务流程图。</p><p>现在，我们就来系统地学习一下这个我个人非常推崇的、能清晰表达多角色协作关系的强大工具。</p><h3 id="4-3-1-泳道图定义"><a href="#4-3-1-泳道图定义" class="headerlink" title="4.3.1 泳道图定义"></a>4.3.1 泳道图定义</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720082100314.png" alt="image-20250720082100314"></p><h4 id="1-跨职能（多角色）流程图"><a href="#1-跨职能（多角色）流程图" class="headerlink" title="1. 跨职能（多角色）流程图"></a>1. 跨职能（多角色）流程图</h4><p>正如它的名字一样，泳道图，我把它就看作是带“泳道”的流程图。它的官方定义是<strong>跨职能流程图</strong>。</p><p>它的核心价值在于，它不仅能展示**“要做什么”（What/流程）<strong>，更能清晰地展示</strong>“由谁来做”（Who/角色/部门）**。</p><h4 id="2-多角色协同与多阶段协同"><a href="#2-多角色协同与多阶段协同" class="headerlink" title="2. 多角色协同与多阶段协同"></a>2. 多角色协同与多阶段协同</h4><p>在我的实践中，泳道的划分方式主要有两种：</p><ul><li><strong>按角色/部门划分</strong>：这是我最常用的一种。就像我们“找工作”案例中的’求职者’、’HR’、’产品经理’。我用它来理清不同的人或团队之间的工作交接关系和职责边界。</li><li><strong>按阶段划分</strong>：有时，一个流程会经历几个大的阶段，比如“需求阶段”、“设计阶段”、“开发阶段”、“测试阶段”。我也可以用泳道来划分这几个阶段，清晰地展示任务在不同阶段的流转。</li></ul><p>不过，在日常工作中，我们绝大多数时候都是<strong>按角色划分</strong>。</p><h3 id="4-3-2-泳道图绘制思路"><a href="#4-3-2-泳道图绘制思路" class="headerlink" title="4.3.2 泳道图绘制思路"></a>4.3.2 泳道图绘制思路</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720082152503.png" alt="image-20250720082152503"></p><p>绘制泳道图，我的思路比画普通流程图会多几个“规划”步骤，这能确保最终的图清晰、准确。</p><ol><li><strong>明确目标对象</strong>：和画所有图一样，第一步永远是明确我画这张图的目的。我要说明的是一个什么样的流程？</li><li><strong>梳理角色/阶段</strong>：这是泳道图独有的一步。我会把这个流程中涉及到的所有**参与方（角色/部门）**全部罗列出来。这是构建泳道的基础。</li><li><strong>划分归属</strong>：我会把流程中的每一个动作（节点），明确地分配给上一步中罗列出的角色。也就是回答“这件事，到底该归谁管？”这个问题。</li><li><strong>对应绘制</strong>：最后一步才是动手画。我先画好垂直或水平的泳道，然后把上一步中“划分好归属”的动作节点，一个一个放到各自的泳道里，再用流程线将它们连接起来。</li></ol><hr><h4 id="案例解析：找工作泳道图"><a href="#案例解析：找工作泳道图" class="headerlink" title="案例解析：找工作泳道图"></a><strong>案例解析：找工作泳道图</strong></h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/38bd1fda5226237bb446e314a9183d54.png" alt="img"></p><p>理论说完了，我们直接来看上一节练习的“标准答案”——<strong>找工作泳道图案例</strong>。这张图完美地诠释了泳道图的绘制思路和价值。</p><ol><li><p><strong>第一步：梳理角色。</strong><br>我们看到，这张图清晰地定义了四个泳道，也就是四个核心角色：<code>求职者</code>、<code>HR</code>、<code>产品经理</code>、<code>产品总监</code>。</p></li><li><p><strong>第二步：划分归属并绘制。</strong><br>我们跟着流程线走一遍，就能清晰地看到动作和角色的对应关系：</p><ul><li>流程从 <strong>求职者</strong> 泳道的 <code>投递简历</code> 开始。</li><li>箭头跨越泳道，流向 <strong>HR</strong> 泳道的 <code>查看简历</code> 和 <code>系统初筛</code>。</li><li>如果通过，流程继续在 <strong>HR</strong> 泳道里走到 <code>邀请面试</code>，然后再次跨越泳道，信息流转回 <strong>求职者</strong> 的 <code>接收信息</code>。</li><li>后续的 <code>初面</code> 由 <strong>产品经理</strong> 负责，<code>复面</code> 由 <strong>产品总监</strong> 负责，最后的 <code>薪资沟通</code> 又回到了 <strong>HR</strong> 这里。</li></ul></li></ol><p><strong>我的洞察：</strong><br>通过这张图，我不仅知道了找工作的完整步骤，更重要的是，我能一眼看清<strong>在每个环节，我应该去找谁，谁是负责人，以及信息和任务是如何在不同角色之间流转交接的</strong>。</p><p>这种对“职责”和“协作”的清晰表达，是普通流程图无法给予的。这就是泳道图的威力所在，也是为什么它在表达复杂业务流程时，是我最重要的工具。</p><hr><h2 id="4-4-结构图介绍"><a href="#4-4-结构图介绍" class="headerlink" title="4.4 结构图介绍"></a>4.4 结构图介绍</h2><p>我们已经掌握了用来描述**动态“过程”<strong>的流程图。现在，我们来学习与它互补的、用来描述</strong>静态“组成”**的结构图。</p><p>如果说流程图是产品的“电影剧本”，那么结构图就是产品的“骨骼X光片”或“解剖图”。它不关心先后顺序，只关心“<strong>这个东西，是由哪些部分构成的？</strong>”</p><h3 id="4-4-1-结构图的定义与分类"><a href="#4-4-1-结构图的定义与分类" class="headerlink" title="4.4.1 结构图的定义与分类"></a>4.4.1 结构图的定义与分类</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093047853.png" alt="image-20250720093047853"></p><h4 id="1-结构图的定义"><a href="#1-结构图的定义" class="headerlink" title="1. 结构图的定义"></a>1. 结构图的定义</h4><p>我给<strong>结构图</strong>的定义是：<strong>一种通过树状或脑图等形式，来表达产品、功能或信息层级关系的可视化图表。</strong><br>它的核心作用，就是帮助我把一个复杂、混沌的整体，拆解成一个个清晰、有序、有归属的部分。</p><h4 id="2-常见结构图类型"><a href="#2-常见结构图类型" class="headerlink" title="2. 常见结构图类型"></a>2. 常见结构图类型</h4><p>就像流程图一样，根据我拆解的对象不同，我主要会用到三种结构图。</p><ul><li><strong>功能结构图 (Functional Structure Diagram)</strong><br>当我需要梳理一个产品或模块**“有哪些功能”**时，我就会画功能结构图。它是一个从抽象到具体的功能拆解过程，帮我梳理出完整的功能清单（Function List）。<br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093116350.png" alt="image-20250720093116350"></li></ul><p>我们来看“挂号功能结构图”这个案例。它清晰地将“挂号”这个大功能，拆解为<code>返回首页</code>、<code>搜索</code>、<code>选择科室</code>、<code>医院列表</code>等子功能，然后又把<code>医院列表</code>这个功能，进一步拆解为<code>筛选</code>和<code>查看医院</code>这两个孙子级功能。通过这张图，我就能确保在设计时，不会遗漏任何一个必要的功能点。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093127402.png" alt="image-20250720093127402"></p><p>​</p><ul><li><strong>信息结构图 (Information Structure Diagram)</strong></li></ul><p>当我需要梳理一个页面或模块**“要展示哪些信息”**时，我就会画信息结构图。它拆解的不是“功能”，而是“数据和信息”。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093358095.png" alt="image-20250720093358095"></p><p>在“挂号信息结构图”这个案例中，我们看到，它把“医院列表”这个模块，拆解为它需要展示的<code>封面图</code>、<code>名称</code>、<code>评分</code>、<code>地区</code>、<code>等级</code>等信息字段。这张图是我和UI设计师沟通界面内容、以及和开发工程师沟通数据字段时的重要依据。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093411563.png" alt="image-20250720093411563"></p><ul><li><p><strong>产品结构图 (Product Structure Diagram)</strong><br>产品结构图，在我看来，是<strong>功能结构图和信息结构图的集合体</strong>，是产品最全面、最宏观的一张“鸟瞰地图”。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093505990.png" alt="image-20250720093505990"></p><p>我们看“挂号产品结构图”这个案例，它既包含了<code>搜索</code>这样的<strong>功能模块</strong>，也包含了<code>科室金刚区</code>、<code>查看Banner</code>这样的<strong>信息模块</strong>和<strong>界面元素</strong>。它是我在进行原型设计之前，用来组织整体产品框架的“总设计图”，能帮我从全局视角思考产品每个部分的构成和关系。</p></li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093515445.png" alt="image-20250720093515445"></p><hr><h2 id="4-5-结构图的绘制"><a href="#4-5-结构图的绘制" class="headerlink" title="4.5 结构图的绘制"></a>4.5 结构图的绘制</h2><h3 id="4-5-1-结构图绘制注意事项"><a href="#4-5-1-结构图绘制注意事项" class="headerlink" title="4.5.1 结构图绘制注意事项"></a>4.5.1 结构图绘制注意事项</h3><p>绘制结构图虽然比流程图要更自由一些，但我依然会遵循一些基本原则，来保证图表的清晰和易读。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093556927.png" alt="image-20250720093556927"></p><ol><li><p><strong>层级数量</strong><br>我画结构图时，会尽量把层级控制在<strong>3-4层</strong>以内。如果一个分支拆解得过深，说明这个模块可能太复杂了，我会考虑把它单独拎出来，为它画一张新的、更详细的结构图。</p></li><li><p><strong>绘制方式</strong><br>我习惯用“<strong>自顶向下，逐层分解</strong>”的方式来画。先确定最顶层的核心主题，然后拆分出第二层的主要构成，再把第二层的每一项继续往下拆，这样能保证逻辑的清晰和结构的完整。</p></li><li><p><strong>顺序</strong><br>和流程图不同，结构图同一层级的节点，左右顺序并没有严格的规定。我的原则是“<strong>表达清楚即可</strong>”，有时我会把逻辑上更重要或更核心的模块放在左边或上边，但这并不是硬性要求。</p></li></ol><hr><h3 id="课习：拆解“视频播放页面”"><a href="#课习：拆解“视频播放页面”" class="headerlink" title="课习：拆解“视频播放页面”"></a>课习：拆解“视频播放页面”</h3><p>现在，我们来做一个结构图的练习。请你想象一下，我们正在设计一个类似于YouTube或Bilibili的视频网站，你的任务是，对最重要的**“视频播放页面”**进行结构化拆解。</p><p>这个页面通常包含以下元素：</p><ul><li>主视频播放器窗口</li><li>视频标题、UP主（上传者）信息（头像、昵称、粉丝数）、订阅按钮</li><li>点赞、不喜欢、分享、下载、收藏等互动按钮</li><li>视频简介、播放量、发布日期等数据</li><li>评论区（包括评论输入框、评论列表）</li><li>右侧的相关视频推荐列表</li></ul><p><strong>【练习任务】</strong></p><ol><li><strong>任务：绘制功能结构图</strong><br>请你画一张<strong>功能结构图</strong>，来拆解这个页面上所有<strong>用户可以进行的操作</strong>。</li></ol><ul><li><p>从顶层的“视频播放页功能”开始，往下拆解出例如“播放器控制”（如：播放/暂停、调节音量、全屏）、“视频互动”（如：点赞、收藏）、“作者互动”（如：订阅）、“评论互动”等几大功能模块，并思考这些模块下还可以有哪些更细分的子功能。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E6%9C%AA%E5%91%BD%E5%90%8D%E7%99%BD%E6%9D%BF%20(3).png" alt="未命名白板 (3)"></p></li></ul><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/13237.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/13237.html&quot;)">产品经理入门（四）：第四章：流程图与结构图</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/13237.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/13237.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/59297.html"><img class="prev-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理入门（三）：第三章：需求分析</div></div></a></div><div class="next-post pull-right"><a href="/posts/23264.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理入门（四）：第四章：流程图与结构图",date:"2025-07-20 19:13:45",updated:"2025-07-21 14:52:17",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第四章：流程图与结构图\n\n在我看来，如果说需求文档是用文字来描述“做什么”和“为什么做”，那么流程图和结构图就是我用来清晰、无歧义地表达“怎么做”的**视觉语言**。\n\n它们是我与设计师、工程师、测试，甚至是老板和业务方进行高效沟通，确保大家对产品理解一致的最重要的工具。掌握这两种图的绘制，是我们产品经理的基本功。\n\n## 4.1 认识流程图\n\n我们先从流程图开始。我用它来描述一个**动态的过程**，即一系列随时间先后发生的动作和决策。它回答的核心问题是：“接下来会发生什么？”。\n\n### 4.1.1 流程图的定义与分类\n\n![image-20250719215002454](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215002454.png)\n\n#### 1. 流程图的定义\n\n**流程图**，对我而言，就是一种**将一个复杂的做事过程，通过标准化的图形和箭头，进行可视化表达的图示**。它的最大价值，就是能把抽象的逻辑、繁琐的步骤，变得直观、清晰，让团队里的每一个人都能快速理解。\n\n#### 2. 常见流程图类型\n\n在我的日常工作中，根据我要沟通的对象和目的不同，我会绘制三种不同类型的流程图。混淆它们，常常是新手产品经理犯的错误。\n\n* **业务流程图 (Business Flowchart)**\n    ![image-20250719215053441](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215053441.png)\n\n    我用它来描述一个**完整的、端到端的业务场景**，特别是当这个场景涉及到多个角色或系统交互时。它聚焦的是业务活动本身，而不是产品内部的具体功能。\n\n    图中的“医院挂号”案例就是绝佳的示范。它清晰地展示了“病人”、“医院服务”、的流程，在项目初期，我会用这种图来和老板、业务方统一对整个商业模式的认知。\n\n* **功能流程图 (Functional Flowchart)**\n    ![image-20250719215234409](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215234409.png)\n\n    我用它来详细说明**某一个具体功能内部的、严谨的逻辑**。它的粒度比业务流程图要细得多。\n\n    我们来看这张“在线挂号”的流程图，它就是一个完美的例子。它描述的是“挂号”这**单个功能**内部的完整逻辑。从“选择科室”开始，到系统进行判断“当天是否已约满”，再到用户选择具体时间、确认就诊人，最后系统再次判断“是否符合科室要求”，直到最终“预约成功”或“提示约满”。\n\n    它把所有可能的情况和分支都严谨地表达了出来。我就是用这种图，来和开发、测试工程师沟通一个功能的具体实现规则，确保没有遗漏任何用户场景和异常情况。\n    \n    \n    \n* **页面流程图 (Page Flowchart)**\n    ![image-20250719215727998](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719215727998.png)\n\n    我用它来表达**用户在产品不同界面之间的跳转路径**。它关注的是用户为了完成一个任务，需要“从哪个页面”流转到“哪个页面”。\n\n    图中的从“App首页”到“搜索结果页”再到“商品详情页”的流程，就是一个典型的页面流程图。我用它和UI/UX设计师合作，来保证整个产品的导航体验是顺畅、无断点的，确保用户不会在我们的产品里“迷路”。\n\n---\n为了方便我们记忆和区分，我将这三种流程图的核心特点总结在了一张表格里：\n\n| **流程图类型** | **核心描述** | **我用它来回答什么问题？** | **主要沟通对象** |\n| :--- | :--- | :--- | :--- |\n| **业务流程图** | 描述完整的商业活动，涉及**多角色/系统**。 | “我们的整体业务是如何运转的？” | 老板、业务方、运营 |\n| **功能流程图** | 描述**单个功能**的内部逻辑和异常处理。 | “这个功能内部是如何工作的？” | 开发、测试工程师 |\n| **页面流程图** | 描述用户在**不同界面**间的跳转路径。 | “用户为了完成任务，需要经过哪些页面？” | UI/UX设计师、开发工程师 |\n\n\n\n\n-----\n\n## 4.2 流程图的绘制\n\n对我来说，画流程图就像在用一种通用的视觉语言写作。要写好，我们得先掌握它的“基本词汇”（元素）和“核心句型”（结构）。\n\n### 4.2.1 流程图常见元素\n\n![image-20250719220137457](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220137457.png)\n\n为了让流程图具有通用性，我始终坚持使用一套标准化的符号。这些符号就是构成流程图的“词汇”。\n\n| **元素样式** | **元素名称** | **我的使用说明** |\n| :--- | :--- | :--- |\n|  | **开始/结束** | 我用它来明确标识一个流程的**起点**和所有可能的**终点**。一个流程只有一个“开始”，但可以有多个“结束”。 |\n|  | **节点/处理** | 这是最常用的符号，代表一个具体的操作、动作或状态。比如“用户输入密码”、“系统保存数据”。 |\n|  | **判定** | 代表一个需要做“是/否”或多分支**判断**的地方。菱形必须有至少两个出口，对应不同的判断结果。 |\n|  | **子流程** | 当一个流程中的某个步骤本身又是一个复杂的流程时（比如“支付流程”），我用这个符号来表示，可以避免主流程图过于臃肿。 |\n|  | **连接线** | 用来连接各个元素，表示流程的**走向**。箭头方向至关重要，我有时还会在连接线上标注文字，比如“是”或“否”。 |\n\n### 4.2.2 流程图常见结构\n\n掌握了基本符号后，我就用它们来组合成三种最基本的“句型”或“结构”。几乎所有复杂的流程，都可以通过这三种基本结构的嵌套和组合来表达。\n\n#### 1\\. 顺序结构\n\n![image-20250719220305242](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220305242.png)\n\n这是最简单的结构，表示一组操作**按照时间先后、从上到下地依次执行**，中间没有任何分支或重复。\n\n图中的“发布新闻评论”流程就是一个典型的顺序结构。用户从`浏览新闻`，到`查看新闻详情`，再到`发布评论`，整个过程是一条直线走到底的（其中“是否已登录”是一个选择结构，我们下面会讲）。\n\n#### 2\\. 选择结构\n\n这是用来表达“判断”和“分支”的结构。当流程走到某一步需要根据不同情况，走向不同路径时，我就用它。\n\n  * **二元选择结构**\n    ![image-20250719220457761](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220457761.png)\n\n    这就是一个简单的“**二选一**”逻辑。流程在决策点上，根据条件“是”或“否”，走向两条不同的道路。\n\n    图中“校验手机号”的例子很清晰：系统判断`手机号是否符合规范？`。如果“是”，流程就继续往下走到`获取验证码`；如果“否”，流程就走另一条路，回到`输入手机号`这一步，让用户重新输入。\n\n  * **多元选择结构**\n    ![image-20250719220534751](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220534751.png)\n\n    当一个决策点可能产生**多于两个**的分支时，我就使用多元选择结构。\n\n    图中的`用户选择登录方式`就是一个很好的例子。用户在这里可以做出三种选择，分别走向`手机号登录`、`账号密码登录`、`第三方登录`这三条完全不同的、并行的路径。\n\n#### 3\\. 循环结构\n\n![image-20250719220609300](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250719220609300.png)\n\n当流程中的某一个或几个步骤，需要**被重复执行**，直到某个条件满足为止时，我就使用循环结构。\n\n图中“发送验证码”的例子非常经典：\n\n1.  系统执行`发送验证码`操作。\n2.  然后进入判断`是否发送成功？`。\n3.  如果“否”，则执行`重新发送`，然后**流程线绕回去**，再次进入`是否发送成功？`的判断。\n4.  这个“发送-判断-重发”的过程会一直循环，直到“是否发送成功？”的判断结果为“是”，流程才会跳出这个循环，继续执行下一步`输入验证码`。\n\n\n---\n\n\n### 4.2.3 流程图绘制工具\n\n[此处放置“流程图绘制工具”的图片]\n\n“工欲善其事，必先利其器”。虽然理论上用纸笔就能画流程图，但在实际工作中，我一定会使用专业的工具，因为它们更高效、更规范，也便于修改和分享。市面上的工具很多，我将几款主流工具的特点总结在了下面的表格里。\n\n| 工具名称 | 核心特点 | 我推荐的使用场景 |\n| :--- | :--- | :--- |\n| `墨刀白板` | 国产在线一体化平台，集原型、设计、流程图于一体，协作功能强大，上手快。 | **强烈推荐新手使用**。尤其适合移动端产品团队，需要快速产出原型并进行协作评审的场景。 |\n| **Axure RP 9** | 功能强大的专业原型工具，同时内置了流程图功能。 | 当你需要在一个工具里，同时完成高保真原型和详细流程图的绘制时，无缝衔接。 |\n| **Visio** | 微软出品，功能全面，模板库强大，非常标准化。 | Windows环境下，需要绘制非常专业、复杂的企业级流程图或网络拓扑图等。 |\n| **OmniGraffle** | Mac平台专属，界面精美，交互体验流畅。 | Mac重度用户，对绘图的视觉效果和体验有较高要求。 |\n| **ProcessOn** | 国产在线协作绘图工具，专注于流程图、思维导图等。 | 需要多人实时共同编辑一份流程图，进行头脑风暴或在线评审的场景。 |\n| **EdrawMax (亿图图示)** | 国产跨平台软件，内置海量模板和素材库。 | 希望快速套用模板，高效产出多种类型图表的用户。 |\n\n**我的建议：**\n对于新手，我通常推荐从 **墨刀 (MockingBot)** 这样的在线一体化工具开始，因为它免费、易用，并且集成了我们产品经理最高频使用的多种功能，协作起来也非常方便。它的确太好用了。\n### 4.2.4 流程图绘制思路与注意事项\n\n选好了工具，接下来就是最重要的部分——如何思考。画图只是思考结果的表达，图画得好不好，本质上是思路清不清晰。我总结了自己的一套“四步思考法”和“五大注意事项”。\n\n#### 1. 绘制思路\n\n![image-20250720080050483](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720080050483.png)\n\n* **明确核心目的**：在动笔前，我一定会先用一句话说清楚：我画这张图，是为了给谁看？想说明白一件什么事？比如，是为了跟开发讲清楚一个功能的逻辑，还是为了跟老板讲明白一个业务模式。\n* **先想后画**：我从不直接在软件上拖拽图形。我习惯先在草稿纸或白板上，把关键节点和流程大致地勾勒出来，想清楚了再用工具画，这样效率最高，也避免了在细节上反复修改。\n* **先主线后支线**：我总是先把一个流程最理想、最通畅的“主干道”画出来。然后再回头，去补充那些异常情况、判断分支等“小路”。这样能保证我的逻辑主线是清晰的。\n* **多思考边界异常**：一个产品经理的价值，很大程度上体现在对异常情况的考虑是否周全。比如，用户输错密码怎么办？网络断了怎么办？库存不足了怎么办？我会尽可能地把这些边界和异常情况都考虑到我的流程图里。\n\n#### 2. 绘制注意事项\n\n![image-20250720080153245](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720080153245.png)\n\n* **顺序排列**：尽量保持从上到下、从左到右的统一流向，避免连接线交叉、混乱。\n* **开头结尾**：一个完整的流程必须有明确的“开始”和“结束”符号。我绝不允许画一个没有终点的流程。\n* **是否闭环**：我要确保流程的每一个分支都有一个明确的去向，最终都能导向一个结束节点或回到主流程，不能出现“断头路”。\n* **善用标注**：当图形本身无法完全说清楚逻辑时，我会毫不犹豫地使用文字标注来补充说明，清晰永远是第一位的。\n* **化繁为简**：如果一个流程图变得过于巨大和复杂，我会思考是否可以把它拆分成几个子流程来表达。我们的目标是用最简洁的图，说明白最复杂的事。\n\n---\n\n### 练习：绘制“找工作”流程图\n\n![image-20250720080301030](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720080301030.png)\n\n现在，我们来做一个练习。请根据我们在图片中看到的“找工作流程图”案例，亲手绘制几张图。这个案例的流程如下：\na. 先在各个招聘网站投简历\nb. 公司的HR看到你的简历后，初步评估，如果符合岗位需求，就邀请你去公司面试\nc. 接到面试通知后，你就去公司参加面试，先由HR面试，再由该岗位的产品经理给你初面\nd. 上面两次面试都通过后，HR会再约你谈薪资，最后确认录用你，就会给你发offer\n\n**【练习任务】**\n\n1.  **任务一：绘制业务流程图**\n    请思考一下，这个流程涉及到哪些核心角色？（比如：求职者、HR、用人部门等）。\n\n    请你画一张**业务流程图**，清晰地表达出这些角色以及他们在整个求职过程中的主要交互和行为。\n    \n    ![未命名白板](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E6%9C%AA%E5%91%BD%E5%90%8D%E7%99%BD%E6%9D%BF.png)\n\n在画的这张图里，所有的动作都放在了一条线上。但实际上，“找工作”这个业务，至少涉及到三个角色：\n\n- **求职者**\n- **HR**\n- **用人部门** (这里就是产品经理)\n\n这三个角色在不同的时间点，做着不同的事，互相配合才完成了整个流程。而我们画“业务流程图”的核心目的，就是要清晰地展现这种**“跨角色的协作关系”**。\n\n**那么，如何优化呢？**\n\n我推荐使用一种最经典的业务流程图——**泳道图 (Swimlane Diagram)**。\n\n您可以想象一个游泳池，我们为“求职者”、“HR”、“用人部门”这三个角色，分别划分出一条独立的“泳道”。然后，我们把现在画的这些步骤，按照“**这个动作是谁做的**”，放回到对应角色的泳道里。\n\n\n\n---\n## 4.3 泳道图\n在上一节的练习中，我们提到了一个关键概念——**泳道图 (Swimlane Diagram)**，用它来优化我们画的业务流程图。\n\n现在，我们就来系统地学习一下这个我个人非常推崇的、能清晰表达多角色协作关系的强大工具。\n\n\n### 4.3.1 泳道图定义\n\n![image-20250720082100314](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720082100314.png)\n\n#### 1. 跨职能（多角色）流程图\n\n正如它的名字一样，泳道图，我把它就看作是带“泳道”的流程图。它的官方定义是**跨职能流程图**。\n\n它的核心价值在于，它不仅能展示**“要做什么”（What/流程）**，更能清晰地展示**“由谁来做”（Who/角色/部门）**。\n\n#### 2. 多角色协同与多阶段协同\n\n在我的实践中，泳道的划分方式主要有两种：\n\n* **按角色/部门划分**：这是我最常用的一种。就像我们“找工作”案例中的'求职者'、'HR'、'产品经理'。我用它来理清不同的人或团队之间的工作交接关系和职责边界。\n* **按阶段划分**：有时，一个流程会经历几个大的阶段，比如“需求阶段”、“设计阶段”、“开发阶段”、“测试阶段”。我也可以用泳道来划分这几个阶段，清晰地展示任务在不同阶段的流转。\n\n不过，在日常工作中，我们绝大多数时候都是**按角色划分**。\n\n### 4.3.2 泳道图绘制思路\n\n![image-20250720082152503](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720082152503.png)\n\n绘制泳道图，我的思路比画普通流程图会多几个“规划”步骤，这能确保最终的图清晰、准确。\n\n1.  **明确目标对象**：和画所有图一样，第一步永远是明确我画这张图的目的。我要说明的是一个什么样的流程？\n2.  **梳理角色/阶段**：这是泳道图独有的一步。我会把这个流程中涉及到的所有**参与方（角色/部门）**全部罗列出来。这是构建泳道的基础。\n3.  **划分归属**：我会把流程中的每一个动作（节点），明确地分配给上一步中罗列出的角色。也就是回答“这件事，到底该归谁管？”这个问题。\n4.  **对应绘制**：最后一步才是动手画。我先画好垂直或水平的泳道，然后把上一步中“划分好归属”的动作节点，一个一个放到各自的泳道里，再用流程线将它们连接起来。\n\n---\n\n#### **案例解析：找工作泳道图**\n\n![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/38bd1fda5226237bb446e314a9183d54.png)\n\n理论说完了，我们直接来看上一节练习的“标准答案”——**找工作泳道图案例**。这张图完美地诠释了泳道图的绘制思路和价值。\n\n1.  **第一步：梳理角色。**\n    我们看到，这张图清晰地定义了四个泳道，也就是四个核心角色：`求职者`、`HR`、`产品经理`、`产品总监`。\n\n2.  **第二步：划分归属并绘制。**\n    我们跟着流程线走一遍，就能清晰地看到动作和角色的对应关系：\n    * 流程从 **求职者** 泳道的 `投递简历` 开始。\n    * 箭头跨越泳道，流向 **HR** 泳道的 `查看简历` 和 `系统初筛`。\n    * 如果通过，流程继续在 **HR** 泳道里走到 `邀请面试`，然后再次跨越泳道，信息流转回 **求职者** 的 `接收信息`。\n    * 后续的 `初面` 由 **产品经理** 负责，`复面` 由 **产品总监** 负责，最后的 `薪资沟通` 又回到了 **HR** 这里。\n\n**我的洞察：**\n通过这张图，我不仅知道了找工作的完整步骤，更重要的是，我能一眼看清**在每个环节，我应该去找谁，谁是负责人，以及信息和任务是如何在不同角色之间流转交接的**。\n\n这种对“职责”和“协作”的清晰表达，是普通流程图无法给予的。这就是泳道图的威力所在，也是为什么它在表达复杂业务流程时，是我最重要的工具。\n\n\n\n\n---\n\n## 4.4 结构图介绍\n\n我们已经掌握了用来描述**动态“过程”**的流程图。现在，我们来学习与它互补的、用来描述**静态“组成”**的结构图。\n\n如果说流程图是产品的“电影剧本”，那么结构图就是产品的“骨骼X光片”或“解剖图”。它不关心先后顺序，只关心“**这个东西，是由哪些部分构成的？**”\n\n### 4.4.1 结构图的定义与分类\n\n![image-20250720093047853](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093047853.png)\n\n#### 1. 结构图的定义\n\n我给**结构图**的定义是：**一种通过树状或脑图等形式，来表达产品、功能或信息层级关系的可视化图表。**\n它的核心作用，就是帮助我把一个复杂、混沌的整体，拆解成一个个清晰、有序、有归属的部分。\n\n#### 2. 常见结构图类型\n\n就像流程图一样，根据我拆解的对象不同，我主要会用到三种结构图。\n\n* **功能结构图 (Functional Structure Diagram)**\n当我需要梳理一个产品或模块**“有哪些功能”**时，我就会画功能结构图。它是一个从抽象到具体的功能拆解过程，帮我梳理出完整的功能清单（Function List）。\n![image-20250720093116350](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093116350.png)\n\n\n我们来看“挂号功能结构图”这个案例。它清晰地将“挂号”这个大功能，拆解为`返回首页`、`搜索`、`选择科室`、`医院列表`等子功能，然后又把`医院列表`这个功能，进一步拆解为`筛选`和`查看医院`这两个孙子级功能。通过这张图，我就能确保在设计时，不会遗漏任何一个必要的功能点。\n\n![image-20250720093127402](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093127402.png)\n    \n\n​    \n\n\n* **信息结构图 (Information Structure Diagram)**\n  \n\n当我需要梳理一个页面或模块**“要展示哪些信息”**时，我就会画信息结构图。它拆解的不是“功能”，而是“数据和信息”。\n    \n![image-20250720093358095](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093358095.png)\n    \n在“挂号信息结构图”这个案例中，我们看到，它把“医院列表”这个模块，拆解为它需要展示的`封面图`、`名称`、`评分`、`地区`、`等级`等信息字段。这张图是我和UI设计师沟通界面内容、以及和开发工程师沟通数据字段时的重要依据。\n\n![image-20250720093411563](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093411563.png)\n\n\n* **产品结构图 (Product Structure Diagram)**\n    产品结构图，在我看来，是**功能结构图和信息结构图的集合体**，是产品最全面、最宏观的一张“鸟瞰地图”。\n\n    ![image-20250720093505990](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093505990.png)\n\n    我们看“挂号产品结构图”这个案例，它既包含了`搜索`这样的**功能模块**，也包含了`科室金刚区`、`查看Banner`这样的**信息模块**和**界面元素**。它是我在进行原型设计之前，用来组织整体产品框架的“总设计图”，能帮我从全局视角思考产品每个部分的构成和关系。\n\n![image-20250720093515445](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093515445.png)\n\n---\n## 4.5 结构图的绘制\n\n### 4.5.1 结构图绘制注意事项\n\n绘制结构图虽然比流程图要更自由一些，但我依然会遵循一些基本原则，来保证图表的清晰和易读。\n\n![image-20250720093556927](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720093556927.png)\n\n1.  **层级数量**\n    我画结构图时，会尽量把层级控制在**3-4层**以内。如果一个分支拆解得过深，说明这个模块可能太复杂了，我会考虑把它单独拎出来，为它画一张新的、更详细的结构图。\n\n2.  **绘制方式**\n    我习惯用“**自顶向下，逐层分解**”的方式来画。先确定最顶层的核心主题，然后拆分出第二层的主要构成，再把第二层的每一项继续往下拆，这样能保证逻辑的清晰和结构的完整。\n\n3.  **顺序**\n    和流程图不同，结构图同一层级的节点，左右顺序并没有严格的规定。我的原则是“**表达清楚即可**”，有时我会把逻辑上更重要或更核心的模块放在左边或上边，但这并不是硬性要求。\n\n\n\n---\n\n### 课习：拆解“视频播放页面”\n\n现在，我们来做一个结构图的练习。请你想象一下，我们正在设计一个类似于YouTube或Bilibili的视频网站，你的任务是，对最重要的**“视频播放页面”**进行结构化拆解。\n\n这个页面通常包含以下元素：\n* 主视频播放器窗口\n* 视频标题、UP主（上传者）信息（头像、昵称、粉丝数）、订阅按钮\n* 点赞、不喜欢、分享、下载、收藏等互动按钮\n* 视频简介、播放量、发布日期等数据\n* 评论区（包括评论输入框、评论列表）\n* 右侧的相关视频推荐列表\n\n**【练习任务】**\n\n1.  **任务：绘制功能结构图**\n    请你画一张**功能结构图**，来拆解这个页面上所有**用户可以进行的操作**。\n    \n* 从顶层的“视频播放页功能”开始，往下拆解出例如“播放器控制”（如：播放/暂停、调节音量、全屏）、“视频互动”（如：点赞、收藏）、“作者互动”（如：订阅）、“评论互动”等几大功能模块，并思考这些模块下还可以有哪些更细分的子功能。\n  \n    ![未命名白板 (3)](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E6%9C%AA%E5%91%BD%E5%90%8D%E7%99%BD%E6%9D%BF%20(3).png)\n    \n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%9B%9B%E7%AB%A0%EF%BC%9A%E6%B5%81%E7%A8%8B%E5%9B%BE%E4%B8%8E%E7%BB%93%E6%9E%84%E5%9B%BE"><span class="toc-number">1.</span> <span class="toc-text">第四章：流程图与结构图</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#4-1-%E8%AE%A4%E8%AF%86%E6%B5%81%E7%A8%8B%E5%9B%BE"><span class="toc-number">1.1.</span> <span class="toc-text">4.1 认识流程图</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-1-1-%E6%B5%81%E7%A8%8B%E5%9B%BE%E7%9A%84%E5%AE%9A%E4%B9%89%E4%B8%8E%E5%88%86%E7%B1%BB"><span class="toc-number">1.1.1.</span> <span class="toc-text">4.1.1 流程图的定义与分类</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%B5%81%E7%A8%8B%E5%9B%BE%E7%9A%84%E5%AE%9A%E4%B9%89"><span class="toc-number">1.1.1.1.</span> <span class="toc-text">1. 流程图的定义</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B8%B8%E8%A7%81%E6%B5%81%E7%A8%8B%E5%9B%BE%E7%B1%BB%E5%9E%8B"><span class="toc-number">1.1.1.2.</span> <span class="toc-text">2. 常见流程图类型</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-2-%E6%B5%81%E7%A8%8B%E5%9B%BE%E7%9A%84%E7%BB%98%E5%88%B6"><span class="toc-number">1.2.</span> <span class="toc-text">4.2 流程图的绘制</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-1-%E6%B5%81%E7%A8%8B%E5%9B%BE%E5%B8%B8%E8%A7%81%E5%85%83%E7%B4%A0"><span class="toc-number">1.2.1.</span> <span class="toc-text">4.2.1 流程图常见元素</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-2-%E6%B5%81%E7%A8%8B%E5%9B%BE%E5%B8%B8%E8%A7%81%E7%BB%93%E6%9E%84"><span class="toc-number">1.2.2.</span> <span class="toc-text">4.2.2 流程图常见结构</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%A1%BA%E5%BA%8F%E7%BB%93%E6%9E%84"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">1. 顺序结构</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E9%80%89%E6%8B%A9%E7%BB%93%E6%9E%84"><span class="toc-number">1.2.2.2.</span> <span class="toc-text">2. 选择结构</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E5%BE%AA%E7%8E%AF%E7%BB%93%E6%9E%84"><span class="toc-number">1.2.2.3.</span> <span class="toc-text">3. 循环结构</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-3-%E6%B5%81%E7%A8%8B%E5%9B%BE%E7%BB%98%E5%88%B6%E5%B7%A5%E5%85%B7"><span class="toc-number">1.2.3.</span> <span class="toc-text">4.2.3 流程图绘制工具</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-4-%E6%B5%81%E7%A8%8B%E5%9B%BE%E7%BB%98%E5%88%B6%E6%80%9D%E8%B7%AF%E4%B8%8E%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9"><span class="toc-number">1.2.4.</span> <span class="toc-text">4.2.4 流程图绘制思路与注意事项</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%BB%98%E5%88%B6%E6%80%9D%E8%B7%AF"><span class="toc-number">1.2.4.1.</span> <span class="toc-text">1. 绘制思路</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%BB%98%E5%88%B6%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9"><span class="toc-number">1.2.4.2.</span> <span class="toc-text">2. 绘制注意事项</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%BB%83%E4%B9%A0%EF%BC%9A%E7%BB%98%E5%88%B6%E2%80%9C%E6%89%BE%E5%B7%A5%E4%BD%9C%E2%80%9D%E6%B5%81%E7%A8%8B%E5%9B%BE"><span class="toc-number">1.2.5.</span> <span class="toc-text">练习：绘制“找工作”流程图</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-3-%E6%B3%B3%E9%81%93%E5%9B%BE"><span class="toc-number">1.3.</span> <span class="toc-text">4.3 泳道图</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-1-%E6%B3%B3%E9%81%93%E5%9B%BE%E5%AE%9A%E4%B9%89"><span class="toc-number">1.3.1.</span> <span class="toc-text">4.3.1 泳道图定义</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%B7%A8%E8%81%8C%E8%83%BD%EF%BC%88%E5%A4%9A%E8%A7%92%E8%89%B2%EF%BC%89%E6%B5%81%E7%A8%8B%E5%9B%BE"><span class="toc-number">1.3.1.1.</span> <span class="toc-text">1. 跨职能（多角色）流程图</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%A4%9A%E8%A7%92%E8%89%B2%E5%8D%8F%E5%90%8C%E4%B8%8E%E5%A4%9A%E9%98%B6%E6%AE%B5%E5%8D%8F%E5%90%8C"><span class="toc-number">1.3.1.2.</span> <span class="toc-text">2. 多角色协同与多阶段协同</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-2-%E6%B3%B3%E9%81%93%E5%9B%BE%E7%BB%98%E5%88%B6%E6%80%9D%E8%B7%AF"><span class="toc-number">1.3.2.</span> <span class="toc-text">4.3.2 泳道图绘制思路</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%A1%88%E4%BE%8B%E8%A7%A3%E6%9E%90%EF%BC%9A%E6%89%BE%E5%B7%A5%E4%BD%9C%E6%B3%B3%E9%81%93%E5%9B%BE"><span class="toc-number">1.3.2.1.</span> <span class="toc-text">案例解析：找工作泳道图</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-4-%E7%BB%93%E6%9E%84%E5%9B%BE%E4%BB%8B%E7%BB%8D"><span class="toc-number">1.4.</span> <span class="toc-text">4.4 结构图介绍</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-1-%E7%BB%93%E6%9E%84%E5%9B%BE%E7%9A%84%E5%AE%9A%E4%B9%89%E4%B8%8E%E5%88%86%E7%B1%BB"><span class="toc-number">1.4.1.</span> <span class="toc-text">4.4.1 结构图的定义与分类</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%BB%93%E6%9E%84%E5%9B%BE%E7%9A%84%E5%AE%9A%E4%B9%89"><span class="toc-number">1.4.1.1.</span> <span class="toc-text">1. 结构图的定义</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B8%B8%E8%A7%81%E7%BB%93%E6%9E%84%E5%9B%BE%E7%B1%BB%E5%9E%8B"><span class="toc-number">1.4.1.2.</span> <span class="toc-text">2. 常见结构图类型</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#4-5-%E7%BB%93%E6%9E%84%E5%9B%BE%E7%9A%84%E7%BB%98%E5%88%B6"><span class="toc-number">1.5.</span> <span class="toc-text">4.5 结构图的绘制</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#4-5-1-%E7%BB%93%E6%9E%84%E5%9B%BE%E7%BB%98%E5%88%B6%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9"><span class="toc-number">1.5.1.</span> <span class="toc-text">4.5.1 结构图绘制注意事项</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E8%AF%BE%E4%B9%A0%EF%BC%9A%E6%8B%86%E8%A7%A3%E2%80%9C%E8%A7%86%E9%A2%91%E6%92%AD%E6%94%BE%E9%A1%B5%E9%9D%A2%E2%80%9D"><span class="toc-number">1.5.2.</span> <span class="toc-text">课习：拆解“视频播放页面”</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>