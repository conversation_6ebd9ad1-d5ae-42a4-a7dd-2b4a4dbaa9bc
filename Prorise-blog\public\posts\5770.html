<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>SpringAI（七）：7. Embedding Models：万物皆可向量化 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="Java微服务篇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="SpringAI（七）：7. Embedding Models：万物皆可向量化"><meta name="application-name" content="SpringAI（七）：7. Embedding Models：万物皆可向量化"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="SpringAI（七）：7. Embedding Models：万物皆可向量化"><meta property="og:url" content="https://prorise666.site/posts/5770.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="7. Embedding Models：万物皆可向量化在之前的章节中，我们已经与 ChatModel 和 ImageModel 进行了深入的互动，它们让我们能够与 AI 的“语言能力”和“创造能力”对话。现在，我们将探索 AI 的另一项核心能力——理解能力。这项能力的关键在于一个名为 Embeddi"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta name="description" content="7. Embedding Models：万物皆可向量化在之前的章节中，我们已经与 ChatModel 和 ImageModel 进行了深入的互动，它们让我们能够与 AI 的“语言能力”和“创造能力”对话。现在，我们将探索 AI 的另一项核心能力——理解能力。这项能力的关键在于一个名为 Embeddi"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/5770.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"SpringAI（七）：7. Embedding Models：万物皆可向量化",postAI:"true",pageFillDescription:"7. Embedding Models：万物皆可向量化, 7.1 Embedding 在我们项目中的应用场景, 7.2 EmbeddingModel API 核心解析, 7.2.1 核心组件概览, 7.2.2 EmbeddingModel 接口方法详解, 7.3 实战：构建文件向量化服务, 7.3.1 后端实现：文件处理与向量化API, 第一步：依赖与配置, 第二步：创建文件处理服务 (service/DocumentEmbeddingService.java), 第三步：创建 API 控制器 (controller/DocumentEmbeddingController.java)万物皆可向量化在之前的章节中我们已经与和进行了深入的互动它们让我们能够与的语言能力和创造能力对话现在我们将探索的另一项核心能力理解能力这项能力的关键在于一个名为的概念它也是我们后续构建高级检索增强生成应用的绝对基石那么究竟什么是的核心思想将离散的非结构化的数据如文本图片代码通过一个深度学习模型映射到一个连续的高维的向量空间中在这个精心设计的空间里语义上相似的对象其对应的向量在几何距离上也更近简单来说模型就是一名宇宙翻译官它能将我们世界中纷繁复杂的信息统一翻译成计算机能够理解和比较的通用语言向量也就是一长串浮点数这种翻译是带有魔力的它保留了原始信息的意义例如一个训练有素的模型可以实现类似国王男人女人的向量运算其计算结果在向量空间中会惊人地接近王后的位置这项技术正是让从一个简单的对话机器人升级为智能知识助手的关键在我们项目中的应用场景在我们构想的蓝图中将扮演至关重要的角色它能解锁许多激动人心的高级功能下面的表格简要列出了几个核心应用场景功能场景核心挑战解决方案历史会话语义搜索无法按模糊印象搜索对话向量化进行相似度搜索企业知识库问答无法回答内部私有问题私有文档向量化实现智能代码片段推荐难以发现功能相似的代码代码块向量化匹配自然语言需求接下来我们对每个场景进行详细解读历史会话语义搜索传统的关键词搜索非常僵化如果用户想找上次那个关于数据库优化的讨论但忘记了具体用词搜索数据库可能会返回大量不相关的结果通过我们可以将每一条用户的聊天记录都转换为向量存入数据库当用户输入数据库优化讨论时我们将这句查询也转换为向量并在数据库中搜索与查询向量几何距离最近的聊天记录向量从而精准定位到语义上最相关的对话企业知识库问答这是最核心的应用我们的目前只能基于通用知识回答问题但无法解答关于我们公司内部的私有的问题例如公司的报销流程是怎样的通过我们可以将公司的所有内部文档如员工手册报销制度进行切片和向量化处理构建一个内部知识库当用户提问时先将其问题向量化并在知识库中检索出最相关的几个文档片段将这些相关的片段作为上下文连同用户的问题一起提交给让它基于这些内部知识生成精准回答智能代码片段推荐想象一下开发者在项目中遇到一个功能需求希望能找到项目内已有的功能相似的代码作为参考我们可以将项目中的所有函数或类的代码块进行向量化当开发者用自然语言描述需求例如一个能处理文件上传并保存到的函数时系统可以将这段描述向量化并检索出与需求描述向量最相似的代码块向量为开发者智能推荐最相关的代码实现掌握了我们就等于拥有了将任何信息转化为可理解语言的钥匙接下来我们将深入全新设计的体系核心解析与旧版的相比引入了以为核心的一系列接口和类设计上与体系保持了高度一致遵循了请求响应的模式更为强大和规范核心组件概览的围绕以下几个关键类构建下表对它们的核心角色进行了概括类接口名角色定位统一模型接口结构化请求对象结构化响应对象单个向量结果可移植配置项特定模型配置项下面我们对每个组件进行详细的讲解这是所有操作的统一入口无论我们后端使用的是智谱还是本地的模型我们的业务代码如层都只与这个标准接口交互完全屏蔽了底层实现的差异这是可移植性的核心体现这是一个结构化的请求对象用于封装一次完整的向量化请求它主要包含两部分内容需要被向量化的文本列表以及本次请求专用的生成选项同样是一个结构化的响应对象它封装了模型返回的全部信息我们可以从中获取一个或多个实例以及可能的元数据例如本次调用消耗了多少代表单个输入文本生成的最终结果我们可以从这个对象中获取真正的向量数据一个浮点数或双精度浮点数数组和它在原始输入列表中的索引一个可跨模型移植的配置接口定义了所有模型都应支持的通用参数例如指定模型名称请求的向量维度等这是特定于智谱的配置实现类它继承自通用的并增加了智谱独有的配置项例如指定返回的向量是还是格式接口方法详解接口为了开发者的便利提供了多个层次的方法方法签名功能描述底层核心方法单文本向量化多文本批量向量化对象向量化这是最底层的核心方法它功能最强大允许我们通过构建一个对象来精细控制所有请求参数例如运行时动态指定模型或向量维度并且其返回的对象包含了最完整的响应信息包括元数据便于我们进行日志记录或成本核算这是最常用最便捷的方法当我们只需要对单个文本进行快速向量化并且不关心额外的元数据时这个方法是我们的首选它直接接收一个字符串返回一个浮点数数组非常直观这是一个为性能优化的批量处理方法当有多个文本需要一次性向量化时我们应该使用它它会将整个列表一次性发送给模型相比于在循环中多次调用网络开销更小效率更高这是为场景设计的专用方法使用对象来表示经过处理的文档片段此方法可以无缝地对对象进行向量化简化了流程中的代码实战构建文件向量化服务理论讲完我们立刻动手本节我们将为项目添加一个全新的功能模块文档向量化工具用户可以通过一个友好的界面上传文本文件如后端服务接收到文件后会立即将其内容解析并转换为向量最后在控制台打印出这个向量的前几位作为演示后端实现文件处理与向量化第一步依赖与配置我们的项目已具备这已经包含了处理文件上传所需的一切我们只需要确保模型的配置是正确的本实战我们选用智谱的模型首先确保依赖已经存在于您的文件中接下来在中确认智谱的配置最关键的是通过属性全局指定使用作为的实现同时我们配置好的文件上传参数核心配置关键全局指定使用作为的实现强烈推荐使用环境变量来管理指定默认使用的嵌入模型文件上传配置允许上传单个文件最大单次请求总文件最大第二步创建文件处理服务这个负责整个核心逻辑接收文件校验文件解析文本调用模型返回结果文档向量化服务注入标准接口而不是任何具体实现定义允许上传的文件内容类型列表未来可以轻松扩展以支持等将上传的文件内容向量化并返回处理结果信息用户上传的文件包含文件名和向量维度信息的成功消息字符串步骤验证文件是否有效步骤从文件中安全地读取文本内容文件内容不能为空成功解析文件内容长度字符步骤调用注入的进行向量化正在调用模型进行向量化向量化成功步骤为了演示在控制台清晰地打印向量的预览信息文件的向量预览维度向量前位步骤返回一个对用户友好的包含关键信息的成功消息文件成功向量化已生成一个维的向量读取文件时发生错误读取文件内容时发生错误请检查文件是否损坏向量化文件时发生未知错误将异常重新抛出交由全局异常处理器统一格式化为标准响应内部私有方法用于校验上传的文件是否符合要求待校验的文件未接收到任何文件请选择一个文件上传接收到文件内容类型不支持的文件类型目前仅支持和文件第三步创建控制器这个非常简单它的职责就是暴露一个端点来接收格式的文件处理文档向量化的端点前端通过字段上传的文件符合项目规范的包含成功消息的将成功信息包装成我们项目的标准响应格式",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-08 13:53:48",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#7-Embedding-Models%EF%BC%9A%E4%B8%87%E7%89%A9%E7%9A%86%E5%8F%AF%E5%90%91%E9%87%8F%E5%8C%96"><span class="toc-text">7. Embedding Models：万物皆可向量化</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-Embedding-%E5%9C%A8%E6%88%91%E4%BB%AC%E9%A1%B9%E7%9B%AE%E4%B8%AD%E7%9A%84%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-text">7.1 Embedding 在我们项目中的应用场景</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-EmbeddingModel-API-%E6%A0%B8%E5%BF%83%E8%A7%A3%E6%9E%90"><span class="toc-text">7.2 EmbeddingModel API 核心解析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-2-1-%E6%A0%B8%E5%BF%83%E7%BB%84%E4%BB%B6%E6%A6%82%E8%A7%88"><span class="toc-text">7.2.1 核心组件概览</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-2-2-EmbeddingModel-%E6%8E%A5%E5%8F%A3%E6%96%B9%E6%B3%95%E8%AF%A6%E8%A7%A3"><span class="toc-text">7.2.2 EmbeddingModel 接口方法详解</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-%E5%AE%9E%E6%88%98%EF%BC%9A%E6%9E%84%E5%BB%BA%E6%96%87%E4%BB%B6%E5%90%91%E9%87%8F%E5%8C%96%E6%9C%8D%E5%8A%A1"><span class="toc-text">7.3 实战：构建文件向量化服务</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-3-1-%E5%90%8E%E7%AB%AF%E5%AE%9E%E7%8E%B0%EF%BC%9A%E6%96%87%E4%BB%B6%E5%A4%84%E7%90%86%E4%B8%8E%E5%90%91%E9%87%8F%E5%8C%96API"><span class="toc-text">7.3.1 后端实现：文件处理与向量化API</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E4%BE%9D%E8%B5%96%E4%B8%8E%E9%85%8D%E7%BD%AE"><span class="toc-text">第一步：依赖与配置</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E5%88%9B%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%84%E7%90%86%E6%9C%8D%E5%8A%A1-service-DocumentEmbeddingService-java"><span class="toc-text">第二步：创建文件处理服务 (service/DocumentEmbeddingService.java)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E6%AD%A5%EF%BC%9A%E5%88%9B%E5%BB%BA-API-%E6%8E%A7%E5%88%B6%E5%99%A8-controller-DocumentEmbeddingController-java"><span class="toc-text">第三步：创建 API 控制器 (controller/DocumentEmbeddingController.java)</span></a></li></ol></li></ol></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Java微服务篇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">SpringAI（七）：7. Embedding Models：万物皆可向量化</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-03-21T02:13:45.000Z" title="发表于 2025-03-21 10:13:45">2025-03-21</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:48.482Z" title="更新于 2025-07-08 13:53:48">2025-07-08</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">3.5k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>12分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="SpringAI（七）：7. Embedding Models：万物皆可向量化"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/5770.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/5770.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url">Java微服务篇</a><h1 id="CrawlerTitle" itemprop="name headline">SpringAI（七）：7. Embedding Models：万物皆可向量化</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-03-21T02:13:45.000Z" title="发表于 2025-03-21 10:13:45">2025-03-21</time><time itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:48.482Z" title="更新于 2025-07-08 13:53:48">2025-07-08</time></header><div id="postchat_postcontent"><h2 id="7-Embedding-Models：万物皆可向量化"><a href="#7-Embedding-Models：万物皆可向量化" class="headerlink" title="7. Embedding Models：万物皆可向量化"></a>7. Embedding Models：万物皆可向量化</h2><p>在之前的章节中，我们已经与 <code>ChatModel</code> 和 <code>ImageModel</code> 进行了深入的互动，它们让我们能够与 AI 的“语言能力”和“创造能力”对话。现在，我们将探索 AI 的另一项核心能力——<strong>理解能力</strong>。这项能力的关键在于一个名为 <strong>Embedding</strong> 的概念，它也是我们后续构建高级 RAG（检索增强生成）应用的绝对基石。</p><p>那么，究竟什么是 Embedding？</p><blockquote><p><strong>Embedding 的核心思想</strong>：将离散的、非结构化的数据（如文本、图片、代码）通过一个深度学习模型，映射到一个连续的、高维的向量空间中。在这个精心设计的空间里，<strong>语义上相似的对象，其对应的向量在几何距离上也更近</strong>。</p></blockquote><p>简单来说，Embedding 模型就是一名“宇宙翻译官”，它能将我们世界中纷繁复杂的信息，统一翻译成计算机能够理解和比较的通用语言——<strong>向量（Vector）</strong>，也就是一长串浮点数。这种翻译是带有“魔力”的，它保留了原始信息的“意义”。例如，一个训练有素的 Embedding 模型可以实现类似 <code>vector("国王") - vector("男人") + vector("女人")</code> 的向量运算，其计算结果在向量空间中会惊人地接近 <code>vector("王后")</code> 的位置。</p><p>这项技术，正是让 AI-Copilot 从一个简单的“对话机器人”升级为“智能知识助手”的关键。</p><h3 id="7-1-Embedding-在我们项目中的应用场景"><a href="#7-1-Embedding-在我们项目中的应用场景" class="headerlink" title="7.1 Embedding 在我们项目中的应用场景"></a>7.1 Embedding 在我们项目中的应用场景</h3><p>在我们构想的 AI-Copilot 蓝图中，Embedding 将扮演至关重要的角色，它能解锁许多激动人心的高级功能。下面的表格简要列出了几个核心应用场景。</p><table><thead><tr><th align="left">功能场景</th><th align="left">核心挑战</th><th align="left">Embedding 解决方案</th></tr></thead><tbody><tr><td align="left"><strong>历史会话语义搜索</strong></td><td align="left">无法按“模糊印象”搜索</td><td align="left">对话向量化，进行相似度搜索</td></tr><tr><td align="left"><strong>企业知识库问答</strong></td><td align="left">无法回答内部、私有问题</td><td align="left">私有文档向量化，实现 RAG</td></tr><tr><td align="left"><strong>智能代码片段推荐</strong></td><td align="left">难以发现功能相似的代码</td><td align="left">代码块向量化，匹配自然语言需求</td></tr></tbody></table><p>接下来，我们对每个场景进行详细解读：</p><ul><li><p><strong>历史会话语义搜索</strong>:<br>传统的关键词搜索非常僵化。如果用户想找“上次那个关于数据库优化的讨论”，但忘记了具体用词，搜索 “数据库” 可能会返回大量不相关的结果。通过 Embedding，我们可以将<strong>每一条</strong>用户的聊天记录都转换为向量存入数据库。当用户输入“数据库优化讨论”时，我们将这句查询也转换为向量，并在数据库中搜索与查询向量“几何距离最近”的聊天记录向量，从而精准定位到语义上最相关的对话。</p></li><li><p><strong>企业知识库问答 (RAG)</strong>:<br>这是 Embedding 最核心的应用。我们的 AI-Copilot 目前只能基于通用知识回答问题，但无法解答关于我们公司内部的、私有的问题，例如：“公司的报销流程是怎样的？”。通过 RAG，我们可以：</p><ol><li>将公司的<strong>所有内部文档</strong>（如《员工手册.pdf》、《报销制度.docx》）进行切片和向量化处理，构建一个内部知识库。</li><li>当用户提问时，先将其问题向量化，并在知识库中检索出最相关的几个文档片段。</li><li>将这些相关的片段作为上下文，连同用户的问题一起，提交给 <code>ChatModel</code>，让它基于这些“内部知识”生成精准回答。</li></ol></li><li><p><strong>智能代码片段推荐</strong>:<br>想象一下，开发者在项目中遇到一个功能需求，希望能找到项目内已有的、功能相似的代码作为参考。我们可以将项目中的<strong>所有函数或类</strong>的代码块进行向量化。当开发者用自然语言描述需求（例如：“一个能处理文件上传并保存到S3的函数”）时，系统可以将这段描述向量化，并检索出与需求描述向量最相似的代码块向量，为开发者智能推荐最相关的代码实现。</p></li></ul><p>掌握了 Embedding，我们就等于拥有了将任何信息转化为 AI 可理解语言的钥匙。接下来，我们将深入 Spring AI 1.0+ 全新设计的 <code>EmbeddingModel</code> API 体系。</p><h3 id="7-2-EmbeddingModel-API-核心解析"><a href="#7-2-EmbeddingModel-API-核心解析" class="headerlink" title="7.2 EmbeddingModel API 核心解析"></a>7.2 <code>EmbeddingModel</code> API 核心解析</h3><p>与旧版的<code>EmbeddingClient</code>相比，Spring AI 1.0+ 引入了以 <code>EmbeddingModel</code> 为核心的一系列接口和类，设计上与 <code>ChatModel</code> 体系保持了高度一致，遵循了“请求-响应”的模式，更为强大和规范。</p><h4 id="7-2-1-核心组件概览"><a href="#7-2-1-核心组件概览" class="headerlink" title="7.2.1 核心组件概览"></a>7.2.1 核心组件概览</h4><p><code>EmbeddingModel</code> 的 API 围绕以下几个关键类构建，下表对它们的核心角色进行了概括。</p><table><thead><tr><th align="left">类/接口名</th><th align="left">角色定位</th></tr></thead><tbody><tr><td align="left"><strong><code>EmbeddingModel</code></strong></td><td align="left">统一模型接口</td></tr><tr><td align="left"><strong><code>EmbeddingRequest</code></strong></td><td align="left">结构化请求对象</td></tr><tr><td align="left"><strong><code>EmbeddingResponse</code></strong></td><td align="left">结构化响应对象</td></tr><tr><td align="left"><strong><code>Embedding</code></strong></td><td align="left">单个向量结果</td></tr><tr><td align="left"><strong><code>EmbeddingOptions</code></strong></td><td align="left">可移植配置项</td></tr><tr><td align="left"><strong><code>ZhiPuAiEmbeddingOptions</code></strong></td><td align="left">特定模型配置项</td></tr></tbody></table><p>下面我们对每个组件进行详细的讲解：</p><ul><li><p><strong><code>EmbeddingModel</code></strong>: 这是所有 Embedding 操作的<strong>统一入口</strong>。无论我们后端使用的是 OpenAI、智谱 AI 还是本地的 Ollama 模型，我们的业务代码（如 <code>Service</code> 层）都只与这个标准接口交互，完全屏蔽了底层实现的差异，这是 Spring AI 可移植性的核心体现。</p></li><li><p><strong><code>EmbeddingRequest</code></strong>: 这是一个结构化的请求对象，用于封装一次完整的向量化请求。它主要包含两部分内容：需要被向量化的<strong>文本列表</strong> (<code>List&lt;String&gt;</code>)，以及本次请求专用的<strong>生成选项</strong> (<code>EmbeddingOptions</code>)。</p></li><li><p><strong><code>EmbeddingResponse</code></strong>: 同样是一个结构化的响应对象，它封装了模型返回的全部信息。我们可以从中获取一个或多个 <code>Embedding</code> 实例，以及可能的元数据（例如本次调用消耗了多少 Token）。</p></li><li><p><strong><code>Embedding</code></strong>: 代表<strong>单个输入文本</strong>生成的最终结果。我们可以从这个对象中获取真正的向量数据（一个浮点数或双精度浮点数数组 <code>float[]</code> / <code>double[]</code>）和它在原始输入列表中的索引。</p></li><li><p><strong><code>EmbeddingOptions</code></strong>: 一个可跨模型移植的配置接口，定义了所有模型都应支持的通用参数，例如 <code>model</code>（指定模型名称）、<code>dimensions</code>（请求的向量维度）等。</p></li><li><p><strong><code>ZhiPuAiEmbeddingOptions</code></strong>: 这是特定于<strong>智谱 AI</strong> 的配置实现类，它继承自通用的 <code>EmbeddingOptions</code>，并增加了智谱 AI 独有的配置项，例如 <code>encoding-format</code>（指定返回的向量是 <code>float</code> 还是 <code>base64</code> 格式）。</p></li></ul><h4 id="7-2-2-EmbeddingModel-接口方法详解"><a href="#7-2-2-EmbeddingModel-接口方法详解" class="headerlink" title="7.2.2 EmbeddingModel 接口方法详解"></a>7.2.2 <code>EmbeddingModel</code> 接口方法详解</h4><p><code>EmbeddingModel</code> 接口为了开发者的便利，提供了多个层次的方法。</p><table><thead><tr><th align="left">方法签名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>EmbeddingResponse call(EmbeddingRequest request)</code></td><td align="left">底层核心方法</td></tr><tr><td align="left"><code>float[] embed(String text)</code></td><td align="left">单文本向量化</td></tr><tr><td align="left"><code>List&lt;float[]&gt; embed(List&lt;String&gt; texts)</code></td><td align="left">多文本批量向量化</td></tr><tr><td align="left"><code>float[] embed(Document document)</code></td><td align="left"><code>Document</code> 对象向量化</td></tr></tbody></table><ul><li><p><strong><code>call(EmbeddingRequest request)</code></strong>: 这是最底层的核心方法。它功能最强大，允许我们通过构建一个 <code>EmbeddingRequest</code> 对象来精细控制所有请求参数（例如，运行时动态指定模型或向量维度），并且其返回的 <code>EmbeddingResponse</code> 对象包含了最完整的响应信息，包括元数据，便于我们进行日志记录或成本核算。</p></li><li><p><strong><code>embed(String text)</code></strong>: 这是<strong>最常用、最便捷</strong>的方法。当我们只需要对单个文本进行快速向量化，并且不关心额外的元数据时，这个方法是我们的首选。它直接接收一个字符串，返回一个浮点数数组，非常直观。</p></li><li><p><strong><code>embed(List&lt;String&gt; texts)</code></strong>: 这是一个为性能优化的批量处理方法。当有多个文本需要一次性向量化时，我们应该使用它。它会将整个列表一次性发送给模型 API，相比于在 <code>for</code> 循环中多次调用 <code>embed(String text)</code>，网络开销更小，效率更高。</p></li><li><p><strong><code>embed(Document document)</code></strong>: 这是为 RAG 场景设计的专用方法。Spring AI 使用 <code>Document</code> 对象来表示经过处理的文档片段，此方法可以无缝地对 <code>Document</code> 对象进行向量化，简化了 RAG 流程中的代码。</p></li></ul><h3 id="7-3-实战：构建文件向量化服务"><a href="#7-3-实战：构建文件向量化服务" class="headerlink" title="7.3 实战：构建文件向量化服务"></a>7.3 实战：构建文件向量化服务</h3><p>理论讲完，我们立刻动手。本节我们将为 AI-Copilot 项目添加一个全新的功能模块——<strong>“文档向量化工具”</strong>。用户可以通过一个友好的界面上传文本文件（如 <code>.txt</code>, <code>.md</code>），后端服务接收到文件后，会立即将其内容解析并转换为向量，最后在控制台打印出这个向量的前几位作为演示。</p><h4 id="7-3-1-后端实现：文件处理与向量化API"><a href="#7-3-1-后端实现：文件处理与向量化API" class="headerlink" title="7.3.1 后端实现：文件处理与向量化API"></a>7.3.1 后端实现：文件处理与向量化API</h4><h5 id="第一步：依赖与配置"><a href="#第一步：依赖与配置" class="headerlink" title="第一步：依赖与配置"></a><strong>第一步：依赖与配置</strong></h5><p>我们的项目已具备 <code>spring-boot-starter-web</code>，这已经包含了处理文件上传所需的一切。我们只需要确保 Embedding 模型的配置是正确的。本实战我们选用<strong>智谱 AI</strong> 的 <code>embedding-3</code> 模型。</p><ul><li><p><strong><code>pom.xml</code></strong>: 首先，确保 <code>spring-ai-starter-model-zhipuai</code> 依赖已经存在于您的 <code>pom.xml</code> 文件中。</p><figure class="highlight xml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>org.springframework.ai<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>spring-ai-starter-model-zhipuai<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong><code>application.yml</code></strong>: 接下来，在 <code>application.yml</code> 中确认智谱 AI 的配置。最关键的是通过 <code>spring.ai.model.embedding</code> 属性，全局指定使用 <code>zhipuai</code> 作为 <code>EmbeddingModel</code> 的实现。同时，我们配置好 Spring MVC 的文件上传参数。</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">spring:</span></span><br><span class="line">  <span class="comment"># Spring AI 核心配置</span></span><br><span class="line">  <span class="attr">ai:</span></span><br><span class="line">    <span class="attr">model:</span></span><br><span class="line">      <span class="comment"># 关键：全局指定使用 zhipuai 作为 EmbeddingModel 的实现</span></span><br><span class="line">      <span class="attr">embedding:</span> <span class="string">zhipuai</span></span><br><span class="line">    <span class="attr">zhipuai:</span></span><br><span class="line">      <span class="comment"># 强烈推荐使用环境变量来管理 API Key</span></span><br><span class="line">      <span class="attr">api-key:</span> <span class="string">${ZHIPU_API_KEY}</span></span><br><span class="line">      <span class="attr">embedding:</span></span><br><span class="line">        <span class="attr">options:</span></span><br><span class="line">          <span class="comment"># 指定默认使用的嵌入模型</span></span><br><span class="line">          <span class="attr">model:</span> <span class="string">embedding-3</span></span><br><span class="line"></span><br><span class="line">  <span class="comment"># Spring MVC 文件上传配置</span></span><br><span class="line">  <span class="attr">servlet:</span></span><br><span class="line">    <span class="attr">multipart:</span></span><br><span class="line">      <span class="attr">enabled:</span> <span class="literal">true</span>           <span class="comment"># 允许上传</span></span><br><span class="line">      <span class="attr">max-file-size:</span> <span class="string">10MB</span>     <span class="comment"># 单个文件最大 10MB</span></span><br><span class="line">      <span class="attr">max-request-size:</span> <span class="string">50MB</span>  <span class="comment"># 单次请求总文件最大 50MB</span></span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="第二步：创建文件处理服务-service-DocumentEmbeddingService-java"><a href="#第二步：创建文件处理服务-service-DocumentEmbeddingService-java" class="headerlink" title="第二步：创建文件处理服务 (service/DocumentEmbeddingService.java)"></a><strong>第二步：创建文件处理服务 (<code>service/DocumentEmbeddingService.java</code>)</strong></h5><p>这个 Service 负责整个核心逻辑：接收文件 -&gt; 校验文件 -&gt; 解析文本 -&gt; 调用模型 -&gt; 返回结果。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// src/main/java/com/copilot/aicopilotbackend/service/DocumentEmbeddingService.java</span></span><br><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.service;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.exception.BusinessException;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.exception.ErrorCode;</span><br><span class="line"><span class="keyword">import</span> lombok.RequiredArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> lombok.extern.slf4j.Slf4j;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.embedding.EmbeddingModel;</span><br><span class="line"><span class="keyword">import</span> org.springframework.stereotype.Service;</span><br><span class="line"><span class="keyword">import</span> org.springframework.util.StringUtils;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.multipart.MultipartFile;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.io.IOException;</span><br><span class="line"><span class="keyword">import</span> java.nio.charset.StandardCharsets;</span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 文档向量化服务</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"><span class="meta">@Slf4j</span></span><br><span class="line"><span class="meta">@Service</span></span><br><span class="line"><span class="meta">@RequiredArgsConstructor</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">DocumentEmbeddingService</span> {</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 注入标准 EmbeddingModel 接口，而不是任何具体实现</span></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> EmbeddingModel embeddingModel;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 定义允许上传的文件内容类型列表</span></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">static</span> <span class="keyword">final</span> List&lt;String&gt; ALLOWED_CONTENT_TYPES = Arrays.asList(</span><br><span class="line">            <span class="string">"text/plain"</span>,       <span class="comment">// .txt</span></span><br><span class="line">            <span class="string">"text/markdown"</span>     <span class="comment">// .md</span></span><br><span class="line">            <span class="comment">// 未来可以轻松扩展以支持 "application/pdf" 等</span></span><br><span class="line">    );</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 将上传的文件内容向量化，并返回处理结果信息。</span></span><br><span class="line"><span class="comment">     *</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> file 用户上传的文件</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@return</span> 包含文件名和向量维度信息的成功消息字符串</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="keyword">public</span> String <span class="title function_">embedDocument</span><span class="params">(MultipartFile file)</span> {</span><br><span class="line">        <span class="comment">// 步骤1: 验证文件是否有效</span></span><br><span class="line">        validateFile(file);</span><br><span class="line"></span><br><span class="line">        <span class="keyword">try</span> {</span><br><span class="line">            <span class="comment">// 步骤2: 从文件中安全地读取文本内容</span></span><br><span class="line">            <span class="type">String</span> <span class="variable">documentContent</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">String</span>(file.getBytes(), StandardCharsets.UTF_8);</span><br><span class="line">            <span class="keyword">if</span> (!StringUtils.hasText(documentContent)) {</span><br><span class="line">                <span class="keyword">throw</span> <span class="keyword">new</span> <span class="title class_">BusinessException</span>(ErrorCode.INVALID_PARAMETER, <span class="string">"文件内容不能为空。"</span>);</span><br><span class="line">            }</span><br><span class="line">            log.info(<span class="string">"成功解析文件 '{}', 内容长度: {} 字符"</span>, file.getOriginalFilename(), documentContent.length());</span><br><span class="line"></span><br><span class="line">            <span class="comment">// 步骤3: 调用注入的 EmbeddingModel 进行向量化</span></span><br><span class="line">            log.info(<span class="string">"正在调用 Embedding 模型进行向量化..."</span>);</span><br><span class="line">            <span class="type">float</span>[] vector = embeddingModel.embed(documentContent);</span><br><span class="line">            log.info(<span class="string">"向量化成功！"</span>);</span><br><span class="line"></span><br><span class="line">            <span class="comment">// 步骤4: 为了演示，在控制台清晰地打印向量的预览信息</span></span><br><span class="line">            log.info(<span class="string">"--- 文件 '{}' 的向量预览 ---"</span>, file.getOriginalFilename());</span><br><span class="line">            log.info(<span class="string">"维度 (Dimensions): {}"</span>, vector.length);</span><br><span class="line">            log.info(<span class="string">"向量前5位 (Top 5 elements): {}"</span>, Arrays.toString(Arrays.copyOfRange(vector, <span class="number">0</span>, <span class="number">5</span>)));</span><br><span class="line">            log.info(<span class="string">"------------------------------------"</span>);</span><br><span class="line">            </span><br><span class="line">            <span class="comment">// 步骤5: 返回一个对用户友好的、包含关键信息的成功消息</span></span><br><span class="line">            <span class="keyword">return</span> String.format(<span class="string">"文件 '%s' 成功向量化！已生成一个 %d 维的向量。"</span>, file.getOriginalFilename(), vector.length);</span><br><span class="line"></span><br><span class="line">        } <span class="keyword">catch</span> (IOException e) {</span><br><span class="line">            log.error(<span class="string">"读取文件 '{}' 时发生 I/O 错误"</span>, file.getOriginalFilename(), e);</span><br><span class="line">            <span class="keyword">throw</span> <span class="keyword">new</span> <span class="title class_">BusinessException</span>(ErrorCode.SYSTEM_ERROR, <span class="string">"读取文件内容时发生错误，请检查文件是否损坏。"</span>);</span><br><span class="line">        } <span class="keyword">catch</span> (Exception e) {</span><br><span class="line">            log.error(<span class="string">"向量化文件 '{}' 时发生未知错误"</span>, file.getOriginalFilename(), e);</span><br><span class="line">            <span class="comment">// 将异常重新抛出，交由全局异常处理器统一格式化为标准 API 响应</span></span><br><span class="line">            <span class="keyword">throw</span> e; </span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 内部私有方法，用于校验上传的文件是否符合要求。</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> file 待校验的文件</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">void</span> <span class="title function_">validateFile</span><span class="params">(MultipartFile file)</span> {</span><br><span class="line">        <span class="keyword">if</span> (file == <span class="literal">null</span> || file.isEmpty()) {</span><br><span class="line">            <span class="keyword">throw</span> <span class="keyword">new</span> <span class="title class_">BusinessException</span>(ErrorCode.MISSING_PARAMETER, <span class="string">"未接收到任何文件，请选择一个文件上传。"</span>);</span><br><span class="line">        }</span><br><span class="line">        </span><br><span class="line">        <span class="type">String</span> <span class="variable">contentType</span> <span class="operator">=</span> file.getContentType();</span><br><span class="line">        log.info(<span class="string">"接收到文件 '{}'，内容类型: {}"</span>, file.getOriginalFilename(), contentType);</span><br><span class="line"></span><br><span class="line">        <span class="keyword">if</span> (!ALLOWED_CONTENT_TYPES.contains(contentType)) {</span><br><span class="line">            <span class="keyword">throw</span> <span class="keyword">new</span> <span class="title class_">BusinessException</span>(ErrorCode.INVALID_PARAMETER, <span class="string">"不支持的文件类型。目前仅支持 .txt 和 .md 文件。"</span>);</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h5 id="第三步：创建-API-控制器-controller-DocumentEmbeddingController-java"><a href="#第三步：创建-API-控制器-controller-DocumentEmbeddingController-java" class="headerlink" title="第三步：创建 API 控制器 (controller/DocumentEmbeddingController.java)"></a><strong>第三步：创建 API 控制器 (<code>controller/DocumentEmbeddingController.java</code>)</strong></h5><p>这个 Controller 非常简单，它的职责就是暴露一个 <code>POST</code> 端点来接收 <code>multipart/form-data</code> 格式的文件。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// src/main/java/com/copilot/aicopilotbackend/controller/DocumentEmbeddingController.java</span></span><br><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.controller;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.dto.response.ApiResponse;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.service.DocumentEmbeddingService;</span><br><span class="line"><span class="keyword">import</span> lombok.RequiredArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.PostMapping;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.RequestMapping;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.RequestParam;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.RestController;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.multipart.MultipartFile;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"></span><br><span class="line"><span class="meta">@RestController</span></span><br><span class="line"><span class="meta">@RequestMapping("/api/v1/documents")</span></span><br><span class="line"><span class="meta">@RequiredArgsConstructor</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">DocumentEmbeddingController</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> DocumentEmbeddingService documentEmbeddingService;</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 处理文档向量化的API端点</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> file 前端通过 'file' 字段上传的文件</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@return</span> 符合项目规范的、包含成功消息的 ApiResponse</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="meta">@PostMapping("/embed")</span></span><br><span class="line">    <span class="keyword">public</span> ApiResponse&lt;Map&lt;String, String&gt;&gt; <span class="title function_">embedDocument</span><span class="params">(<span class="meta">@RequestParam("file")</span> MultipartFile file)</span> {</span><br><span class="line">        <span class="type">String</span> <span class="variable">resultMessage</span> <span class="operator">=</span> documentEmbeddingService.embedDocument(file);</span><br><span class="line">        <span class="comment">// 将成功信息包装成我们项目的标准API响应格式</span></span><br><span class="line">        <span class="keyword">return</span> ApiResponse.success(Map.of(<span class="string">"message"</span>, resultMessage));</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/5770.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/5770.html&quot;)">SpringAI（七）：7. Embedding Models：万物皆可向量化</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/5770.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/5770.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Java<span class="categoryesPageCount">20</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Java微服务篇<span class="tagsPageCount">11</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/29776.html"><img class="prev-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">SpringAI（六）：6. AI 的创造力：集成文生图能力</div></div></a></div><div class="next-post pull-right"><a href="/posts/47912.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">SpringAI（八）：8. Vector Stores：构建 AI 的长期记忆 (Redis 实战篇)</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/59358.html" title="SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元</div></div></a></div><div><a href="/posts/52289.html" title="SpringAI（三）：3. 会话核心 API 深度解析"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（三）：3. 会话核心 API 深度解析</div></div></a></div><div><a href="/posts/18714.html" title="SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用</div></div></a></div><div><a href="/posts/22322.html" title="SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”</div></div></a></div><div><a href="/posts/60609.html" title="SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南</div></div></a></div><div><a href="/posts/29776.html" title="SpringAI（六）：6. AI 的创造力：集成文生图能力"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（六）：6. AI 的创造力：集成文生图能力</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"SpringAI（七）：7. Embedding Models：万物皆可向量化",date:"2025-03-21 10:13:45",updated:"2025-07-08 13:53:48",tags:["Java微服务篇"],categories:["后端技术","Java"],content:'\n## 7\\. Embedding Models：万物皆可向量化\n\n在之前的章节中，我们已经与 `ChatModel` 和 `ImageModel` 进行了深入的互动，它们让我们能够与 AI 的“语言能力”和“创造能力”对话。现在，我们将探索 AI 的另一项核心能力——**理解能力**。这项能力的关键在于一个名为 **Embedding** 的概念，它也是我们后续构建高级 RAG（检索增强生成）应用的绝对基石。\n\n那么，究竟什么是 Embedding？\n\n> **Embedding 的核心思想**：将离散的、非结构化的数据（如文本、图片、代码）通过一个深度学习模型，映射到一个连续的、高维的向量空间中。在这个精心设计的空间里，**语义上相似的对象，其对应的向量在几何距离上也更近**。\n\n简单来说，Embedding 模型就是一名“宇宙翻译官”，它能将我们世界中纷繁复杂的信息，统一翻译成计算机能够理解和比较的通用语言——**向量（Vector）**，也就是一长串浮点数。这种翻译是带有“魔力”的，它保留了原始信息的“意义”。例如，一个训练有素的 Embedding 模型可以实现类似 `vector("国王") - vector("男人") + vector("女人")` 的向量运算，其计算结果在向量空间中会惊人地接近 `vector("王后")` 的位置。\n\n这项技术，正是让 AI-Copilot 从一个简单的“对话机器人”升级为“智能知识助手”的关键。\n\n### 7.1 Embedding 在我们项目中的应用场景\n\n在我们构想的 AI-Copilot 蓝图中，Embedding 将扮演至关重要的角色，它能解锁许多激动人心的高级功能。下面的表格简要列出了几个核心应用场景。\n\n| 功能场景 | 核心挑战 | Embedding 解决方案 |\n| :--- | :--- | :--- |\n| **历史会话语义搜索** | 无法按“模糊印象”搜索 | 对话向量化，进行相似度搜索 |\n| **企业知识库问答** | 无法回答内部、私有问题 | 私有文档向量化，实现 RAG |\n| **智能代码片段推荐** | 难以发现功能相似的代码 | 代码块向量化，匹配自然语言需求 |\n\n接下来，我们对每个场景进行详细解读：\n\n  * **历史会话语义搜索**:\n    传统的关键词搜索非常僵化。如果用户想找“上次那个关于数据库优化的讨论”，但忘记了具体用词，搜索 "数据库" 可能会返回大量不相关的结果。通过 Embedding，我们可以将**每一条**用户的聊天记录都转换为向量存入数据库。当用户输入“数据库优化讨论”时，我们将这句查询也转换为向量，并在数据库中搜索与查询向量“几何距离最近”的聊天记录向量，从而精准定位到语义上最相关的对话。\n\n  * **企业知识库问答 (RAG)**:\n    这是 Embedding 最核心的应用。我们的 AI-Copilot 目前只能基于通用知识回答问题，但无法解答关于我们公司内部的、私有的问题，例如：“公司的报销流程是怎样的？”。通过 RAG，我们可以：\n\n    1.  将公司的**所有内部文档**（如《员工手册.pdf》、《报销制度.docx》）进行切片和向量化处理，构建一个内部知识库。\n    2.  当用户提问时，先将其问题向量化，并在知识库中检索出最相关的几个文档片段。\n    3.  将这些相关的片段作为上下文，连同用户的问题一起，提交给 `ChatModel`，让它基于这些“内部知识”生成精准回答。\n\n  * **智能代码片段推荐**:\n    想象一下，开发者在项目中遇到一个功能需求，希望能找到项目内已有的、功能相似的代码作为参考。我们可以将项目中的**所有函数或类**的代码块进行向量化。当开发者用自然语言描述需求（例如：“一个能处理文件上传并保存到S3的函数”）时，系统可以将这段描述向量化，并检索出与需求描述向量最相似的代码块向量，为开发者智能推荐最相关的代码实现。\n\n掌握了 Embedding，我们就等于拥有了将任何信息转化为 AI 可理解语言的钥匙。接下来，我们将深入 Spring AI 1.0+ 全新设计的 `EmbeddingModel` API 体系。\n\n### 7.2 `EmbeddingModel` API 核心解析\n\n与旧版的`EmbeddingClient`相比，Spring AI 1.0+ 引入了以 `EmbeddingModel` 为核心的一系列接口和类，设计上与 `ChatModel` 体系保持了高度一致，遵循了“请求-响应”的模式，更为强大和规范。\n\n#### 7.2.1 核心组件概览\n\n`EmbeddingModel` 的 API 围绕以下几个关键类构建，下表对它们的核心角色进行了概括。\n\n| 类/接口名 | 角色定位 |\n| :--- | :--- |\n| **`EmbeddingModel`** | 统一模型接口 |\n| **`EmbeddingRequest`** | 结构化请求对象 |\n| **`EmbeddingResponse`**| 结构化响应对象 |\n| **`Embedding`** | 单个向量结果 |\n| **`EmbeddingOptions`** | 可移植配置项 |\n| **`ZhiPuAiEmbeddingOptions`** | 特定模型配置项 |\n\n下面我们对每个组件进行详细的讲解：\n\n  * **`EmbeddingModel`**: 这是所有 Embedding 操作的**统一入口**。无论我们后端使用的是 OpenAI、智谱 AI 还是本地的 Ollama 模型，我们的业务代码（如 `Service` 层）都只与这个标准接口交互，完全屏蔽了底层实现的差异，这是 Spring AI 可移植性的核心体现。\n\n  * **`EmbeddingRequest`**: 这是一个结构化的请求对象，用于封装一次完整的向量化请求。它主要包含两部分内容：需要被向量化的**文本列表** (`List<String>`)，以及本次请求专用的**生成选项** (`EmbeddingOptions`)。\n\n  * **`EmbeddingResponse`**: 同样是一个结构化的响应对象，它封装了模型返回的全部信息。我们可以从中获取一个或多个 `Embedding` 实例，以及可能的元数据（例如本次调用消耗了多少 Token）。\n\n  * **`Embedding`**: 代表**单个输入文本**生成的最终结果。我们可以从这个对象中获取真正的向量数据（一个浮点数或双精度浮点数数组 `float[]` / `double[]`）和它在原始输入列表中的索引。\n\n  * **`EmbeddingOptions`**: 一个可跨模型移植的配置接口，定义了所有模型都应支持的通用参数，例如 `model`（指定模型名称）、`dimensions`（请求的向量维度）等。\n\n  * **`ZhiPuAiEmbeddingOptions`**: 这是特定于**智谱 AI** 的配置实现类，它继承自通用的 `EmbeddingOptions`，并增加了智谱 AI 独有的配置项，例如 `encoding-format`（指定返回的向量是 `float` 还是 `base64` 格式）。\n\n#### 7.2.2 `EmbeddingModel` 接口方法详解\n\n`EmbeddingModel` 接口为了开发者的便利，提供了多个层次的方法。\n\n| 方法签名 | 功能描述 |\n| :--- | :--- |\n| `EmbeddingResponse call(EmbeddingRequest request)` | 底层核心方法 |\n| `float[] embed(String text)` | 单文本向量化 |\n| `List<float[]> embed(List<String> texts)` | 多文本批量向量化 |\n| `float[] embed(Document document)` | `Document` 对象向量化 |\n\n  * **`call(EmbeddingRequest request)`**: 这是最底层的核心方法。它功能最强大，允许我们通过构建一个 `EmbeddingRequest` 对象来精细控制所有请求参数（例如，运行时动态指定模型或向量维度），并且其返回的 `EmbeddingResponse` 对象包含了最完整的响应信息，包括元数据，便于我们进行日志记录或成本核算。\n\n  * **`embed(String text)`**: 这是**最常用、最便捷**的方法。当我们只需要对单个文本进行快速向量化，并且不关心额外的元数据时，这个方法是我们的首选。它直接接收一个字符串，返回一个浮点数数组，非常直观。\n\n  * **`embed(List<String> texts)`**: 这是一个为性能优化的批量处理方法。当有多个文本需要一次性向量化时，我们应该使用它。它会将整个列表一次性发送给模型 API，相比于在 `for` 循环中多次调用 `embed(String text)`，网络开销更小，效率更高。\n\n  * **`embed(Document document)`**: 这是为 RAG 场景设计的专用方法。Spring AI 使用 `Document` 对象来表示经过处理的文档片段，此方法可以无缝地对 `Document` 对象进行向量化，简化了 RAG 流程中的代码。\n\n### 7.3 实战：构建文件向量化服务\n\n理论讲完，我们立刻动手。本节我们将为 AI-Copilot 项目添加一个全新的功能模块——**“文档向量化工具”**。用户可以通过一个友好的界面上传文本文件（如 `.txt`, `.md`），后端服务接收到文件后，会立即将其内容解析并转换为向量，最后在控制台打印出这个向量的前几位作为演示。\n\n#### 7.3.1 后端实现：文件处理与向量化API\n\n##### **第一步：依赖与配置**\n\n我们的项目已具备 `spring-boot-starter-web`，这已经包含了处理文件上传所需的一切。我们只需要确保 Embedding 模型的配置是正确的。本实战我们选用**智谱 AI** 的 `embedding-3` 模型。\n\n  * **`pom.xml`**: 首先，确保 `spring-ai-starter-model-zhipuai` 依赖已经存在于您的 `pom.xml` 文件中。\n\n    ```xml\n    <dependency>\n        <groupId>org.springframework.ai</groupId>\n        <artifactId>spring-ai-starter-model-zhipuai</artifactId>\n    </dependency>\n    ```\n\n  * **`application.yml`**: 接下来，在 `application.yml` 中确认智谱 AI 的配置。最关键的是通过 `spring.ai.model.embedding` 属性，全局指定使用 `zhipuai` 作为 `EmbeddingModel` 的实现。同时，我们配置好 Spring MVC 的文件上传参数。\n\n    ```yaml\n    spring:\n      # Spring AI 核心配置\n      ai:\n        model:\n          # 关键：全局指定使用 zhipuai 作为 EmbeddingModel 的实现\n          embedding: zhipuai\n        zhipuai:\n          # 强烈推荐使用环境变量来管理 API Key\n          api-key: ${ZHIPU_API_KEY}\n          embedding:\n            options:\n              # 指定默认使用的嵌入模型\n              model: embedding-3\n\n      # Spring MVC 文件上传配置\n      servlet:\n        multipart:\n          enabled: true           # 允许上传\n          max-file-size: 10MB     # 单个文件最大 10MB\n          max-request-size: 50MB  # 单次请求总文件最大 50MB\n    ```\n\n##### **第二步：创建文件处理服务 (`service/DocumentEmbeddingService.java`)**\n\n这个 Service 负责整个核心逻辑：接收文件 -\\> 校验文件 -\\> 解析文本 -\\> 调用模型 -\\> 返回结果。\n\n```java\n// src/main/java/com/copilot/aicopilotbackend/service/DocumentEmbeddingService.java\npackage com.copilot.aicopilotbackend.service;\n\nimport com.copilot.aicopilotbackend.exception.BusinessException;\nimport com.copilot.aicopilotbackend.exception.ErrorCode;\nimport lombok.RequiredArgsConstructor;\nimport lombok.extern.slf4j.Slf4j;\nimport org.springframework.ai.embedding.EmbeddingModel;\nimport org.springframework.stereotype.Service;\nimport org.springframework.util.StringUtils;\nimport org.springframework.web.multipart.MultipartFile;\n\nimport java.io.IOException;\nimport java.nio.charset.StandardCharsets;\nimport java.util.Arrays;\nimport java.util.List;\n\n/**\n * 文档向量化服务\n */\n@Slf4j\n@Service\n@RequiredArgsConstructor\npublic class DocumentEmbeddingService {\n\n    // 注入标准 EmbeddingModel 接口，而不是任何具体实现\n    private final EmbeddingModel embeddingModel;\n\n    // 定义允许上传的文件内容类型列表\n    private static final List<String> ALLOWED_CONTENT_TYPES = Arrays.asList(\n            "text/plain",       // .txt\n            "text/markdown"     // .md\n            // 未来可以轻松扩展以支持 "application/pdf" 等\n    );\n\n    /**\n     * 将上传的文件内容向量化，并返回处理结果信息。\n     *\n     * @param file 用户上传的文件\n     * @return 包含文件名和向量维度信息的成功消息字符串\n     */\n    public String embedDocument(MultipartFile file) {\n        // 步骤1: 验证文件是否有效\n        validateFile(file);\n\n        try {\n            // 步骤2: 从文件中安全地读取文本内容\n            String documentContent = new String(file.getBytes(), StandardCharsets.UTF_8);\n            if (!StringUtils.hasText(documentContent)) {\n                throw new BusinessException(ErrorCode.INVALID_PARAMETER, "文件内容不能为空。");\n            }\n            log.info("成功解析文件 \'{}\', 内容长度: {} 字符", file.getOriginalFilename(), documentContent.length());\n\n            // 步骤3: 调用注入的 EmbeddingModel 进行向量化\n            log.info("正在调用 Embedding 模型进行向量化...");\n            float[] vector = embeddingModel.embed(documentContent);\n            log.info("向量化成功！");\n\n            // 步骤4: 为了演示，在控制台清晰地打印向量的预览信息\n            log.info("--- 文件 \'{}\' 的向量预览 ---", file.getOriginalFilename());\n            log.info("维度 (Dimensions): {}", vector.length);\n            log.info("向量前5位 (Top 5 elements): {}", Arrays.toString(Arrays.copyOfRange(vector, 0, 5)));\n            log.info("------------------------------------");\n            \n            // 步骤5: 返回一个对用户友好的、包含关键信息的成功消息\n            return String.format("文件 \'%s\' 成功向量化！已生成一个 %d 维的向量。", file.getOriginalFilename(), vector.length);\n\n        } catch (IOException e) {\n            log.error("读取文件 \'{}\' 时发生 I/O 错误", file.getOriginalFilename(), e);\n            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "读取文件内容时发生错误，请检查文件是否损坏。");\n        } catch (Exception e) {\n            log.error("向量化文件 \'{}\' 时发生未知错误", file.getOriginalFilename(), e);\n            // 将异常重新抛出，交由全局异常处理器统一格式化为标准 API 响应\n            throw e; \n        }\n    }\n\n    /**\n     * 内部私有方法，用于校验上传的文件是否符合要求。\n     * @param file 待校验的文件\n     */\n    private void validateFile(MultipartFile file) {\n        if (file == null || file.isEmpty()) {\n            throw new BusinessException(ErrorCode.MISSING_PARAMETER, "未接收到任何文件，请选择一个文件上传。");\n        }\n        \n        String contentType = file.getContentType();\n        log.info("接收到文件 \'{}\'，内容类型: {}", file.getOriginalFilename(), contentType);\n\n        if (!ALLOWED_CONTENT_TYPES.contains(contentType)) {\n            throw new BusinessException(ErrorCode.INVALID_PARAMETER, "不支持的文件类型。目前仅支持 .txt 和 .md 文件。");\n        }\n    }\n}\n```\n\n##### **第三步：创建 API 控制器 (`controller/DocumentEmbeddingController.java`)**\n\n这个 Controller 非常简单，它的职责就是暴露一个 `POST` 端点来接收 `multipart/form-data` 格式的文件。\n\n```java\n// src/main/java/com/copilot/aicopilotbackend/controller/DocumentEmbeddingController.java\npackage com.copilot.aicopilotbackend.controller;\n\nimport com.copilot.aicopilotbackend.dto.response.ApiResponse;\nimport com.copilot.aicopilotbackend.service.DocumentEmbeddingService;\nimport lombok.RequiredArgsConstructor;\nimport org.springframework.web.bind.annotation.PostMapping;\nimport org.springframework.web.bind.annotation.RequestMapping;\nimport org.springframework.web.bind.annotation.RequestParam;\nimport org.springframework.web.bind.annotation.RestController;\nimport org.springframework.web.multipart.MultipartFile;\n\nimport java.util.Map;\n\n@RestController\n@RequestMapping("/api/v1/documents")\n@RequiredArgsConstructor\npublic class DocumentEmbeddingController {\n\n    private final DocumentEmbeddingService documentEmbeddingService;\n\n    /**\n     * 处理文档向量化的API端点\n     * @param file 前端通过 \'file\' 字段上传的文件\n     * @return 符合项目规范的、包含成功消息的 ApiResponse\n     */\n    @PostMapping("/embed")\n    public ApiResponse<Map<String, String>> embedDocument(@RequestParam("file") MultipartFile file) {\n        String resultMessage = documentEmbeddingService.embedDocument(file);\n        // 将成功信息包装成我们项目的标准API响应格式\n        return ApiResponse.success(Map.of("message", resultMessage));\n    }\n}\n```\n\n-----\n'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#7-Embedding-Models%EF%BC%9A%E4%B8%87%E7%89%A9%E7%9A%86%E5%8F%AF%E5%90%91%E9%87%8F%E5%8C%96"><span class="toc-number">1.</span> <span class="toc-text">7. Embedding Models：万物皆可向量化</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-Embedding-%E5%9C%A8%E6%88%91%E4%BB%AC%E9%A1%B9%E7%9B%AE%E4%B8%AD%E7%9A%84%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-number">1.1.</span> <span class="toc-text">7.1 Embedding 在我们项目中的应用场景</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-EmbeddingModel-API-%E6%A0%B8%E5%BF%83%E8%A7%A3%E6%9E%90"><span class="toc-number">1.2.</span> <span class="toc-text">7.2 EmbeddingModel API 核心解析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-2-1-%E6%A0%B8%E5%BF%83%E7%BB%84%E4%BB%B6%E6%A6%82%E8%A7%88"><span class="toc-number">1.2.1.</span> <span class="toc-text">7.2.1 核心组件概览</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-2-2-EmbeddingModel-%E6%8E%A5%E5%8F%A3%E6%96%B9%E6%B3%95%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.2.2.</span> <span class="toc-text">7.2.2 EmbeddingModel 接口方法详解</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-%E5%AE%9E%E6%88%98%EF%BC%9A%E6%9E%84%E5%BB%BA%E6%96%87%E4%BB%B6%E5%90%91%E9%87%8F%E5%8C%96%E6%9C%8D%E5%8A%A1"><span class="toc-number">1.3.</span> <span class="toc-text">7.3 实战：构建文件向量化服务</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-3-1-%E5%90%8E%E7%AB%AF%E5%AE%9E%E7%8E%B0%EF%BC%9A%E6%96%87%E4%BB%B6%E5%A4%84%E7%90%86%E4%B8%8E%E5%90%91%E9%87%8F%E5%8C%96API"><span class="toc-number">1.3.1.</span> <span class="toc-text">7.3.1 后端实现：文件处理与向量化API</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E4%BE%9D%E8%B5%96%E4%B8%8E%E9%85%8D%E7%BD%AE"><span class="toc-number">1.3.1.1.</span> <span class="toc-text">第一步：依赖与配置</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E5%88%9B%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%84%E7%90%86%E6%9C%8D%E5%8A%A1-service-DocumentEmbeddingService-java"><span class="toc-number">1.3.1.2.</span> <span class="toc-text">第二步：创建文件处理服务 (service/DocumentEmbeddingService.java)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E6%AD%A5%EF%BC%9A%E5%88%9B%E5%BB%BA-API-%E6%8E%A7%E5%88%B6%E5%99%A8-controller-DocumentEmbeddingController-java"><span class="toc-number">1.3.1.3.</span> <span class="toc-text">第三步：创建 API 控制器 (controller/DocumentEmbeddingController.java)</span></a></li></ol></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>