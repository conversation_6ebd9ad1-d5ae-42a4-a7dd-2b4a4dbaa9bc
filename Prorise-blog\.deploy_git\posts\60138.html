<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理进阶（十一）：第十一章：直播电商 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理进阶（十一）：第十一章：直播电商"><meta name="application-name" content="产品经理进阶（十一）：第十一章：直播电商"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="产品经理进阶（十一）：第十一章：直播电商"><meta property="og:url" content="https://prorise666.site/posts/60138.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="第十一章：直播电商欢迎来到第十一章。在过去的学习中，我们已经掌握了平台电商的稳固根基和分销电商的裂变增长。现在，我将带您进入一个能将“购物体验”和“销售转化”推向极致的全新领域——直播电商。这是一种将“实时互动”与“商品销售”无缝融合的、极具沉浸感的商业模式。  11.1 直播电商项目背景在我负责的"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta name="description" content="第十一章：直播电商欢迎来到第十一章。在过去的学习中，我们已经掌握了平台电商的稳固根基和分销电商的裂变增长。现在，我将带您进入一个能将“购物体验”和“销售转化”推向极致的全新领域——直播电商。这是一种将“实时互动”与“商品销售”无缝融合的、极具沉浸感的商业模式。  11.1 直播电商项目背景在我负责的"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/60138.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"产品经理进阶（十一）：第十一章：直播电商",postAI:"true",pageFillDescription:"第十一章：直播电商, 11.1 直播电商项目背景, 11.1.1 为什么需要直播电商？, 11.1.2 到底什么是直播电商？, 11.1.3 直播电商的三种主流模式, 11.2 直播电商的设计思路, 11.2.1 核心角色与需求分析, 11.2.2 核心业务流程梳理, 11.2.3 整体功能架构规划, 11.3 直播电商的产品设计, 11.3.1 平台端：规则的制定者与秩序的守护者, 11.3.2 商家端：商户的运营指挥中心, 11.3.3 用户端：主播与观众的互动舞台, 11.4 直播电商的关键技术, 11.4.1 核心概念：推流与拉流, 11.4.2 直播的技术全景图, 11.4.3 产品经理的技术选型：自研 vs. 第三方SDK第十一章直播电商欢迎来到第十一章在过去的学习中我们已经掌握了平台电商的稳固根基和分销电商的裂变增长现在我将带您进入一个能将购物体验和销售转化推向极致的全新领域直播电商这是一种将实时互动与商品销售无缝融合的极具沉浸感的商业模式直播电商项目背景在我负责的产品中每当要引入一个像直播这样重大的新功能时我都会先回归到最根本的商业问题上我们现有的模式遇到了什么瓶颈而这个新功能是否能成为破局的关键为什么需要直播电商传统的货架式电商本质是人找货用户带着目的来搜索比价这种模式在今天面临着越来越大的挑战流量越来越贵用户的注意力越来越分散单纯的打折促销也越来越难以打动他们我发现直播电商恰好能从三个方面完美地破解这些困局从花钱买流量到内容吸流量传统电商需要不断地投入巨额广告费去购买流量而直播电商特别是与关键意见领袖的合作是利用主播自带的影响力和内容创作能力将他的粉丝高效地吸引到我们的平台上来这是一种更聪明更具性价比的获客方式从理性对比到感性促单在传统电商的图文页用户的决策链路相对较长消费也更趋于理性但在直播间里主播通过现场试用实时互动和限时限量的话术能够营造出一种不买就亏了的紧迫感和热烈氛围这极大地激发了用户的感性消费和冲动购买转化率自然远超平时从静态浏览到沉浸互动图文详情页是静态的单向的而直播是一种所见即所得的沉浸式体验我可以实时看到衣服的上身效果可以要求主播展示产品的某个细节可以通过弹幕与成千上万的人交流这种丰富立体的购物体验是传统电商无法比拟的到底什么是直播电商所以到底什么是直播电商在我看来直播电商的核心是商业模式从以货为中心向以人为中心的彻底转变它不再是冰冷的货架而是基于一个活生生的你所信任或喜爱的主播来建立交易消费者购买的不仅仅是商品本身更是对这个主播的品味专业度或个人魅力的信任票这种以信任为前提的商业模式其根基依然是电商但能量却被放大了无数倍我们想象一个真实的场景当主播在镜头前一边讲解着手机的各项参数一边实时回答着待机时间多久拍照效果怎么样这些弹幕提问并在几万人的共同见证下喊出上链接时那一刻它已经超越了单纯的卖货变成了一场极具参与感的线上狂欢这就是直播电商的魅力直播电商的三种主流模式理解了直播电商的价值和内核后作为产品经理我的下一步就是从顶层设计上思考我们平台到底要做哪一种在我的实践中通常会遇到三种主流的业务模式带货模式这是最典型爆发力最强的一种如果我的业务目标是在短期内快速提升品牌知名度引爆一款单品的销量那么与外部的头部合作无疑是最佳选择他们带来海量粉丝我们提供优质商品这是一场强强联合店铺直播模式店播这是一种更着眼于长期健康的模式我把它看作是平台必须为商家提供的基础设施我们赋能平台上的商家让他们可以在自己的一亩三分地里由老板或者店员自己出镜进行常态化的直播这不追求一夜爆火而是为了帮助商家更好地维护自己的老客沉淀私域流量是一种细水长流的生意直播分销模式这是一种最大化利用平台生态的极具想象力的模式它将直播和分销结合允许我们的普通用户申请成为分销主播平台提供统一的货盘他们只需要开播去推广就能赚取佣金这相当于将我们平台上成千上万的用户都变成了我们行走的会说话的销售渠道直播电商的设计思路在上一节我们明确了为什么要做直播电商现在我的角色就要从一个业务分析师切换到一个产品架构师在真正开始画原型写文档之前我必须先搭建起整个产品的骨架这个过程我称之为设计思路的梳理核心角色与需求分析要设计一个好的系统我首先要清晰地定义出这个系统里都有谁他们分别想做什么这就是角色与需求分析在直播电商这个场景里我识别出了四个核心角色普通用户他们是观众是消费者他们的核心诉求是逛得开心买得方便店铺主播他们是表演者是销售员他们是直播间的灵魂核心诉求是互动热烈卖得更多店铺运营他们是幕后管理者他们负责申请开通直播管理直播计划处理订单等核心诉求是管理高效掌控全局平台这就是我们自己我们的核心诉求是秩序井然生态繁荣需要有最高的管理权限为了确保不遗漏任何关键功能我会将这些角色的核心需求整理成一张清晰的列表作为我们后续产品设计的需求清单角色我的解读核心需求点普通用户能流畅地观看直播并与主播进行实时互动如发弹幕点赞能在直播间里方便地查看正在讲解的商品并快速下单购买店铺运营需要有一个后台可以向平台方提交开通店铺直播功能的申请对于已经创建或正在直播的场次需要有管理和控制的能力店铺主播能够在内轻松地发起一场直播并能便捷地将自己店铺的商品上架到直播间进行讲解在直播过程中能看到观众的互动并进行回应以提升直播间热度平台作为系统的所有者我们需要有能力对所有店铺的直播间进行统一的管理和监控确保合规核心业务流程梳理当我把这些零散的需求点都定义清楚后下一步就是用一条流程线将它们串联起来形成一个完整的业务闭环我需要确保不同角色之间的协作是顺畅的我通常会用一张泳道图来可视化这个核心流程让团队里的每一个人都能清晰地看到自己负责的部分在整个业务链条中所处的位置这个流程是这样运转的一切的起点是店铺运营向平台提交了开通直播的申请平台审核通过后该店铺就获得了直播的能力店铺主播现在可以正式发起直播并将准备好的上架商品海量的普通用户被吸引进入直播间观看直播并在主播的带动下完成下单最后订单流转到店铺运营那里由他们进行确认订单和后续的履约发货你看通过这样一张流程图一个完整的多角色协作的业务故事就被清晰地呈现了出来整体功能架构规划有了角色和流程我就可以在脑海中勾勒出整个产品的功能架构蓝图了我会把需要开发的功能按照使用者的不同划分到不同的端里去我将整个直播电商系统规划为三大功能模块用户端这是我们产品的主阵地承载了最多的功能它既包含了普通用户的观看互动购买功能也包含了主播开播管理商品等核心功能在这里我暂时将主播端和用户端合并在一起考虑因为它们都发生在同一个内很多界面是共通的商家端这就是我为店铺运营人员所设计的后台管理系统他们在这里申请权限管理直播间平台端这是我们自己使用的上帝后台在这里我们可以管理所有商家和直播间设定平台的规则至此直播电商的设计思路就已经非常清晰了我们明确了为谁设计核心角色设计什么需求列表以及它们如何协同工作业务流程和功能架构这个清晰的骨架将是我们下一节进行具体产品功能设计的坚实基础直播电商的产品设计在我们梳理清楚了设计思路明确了要做什么之后现在就到了将蓝图转化为具体页面的阶段作为产品经理我会兵分三路同时推进平台端商家端用户端这三个关键阵地的产品设计平台端规则的制定者与秩序的守护者我设计平台后台的唯一原则就是权责对等平台作为整个直播生态的所有者必须拥有至高无上的管理权限来确保整个业务健康有序地运转这主要体现在两个方面管店铺和管直播直播店铺管理我们必须有一个准入机制并非所有商家都有资格开通直播否则劣质的直播内容会摧毁用户体验因此我需要为平台的运营同事设计一个强大的店铺审核后台这个后台的核心就是对资格状态的精细化管理运营人员在这里可以清晰地看到所有申请店铺的列表并进行审核查看取消资格或恢复资格等操作每一个按钮都代表了平台的一种管理权力是确保直播商家质量的第一道防线直播间管理除了管人店铺我们更要管事直播平台需要能够监控到所有正在发生和已经发生的直播在这个界面我最看重的是操作栏里的结束按钮这代表了平台的干预权当一场直播出现违规内容或其他紧急情况时平台必须有能力在第一时间从最高权限上强制将其关停这是我们作为平台方必须承担的责任也是保障平台安全的生命线商家端商户的运营指挥中心对于商家而言直播是他们最重要的营销工具和销售渠道之一因此我为他们设计的商家后台必须像一个作战指挥室专业高效功能完备申请与配置商家的直播之旅始于申请我需要为他们提供一个清晰的申请入口并明确告知他们需要满足的条件这既是功能也是一种规则的宣导当商家获得资格后他们就需要一个专业的直播间管理后台在这里他们可以创建编辑管理自己所有的直播场次我设计的核心思路是状态驱动你会发现一场直播在未开始直播中已结束等不同状态下商家可以进行的操作是完全不同的比如未开始的可以编辑而已结束的只能查看数据这种精细化的权限控制能有效防止商家的误操作数据复盘直播的魅力在于可以通过数据不断优化一场直播结束后商家最关心的问题就是这场直播效果怎么样如果我不能回答这个问题那么我设计的这个功能就是失败的因此我必须为商家提供一个详尽的数据战报这个战报至少要包含三类核心数据流量数据有多少人看最高同时有多少人在线涨了多少粉互动数据谁给我刷了礼物价值多少带货数据卖了什么商品卖了多少件只有提供了这些数据商家才能进行有效的复盘我们的直播功能才算真正为商家创造了价值用户端主播与观众的互动舞台用户端是整个直播产品的门面是所有用户能直接感知到的地方我把它分为两条主线来设计主播的开播之旅和观众的看播之旅主播的开播之旅我设计主播端的核心理念是简单高效所见即所得主播在手机方寸之间就要完成一场直播的全部准备工作第一步设置直播信息一场直播的门面就是封面和标题我必须让主播可以轻松地上传一张吸引人的封面图并起一个有噱头的标题此外立即开始和预定时间这两个选项也至关重要预定时间能让主播提前预告进行蓄水这是专业运营的必备功能第二步关联带货商品这是直播电商的灵魂我需要为主播提供一个极为便捷的选品流程让他们能从自己的店铺商品库中快速勾选出本场要带货的商品并添加到直播间的小黄车里第三步直播中的掌控当直播开始后主播的手机屏幕就变成了他的驾驶舱美颜滤镜镜头翻转这些是基础功能能让主播呈现出最好的状态更重要的是他需要有管理商品与观众互动等一系列工具观众的看播之旅我设计观众端的核心理念是沉浸体验无缝下单我要让用户看得开心买得顺滑核心互动界面用户进入直播间首先看到的是一个集视频画面和实时互动区于一体的界面下方的聊天弹幕区是营造社区感和热闹氛围的关键让用户感觉自己不是一个人在看而是在和成千上万的人一起云逛街商品浏览与购买当主播开始介绍商品时我必须为用户提供一个清晰无干扰的商品展示区这个区域通常在屏幕下方以列表形式呈现用户点击后无需跳出直播间就能查看商品详情并完成购买这里的设计要点在于商品状态的实时同步当主播讲解某个商品时它的状态可能是待上架当主播喊出上链接时它会立刻变为马上抢而当商品售罄时它又会变为已抢完这种实时的状态变化是制造稀缺感激发用户下单欲望的关键所在直播电商的关键技术在完成了产品的长相用户界面和骨架功能逻辑设计之后我必须和技术团队坐下来探讨它的内脏和血脉也就是实现这一切所需要的技术作为产品经理我不需要会写代码但我必须理解其核心原理这能让我评估技术方案的可行性预估开发成本并在关键的技术选型上与团队进行有质量的对话核心概念推流与拉流整个复杂的直播技术可以被简化为两个最核心的动作推流和拉流推流我把它理解为上传直播的过程它指的是主播的手机端直播端采集自己的声音和画面并将其像水流一样推送到云端服务器的行为拉流我把它理解为下载直播的过程它指的是成千上万的观众从云端服务器那里将直播内容拉取到自己手机上进行观看的行为一次流畅的直播体验本质上就是一次高质量的推和成千上万次高质量的拉所共同构成的直播的技术全景图在推与拉之间是一个庞大而精密的后台服务系统为了让团队清晰地理解这个系统我通常会展示这样一张技术架构图我可以带你走一遍这个流程主播端推送方一切的源头是主播我们会集成一个推流它就像一个专业的打包和邮寄工具负责将主播的音视频内容采集压缩然后通过推流节点发送到最近的云服务器服务端处理中心这是直播的中央厨房直播服务器接收到主播的推流后会立刻进行一系列的加工处理例如转码服务为了适配不同观众的网络状况服务器会将原始视频流实时转码成高清标清流畅等多个版本录制服务服务器会将整场直播录制成一个视频文件方便用户随时回顾截图服务自动截取直播的精彩瞬间作为封面安全服务对直播内容进行实时监控防止违规观众端拉取方经过处理的直播流会被分发到全球的分发节点这就像是遍布全球的前置仓库当观众打开时他们的播放会自动连接到离他们最近的节点去拉取直播内容这样无论用户身在何处都能获得低延迟高流畅的观看体验产品经理的技术选型自研第三方了解到这套系统的复杂性后一个关键的决策就摆在了我的面前这套系统我们是自己从零开始搭建还是直接采购成熟的方案我的答案以及我给几乎所有公司的建议都是果断选择第三方原因很简单作为一家电商公司我们的核心竞争力在于交易而非底层视频技术自研一套稳定高并发低延迟的全球直播系统其投入是天文数字聪明的产品决策是站在巨人的肩膀上市面上有非常多专业成熟的云服务商提供完整的视频直播解决方案我们只需要将他们的集成到我们的产品中就能在短时间内以可控的成本上线高质量的直播功能在做技术选型时我会和技术负责人一起重点考察几家头部厂商例如阿里云它的视频直播阿里云直播服务服务在国内市场份额巨大技术稳定文档齐全网易云信网易云信网易云信直播服务在社交娱乐领域的解决方案经验丰富尤其在即时通讯和音视频的结合上很有优势腾讯云腾讯云的互动直播解决方案腾讯云直播服务尤其强调互动连麦等场景非常适合需要强社交属性的直播玩法最终我们会根据他们的产品性能功能丰富度服务支持以及价格等多个维度综合评估选择最适合我们当前业务需求的合作伙伴",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-25 11:05:48",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E4%B8%80%E7%AB%A0%EF%BC%9A%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86"><span class="toc-text">第十一章：直播电商</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#11-1-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E9%A1%B9%E7%9B%AE%E8%83%8C%E6%99%AF"><span class="toc-text">11.1 直播电商项目背景</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-1-%E4%B8%BA%E4%BB%80%E4%B9%88%E9%9C%80%E8%A6%81%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%EF%BC%9F"><span class="toc-text">11.1.1 为什么需要直播电商？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-2-%E5%88%B0%E5%BA%95%E4%BB%80%E4%B9%88%E6%98%AF%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%EF%BC%9F"><span class="toc-text">11.1.2 到底什么是直播电商？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-3-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E4%B8%89%E7%A7%8D%E4%B8%BB%E6%B5%81%E6%A8%A1%E5%BC%8F"><span class="toc-text">11.1.3 直播电商的三种主流模式</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#11-2-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-text">11.2 直播电商的设计思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-1-%E6%A0%B8%E5%BF%83%E8%A7%92%E8%89%B2%E4%B8%8E%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-text">11.2.1 核心角色与需求分析</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-2-%E6%A0%B8%E5%BF%83%E4%B8%9A%E5%8A%A1%E6%B5%81%E7%A8%8B%E6%A2%B3%E7%90%86"><span class="toc-text">11.2.2 核心业务流程梳理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-3-%E6%95%B4%E4%BD%93%E5%8A%9F%E8%83%BD%E6%9E%B6%E6%9E%84%E8%A7%84%E5%88%92"><span class="toc-text">11.2.3 整体功能架构规划</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#11-3-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">11.3 直播电商的产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-1-%E5%B9%B3%E5%8F%B0%E7%AB%AF%EF%BC%9A%E8%A7%84%E5%88%99%E7%9A%84%E5%88%B6%E5%AE%9A%E8%80%85%E4%B8%8E%E7%A7%A9%E5%BA%8F%E7%9A%84%E5%AE%88%E6%8A%A4%E8%80%85"><span class="toc-text">11.3.1 平台端：规则的制定者与秩序的守护者</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-2-%E5%95%86%E5%AE%B6%E7%AB%AF%EF%BC%9A%E5%95%86%E6%88%B7%E7%9A%84%E8%BF%90%E8%90%A5%E6%8C%87%E6%8C%A5%E4%B8%AD%E5%BF%83"><span class="toc-text">11.3.2 商家端：商户的运营指挥中心</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-3-%E7%94%A8%E6%88%B7%E7%AB%AF%EF%BC%9A%E4%B8%BB%E6%92%AD%E4%B8%8E%E8%A7%82%E4%BC%97%E7%9A%84%E4%BA%92%E5%8A%A8%E8%88%9E%E5%8F%B0"><span class="toc-text">11.3.3 用户端：主播与观众的互动舞台</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#11-4-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E5%85%B3%E9%94%AE%E6%8A%80%E6%9C%AF"><span class="toc-text">11.4 直播电商的关键技术</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-4-1-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%EF%BC%9A%E6%8E%A8%E6%B5%81%E4%B8%8E%E6%8B%89%E6%B5%81"><span class="toc-text">11.4.1 核心概念：推流与拉流</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-4-2-%E7%9B%B4%E6%92%AD%E7%9A%84%E6%8A%80%E6%9C%AF%E5%85%A8%E6%99%AF%E5%9B%BE"><span class="toc-text">11.4.2 直播的技术全景图</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-4-3-%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E7%9A%84%E6%8A%80%E6%9C%AF%E9%80%89%E5%9E%8B%EF%BC%9A%E8%87%AA%E7%A0%94-vs-%E7%AC%AC%E4%B8%89%E6%96%B9SDK"><span class="toc-text">11.4.3 产品经理的技术选型：自研 vs. 第三方SDK</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理进阶（十一）：第十一章：直播电商</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-24T18:13:45.000Z" title="发表于 2025-07-25 02:13:45">2025-07-25</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.661Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">5.4k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>15分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理进阶（十一）：第十一章：直播电商"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/60138.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/60138.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理进阶（十一）：第十一章：直播电商</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-24T18:13:45.000Z" title="发表于 2025-07-25 02:13:45">2025-07-25</time><time itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.661Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></header><div id="postchat_postcontent"><h1 id="第十一章：直播电商"><a href="#第十一章：直播电商" class="headerlink" title="第十一章：直播电商"></a>第十一章：直播电商</h1><p>欢迎来到第十一章。在过去的学习中，我们已经掌握了平台电商的稳固根基和分销电商的裂变增长。现在，我将带您进入一个能将“<strong>购物体验</strong>”和“<strong>销售转化</strong>”推向极致的全新领域——<strong>直播电商</strong>。这是一种将“<strong>实时互动</strong>”与“<strong>商品销售</strong>”无缝融合的、极具沉浸感的商业模式。</p><hr><h2 id="11-1-直播电商项目背景"><a href="#11-1-直播电商项目背景" class="headerlink" title="11.1 直播电商项目背景"></a>11.1 直播电商项目背景</h2><p>在我负责的产品中，每当要引入一个像“直播”这样重大的新功能时，我都会先回归到最根本的商业问题上：我们现有的模式遇到了什么瓶颈？而这个新功能，是否能成为破局的关键？</p><h3 id="11-1-1-为什么需要直播电商？"><a href="#11-1-1-为什么需要直播电商？" class="headerlink" title="11.1.1 为什么需要直播电商？"></a>11.1.1 为什么需要直播电商？</h3><p>传统的货架式电商，本质是“人找货”，用户带着目的来搜索、比价。这种模式在今天面临着越来越大的挑战：流量越来越贵，用户的注意力越来越分散，单纯的打折促销也越来越难以打动他们。</p><p>我发现，直播电商恰好能从三个方面，完美地破解这些困局。</p><ol><li><strong>从“花钱买流量”到“内容吸流量”</strong>：传统电商需要不断地投入巨额广告费，去购买流量。而直播电商，特别是与KOL（关键意见领袖）的合作，是利用主播自带的影响力和内容创作能力，将他的粉丝高效地吸引到我们的平台上来。这是一种更聪明、更具性价比的获客方式。</li><li><strong>从“理性对比”到“感性促单”</strong>：在传统电商的图文页，用户的决策链路相对较长，消费也更趋于理性。但在直播间里，主播通过现场试用、实时互动和限时限量的话术，能够营造出一种“不买就亏了”的紧迫感和热烈氛围，这极大地激发了用户的感性消费和冲动购买，转化率自然远超平时。</li><li><strong>从“静态浏览”到“沉浸互动”</strong>：图文详情页是静态的、单向的。而直播，是一种“所见即所得”的沉浸式体验。我可以实时看到衣服的上身效果，可以要求主播展示产品的某个细节，可以通过弹幕与成千上万的人交流。这种丰富、立体的购物体验，是传统电商无法比拟的。</li></ol><h3 id="11-1-2-到底什么是直播电商？"><a href="#11-1-2-到底什么是直播电商？" class="headerlink" title="11.1.2 到底什么是直播电商？"></a>11.1.2 到底什么是直播电商？</h3><p>所以，到底什么是直播电商？</p><p>在我看来，直播电商的核心，是<strong>商业模式从“以货为中心”向“以人为中心”的彻底转变</strong>。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724194744839.png" alt="image-20250724194744839"></p><p>它不再是冰冷的货架，而是基于一个活生生的、你所信任或喜爱的主播，来建立交易。消费者购买的，不仅仅是商品本身，更是对这个主播的品味、专业度或个人魅力的“信任票”。这种以信任为前提的商业模式，其根基依然是电商，但能量却被放大了无数倍。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724194842043.png" alt="image-20250724194842043"></p><p>我们想象一个真实的场景：当主播在镜头前，一边讲解着手机的各项参数，一边实时回答着“待机时间多久？”、“拍照效果怎么样？”这些弹幕提问，并在几万人的共同见证下，喊出“3、2、1，上链接！”时，那一刻，它已经超越了单纯的“卖货”，变成了一场极具参与感的线上狂欢。这就是直播电商的魅力。</p><h3 id="11-1-3-直播电商的三种主流模式"><a href="#11-1-3-直播电商的三种主流模式" class="headerlink" title="11.1.3 直播电商的三种主流模式"></a>11.1.3 直播电商的三种主流模式</h3><p>理解了直播电商的价值和内核后，作为产品经理，我的下一步就是从顶层设计上，思考我们平台到底要做哪一种。在我的实践中，通常会遇到三种主流的业务模式。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195004429.png" alt="image-20250724195004429"></p><ol><li><p><strong>KOL带货模式</strong><br>这是最典型、爆发力最强的一种。如果我的业务目标是在短期内快速提升品牌知名度、引爆一款单品的销量，那么与外部的头部KOL合作，无疑是最佳选择。他们带来海量粉丝，我们提供优质商品，这是一场强强联合。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195028134.png" alt="image-20250724195028134"></p></li><li><p><strong>店铺直播模式（店播）</strong><br>这是一种更着眼于长期、健康的模式。我把它看作是平台必须为商家提供的“基础设施”。我们赋能平台上的商家，让他们可以在自己的“一亩三分地”里，由老板或者店员自己出镜，进行常态化的直播。这不追求一夜爆火，而是为了帮助商家更好地维护自己的老客、沉淀私域流量，是一种细水长流的生意。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195103702.png" alt="image-20250724195103702"></p></li><li><p><strong>直播分销模式</strong><br>这是一种最大化利用平台生态的、极具想象力的模式。它将直播和分销结合，允许我们的普通用户申请成为“分销主播”。平台提供统一的货盘，他们只需要开播去推广，就能赚取佣金。这相当于将我们平台上成千上万的用户，都变成了我们“行走的、会说话的”销售渠道。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195113471.png" alt="image-20250724195113471"></p></li></ol><hr><h2 id="11-2-直播电商的设计思路"><a href="#11-2-直播电商的设计思路" class="headerlink" title="11.2 直播电商的设计思路"></a>11.2 直播电商的设计思路</h2><p>在上一节，我们明确了“为什么”要做直播电商。现在，我的角色就要从一个业务分析师，切换到一个产品架构师。在真正开始画原型、写文档之前，我必须先搭建起整个产品的“骨架”。这个过程，我称之为“设计思路”的梳理。</p><h3 id="11-2-1-核心角色与需求分析"><a href="#11-2-1-核心角色与需求分析" class="headerlink" title="11.2.1 核心角色与需求分析"></a>11.2.1 核心角色与需求分析</h3><p>要设计一个好的系统，我首先要清晰地定义出：<strong>这个系统里，都有谁？他们分别想做什么？</strong> 这就是角色与需求分析。在直播电商这个场景里，我识别出了四个核心角色。</p><ol><li><strong>普通用户</strong>：他们是观众，是消费者。他们的核心诉求是“逛得开心，买得方便”。</li><li><strong>店铺主播</strong>：他们是表演者，是销售员。他们是直播间的灵魂，核心诉求是“互动热烈，卖得更多”。</li><li><strong>店铺运营</strong>：他们是幕后管理者。他们负责申请开通直播、管理直播计划、处理订单等。核心诉求是“管理高效，掌控全局”。</li><li><strong>平台</strong>：这就是我们自己。我们的核心诉求是“秩序井然，生态繁荣”，需要有最高的管理权限。</li></ol><p>为了确保不遗漏任何关键功能，我会将这些角色的核心需求，整理成一张清晰的列表，作为我们后续产品设计的“需求清单”。</p><table><thead><tr><th align="left"><strong>角色</strong></th><th align="left"><strong>我的解读（核心需求点）</strong></th></tr></thead><tbody><tr><td align="left"><strong>普通用户</strong></td><td align="left">1. 能流畅地观看直播，并与主播进行实时互动（如发弹幕、点赞）。<br>2. 能在直播间里，方便地查看正在讲解的商品，并快速下单购买。</td></tr><tr><td align="left"><strong>店铺运营</strong></td><td align="left">1. 需要有一个后台，可以向平台方，提交开通“店铺直播”功能的申请。<br>2. 对于已经创建或正在直播的场次，需要有管理和控制的能力。</td></tr><tr><td align="left"><strong>店铺主播</strong></td><td align="left">1. 能够在App内，轻松地发起一场直播，并能便捷地将自己店铺的商品，上架到直播间进行讲解。<br>2. 在直播过程中，能看到观众的互动，并进行回应，以提升直播间热度。</td></tr><tr><td align="left"><strong>平台</strong></td><td align="left">作为系统的所有者，我们需要有能力对所有店铺的直播间，进行统一的管理和监控，确保合规。</td></tr></tbody></table><h3 id="11-2-2-核心业务流程梳理"><a href="#11-2-2-核心业务流程梳理" class="headerlink" title="11.2.2 核心业务流程梳理"></a>11.2.2 核心业务流程梳理</h3><p>当我把这些零散的需求点都定义清楚后，下一步，就是用一条“流程线”，将它们串联起来，形成一个完整的业务闭环。我需要确保不同角色之间的协作是顺畅的。</p><p>我通常会用一张“泳道图”来可视化这个核心流程，让团队里的每一个人都能清晰地看到，自己负责的部分，在整个业务链条中所处的位置。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195621927.png" alt="image-20250724195621927"></p><p>这个流程是这样运转的：</p><ol><li>一切的起点，是“<strong>店铺运营</strong>”向“<strong>平台</strong>”提交了开通直播的申请。</li><li>“<strong>平台</strong>”审核通过后，该店铺就获得了直播的能力。</li><li>“<strong>店铺主播</strong>”现在可以正式“<strong>发起直播</strong>”，并将准备好的“<strong>上架商品</strong>”。</li><li>海量的“<strong>普通用户</strong>”被吸引进入直播间“<strong>观看直播</strong>”，并在主播的带动下完成“<strong>下单</strong>”。</li><li>最后，订单流转到“<strong>店铺运营</strong>”那里，由他们进行“<strong>确认订单</strong>”和后续的履约发货。</li></ol><p>你看，通过这样一张流程图，一个完整的、多角色协作的业务故事，就被清晰地呈现了出来。</p><h3 id="11-2-3-整体功能架构规划"><a href="#11-2-3-整体功能架构规划" class="headerlink" title="11.2.3 整体功能架构规划"></a>11.2.3 整体功能架构规划</h3><p>有了角色和流程，我就可以在脑海中，勾勒出整个产品的“功能架构蓝图”了。</p><p>我会把需要开发的功能，按照使用者的不同，划分到不同的“端”里去。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195732918.png" alt="image-20250724195732918"></p><p>我将整个直播电商系统，规划为三大功能模块：</p><ul><li><strong>用户端</strong>：这是我们产品的主阵地，承载了最多的功能。它既包含了“<strong>普通用户</strong>”的观看、互动、购买功能，也包含了“<strong>主播</strong>”开播、管理商品等核心功能。<em>（在这里，我暂时将主播端和用户端合并在一起考虑，因为它们都发生在同一个App内，很多界面是共通的）</em>。</li><li><strong>商家端</strong>：这就是我为“<strong>店铺运营</strong>”人员，所设计的后台管理系统。他们在这里申请权限、管理直播间。</li><li><strong>平台端</strong>：这是我们自己使用的“<strong>上帝后台</strong>”。在这里，我们可以管理所有商家和直播间，设定平台的规则。</li></ul><p>至此，直播电商的设计思路就已经非常清晰了。我们明确了“<strong>为谁设计</strong>”（核心角色）、“<strong>设计什么</strong>”（需求列表）、以及“<strong>它们如何协同工作</strong>”（业务流程和功能架构）。这个清晰的骨架，将是我们下一节进行具体产品功能设计的坚实基础。</p><hr><h2 id="11-3-直播电商的产品设计"><a href="#11-3-直播电商的产品设计" class="headerlink" title="11.3 直播电商的产品设计"></a>11.3 直播电商的产品设计</h2><p>在我们梳理清楚了设计思路、明确了“要做什么”之后，现在，就到了将蓝图转化为具体页面的阶段。作为产品经理，我会兵分三路，同时推进<strong>平台端、商家端、用户端</strong>这三个关键阵地的产品设计。</p><h3 id="11-3-1-平台端：规则的制定者与秩序的守护者"><a href="#11-3-1-平台端：规则的制定者与秩序的守护者" class="headerlink" title="11.3.1 平台端：规则的制定者与秩序的守护者"></a>11.3.1 平台端：规则的制定者与秩序的守护者</h3><p>我设计平台后台的唯一原则，就是“<strong>权责对等</strong>”。平台作为整个直播生态的“所有者”，必须拥有至高无上的管理权限，来确保整个业务健康、有序地运转。这主要体现在两个方面：<strong>管店铺</strong>和<strong>管直播</strong>。</p><p><strong>1. 直播店铺管理</strong></p><p>我们必须有一个“准入机制”。并非所有商家都有资格开通直播，否则劣质的直播内容会摧毁用户体验。因此，我需要为平台的运营同事，设计一个强大的店铺审核后台。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095520168.png" alt="image-20250725095520168"></p><p>这个后台的核心，就是对“<strong>资格状态</strong>”的精细化管理。运营人员在这里，可以清晰地看到所有申请店铺的列表，并进行“<strong>审核</strong>”、“<strong>查看</strong>”、“<strong>取消资格</strong>”或“<strong>恢复资格</strong>”等操作。每一个按钮，都代表了平台的一种管理权力，是确保直播商家质量的第一道防线。</p><p><strong>2. 直播间管理</strong></p><p>除了管“人”（店铺），我们更要管“事”（直播）。平台需要能够监控到所有正在发生和已经发生的直播。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095618366.png" alt="image-20250725095618366"></p><p>在这个界面，我最看重的，是“<strong>操作</strong>”栏里的“<strong>结束</strong>”按钮。这代表了平台的“<strong>干预权</strong>”。当一场直播出现违规内容或其他紧急情况时，平台必须有能力在第一时间，从最高权限上，强制将其关停。这是我们作为平台方，必须承担的责任，也是保障平台安全的生命线。</p><h3 id="11-3-2-商家端：商户的运营指挥中心"><a href="#11-3-2-商家端：商户的运营指挥中心" class="headerlink" title="11.3.2 商家端：商户的运营指挥中心"></a>11.3.2 商家端：商户的运营指挥中心</h3><p>对于商家而言，直播是他们最重要的营销工具和销售渠道之一。因此，我为他们设计的商家后台，必须像一个“<strong>作战指挥室</strong>”，专业、高效、功能完备。</p><p><strong>1. 申请与配置</strong></p><p>商家的直播之旅，始于“<strong>申请</strong>”。我需要为他们提供一个清晰的申请入口，并明确告知他们需要满足的条件，这既是功能，也是一种规则的宣导。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095657234.png" alt="image-20250725095657234"></p><p>当商家获得资格后，他们就需要一个专业的“<strong>直播间管理</strong>”后台。在这里，他们可以创建、编辑、管理自己所有的直播场次。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095735548.png" alt="image-20250725095735548"></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100501358.png" alt="image-20250725100501358"></p><p>我设计的核心思路是“<strong>状态驱动</strong>”。你会发现，一场直播在“未开始”、“直播中”、“已结束”等不同状态下，商家可以进行的操作是完全不同的。比如，“未开始”的可以“编辑”，而“已结束”的只能“查看数据”。这种精细化的权限控制，能有效防止商家的误操作。</p><p><strong>2. 数据复盘</strong></p><p>直播的魅力，在于可以通过数据不断优化。一场直播结束后，商家最关心的问题就是：“<strong>这场直播效果怎么样？</strong>”。如果我不能回答这个问题，那么我设计的这个功能就是失败的。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095756930.png" alt="image-20250725095756930"></p><p>因此，我必须为商家提供一个详尽的“<strong>数据战报</strong>”。这个战报至少要包含三类核心数据：</p><ul><li><strong>流量数据</strong>：有多少人看？最高同时有多少人在线？涨了多少粉？</li><li><strong>互动数据</strong>：谁给我刷了礼物？价值多少？</li><li><strong>带货数据</strong>：卖了什么商品？卖了多少件？</li></ul><p>只有提供了这些数据，商家才能进行有效的复盘，我们的直播功能才算真正为商家创造了价值。</p><h3 id="11-3-3-用户端：主播与观众的互动舞台"><a href="#11-3-3-用户端：主播与观众的互动舞台" class="headerlink" title="11.3.3 用户端：主播与观众的互动舞台"></a>11.3.3 用户端：主播与观众的互动舞台</h3><p>用户端，是整个直播产品的“门面”，是所有用户能直接感知到的地方。我把它分为两条主线来设计：<strong>主播的“开播”之旅</strong>，和<strong>观众的“看播”之旅</strong>。</p><p><strong>1. 主播的开播之旅</strong></p><p>我设计主播端的核心理念是“<strong>简单高效，所见即所得</strong>”。主播在手机方寸之间，就要完成一场直播的全部准备工作。</p><ul><li><p><strong>第一步：设置直播信息</strong><br>一场直播的“门面”，就是封面和标题。我必须让主播可以轻松地上传一张吸引人的封面图，并起一个有噱头的标题。此外，“<strong>立即开始</strong>”和“<strong>预定时间</strong>”这两个选项也至关重要。“预定时间”能让主播提前预告，进行蓄水，这是专业运营的必备功能。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100613314.png" alt="image-20250725100613314"></p></li></ul><p>​	<img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100636104.png" alt="image-20250725100636104"></p><ul><li><p><strong>第二步：关联带货商品</strong><br>这是直播电商的“灵魂”。我需要为主播提供一个极为便捷的“<strong>选品</strong>”流程，让他们能从自己的店铺商品库中，快速勾选出本场要带货的商品，并添加到直播间的“小黄车”里。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100708800.png" alt="image-20250725100708800"></p></li><li><p><strong>第三步：直播中的掌控</strong><br>当直播开始后，主播的手机屏幕就变成了他的“<strong>驾驶舱</strong>”。美颜、滤镜、镜头翻转这些是基础功能，能让主播呈现出最好的状态。更重要的是，他需要有管理商品、与观众互动等一系列工具。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100747775.png" alt="image-20250725100747775"></p></li></ul><p><strong>2. 观众的看播之旅</strong></p><p>我设计观众端的核心理念是“<strong>沉浸体验，无缝下单</strong>”。我要让用户看得开心，买得顺滑。</p><ul><li><p><strong>核心互动界面</strong><br>用户进入直播间，首先看到的是一个集“<strong>视频画面</strong>”和“<strong>实时互动区</strong>”于一体的界面。下方的聊天弹幕区是营造社区感和热闹氛围的关键，让用户感觉自己不是一个人在看，而是在和成千上万的人一起“云逛街”。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100818156.png" alt="image-20250725100818156"></p></li><li><p><strong>商品浏览与购买</strong><br>当主播开始介绍商品时，我必须为用户提供一个清晰、无干扰的商品展示区。这个区域通常在屏幕下方，以列表形式呈现。用户点击后，无需跳出直播间，就能查看商品详情并完成购买。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100846494.png" alt="image-20250725100846494"></p><p>这里的设计要点在于商品“<strong>状态</strong>”的实时同步。当主播讲解某个商品时，它的状态可能是“<strong>待上架</strong>”；当主播喊出“上链接”时，它会立刻变为“<strong>马上抢</strong>”；而当商品售罄时，它又会变为“<strong>已抢完</strong>”。这种实时的状态变化，是制造稀缺感、激发用户下单欲望的关键所在。</p></li></ul><hr><h2 id="11-4-直播电商的关键技术"><a href="#11-4-直播电商的关键技术" class="headerlink" title="11.4 直播电商的关键技术"></a>11.4 直播电商的关键技术</h2><p>在完成了产品的“长相”（用户界面）和“骨架”（功能逻辑）设计之后，我必须和技术团队坐下来，探讨它的“内脏和血脉”——也就是实现这一切所需要的技术。</p><p>作为产品经理，我不需要会写代码，但我必须理解其核心原理。这能让我评估技术方案的可行性、预估开发成本，并在关键的技术选型上，与团队进行有质量的对话。</p><h3 id="11-4-1-核心概念：推流与拉流"><a href="#11-4-1-核心概念：推流与拉流" class="headerlink" title="11.4.1 核心概念：推流与拉流"></a>11.4.1 核心概念：推流与拉流</h3><p>整个复杂的直播技术，可以被简化为两个最核心的动作：“<strong>推流</strong>”和“<strong>拉流</strong>”。</p><ul><li><strong>推流</strong>：我把它理解为“<strong>上传直播</strong>”的过程。它指的是主播的手机端（直播端）采集自己的声音和画面，并将其像水流一样，“推”送到云端服务器的行为。</li><li><strong>拉流</strong>：我把它理解为“<strong>下载直播</strong>”的过程。它指的是成千上万的观众，从云端服务器那里，将直播内容“拉”取到自己手机上进行观看的行为。</li></ul><p>一次流畅的直播体验，本质上就是一次高质量的“推”和成千上万次高质量的“拉”所共同构成的。</p><h3 id="11-4-2-直播的技术全景图"><a href="#11-4-2-直播的技术全景图" class="headerlink" title="11.4.2 直播的技术全景图"></a>11.4.2 直播的技术全景图</h3><p>在“推”与“拉”之间，是一个庞大而精密的后台服务系统。为了让团队清晰地理解这个系统，我通常会展示这样一张技术架构图。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725101657009.png" alt="image-20250725101657009"></p><p>我可以带你走一遍这个流程：</p><ol><li><strong>主播端（推送方）</strong>：一切的源头是主播。我们App会集成一个“<strong>推流SDK</strong>”，它就像一个专业的打包和邮寄工具，负责将主播的音视频内容采集、压缩，然后通过“<strong>推流节点</strong>”，发送到最近的云服务器。</li><li><strong>服务端（处理中心）</strong>：这是直播的“中央厨房”。“<strong>直播服务器</strong>”接收到主播的推流后，会立刻进行一系列的加工处理，例如：<ul><li><strong>转码服务</strong>：为了适配不同观众的网络状况，服务器会将原始视频流，实时转码成高清、标清、流畅等多个版本。</li><li><strong>录制服务</strong>：服务器会将整场直播，录制成一个视频文件（VOD），方便用户随时回顾。</li><li><strong>截图服务</strong>：自动截取直播的精彩瞬间作为封面。</li><li><strong>安全服务</strong>：对直播内容进行实时监控，防止违规。</li></ul></li><li><strong>观众端（拉取方）</strong>：经过处理的直播流，会被分发到全球的“<strong>CDN分发节点</strong>”。这就像是遍布全球的“前置仓库”。当观众打开App时，他们的“<strong>播放SDK</strong>”会自动连接到离他们最近的CDN节点，去“拉取”直播内容。这样，无论用户身在何处，都能获得低延迟、高流畅的观看体验。</li></ol><h3 id="11-4-3-产品经理的技术选型：自研-vs-第三方SDK"><a href="#11-4-3-产品经理的技术选型：自研-vs-第三方SDK" class="headerlink" title="11.4.3 产品经理的技术选型：自研 vs. 第三方SDK"></a>11.4.3 产品经理的技术选型：自研 vs. 第三方SDK</h3><p>了解到这套系统的复杂性后，一个关键的决策就摆在了我的面前：<strong>这套系统，我们是自己从零开始搭建，还是直接采购成熟的方案？</strong></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725101806254.png" alt="image-20250725101806254"></p><p>我的答案，以及我给几乎所有公司的建议都是：<strong>果断选择第三方。</strong></p><p>原因很简单：作为一家电商公司，我们的核心竞争力在于“交易”而非“底层视频技术”。自研一套稳定、高并发、低延迟的全球直播系统，其投入是天文数字。聪明的产品决策，是“<strong>站在巨人的肩膀上</strong>”。</p><p>市面上有非常多专业、成熟的云服务商，提供完整的视频直播解决方案。我们只需要将他们的SDK集成到我们的产品中，就能在短时间内，以可控的成本，上线高质量的直播功能。</p><p>在做技术选型时，我会和技术负责人一起，重点考察几家头部厂商，例如：</p><ul><li><strong>阿里云</strong>：它的视频直播（<a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly93d3cuYWxpeXVuLmNvbS9wcm9kdWN0L2xpdmU">阿里云直播服务</a>）服务，在国内市场份额巨大，技术稳定，文档齐全。</li><li><strong>网易云信</strong>：网易云信（<a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly95dW54aW4uMTYzLmNvbS9saXZl">网易云信直播服务</a>）在社交、娱乐领域的解决方案经验丰富，尤其在IM（即时通讯）和音视频的结合上很有优势。</li><li><strong>腾讯云</strong>：腾讯云的互动直播解决方案（<a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly9jbG91ZC50ZW5jZW50LmNvbS9zb2x1dGlvbi9pbHZi">腾讯云直播服务</a>），尤其强调“互动连麦”等场景，非常适合需要强社交属性的直播玩法。</li></ul><p>最终，我们会根据他们的产品性能、功能丰富度、服务支持以及价格等多个维度，综合评估，选择最适合我们当前业务需求的合作伙伴。</p></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/60138.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/60138.html&quot;)">产品经理进阶（十一）：第十一章：直播电商</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/60138.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/60138.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/27703.html"><img class="prev-cover" src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理进阶（十）：第十章：分销电商</div></div></a></div><div class="next-post pull-right"><a href="/posts/45404.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">3️⃣ 电商运营实战</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理进阶（十一）：第十一章：直播电商",date:"2025-07-25 02:13:45",updated:"2025-07-25 11:05:48",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第十一章：直播电商\n\n欢迎来到第十一章。在过去的学习中，我们已经掌握了平台电商的稳固根基和分销电商的裂变增长。现在，我将带您进入一个能将“**购物体验**”和“**销售转化**”推向极致的全新领域——**直播电商**。这是一种将“**实时互动**”与“**商品销售**”无缝融合的、极具沉浸感的商业模式。\n\n---\n## 11.1 直播电商项目背景\n\n在我负责的产品中，每当要引入一个像“直播”这样重大的新功能时，我都会先回归到最根本的商业问题上：我们现有的模式遇到了什么瓶颈？而这个新功能，是否能成为破局的关键？\n\n### 11.1.1 为什么需要直播电商？\n\n传统的货架式电商，本质是“人找货”，用户带着目的来搜索、比价。这种模式在今天面临着越来越大的挑战：流量越来越贵，用户的注意力越来越分散，单纯的打折促销也越来越难以打动他们。\n\n我发现，直播电商恰好能从三个方面，完美地破解这些困局。\n\n1.  **从“花钱买流量”到“内容吸流量”**：传统电商需要不断地投入巨额广告费，去购买流量。而直播电商，特别是与KOL（关键意见领袖）的合作，是利用主播自带的影响力和内容创作能力，将他的粉丝高效地吸引到我们的平台上来。这是一种更聪明、更具性价比的获客方式。\n2.  **从“理性对比”到“感性促单”**：在传统电商的图文页，用户的决策链路相对较长，消费也更趋于理性。但在直播间里，主播通过现场试用、实时互动和限时限量的话术，能够营造出一种“不买就亏了”的紧迫感和热烈氛围，这极大地激发了用户的感性消费和冲动购买，转化率自然远超平时。\n3.  **从“静态浏览”到“沉浸互动”**：图文详情页是静态的、单向的。而直播，是一种“所见即所得”的沉浸式体验。我可以实时看到衣服的上身效果，可以要求主播展示产品的某个细节，可以通过弹幕与成千上万的人交流。这种丰富、立体的购物体验，是传统电商无法比拟的。\n\n### 11.1.2 到底什么是直播电商？\n\n所以，到底什么是直播电商？\n\n在我看来，直播电商的核心，是**商业模式从“以货为中心”向“以人为中心”的彻底转变**。\n\n![image-20250724194744839](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724194744839.png)\n\n它不再是冰冷的货架，而是基于一个活生生的、你所信任或喜爱的主播，来建立交易。消费者购买的，不仅仅是商品本身，更是对这个主播的品味、专业度或个人魅力的“信任票”。这种以信任为前提的商业模式，其根基依然是电商，但能量却被放大了无数倍。\n\n![image-20250724194842043](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724194842043.png)\n\n我们想象一个真实的场景：当主播在镜头前，一边讲解着手机的各项参数，一边实时回答着“待机时间多久？”、“拍照效果怎么样？”这些弹幕提问，并在几万人的共同见证下，喊出“3、2、1，上链接！”时，那一刻，它已经超越了单纯的“卖货”，变成了一场极具参与感的线上狂欢。这就是直播电商的魅力。\n\n### 11.1.3 直播电商的三种主流模式\n\n理解了直播电商的价值和内核后，作为产品经理，我的下一步就是从顶层设计上，思考我们平台到底要做哪一种。在我的实践中，通常会遇到三种主流的业务模式。\n\n![image-20250724195004429](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195004429.png)\n\n1.  **KOL带货模式**\n    这是最典型、爆发力最强的一种。如果我的业务目标是在短期内快速提升品牌知名度、引爆一款单品的销量，那么与外部的头部KOL合作，无疑是最佳选择。他们带来海量粉丝，我们提供优质商品，这是一场强强联合。\n\n    ![image-20250724195028134](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195028134.png)\n\n2.  **店铺直播模式（店播）**\n    这是一种更着眼于长期、健康的模式。我把它看作是平台必须为商家提供的“基础设施”。我们赋能平台上的商家，让他们可以在自己的“一亩三分地”里，由老板或者店员自己出镜，进行常态化的直播。这不追求一夜爆火，而是为了帮助商家更好地维护自己的老客、沉淀私域流量，是一种细水长流的生意。\n\n    ![image-20250724195103702](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195103702.png)\n\n3.  **直播分销模式**\n    这是一种最大化利用平台生态的、极具想象力的模式。它将直播和分销结合，允许我们的普通用户申请成为“分销主播”。平台提供统一的货盘，他们只需要开播去推广，就能赚取佣金。这相当于将我们平台上成千上万的用户，都变成了我们“行走的、会说话的”销售渠道。\n\n    ![image-20250724195113471](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195113471.png)\n\n\n-----\n\n## 11.2 直播电商的设计思路\n\n在上一节，我们明确了“为什么”要做直播电商。现在，我的角色就要从一个业务分析师，切换到一个产品架构师。在真正开始画原型、写文档之前，我必须先搭建起整个产品的“骨架”。这个过程，我称之为“设计思路”的梳理。\n\n### 11.2.1 核心角色与需求分析\n\n要设计一个好的系统，我首先要清晰地定义出：**这个系统里，都有谁？他们分别想做什么？** 这就是角色与需求分析。在直播电商这个场景里，我识别出了四个核心角色。\n\n1.  **普通用户**：他们是观众，是消费者。他们的核心诉求是“逛得开心，买得方便”。\n2.  **店铺主播**：他们是表演者，是销售员。他们是直播间的灵魂，核心诉求是“互动热烈，卖得更多”。\n3.  **店铺运营**：他们是幕后管理者。他们负责申请开通直播、管理直播计划、处理订单等。核心诉求是“管理高效，掌控全局”。\n4.  **平台**：这就是我们自己。我们的核心诉求是“秩序井然，生态繁荣”，需要有最高的管理权限。\n\n为了确保不遗漏任何关键功能，我会将这些角色的核心需求，整理成一张清晰的列表，作为我们后续产品设计的“需求清单”。\n\n| **角色** | **我的解读（核心需求点）** |\n| :--- | :--- |\n| **普通用户** | 1. 能流畅地观看直播，并与主播进行实时互动（如发弹幕、点赞）。<br>2. 能在直播间里，方便地查看正在讲解的商品，并快速下单购买。 |\n| **店铺运营** | 1. 需要有一个后台，可以向平台方，提交开通“店铺直播”功能的申请。<br>2. 对于已经创建或正在直播的场次，需要有管理和控制的能力。 |\n| **店铺主播** | 1. 能够在App内，轻松地发起一场直播，并能便捷地将自己店铺的商品，上架到直播间进行讲解。<br>2. 在直播过程中，能看到观众的互动，并进行回应，以提升直播间热度。 |\n| **平台** | 作为系统的所有者，我们需要有能力对所有店铺的直播间，进行统一的管理和监控，确保合规。 |\n\n### 11.2.2 核心业务流程梳理\n\n当我把这些零散的需求点都定义清楚后，下一步，就是用一条“流程线”，将它们串联起来，形成一个完整的业务闭环。我需要确保不同角色之间的协作是顺畅的。\n\n我通常会用一张“泳道图”来可视化这个核心流程，让团队里的每一个人都能清晰地看到，自己负责的部分，在整个业务链条中所处的位置。\n\n![image-20250724195621927](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195621927.png)\n\n这个流程是这样运转的：\n\n1.  一切的起点，是“**店铺运营**”向“**平台**”提交了开通直播的申请。\n2.  “**平台**”审核通过后，该店铺就获得了直播的能力。\n3.  “**店铺主播**”现在可以正式“**发起直播**”，并将准备好的“**上架商品**”。\n4.  海量的“**普通用户**”被吸引进入直播间“**观看直播**”，并在主播的带动下完成“**下单**”。\n5.  最后，订单流转到“**店铺运营**”那里，由他们进行“**确认订单**”和后续的履约发货。\n\n你看，通过这样一张流程图，一个完整的、多角色协作的业务故事，就被清晰地呈现了出来。\n\n### 11.2.3 整体功能架构规划\n\n有了角色和流程，我就可以在脑海中，勾勒出整个产品的“功能架构蓝图”了。\n\n我会把需要开发的功能，按照使用者的不同，划分到不同的“端”里去。\n\n![image-20250724195732918](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250724195732918.png)\n\n我将整个直播电商系统，规划为三大功能模块：\n\n  * **用户端**：这是我们产品的主阵地，承载了最多的功能。它既包含了“**普通用户**”的观看、互动、购买功能，也包含了“**主播**”开播、管理商品等核心功能。*（在这里，我暂时将主播端和用户端合并在一起考虑，因为它们都发生在同一个App内，很多界面是共通的）*。\n  * **商家端**：这就是我为“**店铺运营**”人员，所设计的后台管理系统。他们在这里申请权限、管理直播间。\n  * **平台端**：这是我们自己使用的“**上帝后台**”。在这里，我们可以管理所有商家和直播间，设定平台的规则。\n\n至此，直播电商的设计思路就已经非常清晰了。我们明确了“**为谁设计**”（核心角色）、“**设计什么**”（需求列表）、以及“**它们如何协同工作**”（业务流程和功能架构）。这个清晰的骨架，将是我们下一节进行具体产品功能设计的坚实基础。\n\n\n\n\n---\n\n## 11.3 直播电商的产品设计\n\n在我们梳理清楚了设计思路、明确了“要做什么”之后，现在，就到了将蓝图转化为具体页面的阶段。作为产品经理，我会兵分三路，同时推进**平台端、商家端、用户端**这三个关键阵地的产品设计。\n\n### 11.3.1 平台端：规则的制定者与秩序的守护者\n\n我设计平台后台的唯一原则，就是“**权责对等**”。平台作为整个直播生态的“所有者”，必须拥有至高无上的管理权限，来确保整个业务健康、有序地运转。这主要体现在两个方面：**管店铺**和**管直播**。\n\n**1. 直播店铺管理**\n\n我们必须有一个“准入机制”。并非所有商家都有资格开通直播，否则劣质的直播内容会摧毁用户体验。因此，我需要为平台的运营同事，设计一个强大的店铺审核后台。\n\n![image-20250725095520168](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095520168.png)\n\n这个后台的核心，就是对“**资格状态**”的精细化管理。运营人员在这里，可以清晰地看到所有申请店铺的列表，并进行“**审核**”、“**查看**”、“**取消资格**”或“**恢复资格**”等操作。每一个按钮，都代表了平台的一种管理权力，是确保直播商家质量的第一道防线。\n\n**2. 直播间管理**\n\n除了管“人”（店铺），我们更要管“事”（直播）。平台需要能够监控到所有正在发生和已经发生的直播。\n\n![image-20250725095618366](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095618366.png)\n\n在这个界面，我最看重的，是“**操作**”栏里的“**结束**”按钮。这代表了平台的“**干预权**”。当一场直播出现违规内容或其他紧急情况时，平台必须有能力在第一时间，从最高权限上，强制将其关停。这是我们作为平台方，必须承担的责任，也是保障平台安全的生命线。\n\n### 11.3.2 商家端：商户的运营指挥中心\n\n对于商家而言，直播是他们最重要的营销工具和销售渠道之一。因此，我为他们设计的商家后台，必须像一个“**作战指挥室**”，专业、高效、功能完备。\n\n**1. 申请与配置**\n\n商家的直播之旅，始于“**申请**”。我需要为他们提供一个清晰的申请入口，并明确告知他们需要满足的条件，这既是功能，也是一种规则的宣导。\n\n![image-20250725095657234](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095657234.png)\n\n当商家获得资格后，他们就需要一个专业的“**直播间管理**”后台。在这里，他们可以创建、编辑、管理自己所有的直播场次。\n\n![image-20250725095735548](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095735548.png)\n\n\n\n![image-20250725100501358](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100501358.png)\n\n我设计的核心思路是“**状态驱动**”。你会发现，一场直播在“未开始”、“直播中”、“已结束”等不同状态下，商家可以进行的操作是完全不同的。比如，“未开始”的可以“编辑”，而“已结束”的只能“查看数据”。这种精细化的权限控制，能有效防止商家的误操作。\n\n**2. 数据复盘**\n\n直播的魅力，在于可以通过数据不断优化。一场直播结束后，商家最关心的问题就是：“**这场直播效果怎么样？**”。如果我不能回答这个问题，那么我设计的这个功能就是失败的。\n\n![image-20250725095756930](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725095756930.png)\n\n因此，我必须为商家提供一个详尽的“**数据战报**”。这个战报至少要包含三类核心数据：\n* **流量数据**：有多少人看？最高同时有多少人在线？涨了多少粉？\n* **互动数据**：谁给我刷了礼物？价值多少？\n* **带货数据**：卖了什么商品？卖了多少件？\n\n只有提供了这些数据，商家才能进行有效的复盘，我们的直播功能才算真正为商家创造了价值。\n\n### 11.3.3 用户端：主播与观众的互动舞台\n\n用户端，是整个直播产品的“门面”，是所有用户能直接感知到的地方。我把它分为两条主线来设计：**主播的“开播”之旅**，和**观众的“看播”之旅**。\n\n**1. 主播的开播之旅**\n\n我设计主播端的核心理念是“**简单高效，所见即所得**”。主播在手机方寸之间，就要完成一场直播的全部准备工作。\n\n* **第一步：设置直播信息**\n    一场直播的“门面”，就是封面和标题。我必须让主播可以轻松地上传一张吸引人的封面图，并起一个有噱头的标题。此外，“**立即开始**”和“**预定时间**”这两个选项也至关重要。“预定时间”能让主播提前预告，进行蓄水，这是专业运营的必备功能。\n\n    ![image-20250725100613314](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100613314.png)\n\n\n​\t![image-20250725100636104](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100636104.png)\n* **第二步：关联带货商品**\n    这是直播电商的“灵魂”。我需要为主播提供一个极为便捷的“**选品**”流程，让他们能从自己的店铺商品库中，快速勾选出本场要带货的商品，并添加到直播间的“小黄车”里。\n\n    ![image-20250725100708800](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100708800.png)\n\n* **第三步：直播中的掌控**\n    当直播开始后，主播的手机屏幕就变成了他的“**驾驶舱**”。美颜、滤镜、镜头翻转这些是基础功能，能让主播呈现出最好的状态。更重要的是，他需要有管理商品、与观众互动等一系列工具。\n\n    ![image-20250725100747775](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100747775.png)\n\n**2. 观众的看播之旅**\n\n我设计观众端的核心理念是“**沉浸体验，无缝下单**”。我要让用户看得开心，买得顺滑。\n\n* **核心互动界面**\n    用户进入直播间，首先看到的是一个集“**视频画面**”和“**实时互动区**”于一体的界面。下方的聊天弹幕区是营造社区感和热闹氛围的关键，让用户感觉自己不是一个人在看，而是在和成千上万的人一起“云逛街”。\n\n    ![image-20250725100818156](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100818156.png)\n\n* **商品浏览与购买**\n    当主播开始介绍商品时，我必须为用户提供一个清晰、无干扰的商品展示区。这个区域通常在屏幕下方，以列表形式呈现。用户点击后，无需跳出直播间，就能查看商品详情并完成购买。\n\n    ![image-20250725100846494](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725100846494.png)\n\n    这里的设计要点在于商品“**状态**”的实时同步。当主播讲解某个商品时，它的状态可能是“**待上架**”；当主播喊出“上链接”时，它会立刻变为“**马上抢**”；而当商品售罄时，它又会变为“**已抢完**”。这种实时的状态变化，是制造稀缺感、激发用户下单欲望的关键所在。\n\n\n\n---\n\n\n## 11.4 直播电商的关键技术\n\n在完成了产品的“长相”（用户界面）和“骨架”（功能逻辑）设计之后，我必须和技术团队坐下来，探讨它的“内脏和血脉”——也就是实现这一切所需要的技术。\n\n作为产品经理，我不需要会写代码，但我必须理解其核心原理。这能让我评估技术方案的可行性、预估开发成本，并在关键的技术选型上，与团队进行有质量的对话。\n\n### 11.4.1 核心概念：推流与拉流\n\n整个复杂的直播技术，可以被简化为两个最核心的动作：“**推流**”和“**拉流**”。\n\n* **推流**：我把它理解为“**上传直播**”的过程。它指的是主播的手机端（直播端）采集自己的声音和画面，并将其像水流一样，“推”送到云端服务器的行为。\n* **拉流**：我把它理解为“**下载直播**”的过程。它指的是成千上万的观众，从云端服务器那里，将直播内容“拉”取到自己手机上进行观看的行为。\n\n一次流畅的直播体验，本质上就是一次高质量的“推”和成千上万次高质量的“拉”所共同构成的。\n\n### 11.4.2 直播的技术全景图\n\n在“推”与“拉”之间，是一个庞大而精密的后台服务系统。为了让团队清晰地理解这个系统，我通常会展示这样一张技术架构图。\n\n![image-20250725101657009](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725101657009.png)\n\n我可以带你走一遍这个流程：\n1.  **主播端（推送方）**：一切的源头是主播。我们App会集成一个“**推流SDK**”，它就像一个专业的打包和邮寄工具，负责将主播的音视频内容采集、压缩，然后通过“**推流节点**”，发送到最近的云服务器。\n2.  **服务端（处理中心）**：这是直播的“中央厨房”。“**直播服务器**”接收到主播的推流后，会立刻进行一系列的加工处理，例如：\n    * **转码服务**：为了适配不同观众的网络状况，服务器会将原始视频流，实时转码成高清、标清、流畅等多个版本。\n    * **录制服务**：服务器会将整场直播，录制成一个视频文件（VOD），方便用户随时回顾。\n    * **截图服务**：自动截取直播的精彩瞬间作为封面。\n    * **安全服务**：对直播内容进行实时监控，防止违规。\n3.  **观众端（拉取方）**：经过处理的直播流，会被分发到全球的“**CDN分发节点**”。这就像是遍布全球的“前置仓库”。当观众打开App时，他们的“**播放SDK**”会自动连接到离他们最近的CDN节点，去“拉取”直播内容。这样，无论用户身在何处，都能获得低延迟、高流畅的观看体验。\n\n### 11.4.3 产品经理的技术选型：自研 vs. 第三方SDK\n\n了解到这套系统的复杂性后，一个关键的决策就摆在了我的面前：**这套系统，我们是自己从零开始搭建，还是直接采购成熟的方案？**\n\n![image-20250725101806254](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250725101806254.png)\n\n我的答案，以及我给几乎所有公司的建议都是：**果断选择第三方。**\n\n原因很简单：作为一家电商公司，我们的核心竞争力在于“交易”而非“底层视频技术”。自研一套稳定、高并发、低延迟的全球直播系统，其投入是天文数字。聪明的产品决策，是“**站在巨人的肩膀上**”。\n\n市面上有非常多专业、成熟的云服务商，提供完整的视频直播解决方案。我们只需要将他们的SDK集成到我们的产品中，就能在短时间内，以可控的成本，上线高质量的直播功能。\n\n在做技术选型时，我会和技术负责人一起，重点考察几家头部厂商，例如：\n* **阿里云**：它的视频直播（[阿里云直播服务](https://www.aliyun.com/product/live)）服务，在国内市场份额巨大，技术稳定，文档齐全。\n* **网易云信**：网易云信（[网易云信直播服务](https://yunxin.163.com/live)）在社交、娱乐领域的解决方案经验丰富，尤其在IM（即时通讯）和音视频的结合上很有优势。\n* **腾讯云**：腾讯云的互动直播解决方案（[腾讯云直播服务](https://cloud.tencent.com/solution/ilvb)），尤其强调“互动连麦”等场景，非常适合需要强社交属性的直播玩法。\n\n最终，我们会根据他们的产品性能、功能丰富度、服务支持以及价格等多个维度，综合评估，选择最适合我们当前业务需求的合作伙伴。"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E4%B8%80%E7%AB%A0%EF%BC%9A%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86"><span class="toc-number">1.</span> <span class="toc-text">第十一章：直播电商</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#11-1-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E9%A1%B9%E7%9B%AE%E8%83%8C%E6%99%AF"><span class="toc-number">1.1.</span> <span class="toc-text">11.1 直播电商项目背景</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-1-%E4%B8%BA%E4%BB%80%E4%B9%88%E9%9C%80%E8%A6%81%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%EF%BC%9F"><span class="toc-number">1.1.1.</span> <span class="toc-text">11.1.1 为什么需要直播电商？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-2-%E5%88%B0%E5%BA%95%E4%BB%80%E4%B9%88%E6%98%AF%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%EF%BC%9F"><span class="toc-number">1.1.2.</span> <span class="toc-text">11.1.2 到底什么是直播电商？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-3-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E4%B8%89%E7%A7%8D%E4%B8%BB%E6%B5%81%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.1.3.</span> <span class="toc-text">11.1.3 直播电商的三种主流模式</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#11-2-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-number">1.2.</span> <span class="toc-text">11.2 直播电商的设计思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-1-%E6%A0%B8%E5%BF%83%E8%A7%92%E8%89%B2%E4%B8%8E%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-number">1.2.1.</span> <span class="toc-text">11.2.1 核心角色与需求分析</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-2-%E6%A0%B8%E5%BF%83%E4%B8%9A%E5%8A%A1%E6%B5%81%E7%A8%8B%E6%A2%B3%E7%90%86"><span class="toc-number">1.2.2.</span> <span class="toc-text">11.2.2 核心业务流程梳理</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-3-%E6%95%B4%E4%BD%93%E5%8A%9F%E8%83%BD%E6%9E%B6%E6%9E%84%E8%A7%84%E5%88%92"><span class="toc-number">1.2.3.</span> <span class="toc-text">11.2.3 整体功能架构规划</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#11-3-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.</span> <span class="toc-text">11.3 直播电商的产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-1-%E5%B9%B3%E5%8F%B0%E7%AB%AF%EF%BC%9A%E8%A7%84%E5%88%99%E7%9A%84%E5%88%B6%E5%AE%9A%E8%80%85%E4%B8%8E%E7%A7%A9%E5%BA%8F%E7%9A%84%E5%AE%88%E6%8A%A4%E8%80%85"><span class="toc-number">1.3.1.</span> <span class="toc-text">11.3.1 平台端：规则的制定者与秩序的守护者</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-2-%E5%95%86%E5%AE%B6%E7%AB%AF%EF%BC%9A%E5%95%86%E6%88%B7%E7%9A%84%E8%BF%90%E8%90%A5%E6%8C%87%E6%8C%A5%E4%B8%AD%E5%BF%83"><span class="toc-number">1.3.2.</span> <span class="toc-text">11.3.2 商家端：商户的运营指挥中心</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-3-%E7%94%A8%E6%88%B7%E7%AB%AF%EF%BC%9A%E4%B8%BB%E6%92%AD%E4%B8%8E%E8%A7%82%E4%BC%97%E7%9A%84%E4%BA%92%E5%8A%A8%E8%88%9E%E5%8F%B0"><span class="toc-number">1.3.3.</span> <span class="toc-text">11.3.3 用户端：主播与观众的互动舞台</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#11-4-%E7%9B%B4%E6%92%AD%E7%94%B5%E5%95%86%E7%9A%84%E5%85%B3%E9%94%AE%E6%8A%80%E6%9C%AF"><span class="toc-number">1.4.</span> <span class="toc-text">11.4 直播电商的关键技术</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-4-1-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%EF%BC%9A%E6%8E%A8%E6%B5%81%E4%B8%8E%E6%8B%89%E6%B5%81"><span class="toc-number">1.4.1.</span> <span class="toc-text">11.4.1 核心概念：推流与拉流</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-4-2-%E7%9B%B4%E6%92%AD%E7%9A%84%E6%8A%80%E6%9C%AF%E5%85%A8%E6%99%AF%E5%9B%BE"><span class="toc-number">1.4.2.</span> <span class="toc-text">11.4.2 直播的技术全景图</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-4-3-%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E7%9A%84%E6%8A%80%E6%9C%AF%E9%80%89%E5%9E%8B%EF%BC%9A%E8%87%AA%E7%A0%94-vs-%E7%AC%AC%E4%B8%89%E6%96%B9SDK"><span class="toc-number">1.4.3.</span> <span class="toc-text">11.4.3 产品经理的技术选型：自研 vs. 第三方SDK</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>