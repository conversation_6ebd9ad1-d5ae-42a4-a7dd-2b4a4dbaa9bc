<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>Java（八）：8.0 Java新语法总结 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="Java基础知识总汇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="Java（八）：8.0 Java新语法总结"><meta name="application-name" content="Java（八）：8.0 Java新语法总结"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="Java（八）：8.0 Java新语法总结"><meta property="og:url" content="https://prorise666.site/posts/14501.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="8.0 Java新语法总结8.1 [核心] [Java 8+] Lambda 表达式与方法引用 本章导读: Java 8 的发布是 Java 发展史上的一个里程碑，其最核心、最具代表性的特性无疑是 Lambda 表达式的引入。它将函数式编程思想正式带入了 Java 世界，从根本上改变了我们处理行为参"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp"><meta name="description" content="8.0 Java新语法总结8.1 [核心] [Java 8+] Lambda 表达式与方法引用 本章导读: Java 8 的发布是 Java 发展史上的一个里程碑，其最核心、最具代表性的特性无疑是 Lambda 表达式的引入。它将函数式编程思想正式带入了 Java 世界，从根本上改变了我们处理行为参"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/14501.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"Java（八）：8.0 Java新语法总结",postAI:"true",pageFillDescription:"8.0 Java新语法总结, 8.1 [核心] [Java 8+] Lambda 表达式与方法引用, 8.1.1 背景：为什么要引入 Lambda, 8.1.2 核心：Lambda 语法与函数式接口, 1. Lambda 表达式的三部分构成：(参数) -gt 123方法体125 - 一种更紧凑的代码表示法, 2. 函数式接口 (@FunctionalInterface) - Lambda 的类型基础, 8.1.3 进阶：方法引用的四种类型 (), 8.1.4 [高频面试题] Lambda 与匿名内部类的区别, 8.2 [数据处理革命] [Java 8+] Stream API, 8.2.1 核心概念：流的创建与生命周期, 8.2.2 中间操作详解, 1. 筛选与切片 - 从流中选取我们需要的元素, 2. 映射 - 将流中元素转换为其他形式或提取信息, 3. 排序 - 对流中元素进行排序, 8.2.3 终端操作详解, 1. 匹配与查找 - 检查流中元素是否满足特定条件, 2. 规约 (Reduction) - 将流中所有元素计算得到一个值, 3. 收集 (Collect) - 将流中元素转换成集合、Map或其他复杂对象, 8.3 [空指针终结者] [Java 8+] Optional 容器类, 8.3.1 设计哲学：为什么需要 Optional, 8.3.2 核心方法分类使用, 1. 创建 Optional 对象 - 包装你的值, 2. 获取值（带默认处理） - 安全地取出结果, 3. 值的转换与过滤 - 以函数式风格处理值, 4. 判断与消费 - 在值存在时执行操作, 8.4 [语言增强] [Java 8+] 语言及 API 增强, 8.4.1 接口的演进 [Java 8], 1. 默认方法 (default) - 在不破坏实现的前提下为接口添加新功能, 2. 静态方法 (static) - 在接口中定义工具方法, 8.4.2 集合工厂方法 [Java 9+], 8.4.3 常用 API 增强 [Java 11+], 8.5 [语法现代化] [Java 10+] 局部变量类型推断 (var), 8.5.1 var 的使用场景与优势, 8.5.2 [避坑指南] var 的使用限制, 8.6 [语法现代化] [Java 16x2F17+] Record 类与 Sealed 类, 8.6.1 Record 类：不可变数据载体 [Java 16+], 8.6.2 Sealed 类x2F接口：精准的继承控制 [Java 17+], [高频面试题] Record VS Lombok, 8.7 [语法现代化] [Java 16-21+] 模式匹配, 8.7.1 instanceof 的模式匹配 [Java 16+], 8.7.2 switch 表达式与模式匹配 [Java 21+]新语法总结核心表达式与方法引用本章导读的发布是发展史上的一个里程碑其最核心最具代表性的特性无疑是表达式的引入它将函数式编程思想正式带入了世界从根本上改变了我们处理行为参数化的方式在本节中我们将从的诞生背景出发深入其核心语法函数式接口并最终掌握其语法糖方法引用的使用技巧为我们后续学习等高级特性打下坚实的基础背景为什么要引入在之前如果我们想将一个行为一段代码逻辑传递给另一个方法通常需要依赖匿名内部类这种写法不仅语法冗长可读性差而且会为每个实例生成一个独立的文件显得非常笨重让我们通过一个简单的例子直观感受一下对一个字符串列表按长度进行排序背景假设我们需要对一个集合按照字符串的长度进行升序排序场景之前的实现方式使用匿名内部类按照字符串的长度进行升序排序排序后的列表输出排序后的列表小结我们可以看到为了实现一个简单的比较逻辑我们不得不编写一个完整的匿名内部类实例其中真正核心的代码只有一行其余都是模板化的语法噪音这种写法显得头重脚轻不够优雅表达式的诞生正是为了解决这一痛点核心语法与函数式接口表达式的三部分构成参数方法体一种更紧凑的代码表示法表达式本质上是一个匿名方法它提供了一种清晰简洁的方式来表示一个函数式接口的实例其构成如下参数列表与接口中抽象方法的参数列表相对应可以省略参数类型编译器会自动推断如果只有一个参数可以省略括号箭头符号将参数列表与方法体分开读作方法体包含了方法的实现逻辑如果方法体只有一条语句可以省略大括号和关键字场景使用表达式重写排序背景同样是对字符串列表按长度排序我们看看使用如何实现带有类型声明的完整语法省略类型声明由编译器推断推荐当方法体只有一行时省略大括号和最简洁推荐使用排序后的列表输出使用排序后的列表小结对比之前的匿名内部类表达式极大地简化了代码使我们的意图一目了然传入两个字符串和返回它们的长度差函数式接口的类型基础表达式本身没有类型它必须被赋值给一个函数式接口类型的变量定义函数式接口是有且仅有一个抽象方法的接口可以包含多个默认方法或静态方法为了让编译器强制检查一个接口是否为函数式接口引入了注解最佳实践我们强烈推荐为所有函数式接口添加注解以明确接口意图并利用编译时检查常用函数式接口速查表包中预定义了大量常用的函数式接口以下是四大核心接口接口名抽象方法功能描述主力推荐接收一个参数并返回一个布尔值常用于过滤主力推荐接收一个参数返回一个结果常用于映射转换不接收参数返回一个结果常用于对象的工厂方法接收一个参数没有返回值常用于对元素执行某种消费操作如打印场景四大核心函数式接口实战背景我们将通过一个简单的程序来演示这四个核心接口在实际代码中的应用判断一个整数是否大于是否大于将字符串转换为其长度的长度是打印传入的字符串正在消费生成一个随机数生成的随机数输出是否大于的长度是正在消费生成的随机数每次不同你可能已经注意到上面的示例代码中我们直接使用了等接口但并没有看到注解的影子这是为什么呢关键在于区分接口的定义者与接口的使用者定义者注解是给接口的创建者使用的官方在设计等接口时已经在它们的源码中添加了注解这保证了提供的这些接口绝对符合函数式接口的规范使用者在我们的示例代码中我们是这些接口的使用者我们直接利用这些已经由定义好并验证过的接口作为表达式的类型而不需要也不能再去重复声明注解进阶方法引用的四种类型方法引用是表达式的一种特殊语法糖它允许我们通过方法的名字来直接引用一个已经存在的方法当表达式的方法体已经有现成的方法可以实现时使用方法引用会让代码更加简洁易读用更具体的例子来说假设我们要对一个字符串列表进行排序不使用方法引用张三李四王五表达式这里就是你的表达式你是在教方法如何比较两个字符串使用方法引用就像直接用现成的菜张三李四王五方法引用就是对已经存在的类里的方法的引用你不再需要写而是直接说用类那个叫的方法来比吧方法引用类型示例语法实际代码等价形式实际代码核心意图引用静态方法类名静态方法名参数类名静态方法名参数调用类的静态方法引用特定类型的任意对象的实例方法类名实例方法名对象实例参数对象实例实例方法名参数调用对象身上的实例方法对象是传入的引用特定对象的实例方法具体对象实例方法名参数具体对象实例方法名参数调用某个固定对象的实例方法引用构造函数类名参数类名参数创建类的实例说明这里的参数是根据实际的函数式接口定义来决定的可能是一个或多个参数也可能没有参数对象实例在引用特定类型的任意对象的实例方法中是作为第一个隐式参数传入的具体对象在引用特定对象的实例方法中是方法引用语法的一部分已经固定了方法引用实战场景详解背景我们将通过代码示例逐一展示四种方法引用的应用场景场景引用静态方法将字符串转换为整数字符串转为整数场景引用特定类型的任意对象的实例方法将字符串列表全部转为大写场景引用特定对象的实例方法使用对象的方法场景引用构造函数创建一个对象创建了一个新小结当的逻辑仅仅是调用一个已存在的方法时方法引用是最佳选择它能消除冗余的参数声明让代码的意图而非实现细节凸显出来高频面试题与匿名内部类的区别表达式和匿名内部类有什么核心区别尽管表达式在很多场景下可以替代匿名内部类但它们在底层实现和行为上存在显著差异的指向不同这是最核心的区别匿名内部类关键字指向的是匿名内部类自身的实例表达式关键字指向的是其外层封装类的实例表达式本身没有自己的它在词法上是其外层作用域的一部分编译后产物不同匿名内部类编译器会为每一个匿名内部类生成一个单独的文件格式通常是表达式编译器不会为每个生成一个独立的文件它会将的逻辑翻译成一个私有的静态方法并在运行时通过指令来动态地创建实现函数式接口的对象这种方式在性能和内存占用上通常更优功能限制匿名内部类功能更强大它可以实现有多个抽象方法的接口可以继承具体的类也可以拥有自己的实例变量和方法表达式功能更专注它只能用于实现函数式接口即只有一个抽象方法的接口性能由于的机制在运行时有更多的优化空间比如可以对的调用进行内联或者延迟实例化因此在很多情况下的性能会优于或等于匿名内部类数据处理革命本章导读如果说表达式是的引擎那么就是搭载了这个强大引擎的超级跑车它提供了一套声明式可组合可并行的彻底改变了我们对集合数据的处理方式告别繁琐的循环和临时变量让我们能以一种更优雅更符合人类思维的流水线方式来操作数据本节我们将全面探索的世界核心概念流的创建与生命周期一个典型的操作流包含三个阶段创建从一个数据源如集合数组获取一个流中间操作对流进行一系列的转换或筛选操作每个中间操作都会返回一个新的流这使得操作可以像链条一样串联起来这些操作是惰性求值的也就是说它们在终端操作被调用前不会真正执行终端操作触发流的实际计算并产生一个最终结果如一个值一个集合或无返回值终端操作是及早求值的一旦执行流就会被消耗且无法再次使用场景流的多种创建方式背景在开始处理数据前我们首先需要知道如何从不同的数据源创建流从集合创建流最常用从创建的流从数组创建流从创建的流使用静态方法创建流使用创建的流使用创建无限流必须用限制使用创建的无限流限制后输出从创建的流从创建的流使用创建的流使用创建的无限流限制后小结创建流是所有操作的第一步是最常用的方式对于无限流务必使用等中间操作将其变为有限流否则可能会导致无限循环中间操作详解中间操作是的精髓所在它们允许我们将复杂逻辑分解为一系列小而清晰的步骤筛选与切片从流中选取我们需要的元素常用筛选与切片方法速查表方法名功能描述主力推荐根据条件过滤流中元素只保留满足条件的去除流中重复的元素基于元素的方法截断流使其元素数量不超过给定值跳过前个元素返回一个扔掉了前个元素的新流场景筛选出前两名不重复的偶数背景给定一个整数列表我们需要找到其中不重复的偶数并只取前两个处理后的结果筛选偶数去重跳过第一个取前两个输出处理后的结果映射将流中元素转换为其他形式或提取信息常用映射方法速查表方法名功能描述主力推荐将流中每个元素转换为另一个元素一对一映射进阶常用将每个元素转换为一个新流然后将所有子流连接成一个流一对多扁平化场景从单词列表获取所有不同的字符背景给定一个单词列表我们希望得到其中所有字符列表得到扁平化为转为输出小结当你的转换逻辑会从一个元素产生多个结果时例如从一个单词产生多个字符就是你需要的排序对流中元素进行排序常用排序方法速查表方法名功能描述按自然顺序排序元素需实现主力推荐根据自定义比较器进行排序表达能力更强场景对自定义对象进行排序背景我们有一个列表需要先按价格降序排序如果价格相同再按名称升序排序原生流的实现方式按价格降序再按名称升序小结接口在中也得到了极大的增强等静态和默认方法使得构建复杂的多级排序逻辑变得异常简单和直观终端操作详解终端操作是流处理的最后一步它会触发所有懒加载的中间操作并生成最终结果匹配与查找检查流中元素是否满足特定条件常用匹配与查找方法速查表方法名功能描述检查流中是否至少有一个元素匹配条件检查流中是否所有元素都匹配条件检查流中是否没有元素匹配条件返回流的第一个元素用包装返回流中的任意一个元素用包装并行流中更高效场景检查产品列表中是否存在高价商品是否有价格的产品输出是否有价格的产品规约将流中所有元素计算得到一个值常用规约方法速查表方法名功能描述主力推荐从一个初始值开始对流中所有元素进行规约操作无初始值的规约因为流可能为空所以返回一个场景计算所有产品价格的总和是初始值是所有产品的总价是输出所有产品的总价是收集将流中元素转换成集合或其他复杂对象常用收集器速查表收集器功能描述最常用将流中元素收集到或将流中元素收集到需注意重复问题主力推荐根据分类函数对元素进行分组返回一个将流中元素通常是字符串连接成一个字符串场景将产品列表按价格分组按价格分组的结果价格输出按价格分组的结果价格价格价格小结工具类提供了极其丰富和强大的收集器是操作的弹药库熟练掌握是高效使用的关键空指针终结者容器类本章导读被其创造者称为价值十亿美元的错误它长久以来都是开发者的噩梦为了应对这一挑战引入了它是一个容器类其核心设计思想并非简单地替代而是通过类型系统来显式地表达一个值可能缺失的情况这迫使我们作为调用者必须正视并处理值不存在的可能性从而将潜在的运行时转换为编译时可见的设计考量让代码更加健壮和优雅设计哲学为什么需要在出现之前方法通常通过返回来表示没有找到结果这种做法存在一个致命缺陷的含义是模糊的并且完全依赖于调用者的自觉性去进行非空检查返回的问题意图不明可能代表查询无结果操作失败或值本身就是空容易引起混淆契约不清方法签名无法告诉调用者它可能会返回这是一种隐藏的脆弱的契约风险转嫁将校验的责任完全抛给了调用方一旦忘记检查就会在运行时不期而至通过将可能没有值这一情况包装成一个对象完美地解决了上述问题一个返回的方法其签名本身就在大声宣告我返回的可能是一个也可能什么都没有请你务必处理这两种情况核心方法分类使用创建对象包装你的值常用创建方法速查表方法名功能描述谨慎使用为一个你确定非的值创建一个如果为会立即抛出主力推荐为一个可能为的值创建一个如果为则返回一个空的对象这是最安全最常用的创建方式创建一个明确的空的实例场景创建实例背景我们有一个方法可能返回一个对象也可能返回我们需要将这个结果安全地包装起来最安全的方式有值有值无值无值当你确定值不为时使用有值有值下面这行会立即抛出创建一个空实例获取值带默认处理安全地取出结果避坑指南应尽量避免使用方法它在为空时会抛出与我们期望避免运行时异常的初衷背道而驰只有在万分确定中有值时例如在判断之后才可使用安全获取值方法速查表方法名功能描述常用如果中有值则返回该值否则返回指定的默认值注意无论是否为空参数都会被求值主力推荐如果中有值则返回该值否则调用函数式接口来生成一个默认值优势只在为空时才会被调用实现了懒加载性能更优如果中有值则返回该值否则抛出由生成的异常场景为可能不存在的用户提供默认名称背景我们需要从一个中获取用户名如果用户不存在则返回无论是否为空都会被执行有值时无值时只有在为空时表达式才会执行无值时输出有值时无值时无值时值的转换与过滤以函数式风格处理值这些方法让我们可以像操作一样对内部的值进行链式处理而无需显式地进行非空判断常用转换与过滤方法速查表方法名功能描述主力推荐如果值存在则对其应用映射并返回一个包装了新值的如果值不存在则直接返回一个空的进阶常用与类似但要求映射函数本身返回一个会将结果扁平化避免出现这样的嵌套结构如果值存在且满足条件则返回包含该值的否则返回一个空的场景获取用户地址的邮政编码多级可能为空背景可能没有可能没有我们需要安全地获取邮编如果中间任何一环为空都应返回一个拥有完整地址信息的用户用于处理返回的返回返回用户的邮政编码是用户的邮政编码是一个没有地址信息的用户无地址用户的邮政编码是无地址用户的邮政编码是输出用户的邮政编码是无地址用户的邮政编码是小结当你的操作返回的是一个对象时就应该使用来代替以保持结构的扁平判断与消费在值存在时执行操作常用判断与消费方法速查表方法名功能描述不推荐检查值是否存在通常可以用函数式方法替代以避免命令式的语句主力推荐如果值存在则对该值执行操作这是处理有值情况的最常用方式如果值存在执行第一个否则执行第二个任务场景如果用户存在就打印其信息只有在有值时才会执行用户信息用户信息为空不会被执行这段代码不会被执行的用户信息用户信息用户不存在若用户不存在则会执行此输出用户信息语言增强语言及增强本章导读除了像和这样的大型主题外及后续版本还带来了许多小而美的改进本节将作为这些实用特性的一个合集我们将学习接口如何通过默认方法和静态方法实现优雅演进了解如何用一行代码创建不可变集合并探索等日常核心的便利新方法以及可重复注解和等实用工具接口的演进在之前接口是纯粹的只能包含抽象方法和常量这种设计的弊端在于一旦接口发布就很难再向其中添加新方法因为这会强制所有已有的实现类都去实现这个新方法造成大规模的代码破坏通过引入默认方法和静态方法完美地解决了这个问题默认方法在不破坏实现的前提下为接口添加新功能默认方法允许我们在接口中提供一个方法的默认实现实现该接口的类将自动继承这个默认方法无需强制重写这对于的向后兼容和优雅演进至关重要常用方法速查表关键字功能描述在接口方法声明前使用用于提供一个默认的方法体实现场景为一个接口添加新的日志级别方法背景假设我们有一个已广泛使用的接口现在希望为它增加一个方法但又不想让所有旧的实现类都报错使用关键字为接口添加一个新方法一个老的实现类它只实现了一个新的实现类它选择重写默认方法老的实现类可以直接调用新的默认方法而无需修改自身代码新的实现类调用的是它自己重写后的版本输出小结默认方法是设计者向库中添加新功能而不破坏向后兼容性的强大工具它使得接口的演进变得平滑和安全静态方法在接口中定义工具方法还允许在接口中定义静态方法这些方法不属于任何对象实例而是直接属于接口本身这使得我们可以将一些相关的工具方法直接组织在接口内部而不是创建一个单独的工具类常用方法速查表关键字功能描述在接口方法声明前使用定义一个属于接口本身的静态方法场景在接口中提供一个工厂方法背景我们希望提供一个简单的方式来获取的实例可以直接在接口中定义一个静态工厂方法在接口中定义一个静态工厂方法直接通过接口名调用静态方法就像调用普通工具类一样输出小结接口静态方法进一步提升了接口作为代码组织单元的能力让接口不仅能定义契约还能提供相关的辅助工具集合工厂方法在之前创建一个包含少量元素的集合通常需要好几行代码引入了一系列静态工厂方法极大地简化了不可变集合的创建常用方法速查表方法名功能描述主力推荐创建一个不可变的创建一个不可变的不允许重复元素创建一个不可变的不允许重复的键避坑指南使用方法创建的集合是不可变的任何尝试对其进行添加删除等修改操作的行为都会抛出此外它们不允许存入元素场景快速创建只读的配置集合背景我们需要创建一个包含默认配置项的这个配置在程序运行期间不应被修改创建不可变创建不可变创建不可变尝试修改会抛出异常捕获到异常输出捕获到异常常用增强在后续版本中也持续对一些我们日常使用最频繁的类如进行功能增强常用增强速查表方法名引入版本功能描述判断字符串是否为空白或只包含空白字符将字符串按行分隔符拆分为一个将字符串重复指定次数去除字符串首尾的空白字符比更能识别空白符场景处理多行文本并去除空白行背景我们从外部系统获取了一段文本其中包含多行内容和一些空白行需要进行清洗这是的文本块语法此处用于方便地表示多行文本清洗前的文本清洗后的文本将文本分割成一个使用过滤掉空白行使用去除每行首尾的空白重新组合成一个字符串其他重复三次输出清洗前的文本清洗后的文本其他重复三次好的我们已经完成了语言及增强中多个版本的特性学习接下来我们进入一个在中引入的极大提升编码便利性的语法糖局部变量类型推断语法现代化局部变量类型推断本章导读在之前我们声明变量时必须在左侧明确写出其类型即使在右侧的初始化语句中类型已经非常清晰这种冗余在处理复杂的泛型类型时尤为明显为了解决这一问题引入了关键字它允许编译器根据变量的初始化表达式自动推断出其类型这并非引入了动态类型声明的变量仍然是静态类型的它仅仅是为我们省去了手动声明类型的麻烦让代码更简洁的使用场景与优势主要的优势在于能够简化代码尤其是在处理嵌套泛型等复杂类型时能极大地提升代码的可读性常用场景速查表场景示例优势简化复杂类型声明核心优势大幅减少冗余的模板代码让代码更聚焦于变量名和业务逻辑循环简化循环变量的声明简化资源变量的声明使代码更紧凑场景简化复杂的声明背景我们需要创建一个其键是字符串值是一个存储了对象的我们来对比一下使用前后的代码差异之前的写法类型声明非常冗长使用的写法代码瞬间清爽编译器为推断出的类型仍然是的类型是的内容循环中使用的类型被正确推断为循环中的用户输出的类型是的内容循环中使用循环中的用户循环中的用户小结让我们的代码不再被冗长的类型声明所淹没尤其是在泛型和复杂的类名中它鼓励我们为变量起一个更有意义的名字因为类型的上下文已经由初始化表达式提供了避坑指南的使用限制虽然好用但它不是万金油只能在特定的上下文中使用滥用或误用反而会降低代码的可读性核心原则所有声明的变量编译器都必须能在编译时仅通过其初始化表达式就明确地推断出它的唯一类型以下是的主要使用限制必须有初始化器不能只声明不赋值错误正确不能用初始化无法从推断出具体类型错误只能用于局部变量这是最重要的一条规则不能用于类的成员变量字段方法的参数方法的返回类型表达式和方法引用需要显式目标类型错误原因表达式的类型依赖于其上下文的目标接口仅凭自身无法推断正确小心泛型和菱形操作符这行代码是合法的但的类型会被推断为这可能不是我们想要的最佳实践如果想让推断出正确的泛型类型应在初始化表达式中明确指定它场景的非法使用示例背景下面的代码展示了几种常见的错误用法帮助我们加深理解错误不能用于成员变量错误不能用于方法返回类型错误不能用于方法参数合法用法非法用法错误没有初始化器错误用初始化错误表达式需要显式目标类型正确的用法输出小结是一个强大的便利工具但绝不能滥用在类型不明显或者会降低代码清晰度的场景下我们仍应坚持使用明确的类型声明代码首先是写给人看的其次才是给机器执行的好的我们已经学习了引入的关键字接下来我们将把目光投向和中两个重要的现代化基石它们共同致力于增强的数据建模和领域建模能力我们将把它们合并在类与类这一节中进行讲解语法现代化类与类本章导读随着应用日益复杂如何清晰准确地对业务领域进行建模变得至关重要类和类是在这方面给出的强力回应类旨在用最少的代码创建不可变的数据载体彻底终结了传统的冗长样板代码而类则允许我们创建可控的封闭的继承体系这两者结合特别是在后续的模式匹配中能够构建出既安全又极具表达力的领域模型类不可变数据载体在出现之前创建一个简单的数据类如一个二维坐标点需要我们手动编写构造函数以及方法这些都是高度重复且容易出错的样板代码类就是为了解决这个问题而生的核心思想是一种特殊的类它专门用于充当不可变数据的透明载体我们只需在声明时定义其组件编译器就会自动为我们生成所有必要的成员自动生成内容速查表当我们声明时编译器自动生成和私有字段一个包含所有组件的公共构造器每个组件的公共访问器方法如和注意不是一个完整的实现一个完整的实现一个完整的实现场景使用定义一个坐标点类背景我们需要一个类来表示二维坐标我们来对比一下传统方式和方式的巨大差异这是使用的方式仅需一行我们还可以在内部添加静态方法或自定义的实例方法如果不用我们需要手写这么多代码还需要手动实现创建实例访问器方法的坐标是注意是而不是的坐标是的字符串表示和是否相等和是否相等的哈希码的哈希码调用自定义方法到原点的距离是输出的坐标是的坐标是的字符串表示和是否相等和是否相等的哈希码的哈希码到原点的距离是小结类是创建不可变数据聚合的理想选择它用最少的语法提供了完整正确的功能极大地减少了样板代码使我们的模型类定义更清晰更可靠类接口精准的继承控制在面向对象设计中我们有时希望限制一个类或接口的继承体系明确地指定谁可以成为我的子类例如一个形状接口我们可能只希望它被圆形方形和三角形实现类接口正是为此而生它允许我们创建一个封闭的可控的继承层次结构核心语法使用关键字修饰类或接口并使用关键字列出所有允许继承或实现的直接子类子类的规则所有被关键字指定的子类都必须遵循以下三条规则之一必须声明为表示继承关系到此为止不能再被继承必须声明为表示它可以被继续继承但同样需要用指定其子类必须声明为表示打开继承限制任何类都可以继承它回归到普通的继承模式场景定义一个封闭的图形继承体系背景我们正在设计一个绘图程序需要定义一个接口并确保系统中只存在和这两种形状定义一个接口只允许和实现它面积是的一个子类我们将其声明为表示它不能再被继承是的另一个子类我们将其声明为表示任何其他类如都可以继承它由于是我们可以自由地继承它圆形的面积是方形的面积是输出圆形的面积是方形的面积是小结类和接口为我们的领域建模提供了更强的控制力它使得类的继承关系从开放变为有意识的设计这在与后面要讲的模式匹配结合时威力会得到最大程度的体现因为编译器能够知晓所有可能的子类型从而检查语句是否穷尽了所有情况高频面试题和是不是一样的的功能不是比更强吗这是一个非常经典的问题虽然和的注解在目标上即减少数据类的样板代码有重叠但它们在本质设计哲学和使用方式上有着根本性的不同一句话总结就是比牛逼语法现代化模式匹配本章导读在模式匹配出现之前处理不同类型的对象通常需要遵循一个繁琐的固定流程类型检查强制类型转换使用转换后的变量这个过程不仅冗长而且容易出错例如忘记转换或转换错误模式匹配旨在彻底改变这一现状它将类型测试变量声明和条件提取合并为一步让我们能以一种更具声明性的方式来表达如果对象是这种模式就这么做的模式匹配这是模式匹配最基础最直接的应用它首先对我们熟悉的运算符进行了增强核心思想在类型检查成功后直接声明一个该类型的变量省去手动强制转换的步骤假设是一个类型的变量可能包含等传统写法之前类型检查后需要显式向下转型传统方式打印大写模式匹配写法类型检查和变量声明绑定一步完成模式匹配打印大写直接使用无需转型小结模式匹配虽然只是一个小改动但它为更强大的模式匹配奠定了基础并培养了我们用模式的思维去替代传统类型判断的习惯表达式与模式匹配这是模式匹配的完全体它极大地增强了语句和表达式的能力使其可以对任意类型的对象进行匹配并应用更复杂的逻辑模式匹配核心增强速查表新特性功能描述类型模式标签可以直接匹配对象的类型如现在可以直接处理情况无需在外部进行判断守护模式使用关键字为增加一个额外的布尔条件如穷尽性检查安全保障当对类或枚举进行时编译器会检查是否覆盖了所有可能的子类型否则会报错场景使用模式匹配和接口重构图形面积计算背景我们重用上一节定义的编写一个方法使用模式匹配来计算不同形状的面积复用上一节定义的继承体系使用表达式进行模式匹配直接处理输入类型模式匹配类型并创建变量守护模式匹配类型但只有当边长大于时才进入该分支分支覆盖所有其他情况例如一个边长小于等于的如果不写对于接口编译器会检查是否穷尽所有可能在本例中因为有条件所以必须有圆形面积方形面积图形面积无效方形面积输出圆形面积方形面积图形面积无效方形面积小结模式匹配是语法现代化的一个巨大飞跃它将类型判断条件逻辑和安全性通过穷尽性检查完美结合使得我们处理复杂多态的业务逻辑时代码能变得像查表一样清晰直观和安全",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-14 15:58:33",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#8-0-Java%E6%96%B0%E8%AF%AD%E6%B3%95%E6%80%BB%E7%BB%93"><span class="toc-text">8.0 Java新语法总结</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-1-%E6%A0%B8%E5%BF%83-Java-8-Lambda-%E8%A1%A8%E8%BE%BE%E5%BC%8F%E4%B8%8E%E6%96%B9%E6%B3%95%E5%BC%95%E7%94%A8"><span class="toc-text">8.1 [核心] [Java 8+] Lambda 表达式与方法引用</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-1-%E8%83%8C%E6%99%AF%EF%BC%9A%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E5%BC%95%E5%85%A5-Lambda"><span class="toc-text">8.1.1 背景：为什么要引入 Lambda</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-2-%E6%A0%B8%E5%BF%83%EF%BC%9ALambda-%E8%AF%AD%E6%B3%95%E4%B8%8E%E5%87%BD%E6%95%B0%E5%BC%8F%E6%8E%A5%E5%8F%A3"><span class="toc-text">8.1.2 核心：Lambda 语法与函数式接口</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-Lambda-%E8%A1%A8%E8%BE%BE%E5%BC%8F%E7%9A%84%E4%B8%89%E9%83%A8%E5%88%86%E6%9E%84%E6%88%90%EF%BC%9A-%E5%8F%82%E6%95%B0-%E6%96%B9%E6%B3%95%E4%BD%93-%E4%B8%80%E7%A7%8D%E6%9B%B4%E7%B4%A7%E5%87%91%E7%9A%84%E4%BB%A3%E7%A0%81%E8%A1%A8%E7%A4%BA%E6%B3%95"><span class="toc-text">1. Lambda 表达式的三部分构成：(参数) -&gt; {方法体} - 一种更紧凑的代码表示法</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E5%87%BD%E6%95%B0%E5%BC%8F%E6%8E%A5%E5%8F%A3-FunctionalInterface-Lambda-%E7%9A%84%E7%B1%BB%E5%9E%8B%E5%9F%BA%E7%A1%80"><span class="toc-text">2. 函数式接口 (@FunctionalInterface) - Lambda 的类型基础</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-3-%E8%BF%9B%E9%98%B6%EF%BC%9A%E6%96%B9%E6%B3%95%E5%BC%95%E7%94%A8%E7%9A%84%E5%9B%9B%E7%A7%8D%E7%B1%BB%E5%9E%8B"><span class="toc-text">8.1.3 进阶：方法引用的四种类型 (::)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-4-%E9%AB%98%E9%A2%91%E9%9D%A2%E8%AF%95%E9%A2%98-Lambda-%E4%B8%8E%E5%8C%BF%E5%90%8D%E5%86%85%E9%83%A8%E7%B1%BB%E7%9A%84%E5%8C%BA%E5%88%AB"><span class="toc-text">8.1.4 [高频面试题] Lambda 与匿名内部类的区别</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86%E9%9D%A9%E5%91%BD-Java-8-Stream-API"><span class="toc-text">8.2 [数据处理革命] [Java 8+] Stream API</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-1-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%EF%BC%9A%E6%B5%81%E7%9A%84%E5%88%9B%E5%BB%BA%E4%B8%8E%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F"><span class="toc-text">8.2.1 核心概念：流的创建与生命周期</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-2-%E4%B8%AD%E9%97%B4%E6%93%8D%E4%BD%9C%E8%AF%A6%E8%A7%A3"><span class="toc-text">8.2.2 中间操作详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E7%AD%9B%E9%80%89%E4%B8%8E%E5%88%87%E7%89%87-%E4%BB%8E%E6%B5%81%E4%B8%AD%E9%80%89%E5%8F%96%E6%88%91%E4%BB%AC%E9%9C%80%E8%A6%81%E7%9A%84%E5%85%83%E7%B4%A0"><span class="toc-text">1. 筛选与切片 - 从流中选取我们需要的元素</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E6%98%A0%E5%B0%84-%E5%B0%86%E6%B5%81%E4%B8%AD%E5%85%83%E7%B4%A0%E8%BD%AC%E6%8D%A2%E4%B8%BA%E5%85%B6%E4%BB%96%E5%BD%A2%E5%BC%8F%E6%88%96%E6%8F%90%E5%8F%96%E4%BF%A1%E6%81%AF"><span class="toc-text">2. 映射 - 将流中元素转换为其他形式或提取信息</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E6%8E%92%E5%BA%8F-%E5%AF%B9%E6%B5%81%E4%B8%AD%E5%85%83%E7%B4%A0%E8%BF%9B%E8%A1%8C%E6%8E%92%E5%BA%8F"><span class="toc-text">3. 排序 - 对流中元素进行排序</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-3-%E7%BB%88%E7%AB%AF%E6%93%8D%E4%BD%9C%E8%AF%A6%E8%A7%A3"><span class="toc-text">8.2.3 终端操作详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E5%8C%B9%E9%85%8D%E4%B8%8E%E6%9F%A5%E6%89%BE-%E6%A3%80%E6%9F%A5%E6%B5%81%E4%B8%AD%E5%85%83%E7%B4%A0%E6%98%AF%E5%90%A6%E6%BB%A1%E8%B6%B3%E7%89%B9%E5%AE%9A%E6%9D%A1%E4%BB%B6"><span class="toc-text">1. 匹配与查找 - 检查流中元素是否满足特定条件</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E8%A7%84%E7%BA%A6-Reduction-%E5%B0%86%E6%B5%81%E4%B8%AD%E6%89%80%E6%9C%89%E5%85%83%E7%B4%A0%E8%AE%A1%E7%AE%97%EF%BC%8C%E5%BE%97%E5%88%B0%E4%B8%80%E4%B8%AA%E5%80%BC"><span class="toc-text">2. 规约 (Reduction) - 将流中所有元素计算，得到一个值</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E6%94%B6%E9%9B%86-Collect-%E5%B0%86%E6%B5%81%E4%B8%AD%E5%85%83%E7%B4%A0%E8%BD%AC%E6%8D%A2%E6%88%90%E9%9B%86%E5%90%88%E3%80%81Map%E6%88%96%E5%85%B6%E4%BB%96%E5%A4%8D%E6%9D%82%E5%AF%B9%E8%B1%A1"><span class="toc-text">3. 收集 (Collect) - 将流中元素转换成集合、Map或其他复杂对象</span></a></li></ol></li></ol><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-%E7%A9%BA%E6%8C%87%E9%92%88%E7%BB%88%E7%BB%93%E8%80%85-Java-8-Optional-%E5%AE%B9%E5%99%A8%E7%B1%BB"><span class="toc-text">8.3 [空指针终结者] [Java 8+] Optional 容器类</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-3-1-%E8%AE%BE%E8%AE%A1%E5%93%B2%E5%AD%A6%EF%BC%9A%E4%B8%BA%E4%BB%80%E4%B9%88%E9%9C%80%E8%A6%81-Optional"><span class="toc-text">8.3.1 设计哲学：为什么需要 Optional</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-3-2-%E6%A0%B8%E5%BF%83%E6%96%B9%E6%B3%95%E5%88%86%E7%B1%BB%E4%BD%BF%E7%94%A8"><span class="toc-text">8.3.2 核心方法分类使用</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E5%88%9B%E5%BB%BA-Optional-%E5%AF%B9%E8%B1%A1-%E5%8C%85%E8%A3%85%E4%BD%A0%E7%9A%84%E5%80%BC"><span class="toc-text">1. 创建 Optional 对象 - 包装你的值</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E8%8E%B7%E5%8F%96%E5%80%BC%EF%BC%88%E5%B8%A6%E9%BB%98%E8%AE%A4%E5%A4%84%E7%90%86%EF%BC%89-%E5%AE%89%E5%85%A8%E5%9C%B0%E5%8F%96%E5%87%BA%E7%BB%93%E6%9E%9C"><span class="toc-text">2. 获取值（带默认处理） - 安全地取出结果</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E5%80%BC%E7%9A%84%E8%BD%AC%E6%8D%A2%E4%B8%8E%E8%BF%87%E6%BB%A4-%E4%BB%A5%E5%87%BD%E6%95%B0%E5%BC%8F%E9%A3%8E%E6%A0%BC%E5%A4%84%E7%90%86%E5%80%BC"><span class="toc-text">3. 值的转换与过滤 - 以函数式风格处理值</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#4-%E5%88%A4%E6%96%AD%E4%B8%8E%E6%B6%88%E8%B4%B9-%E5%9C%A8%E5%80%BC%E5%AD%98%E5%9C%A8%E6%97%B6%E6%89%A7%E8%A1%8C%E6%93%8D%E4%BD%9C"><span class="toc-text">4. 判断与消费 - 在值存在时执行操作</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-4-%E8%AF%AD%E8%A8%80%E5%A2%9E%E5%BC%BA-Java-8-%E8%AF%AD%E8%A8%80%E5%8F%8A-API-%E5%A2%9E%E5%BC%BA"><span class="toc-text">8.4 [语言增强] [Java 8+] 语言及 API 增强</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-4-1-%E6%8E%A5%E5%8F%A3%E7%9A%84%E6%BC%94%E8%BF%9B-Java-8"><span class="toc-text">8.4.1 接口的演进 [Java 8]</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E9%BB%98%E8%AE%A4%E6%96%B9%E6%B3%95-default-%E5%9C%A8%E4%B8%8D%E7%A0%B4%E5%9D%8F%E5%AE%9E%E7%8E%B0%E7%9A%84%E5%89%8D%E6%8F%90%E4%B8%8B%E4%B8%BA%E6%8E%A5%E5%8F%A3%E6%B7%BB%E5%8A%A0%E6%96%B0%E5%8A%9F%E8%83%BD"><span class="toc-text">1. 默认方法 (default) - 在不破坏实现的前提下为接口添加新功能</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E9%9D%99%E6%80%81%E6%96%B9%E6%B3%95-static-%E5%9C%A8%E6%8E%A5%E5%8F%A3%E4%B8%AD%E5%AE%9A%E4%B9%89%E5%B7%A5%E5%85%B7%E6%96%B9%E6%B3%95"><span class="toc-text">2. 静态方法 (static) - 在接口中定义工具方法</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-4-2-%E9%9B%86%E5%90%88%E5%B7%A5%E5%8E%82%E6%96%B9%E6%B3%95-Java-9"><span class="toc-text">8.4.2 集合工厂方法 [Java 9+]</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-4-3-%E5%B8%B8%E7%94%A8-API-%E5%A2%9E%E5%BC%BA-Java-11"><span class="toc-text">8.4.3 常用 API 增强 [Java 11+]</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-5-%E8%AF%AD%E6%B3%95%E7%8E%B0%E4%BB%A3%E5%8C%96-Java-10-%E5%B1%80%E9%83%A8%E5%8F%98%E9%87%8F%E7%B1%BB%E5%9E%8B%E6%8E%A8%E6%96%AD-var"><span class="toc-text">8.5 [语法现代化] [Java 10+] 局部变量类型推断 (var)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-5-1-var-%E7%9A%84%E4%BD%BF%E7%94%A8%E5%9C%BA%E6%99%AF%E4%B8%8E%E4%BC%98%E5%8A%BF"><span class="toc-text">8.5.1 var 的使用场景与优势</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-5-2-%E9%81%BF%E5%9D%91%E6%8C%87%E5%8D%97-var-%E7%9A%84%E4%BD%BF%E7%94%A8%E9%99%90%E5%88%B6"><span class="toc-text">8.5.2 [避坑指南] var 的使用限制</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-6-%E8%AF%AD%E6%B3%95%E7%8E%B0%E4%BB%A3%E5%8C%96-Java-16-17-Record-%E7%B1%BB%E4%B8%8E-Sealed-%E7%B1%BB"><span class="toc-text">8.6 [语法现代化] [Java 16/17+] Record 类与 Sealed 类</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-6-1-Record-%E7%B1%BB%EF%BC%9A%E4%B8%8D%E5%8F%AF%E5%8F%98%E6%95%B0%E6%8D%AE%E8%BD%BD%E4%BD%93-Java-16"><span class="toc-text">8.6.1 Record 类：不可变数据载体 [Java 16+]</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-6-2-Sealed-%E7%B1%BB-%E6%8E%A5%E5%8F%A3%EF%BC%9A%E7%B2%BE%E5%87%86%E7%9A%84%E7%BB%A7%E6%89%BF%E6%8E%A7%E5%88%B6-Java-17"><span class="toc-text">8.6.2 Sealed 类/接口：精准的继承控制 [Java 17+]</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E9%AB%98%E9%A2%91%E9%9D%A2%E8%AF%95%E9%A2%98-Record-VS-Lombok"><span class="toc-text">[高频面试题] Record VS Lombok</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-7-%E8%AF%AD%E6%B3%95%E7%8E%B0%E4%BB%A3%E5%8C%96-Java-16-21-%E6%A8%A1%E5%BC%8F%E5%8C%B9%E9%85%8D"><span class="toc-text">8.7 [语法现代化] [Java 16-21+] 模式匹配</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-7-1-instanceof-%E7%9A%84%E6%A8%A1%E5%BC%8F%E5%8C%B9%E9%85%8D-Java-16"><span class="toc-text">8.7.1 instanceof 的模式匹配 [Java 16+]</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-7-2-switch-%E8%A1%A8%E8%BE%BE%E5%BC%8F%E4%B8%8E%E6%A8%A1%E5%BC%8F%E5%8C%B9%E9%85%8D-Java-21"><span class="toc-text">8.7.2 switch 表达式与模式匹配 [Java 21+]</span></a></li></ol></li></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Java基础知识总汇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">Java（八）：8.0 Java新语法总结</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-05-08T15:13:45.000Z" title="发表于 2025-05-08 23:13:45">2025-05-08</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-14T07:58:33.048Z" title="更新于 2025-07-14 15:58:33">2025-07-14</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">14.3k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>55分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="Java（八）：8.0 Java新语法总结"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/14501.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/14501.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url">Java基础知识总汇</a><h1 id="CrawlerTitle" itemprop="name headline">Java（八）：8.0 Java新语法总结</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-05-08T15:13:45.000Z" title="发表于 2025-05-08 23:13:45">2025-05-08</time><time itemprop="dateCreated datePublished" datetime="2025-07-14T07:58:33.048Z" title="更新于 2025-07-14 15:58:33">2025-07-14</time></header><div id="postchat_postcontent"><h2 id="8-0-Java新语法总结"><a href="#8-0-Java新语法总结" class="headerlink" title="8.0 Java新语法总结"></a>8.0 Java新语法总结</h2><h3 id="8-1-核心-Java-8-Lambda-表达式与方法引用"><a href="#8-1-核心-Java-8-Lambda-表达式与方法引用" class="headerlink" title="8.1 [核心] [Java 8+] Lambda 表达式与方法引用"></a>8.1 [核心] [Java 8+] Lambda 表达式与方法引用</h3><blockquote><p><strong>本章导读</strong>: Java 8 的发布是 Java 发展史上的一个里程碑，其最核心、最具代表性的特性无疑是 <strong>Lambda 表达式</strong>的引入。它将<strong>函数式编程</strong>思想正式带入了 Java 世界，从根本上改变了我们处理<strong>行为参数化</strong> 的方式。在本节中，我们将从 Lambda 的诞生背景出发，深入其核心语法、<strong>函数式接口</strong>，并最终掌握其语法糖——<strong>方法引用</strong>的使用技巧，为我们后续学习 Stream API 等高级特性打下坚实的基础。</p></blockquote><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.smartcis.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250714145916792.png" alt="image-20250714145916792"></p><h4 id="8-1-1-背景：为什么要引入-Lambda"><a href="#8-1-1-背景：为什么要引入-Lambda" class="headerlink" title="8.1.1 背景：为什么要引入 Lambda"></a>8.1.1 背景：为什么要引入 Lambda</h4><p>在 Java 8 之前，如果我们想将一个行为（一段代码逻辑）传递给另一个方法，通常需要依赖<strong>匿名内部类</strong> (<code>Anonymous Inner Class</code>)。这种写法不仅语法冗长、可读性差，而且会为每个实例生成一个独立的 <code>.class</code> 文件，显得非常笨重。</p><p>让我们通过一个简单的例子直观感受一下：对一个字符串列表按长度进行排序。</p><p><strong>背景</strong>：假设我们需要对一个 <code>List&lt;String&gt;</code> 集合，按照字符串的长度进行升序排序。</p><p>####### <strong>场景1：Java 8 之前的实现方式 - 使用匿名内部类</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.Collections;</span><br><span class="line"><span class="keyword">import</span> java.util.Comparator;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        List&lt;String&gt; names = Arrays.asList(<span class="string">"peter"</span>, <span class="string">"anna"</span>, <span class="string">"mike"</span>, <span class="string">"xenia"</span>);</span><br><span class="line">        <span class="comment">// 按照字符串的长度进行升序排序。</span></span><br><span class="line">        names.sort(<span class="keyword">new</span> <span class="title class_">Comparator</span>&lt;String&gt;() {</span><br><span class="line">            <span class="meta">@Override</span></span><br><span class="line">            <span class="keyword">public</span> <span class="type">int</span> <span class="title function_">compare</span><span class="params">(String a, String b)</span> {</span><br><span class="line">                <span class="keyword">return</span> a.length() - b.length();</span><br><span class="line">            }</span><br><span class="line">        });</span><br><span class="line">        System.out.println(<span class="string">"排序后的列表: "</span> + names);</span><br><span class="line"></span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出： </span></span><br><span class="line"><span class="comment">// 排序后的列表: [anna, mike, peter, xenia]</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: 我们可以看到，为了实现一个简单的比较逻辑，我们不得不编写一个完整的匿名内部类实例，其中真正核心的代码只有一行 <code>a.length() - b.length()</code>，其余都是模板化的语法噪音。这种写法显得“头重脚轻”，不够优雅。Lambda 表达式的诞生，正是为了解决这一痛点。</p></blockquote><h4 id="8-1-2-核心：Lambda-语法与函数式接口"><a href="#8-1-2-核心：Lambda-语法与函数式接口" class="headerlink" title="8.1.2 核心：Lambda 语法与函数式接口"></a>8.1.2 核心：Lambda 语法与函数式接口</h4><h6 id="1-Lambda-表达式的三部分构成：-参数-方法体-一种更紧凑的代码表示法"><a href="#1-Lambda-表达式的三部分构成：-参数-方法体-一种更紧凑的代码表示法" class="headerlink" title="1. Lambda 表达式的三部分构成：(参数) -> {方法体} - 一种更紧凑的代码表示法"></a>1. Lambda 表达式的三部分构成：<code>(参数) -&gt; {方法体}</code> - 一种更紧凑的代码表示法</h6><p>Lambda 表达式本质上是一个<strong>匿名方法</strong>，它提供了一种清晰、简洁的方式来表示一个函数式接口的实例。其构成如下：</p><ul><li><strong>参数列表 <code>(parameters)</code></strong>：与接口中抽象方法的参数列表相对应。可以省略参数类型，编译器会自动推断。如果只有一个参数，可以省略括号 <code>()</code>。</li><li><strong>箭头符号 <code>-&gt;</code></strong>：将参数列表与方法体分开，读作 “goes to”。</li><li><strong>方法体 <code>body</code></strong>：包含了方法的实现逻辑。如果方法体只有一条语句，可以省略大括号 <code>{}</code> 和 <code>return</code> 关键字。</li></ul><p>####### <strong>场景1：使用 Lambda 表达式重写排序</strong></p><p><strong>背景</strong>：同样是对字符串列表按长度排序，我们看看使用 Lambda 如何实现。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        List &lt;String&gt; names = Arrays.asList(<span class="string">"peter"</span>, <span class="string">"anna"</span>, <span class="string">"mike"</span>, <span class="string">"xenia"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 1. 带有类型声明的完整语法</span></span><br><span class="line">        <span class="comment">// names.sort((String a, String b) -&gt; {</span></span><br><span class="line">        <span class="comment">//     return a.length() - b.length();</span></span><br><span class="line">        <span class="comment">// });</span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// 2. 省略类型声明，由编译器推断 (推荐)</span></span><br><span class="line">        <span class="comment">// names.sort((a, b) -&gt; {</span></span><br><span class="line">        <span class="comment">//     return a.length() - b.length();</span></span><br><span class="line">        <span class="comment">// });</span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// 3. 当方法体只有一行时，省略大括号和 return (最简洁，推荐)</span></span><br><span class="line">        names.sort((a, b) -&gt; a.length() - b.length());</span><br><span class="line"></span><br><span class="line">        System.out.println(<span class="string">"使用 Lambda 排序后的列表: "</span> + names);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// 使用 Lambda 排序后的列表: [anna, mike, peter, xenia]</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: 对比之前的匿名内部类，Lambda 表达式极大地简化了代码，使我们的意图一目了然：<strong>传入两个字符串 <code>a</code> 和 <code>b</code>，返回它们的长度差</strong>。</p></blockquote><h6 id="2-函数式接口-FunctionalInterface-Lambda-的类型基础"><a href="#2-函数式接口-FunctionalInterface-Lambda-的类型基础" class="headerlink" title="2. 函数式接口 (@FunctionalInterface) - Lambda 的类型基础"></a>2. 函数式接口 (<code>@FunctionalInterface</code>) - Lambda 的类型基础</h6><p>Lambda 表达式本身没有类型，它必须被赋值给一个<strong>函数式接口</strong>类型的变量。</p><blockquote><p><strong>定义</strong>：函数式接口是<strong>有且仅有一个抽象方法</strong>的接口（可以包含多个默认方法或静态方法）。</p></blockquote><p>为了让编译器强制检查一个接口是否为函数式接口，Java 8 引入了 <code>@FunctionalInterface</code> 注解。</p><blockquote><p><strong>最佳实践</strong>: 我们强烈推荐为所有函数式接口添加 <code>@FunctionalInterface</code> 注解，以明确接口意图并利用编译时检查。</p></blockquote><ul><li><strong>常用函数式接口速查表</strong></li></ul><p><code>java.util.function</code> 包中预定义了大量常用的函数式接口，以下是四大核心接口：</p><table><thead><tr><th align="left">接口名</th><th align="left">抽象方法</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>Predicate&lt;T&gt;</code></td><td align="left"><code>boolean test(T t)</code></td><td align="left"><strong>（主力/推荐）接收一个参数并返回一个布尔值，常用于过滤</strong>。</td></tr><tr><td align="left"><code>Function&lt;T, R&gt;</code></td><td align="left"><code>R apply(T t)</code></td><td align="left"><strong>（主力/推荐）接收一个参数，返回一个结果，常用于映射/转换</strong>。</td></tr><tr><td align="left"><code>Supplier&lt;T&gt;</code></td><td align="left"><code>T get()</code></td><td align="left">不接收参数，返回一个结果，常用于对象的<strong>工厂方法</strong>。</td></tr><tr><td align="left"><code>Consumer&lt;T&gt;</code></td><td align="left"><code>void accept(T t)</code></td><td align="left">接收一个参数，没有返回值，常用于对元素执行某种<strong>消费操作</strong>（如打印）。</td></tr></tbody></table><p>####### <strong>场景2：四大核心函数式接口实战</strong></p><p><strong>背景</strong>：我们将通过一个简单的程序来演示这四个核心接口在实际代码中的应用。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.function.Consumer;</span><br><span class="line"><span class="keyword">import</span> java.util.function.Function;</span><br><span class="line"><span class="keyword">import</span> java.util.function.Predicate;</span><br><span class="line"><span class="keyword">import</span> java.util.function.Supplier;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        <span class="comment">// 1. Predicate &lt;T&gt;: 判断一个整数是否大于 10</span></span><br><span class="line">        Predicate &lt;Integer&gt; isGreaterThan10 = x -&gt; x &gt; <span class="number">10</span>;</span><br><span class="line">        System.out.println(<span class="string">"15 是否大于 10? "</span> + isGreaterThan10.test(<span class="number">15</span>));</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 2. Function &lt;T, R&gt;: 将字符串转换为其长度</span></span><br><span class="line">        Function &lt;String, Integer&gt; stringLengthFunction = s -&gt; s.length();</span><br><span class="line">        System.out.println(<span class="string">"'hello' 的长度是: "</span> + stringLengthFunction.apply(<span class="string">"hello"</span>));</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 3. Consumer &lt;T&gt;: 打印传入的字符串</span></span><br><span class="line">        Consumer &lt;String&gt; printConsumer = s -&gt; System.out.println(<span class="string">"正在消费: "</span> + s);</span><br><span class="line">        printConsumer.accept(<span class="string">"This is a message."</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 4. Supplier &lt;T&gt;: 生成一个随机数</span></span><br><span class="line">        Supplier &lt;Double&gt; randomSupplier = () -&gt; Math.random();</span><br><span class="line">        System.out.println(<span class="string">"生成的随机数: "</span> + randomSupplier.get());</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// 15 是否大于 10? true</span></span><br><span class="line"><span class="comment">// 'hello' 的长度是: 5</span></span><br><span class="line"><span class="comment">// 正在消费: This is a message.</span></span><br><span class="line"><span class="comment">// 生成的随机数: 0.12345... (每次不同)</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p>你可能已经注意到，上面的示例代码中我们直接使用了 Predicate、Function 等接口，但并没有看到 @FunctionalInterface 注解的影子。这是为什么呢？</p></blockquote><p><strong>关键在于区分“接口的定义者”与“接口的使用者”：</strong></p><ol><li><strong>定义者</strong>：<code>@FunctionalInterface</code> 注解是给<strong>接口的创建者</strong>使用的。Java 官方在 设计 Predicate, Function 等接口时，<strong>已经在它们的源码中添加了 <code>@FunctionalInterface</code> 注解</strong>。这保证了 JDK 提供的这些接口绝对符合函数式接口的规范。</li><li><strong>使用者</strong>：在我们的示例代码中，我们是这些接口的<strong>使用者</strong>。我们直接利用这些已经由 JDK 定义好并验证过的接口作为 Lambda 表达式的类型，而不需要（也不能）再去重复声明注解。</li></ol><h4 id="8-1-3-进阶：方法引用的四种类型"><a href="#8-1-3-进阶：方法引用的四种类型" class="headerlink" title="8.1.3 进阶：方法引用的四种类型 (::)"></a>8.1.3 进阶：方法引用的四种类型 (<code>::</code>)</h4><p><strong>方法引用</strong> (<code>Method Reference</code>) 是 Lambda 表达式的一种特殊语法糖，它允许我们通过方法的名字来直接引用一个已经存在的方法。当 Lambda 表达式的方法体已经有现成的方法可以实现时，使用方法引用会让代码更加简洁易读。</p><p><strong>用更具体的例子来说：</strong></p><p>假设我们要对一个字符串列表进行排序。</p><ul><li><p><strong>不使用方法引用：</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">List &lt;String&gt; names = Arrays.asList(<span class="string">"张三"</span>, <span class="string">"李四"</span>, <span class="string">"王五"</span>);</span><br><span class="line">Collections.sort(names, (s1, s2) -&gt; s1.compareTo(s2)); <span class="comment">// Lambda 表达式</span></span><br></pre></td></tr></tbody></table></figure></li></ul><p>这里，<code>(s1, s2) -&gt; s1.compareTo(s2)</code> 就是你的 Lambda 表达式，你是在“教” <code>sort</code> 方法如何比较两个字符串。</p><ul><li><p><strong>使用方法引用（就像直接用现成的菜）：</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">List &lt;String&gt; names = Arrays.asList(<span class="string">"张三"</span>, <span class="string">"李四"</span>, <span class="string">"王五"</span>);</span><br><span class="line">Collections.sort(names, String:: compareTo); <span class="comment">// 方法引用</span></span><br></pre></td></tr></tbody></table></figure><p><code>String::compareTo</code> 就是对已经存在的 <code>String</code> 类里的 <code>compareTo</code> 方法的引用。你不再需要写 <code>(s1, s2) -&gt; s1.compareTo(s2)</code>，而是直接说：“用 <code>String</code> 类那个叫 <code>compareTo</code> 的方法来比吧！”</p></li></ul><table><thead><tr><th align="left">方法引用类型</th><th align="left">示例语法（实际代码）</th><th align="left">Lambda 等价形式（实际代码）</th><th align="left">核心意图</th></tr></thead><tbody><tr><td align="left"><strong>引用静态方法</strong></td><td align="left"><code>类名::静态方法名</code></td><td align="left"><code>参数 -&gt; 类名.静态方法名(参数)</code></td><td align="left">调用类的静态方法</td></tr><tr><td align="left"><strong>引用特定类型的任意对象的实例方法</strong></td><td align="left"><code>类名::实例方法名</code></td><td align="left"><code>对象实例, 参数 -&gt; 对象实例.实例方法名(参数)</code></td><td align="left">调用对象身上的实例方法（对象是传入的）</td></tr><tr><td align="left"><strong>引用特定对象的实例方法</strong></td><td align="left"><code>具体对象::实例方法名</code></td><td align="left"><code>参数 -&gt; 具体对象.实例方法名(参数)</code></td><td align="left">调用某个固定对象的实例方法</td></tr><tr><td align="left"><strong>引用构造函数</strong></td><td align="left"><code>类名::new</code></td><td align="left"><code>参数 -&gt; new 类名(参数)</code></td><td align="left">创建类的实例</td></tr></tbody></table><p><strong>说明：</strong></p><ul><li>这里的“参数”是根据实际的函数式接口定义来决定的，可能是一个或多个参数，也可能没有参数。</li><li>“对象实例”在“引用特定类型的任意对象的实例方法”中，是作为第一个隐式参数传入的。</li><li>“具体对象”在“引用特定对象的实例方法”中，是方法引用语法的一部分，已经固定了。</li></ul><p>####### <strong>方法引用实战场景详解</strong></p><p><strong>背景</strong>：我们将通过代码示例逐一展示四种方法引用的应用场景。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.function.Consumer;</span><br><span class="line"><span class="keyword">import</span> java.util.function.Function;</span><br><span class="line"><span class="keyword">import</span> java.util.function.Supplier;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        List &lt;String&gt; strList = Arrays.asList(<span class="string">"apple"</span>, <span class="string">"banana"</span>, <span class="string">"cherry"</span>);</span><br><span class="line">        <span class="comment">// 场景 1: 引用静态方法</span></span><br><span class="line">        <span class="comment">// 将字符串转换为整数</span></span><br><span class="line">        Function &lt;String, Integer&gt; parseIntFunc = s -&gt; Integer.parseInt(s.replaceAll(<span class="string">"\\D+"</span>, <span class="string">""</span>));</span><br><span class="line">        Function &lt;String, Integer&gt; valueOfFunc = Integer:: valueOf;</span><br><span class="line">        System.out.println(<span class="string">"字符串 '123' 转为整数: "</span> + valueOfFunc.apply(<span class="string">"123"</span>));</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 场景 2: 引用特定类型的任意对象的实例方法</span></span><br><span class="line">        <span class="comment">// 将字符串列表全部转为大写</span></span><br><span class="line">        strList.stream().map(String:: toUpperCase).forEach(s -&gt; System.out.print(s + <span class="string">" "</span>));</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 场景 3: 引用特定对象的实例方法</span></span><br><span class="line">        <span class="comment">// 使用 System.out 对象的 println 方法</span></span><br><span class="line">        Consumer &lt;String&gt; printer = System.out:: println;</span><br><span class="line">        printer.accept(<span class="string">"Hello, Method Reference!"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 场景 4: 引用构造函数</span></span><br><span class="line">        <span class="comment">// 创建一个 Person 对象</span></span><br><span class="line">        Supplier &lt;Person&gt; personSupplier = Person:: <span class="keyword">new</span>;</span><br><span class="line">        <span class="type">Person</span> <span class="variable">person</span> <span class="operator">=</span> personSupplier.get();</span><br><span class="line">        System.out.println(<span class="string">"创建了一个新 Person: "</span> + person);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Person</span> {</span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> String <span class="title function_">toString</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">return</span> <span class="string">"A Person instance"</span>;</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: 当 Lambda 的逻辑仅仅是调用一个已存在的方法时，方法引用是最佳选择。它能消除冗余的参数声明，让代码的**“意图”<strong>而非</strong>“实现细节”**凸显出来。</p></blockquote><h4 id="8-1-4-高频面试题-Lambda-与匿名内部类的区别"><a href="#8-1-4-高频面试题-Lambda-与匿名内部类的区别" class="headerlink" title="8.1.4 [高频面试题] Lambda 与匿名内部类的区别"></a>8.1.4 [高频面试题] Lambda 与匿名内部类的区别</h4><blockquote><p><strong>Q: Lambda 表达式和匿名内部类有什么核心区别？</strong></p><p><strong>A:</strong> 尽管 Lambda 表达式在很多场景下可以替代匿名内部类，但它们在底层实现和行为上存在显著差异。</p><ol><li><strong><code>this</code> 的指向不同 (Scope)</strong>: 这是最核心的区别。</li></ol><ul><li><strong>匿名内部类</strong>: <code>this</code> 关键字指向的是匿名内部类<strong>自身的实例</strong>。<br>* <strong>Lambda 表达式</strong>: <code>this</code> 关键字指向的是其<strong>外层封装类</strong>的实例。Lambda 表达式本身没有自己的 <code>this</code>，它在词法上是其外层作用域的一部分。</li></ul><ol start="2"><li><strong>编译后产物不同</strong>:</li></ol><ul><li><strong>匿名内部类</strong>: 编译器会为每一个匿名内部类生成一个单独的 <code>.class</code> 文件，格式通常是 <code>EnclosingClass$1.class</code>。<ul><li><strong>Lambda 表达式</strong>: 编译器不会为每个 Lambda 生成一个独立的 <code>.class</code> 文件。它会将 Lambda 的逻辑翻译成一个私有的静态方法，并在运行时通过 <code>invokedynamic</code> 指令来动态地创建实现函数式接口的对象。这种方式在性能和内存占用上通常更优。</li></ul></li></ul><ol start="3"><li><strong>功能限制</strong>:</li></ol><ul><li><strong>匿名内部类</strong>: 功能更强大。它可以实现有多个抽象方法的接口，可以继承具体的类，也可以拥有自己的实例变量和方法。<br>* <strong>Lambda 表达式</strong>: 功能更专注。它只能用于实现<strong>函数式接口</strong>（即只有一个抽象方法的接口）。</li></ul><ol start="4"><li>**性能 :</li></ol><ul><li>由于 <code>invokedynamic</code> 的机制，JVM 在运行时有更多的优化空间，比如可以对 Lambda 的调用进行内联或者延迟实例化，因此在很多情况下，Lambda 的性能会优于或等于匿名内部类。</li></ul></blockquote><hr><h3 id="8-2-数据处理革命-Java-8-Stream-API"><a href="#8-2-数据处理革命-Java-8-Stream-API" class="headerlink" title="8.2 [数据处理革命] [Java 8+] Stream API"></a>8.2 [数据处理革命] [Java 8+] Stream API</h3><blockquote><p><strong>本章导读</strong>: 如果说 Lambda 表达式是 Java 8 的“引擎”，那么 Stream API 就是搭载了这个强大引擎的“超级跑车”。它提供了一套声明式、可组合、可并行的 API，彻底改变了我们对集合数据的处理方式。告别繁琐的 <code>for</code> 循环和临时变量，Stream API 让我们能以一种更优雅、更符合人类思维的“流水线”方式来操作数据。本节，我们将全面探索 Stream 的世界。</p></blockquote><h4 id="8-2-1-核心概念：流的创建与生命周期"><a href="#8-2-1-核心概念：流的创建与生命周期" class="headerlink" title="8.2.1 核心概念：流的创建与生命周期"></a>8.2.1 核心概念：流的创建与生命周期</h4><p>一个典型的 Stream 操作流包含三个阶段：</p><ol><li><strong>创建 (Creation)</strong>: 从一个数据源（如集合、数组）获取一个流。</li><li><strong>中间操作 (Intermediate Operations)</strong>: 对流进行一系列的转换或筛选操作。每个中间操作都会返回一个新的流，这使得操作可以像链条一样串联起来。这些操作是<strong>惰性求值 (Lazy)</strong> 的，也就是说，它们在终端操作被调用前不会真正执行。</li><li><strong>终端操作 (Terminal Operations)</strong>: 触发流的实际计算，并产生一个最终结果（如一个值、一个集合，或无返回值）。终端操作是<strong>及早求值 (Eager)</strong> 的，一旦执行，流就会被消耗且无法再次使用。</li></ol><p>####### <strong>场景1：流的多种创建方式</strong></p><p><strong>背景</strong>：在开始处理数据前，我们首先需要知道如何从不同的数据源创建流。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.stream.IntStream;</span><br><span class="line"><span class="keyword">import</span> java.util.stream.Stream;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        <span class="comment">// 1. 从集合创建流 (最常用)</span></span><br><span class="line">        List &lt;String&gt; list = Arrays.asList(<span class="string">"a"</span>, <span class="string">"b"</span>, <span class="string">"c"</span>);</span><br><span class="line">        Stream &lt;String&gt; listStream = list.stream();</span><br><span class="line">        System.out.print(<span class="string">"从 List 创建的流: "</span>);</span><br><span class="line">        listStream.forEach(s -&gt; System.out.print(s + <span class="string">" "</span>));</span><br><span class="line">        System.out.println();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 2. 从数组创建流</span></span><br><span class="line">        String [] array = {<span class="string">"x"</span>, <span class="string">"y"</span>, <span class="string">"z"</span>};</span><br><span class="line">        Stream &lt;String&gt; arrayStream = Arrays.stream(array);</span><br><span class="line">        System.out.print(<span class="string">"从 Array 创建的流: "</span>);</span><br><span class="line">        arrayStream.forEach(s -&gt; System.out.print(s + <span class="string">" "</span>));</span><br><span class="line">        System.out.println();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 3. 使用 Stream.of() 静态方法创建流</span></span><br><span class="line">        Stream &lt;Integer&gt; ofStream = Stream.of(<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>);</span><br><span class="line">        System.out.print(<span class="string">"使用 Stream.of() 创建的流: "</span>);</span><br><span class="line">        ofStream.forEach(s -&gt; System.out.print(s + <span class="string">" "</span>));</span><br><span class="line">        System.out.println();</span><br><span class="line">        </span><br><span class="line">        <span class="comment">// 4. 使用 Stream.iterate() 创建无限流</span></span><br><span class="line">        Stream &lt;Integer&gt; iterateStream = Stream.iterate(<span class="number">0</span>, n -&gt; n + <span class="number">2</span>).limit(<span class="number">5</span>); <span class="comment">// 必须用 limit 限制</span></span><br><span class="line">        System.out.print(<span class="string">"使用 Stream.iterate() 创建的无限流 (限制后): "</span>);</span><br><span class="line">        iterateStream.forEach(s -&gt; System.out.print(s + <span class="string">" "</span>));</span><br><span class="line">        System.out.println();</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// 从 List 创建的流: a b c </span></span><br><span class="line"><span class="comment">// 从 Array 创建的流: x y z </span></span><br><span class="line"><span class="comment">// 使用 Stream.of() 创建的流: 1 2 3 4 5 </span></span><br><span class="line"><span class="comment">// 使用 Stream.iterate() 创建的无限流 (限制后): 0 2 4 6 8 </span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: 创建流是所有操作的第一步。<code>collection.stream()</code> 是最常用的方式。对于无限流，务必使用 <code>limit()</code> 等中间操作将其变为有限流，否则可能会导致无限循环。</p></blockquote><h4 id="8-2-2-中间操作详解"><a href="#8-2-2-中间操作详解" class="headerlink" title="8.2.2 中间操作详解"></a>8.2.2 中间操作详解</h4><p>中间操作是 Stream API 的精髓所在，它们允许我们将复杂逻辑分解为一系列小而清晰的步骤。</p><h6 id="1-筛选与切片-从流中选取我们需要的元素"><a href="#1-筛选与切片-从流中选取我们需要的元素" class="headerlink" title="1. 筛选与切片 - 从流中选取我们需要的元素"></a>1. 筛选与切片 - 从流中选取我们需要的元素</h6><ul><li><strong>常用筛选与切片方法速查表</strong></li></ul><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>filter(Predicate&lt;T&gt;)</code></td><td align="left"><strong>(主力/推荐)</strong> 根据条件过滤流中元素，只保留满足条件的。</td></tr><tr><td align="left"><code>distinct()</code></td><td align="left">去除流中重复的元素（基于元素的 <code>equals()</code> 方法）。</td></tr><tr><td align="left"><code>limit(long)</code></td><td align="left">截断流，使其元素数量不超过给定值。</td></tr><tr><td align="left"><code>skip(long)</code></td><td align="left">跳过前 N 个元素，返回一个扔掉了前 N 个元素的新流。</td></tr></tbody></table><p>####### <strong>场景2：筛选出前两名不重复的偶数</strong></p><p><strong>背景</strong>：给定一个整数列表，我们需要找到其中不重复的偶数，并只取前两个。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        List &lt;Integer&gt; numbers = Arrays.asList(<span class="number">2</span>, <span class="number">4</span>, <span class="number">6</span>, <span class="number">4</span>, <span class="number">8</span>, <span class="number">2</span>, <span class="number">10</span>, <span class="number">12</span>);</span><br><span class="line"></span><br><span class="line">        System.out.print(<span class="string">"处理后的结果: "</span>);</span><br><span class="line">        numbers.stream()</span><br><span class="line">                .filter(n -&gt; n % <span class="number">2</span> == <span class="number">0</span>)   <span class="comment">// 筛选偶数: [2, 4, 6, 4, 8, 2, 10, 12]</span></span><br><span class="line">                .distinct()               <span class="comment">// 去重: [2, 4, 6, 8, 10, 12]</span></span><br><span class="line">                .skip(<span class="number">1</span>)                  <span class="comment">// 跳过第一个: [4, 6, 8, 10, 12]</span></span><br><span class="line">                .limit(<span class="number">2</span>)                 <span class="comment">// 取前两个: [4, 6]</span></span><br><span class="line">                .forEach(n -&gt; System.out.print(n + <span class="string">" "</span>));</span><br><span class="line">        System.out.println();</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// 处理后的结果: 4 6 </span></span><br></pre></td></tr></tbody></table></figure><h6 id="2-映射-将流中元素转换为其他形式或提取信息"><a href="#2-映射-将流中元素转换为其他形式或提取信息" class="headerlink" title="2. 映射 - 将流中元素转换为其他形式或提取信息"></a>2. 映射 - 将流中元素转换为其他形式或提取信息</h6><ul><li><strong>常用映射方法速查表</strong></li></ul><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>map(Function&lt;T,R&gt;)</code></td><td align="left"><strong>(主力/推荐)</strong> 将流中每个元素 <code>T</code> 转换为另一个元素 <code>R</code> (一对一映射)。</td></tr><tr><td align="left"><code>flatMap(Function&lt;T,Stream&lt;R&gt;&gt;)</code></td><td align="left"><strong>(进阶/常用)</strong> 将每个元素转换为一个新流，然后将所有子流连接成一个流 (一对多扁平化)。</td></tr></tbody></table><p>####### <strong>场景3：从单词列表获取所有不同的字符</strong></p><p><strong>背景</strong>：给定一个单词列表 <code>["Hello", "World"]</code>，我们希望得到其中所有字符列表 <code>[H, e, l, l, o, W, o, r, l, d]</code>。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.stream.Collectors;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        List &lt;String&gt; words = Arrays.asList(<span class="string">"Hello"</span>, <span class="string">"World"</span>);</span><br><span class="line">        List &lt;String&gt; list = words.stream()</span><br><span class="line">                .map(word -&gt; word.split(<span class="string">""</span>)) <span class="comment">// 1. 得到 Stream &lt;String[]&gt;</span></span><br><span class="line">                .flatMap(Arrays:: stream)   <span class="comment">// 2. 扁平化为 Stream &lt;String&gt;</span></span><br><span class="line">                .toList();<span class="comment">// 3. 转为 List</span></span><br><span class="line"></span><br><span class="line">        System.out.println(list);</span><br><span class="line"></span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出：</span></span><br><span class="line"><span class="comment">// [H, e, l, l, o, W, o, r, l, d]</span></span><br><span class="line"></span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: 当你的转换逻辑会从<strong>一个元素</strong>产生<strong>多个结果</strong>时（例如，从一个单词产生多个字符），<code>flatMap</code> 就是你需要的。</p></blockquote><h6 id="3-排序-对流中元素进行排序"><a href="#3-排序-对流中元素进行排序" class="headerlink" title="3. 排序 - 对流中元素进行排序"></a>3. 排序 - 对流中元素进行排序</h6><ul><li><strong>常用排序方法速查表</strong></li></ul><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>sorted()</code></td><td align="left">按自然顺序排序（元素需实现 <code>Comparable</code>）。</td></tr><tr><td align="left"><code>sorted(Comparator&lt;T&gt;)</code></td><td align="left"><strong>(主力/推荐)</strong> 根据自定义比较器进行排序，表达能力更强。</td></tr></tbody></table><p>####### <strong>场景4：对自定义对象进行排序</strong></p><p><strong>背景</strong>：我们有一个 <code>Product</code> 列表，需要先按价格降序排序，如果价格相同，再按名称升序排序。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> lombok.AllArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> lombok.Data;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.Comparator;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        List &lt;Product&gt; products = Arrays.asList(</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"Laptop"</span>, <span class="number">1200</span>), <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"Mouse"</span>, <span class="number">50</span>),</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"Keyboard"</span>, <span class="number">100</span>), <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"MousePad"</span>, <span class="number">50</span>),</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"Keyboard"</span>, <span class="number">300</span>), <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"MousePad"</span>, <span class="number">50</span>)</span><br><span class="line">        );</span><br><span class="line">        <span class="comment">// 原生 Stream 流的实现方式</span></span><br><span class="line">        products.stream()</span><br><span class="line">                .sorted(Comparator.comparing(Product:: getPrice).reversed() <span class="comment">// 按价格降序</span></span><br><span class="line">                        .thenComparing(Product:: getName))       <span class="comment">// 再按名称升序</span></span><br><span class="line">                .forEach(System.out:: println);</span><br><span class="line"></span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="meta">@Data</span></span><br><span class="line"><span class="meta">@AllArgsConstructor</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Product</span> {</span><br><span class="line">    <span class="keyword">private</span> String name;</span><br><span class="line">    <span class="keyword">private</span> <span class="type">int</span> price;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: <code>Comparator</code> 接口在 Java 8 中也得到了极大的增强，<code>comparing()</code>, <code>thenComparing()</code>, <code>reversed()</code> 等静态和默认方法使得构建复杂的多级排序逻辑变得异常简单和直观。</p></blockquote><h4 id="8-2-3-终端操作详解"><a href="#8-2-3-终端操作详解" class="headerlink" title="8.2.3 终端操作详解"></a>8.2.3 终端操作详解</h4><p>终端操作是流处理的最后一步，它会触发所有懒加载的中间操作并生成最终结果。</p><h6 id="1-匹配与查找-检查流中元素是否满足特定条件"><a href="#1-匹配与查找-检查流中元素是否满足特定条件" class="headerlink" title="1. 匹配与查找 - 检查流中元素是否满足特定条件"></a>1. 匹配与查找 - 检查流中元素是否满足特定条件</h6><ul><li><strong>常用匹配与查找方法速查表</strong></li></ul><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>anyMatch(Predicate&lt;T&gt;)</code></td><td align="left">检查流中是否<strong>至少有一个</strong>元素匹配条件。</td></tr><tr><td align="left"><code>allMatch(Predicate&lt;T&gt;)</code></td><td align="left">检查流中是否<strong>所有</strong>元素都匹配条件。</td></tr><tr><td align="left"><code>noneMatch(Predicate&lt;T&gt;)</code></td><td align="left">检查流中是否<strong>没有</strong>元素匹配条件。</td></tr><tr><td align="left"><code>findFirst()</code></td><td align="left">返回流的第一个元素，用 <code>Optional</code> 包装。</td></tr><tr><td align="left"><code>findAny()</code></td><td align="left">返回流中的任意一个元素，用 <code>Optional</code> 包装（并行流中更高效）。</td></tr></tbody></table><p>####### <strong>场景5：检查产品列表中是否存在高价商品</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Optional;</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="meta">@Data</span></span><br><span class="line"><span class="meta">@AllArgsConstructor</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Product</span> {</span><br><span class="line">    <span class="keyword">private</span> String name;</span><br><span class="line">    <span class="keyword">private</span> <span class="type">int</span> price;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">         List &lt;Product&gt; products = Arrays.asList(</span><br><span class="line">            <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"Laptop"</span>, <span class="number">1200</span>), <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"Mouse"</span>, <span class="number">50</span>)</span><br><span class="line">        );</span><br><span class="line">        <span class="type">boolean</span> <span class="variable">hasExpensiveProduct</span> <span class="operator">=</span> products.stream().anyMatch(p -&gt; p.getPrice() &gt; <span class="number">1000</span>);</span><br><span class="line">        System.out.println(<span class="string">"是否有价格 &gt; 1000 的产品? "</span> + hasExpensiveProduct);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// 是否有价格 &gt; 1000 的产品? true</span></span><br></pre></td></tr></tbody></table></figure><h6 id="2-规约-Reduction-将流中所有元素计算，得到一个值"><a href="#2-规约-Reduction-将流中所有元素计算，得到一个值" class="headerlink" title="2. 规约 (Reduction) - 将流中所有元素计算，得到一个值"></a>2. 规约 (Reduction) - 将流中所有元素计算，得到一个值</h6><ul><li><strong>常用规约方法速查表</strong></li></ul><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>reduce(T identity, BinaryOperator&lt;T&gt;)</code></td><td align="left"><strong>(主力/推荐)</strong> 从一个初始值 <code>identity</code> 开始，对流中所有元素进行规约操作。</td></tr><tr><td align="left"><code>reduce(BinaryOperator&lt;T&gt;)</code></td><td align="left">无初始值的规约，因为流可能为空，所以返回一个 <code>Optional</code>。</td></tr></tbody></table><p>####### <strong>场景6：计算所有产品价格的总和</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Data</span></span><br><span class="line"><span class="meta">@AllArgsConstructor</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Product</span> {</span><br><span class="line">    <span class="keyword">private</span> String name;</span><br><span class="line">    <span class="keyword">private</span> <span class="type">int</span> price;</span><br><span class="line">}</span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        List &lt;Product&gt; products = Arrays.asList(</span><br><span class="line">            <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"Laptop"</span>, <span class="number">1200</span>), <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"Mouse"</span>, <span class="number">50</span>)</span><br><span class="line">        );</span><br><span class="line">        <span class="type">int</span> <span class="variable">totalPrice</span> <span class="operator">=</span> products.stream()</span><br><span class="line">            .map(Product:: getPrice)</span><br><span class="line">            .reduce(<span class="number">0</span>, Integer:: sum); <span class="comment">// 0 是初始值, Integer:: sum 是 (a, b) -&gt; a + b</span></span><br><span class="line">        System.out.println(<span class="string">"所有产品的总价是: "</span> + totalPrice);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// 所有产品的总价是: 1250</span></span><br></pre></td></tr></tbody></table></figure><h6 id="3-收集-Collect-将流中元素转换成集合、Map或其他复杂对象"><a href="#3-收集-Collect-将流中元素转换成集合、Map或其他复杂对象" class="headerlink" title="3. 收集 (Collect) - 将流中元素转换成集合、Map或其他复杂对象"></a>3. 收集 (Collect) - 将流中元素转换成集合、Map或其他复杂对象</h6><ul><li><strong>常用收集器 (<code>Collectors</code>) 速查表</strong></li></ul><table><thead><tr><th align="left">收集器 (<code>Collector</code>)</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>toList()</code> / <code>toSet()</code></td><td align="left"><strong>(最常用)</strong> 将流中元素收集到 <code>List</code> 或 <code>Set</code>。</td></tr><tr><td align="left"><code>toMap(keyMapper, valueMapper)</code></td><td align="left">将流中元素收集到 <code>Map</code>，需注意 key 重复问题。</td></tr><tr><td align="left"><code>groupingBy(classifier)</code></td><td align="left"><strong>(主力/推荐)</strong> 根据分类函数对元素进行分组，返回一个 <code>Map</code>。</td></tr><tr><td align="left"><code>joining(delimiter)</code></td><td align="left">将流中元素（通常是字符串）连接成一个字符串。</td></tr></tbody></table><p>####### <strong>场景7：将产品列表按价格分组</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> lombok.AllArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> lombok.Data;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Arrays;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"><span class="keyword">import</span> java.util.stream.Collectors;</span><br><span class="line"><span class="meta">@Data</span></span><br><span class="line"><span class="meta">@AllArgsConstructor</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Product</span> {</span><br><span class="line">    <span class="keyword">private</span> String name;</span><br><span class="line">    <span class="keyword">private</span> <span class="type">int</span> price;</span><br><span class="line">}</span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        List &lt;Product&gt; products = Arrays.asList(</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"Laptop"</span>, <span class="number">1200</span>), <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"Mouse"</span>, <span class="number">50</span>),</span><br><span class="line">                <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"Keyboard"</span>, <span class="number">100</span>), <span class="keyword">new</span> <span class="title class_">Product</span>(<span class="string">"MousePad"</span>, <span class="number">50</span>)</span><br><span class="line">        );</span><br><span class="line"></span><br><span class="line">        Map &lt;Integer, List&lt;Product&gt; &gt; productsByPrice = products.stream()</span><br><span class="line">                .collect(Collectors.groupingBy(Product:: getPrice));</span><br><span class="line"></span><br><span class="line">        System.out.println(<span class="string">"按价格分组的结果:"</span>);</span><br><span class="line">        productsByPrice.forEach((price, list) -&gt; System.out.println(<span class="string">"价格: "</span> + price + <span class="string">" -&gt; "</span> + list));</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// 按价格分组的结果:</span></span><br><span class="line"><span class="comment">// 价格: 50 -&gt; [Product{name ='Mouse', price = 50}, Product{name ='MousePad', price = 50}]</span></span><br><span class="line"><span class="comment">// 价格: 100 -&gt; [Product{name ='Keyboard', price = 100}]</span></span><br><span class="line"><span class="comment">// 价格: 1200 -&gt; [Product{name ='Laptop', price = 1200}]</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: <code>Collectors</code> 工具类提供了极其丰富和强大的收集器，是 <code>collect</code> 操作的“弹药库”。熟练掌握 <code>Collectors</code> 是高效使用 Stream API 的关键。</p></blockquote><hr><h3 id="8-3-空指针终结者-Java-8-Optional-容器类"><a href="#8-3-空指针终结者-Java-8-Optional-容器类" class="headerlink" title="8.3 [空指针终结者] [Java 8+] Optional 容器类"></a>8.3 [空指针终结者] [Java 8+] Optional 容器类</h3><blockquote><p><strong>本章导读</strong>: <code>NullPointerException</code> (NPE) 被其创造者称为“价值十亿美元的错误”，它长久以来都是 Java 开发者的噩梦。为了应对这一挑战，Java 8 引入了 <code>Optional&lt;T&gt;</code>。它是一个容器类，其核心设计思想并非简单地替代 <code>null</code>，而是通过<strong>类型系统</strong>来显式地表达一个值<strong>可能缺失</strong>的情况。这迫使我们作为调用者，必须正视并处理值不存在的可能性，从而将潜在的运行时 NPE 转换为编译时可见的设计考量，让代码更加健壮和优雅。</p></blockquote><h4 id="8-3-1-设计哲学：为什么需要-Optional"><a href="#8-3-1-设计哲学：为什么需要-Optional" class="headerlink" title="8.3.1 设计哲学：为什么需要 Optional"></a>8.3.1 设计哲学：为什么需要 Optional</h4><p>在 <code>Optional</code> 出现之前，方法通常通过返回 <code>null</code> 来表示“没有找到结果”。这种做法存在一个致命缺陷：<code>null</code> 的含义是模糊的，并且完全依赖于调用者的“自觉性”去进行非空检查。</p><ul><li><strong>返回 <code>null</code> 的问题</strong>:<ol><li><strong>意图不明</strong>: <code>null</code> 可能代表“查询无结果”、“操作失败”或“值本身就是空”，容易引起混淆。</li><li><strong>契约不清</strong>: 方法签名 <code>User findById(long id)</code> 无法告诉调用者它可能会返回 <code>null</code>，这是一种隐藏的、脆弱的契约。</li><li><strong>风险转嫁</strong>: 将校验 <code>null</code> 的责任完全抛给了调用方，一旦忘记检查，<code>NullPointerException</code> 就会在运行时不期而至。</li></ol></li></ul><p><code>Optional</code> 通过将“可能没有值”这一情况包装成一个对象，完美地解决了上述问题。一个返回 <code>Optional&lt;User&gt;</code> 的方法，其签名本身就在大声宣告：“我返回的可能是一个 <code>User</code>，也可能什么都没有，请你务必处理这两种情况！”</p><h4 id="8-3-2-核心方法分类使用"><a href="#8-3-2-核心方法分类使用" class="headerlink" title="8.3.2 核心方法分类使用"></a>8.3.2 核心方法分类使用</h4><h6 id="1-创建-Optional-对象-包装你的值"><a href="#1-创建-Optional-对象-包装你的值" class="headerlink" title="1. 创建 Optional 对象 - 包装你的值"></a>1. 创建 Optional 对象 - 包装你的值</h6><ul><li><strong>常用创建方法速查表</strong></li></ul><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>of(T value)</code></td><td align="left"><strong>(谨慎使用)</strong> 为一个<strong>你确定非 null</strong> 的值创建一个 <code>Optional</code>。如果 <code>value</code> 为 <code>null</code>，会立即抛出 <code>NullPointerException</code>。</td></tr><tr><td align="left"><code>ofNullable(T value)</code></td><td align="left"><strong>(主力/推荐)</strong> 为一个<strong>可能为 null</strong> 的值创建一个 <code>Optional</code>。如果 <code>value</code> 为 <code>null</code>，则返回一个空的 <code>Optional</code> 对象。这是最安全、最常用的创建方式。</td></tr><tr><td align="left"><code>empty()</code></td><td align="left">创建一个明确的、空的 <code>Optional</code> 实例。</td></tr></tbody></table><p>####### <strong>场景1：创建 Optional 实例</strong></p><p><strong>背景</strong>：我们有一个方法，可能返回一个 <code>User</code> 对象，也可能返回 <code>null</code>。我们需要将这个结果安全地包装起来。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> lombok.AllArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> lombok.Data;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Optional;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        <span class="type">User</span> <span class="variable">user</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"John Doe"</span>);</span><br><span class="line">        <span class="type">User</span> <span class="variable">nullUser</span> <span class="operator">=</span> <span class="literal">null</span>;</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 1. ofNullable: 最安全的方式</span></span><br><span class="line">        Optional &lt;User&gt; optUser = Optional.ofNullable(user);</span><br><span class="line">        Optional &lt;User&gt; optNullUser = Optional.ofNullable(nullUser);</span><br><span class="line">        System.out.println(<span class="string">"ofNullable (有值): "</span> + optUser.isPresent());  <span class="comment">// ofNullable (有值): true</span></span><br><span class="line">        System.out.println(<span class="string">"ofNullable (无值): "</span> + optNullUser.isPresent()); <span class="comment">// ofNullable (无值): false</span></span><br><span class="line">        <span class="comment">// 2. of: 当你确定值不为 null 时使用</span></span><br><span class="line">        Optional &lt;User&gt; optUserOf = Optional.of(user);</span><br><span class="line">        System.out.println(<span class="string">"of (有值): "</span> + optUserOf.isPresent()); <span class="comment">// of (有值): true</span></span><br><span class="line">        <span class="comment">// 下面这行会立即抛出 NullPointerException</span></span><br><span class="line">        <span class="comment">// Optional.of(nullUser);</span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// 3. empty: 创建一个空实例</span></span><br><span class="line">        Optional &lt;User&gt; emptyOpt = Optional.empty();</span><br><span class="line">        System.out.println(<span class="string">"empty: "</span> + emptyOpt.isPresent()); <span class="comment">// empty: false</span></span><br><span class="line"></span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="meta">@Data</span> <span class="meta">@AllArgsConstructor</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">User</span> {<span class="keyword">private</span> String name;}</span><br></pre></td></tr></tbody></table></figure><h6 id="2-获取值（带默认处理）-安全地取出结果"><a href="#2-获取值（带默认处理）-安全地取出结果" class="headerlink" title="2. 获取值（带默认处理） - 安全地取出结果"></a>2. 获取值（带默认处理） - 安全地取出结果</h6><blockquote><p><strong>[避坑指南]</strong>: 应尽量避免使用 <code>get()</code> 方法。它在 <code>Optional</code> 为空时会抛出 <code>NoSuchElementException</code>，与我们期望避免运行时异常的初衷背道而驰。只有在万分确定 <code>Optional</code> 中有值时（例如，在 <code>isPresent()</code> 判断之后），才可使用。</p></blockquote><ul><li><strong>安全获取值方法速查表</strong></li></ul><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>orElse(T other)</code></td><td align="left"><strong>(常用)</strong> 如果 <code>Optional</code> 中有值，则返回该值；否则返回指定的默认值 <code>other</code>。<strong>注意</strong>：无论 <code>Optional</code> 是否为空，<code>other</code> 参数都会被求值。</td></tr><tr><td align="left"><code>orElseGet(Supplier&lt;? extends T&gt;)</code></td><td align="left"><strong>(主力/推荐)</strong> 如果 <code>Optional</code> 中有值，则返回该值；否则调用 <code>Supplier</code> 函数式接口来生成一个默认值。<strong>优势</strong>：<code>Supplier</code> 只在 <code>Optional</code> 为空时才会被调用，实现了懒加载，性能更优。</td></tr><tr><td align="left"><code>orElseThrow(Supplier&lt;? extends X&gt;)</code></td><td align="left">如果 <code>Optional</code> 中有值，则返回该值；否则抛出由 <code>Supplier</code> 生成的异常。</td></tr></tbody></table><p>####### <strong>场景2：为可能不存在的用户提供默认名称</strong></p><p><strong>背景</strong>：我们需要从一个 <code>Optional&lt;User&gt;</code> 中获取用户名，如果用户不存在，则返回 “Guest”。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> lombok.AllArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> lombok.Data;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Optional;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Data</span> <span class="meta">@AllArgsConstructor</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">User</span> {<span class="keyword">private</span> String name;}</span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        Optional &lt;User&gt; userOpt = Optional.of(<span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"Alice"</span>));</span><br><span class="line">        Optional &lt;User&gt; emptyOpt = Optional.empty();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// orElse: 无论 Optional 是否为空，"new User(" Guest ")" 都会被执行</span></span><br><span class="line">        <span class="type">String</span> <span class="variable">name1</span> <span class="operator">=</span> userOpt.orElse(<span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"Guest"</span>)).getName();</span><br><span class="line">        System.out.println(<span class="string">"orElse (有值时): "</span> + name1);</span><br><span class="line"></span><br><span class="line">        <span class="type">String</span> <span class="variable">name2</span> <span class="operator">=</span> emptyOpt.orElse(<span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"Guest"</span>)).getName();</span><br><span class="line">        System.out.println(<span class="string">"orElse (无值时): "</span> + name2);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// orElseGet: 只有在 Optional 为空时，lambda 表达式才会执行</span></span><br><span class="line">        <span class="type">String</span> <span class="variable">name3</span> <span class="operator">=</span> emptyOpt.orElseGet(() -&gt; <span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"Default User"</span>)).getName();</span><br><span class="line">        System.out.println(<span class="string">"orElseGet (无值时): "</span> + name3);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// orElse (有值时): Alice</span></span><br><span class="line"><span class="comment">// orElse (无值时): Guest</span></span><br><span class="line"><span class="comment">// orElseGet (无值时): Default User</span></span><br></pre></td></tr></tbody></table></figure><h6 id="3-值的转换与过滤-以函数式风格处理值"><a href="#3-值的转换与过滤-以函数式风格处理值" class="headerlink" title="3. 值的转换与过滤 - 以函数式风格处理值"></a>3. 值的转换与过滤 - 以函数式风格处理值</h6><p>这些方法让我们可以像操作 Stream 一样，对 <code>Optional</code> 内部的值进行链式处理，而无需显式地进行非空判断。</p><ul><li><strong>常用转换与过滤方法速查表</strong></li></ul><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>map(Function&lt;T, R&gt;)</code></td><td align="left"><strong>(主力/推荐)</strong> 如果值存在，则对其应用 <code>Function</code> 映射，并返回一个包装了新值的 <code>Optional&lt;R&gt;</code>。如果值不存在，则直接返回一个空的 <code>Optional&lt;R&gt;</code>。</td></tr><tr><td align="left"><code>flatMap(Function&lt;T, Optional&lt;R&gt;&gt;)</code></td><td align="left"><strong>(进阶/常用)</strong> 与 <code>map</code> 类似，但要求映射函数本身返回一个 <code>Optional</code>。<code>flatMap</code> 会将结果“扁平化”，避免出现 <code>Optional&lt;Optional&lt;T&gt;&gt;</code> 这样的嵌套结构。</td></tr><tr><td align="left"><code>filter(Predicate&lt;T&gt;)</code></td><td align="left">如果值存在且满足 <code>Predicate</code> 条件，则返回包含该值的 <code>Optional</code>；否则返回一个空的 <code>Optional</code>。</td></tr></tbody></table><p>####### <strong>场景3：获取用户地址的邮政编码（多级可能为空）</strong></p><p><strong>背景</strong>：<code>User</code> 可能没有 <code>Address</code>，<code>Address</code> 可能没有 <code>zipCode</code>。我们需要安全地获取邮编，如果中间任何一环为空，都应返回 “UNKNOWN”。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Optional;</span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">Address</span> {</span><br><span class="line">    <span class="keyword">private</span> String zipCode;</span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">Address</span><span class="params">(String zipCode)</span> { <span class="built_in">this</span>.zipCode = zipCode; }</span><br><span class="line">    <span class="keyword">public</span> Optional &lt;String&gt; getZipCode() { <span class="keyword">return</span> Optional.ofNullable(zipCode); }</span><br><span class="line">}</span><br><span class="line"><span class="keyword">class</span> <span class="title class_">UserWithAddress</span> {</span><br><span class="line">    <span class="keyword">private</span> Address address;</span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">UserWithAddress</span><span class="params">(Address address)</span> { <span class="built_in">this</span>.address = address; }</span><br><span class="line">    <span class="keyword">public</span> Optional &lt;Address&gt; getAddress() { <span class="keyword">return</span> Optional.ofNullable(address); }</span><br><span class="line">}</span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        <span class="comment">// 一个拥有完整地址信息的用户</span></span><br><span class="line">        <span class="type">Address</span> <span class="variable">address</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">Address</span>(<span class="string">"100-0001"</span>);</span><br><span class="line">        <span class="type">UserWithAddress</span> <span class="variable">user</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">UserWithAddress</span>(address);</span><br><span class="line">        Optional &lt;UserWithAddress&gt; userOpt = Optional.of(user);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// flatMap 用于处理返回 Optional 的 getter</span></span><br><span class="line">        <span class="type">String</span> <span class="variable">zip</span> <span class="operator">=</span> userOpt</span><br><span class="line">                .flatMap(UserWithAddress:: getAddress)  <span class="comment">// 返回 Optional &lt;Address&gt;</span></span><br><span class="line">                .flatMap(Address:: getZipCode)         <span class="comment">// 返回 Optional &lt;String&gt;</span></span><br><span class="line">                .orElse(<span class="string">"UNKNOWN"</span>);</span><br><span class="line"></span><br><span class="line">        System.out.println(<span class="string">"用户的邮政编码是: "</span> + zip); <span class="comment">// 用户的邮政编码是: 100-0001</span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// 一个没有地址信息的用户</span></span><br><span class="line">        <span class="type">UserWithAddress</span> <span class="variable">userWithoutAddress</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">UserWithAddress</span>(<span class="literal">null</span>);</span><br><span class="line">        Optional &lt;UserWithAddress&gt; userOpt2 = Optional.of(userWithoutAddress);</span><br><span class="line">        <span class="type">String</span> <span class="variable">zip2</span> <span class="operator">=</span> userOpt2</span><br><span class="line">                .flatMap(UserWithAddress:: getAddress)</span><br><span class="line">                .flatMap(Address:: getZipCode)</span><br><span class="line">                .orElse(<span class="string">"UNKNOWN"</span>);</span><br><span class="line">        System.out.println(<span class="string">"无地址用户的邮政编码是: "</span> + zip2); <span class="comment">// 无地址用户的邮政编码是: UNKNOWN</span></span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// 用户的邮政编码是: 100-0001</span></span><br><span class="line"><span class="comment">// 无地址用户的邮政编码是: UNKNOWN</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: 当你的 <code>map</code> 操作返回的是一个 <code>Optional</code> 对象时，就应该使用 <code>flatMap</code> 来代替 <code>map</code>，以保持结构的扁平。</p></blockquote><h6 id="4-判断与消费-在值存在时执行操作"><a href="#4-判断与消费-在值存在时执行操作" class="headerlink" title="4. 判断与消费 - 在值存在时执行操作"></a>4. 判断与消费 - 在值存在时执行操作</h6><ul><li><strong>常用判断与消费方法速查表</strong></li></ul><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>isPresent()</code></td><td align="left"><strong>(不推荐)</strong> 检查值是否存在。通常可以用函数式方法替代，以避免命令式的 <code>if</code> 语句。</td></tr><tr><td align="left"><code>ifPresent(Consumer&lt;T&gt;)</code></td><td align="left"><strong>(主力/推荐)</strong> 如果值存在，则对该值执行 <code>Consumer</code> 操作。这是处理“有值”情况的最常用方式。</td></tr><tr><td align="left"><code>ifPresentOrElse(Consumer&lt;T&gt;, Runnable)</code></td><td align="left"><code>[Java 9+]</code> 如果值存在，执行第一个 <code>Consumer</code>；否则，执行第二个 <code>Runnable</code> 任务。</td></tr></tbody></table><p>####### <strong>场景4：如果用户存在，就打印其信息</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> lombok.AllArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> lombok.Data;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.Optional;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Data</span></span><br><span class="line"><span class="meta">@AllArgsConstructor</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">User</span> {<span class="keyword">private</span> String name;}</span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        Optional &lt;User&gt; userOpt = Optional.of(<span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"Charles"</span>));</span><br><span class="line">        Optional &lt;User&gt; emptyOpt = Optional.empty();</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 只有在 userOpt 有值时，才会执行 lambda</span></span><br><span class="line">        userOpt.ifPresent(user -&gt; System.out.println(<span class="string">"用户信息: "</span> + user.getName())); <span class="comment">// 用户信息: Charles</span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// emptyOpt 为空，lambda 不会被执行</span></span><br><span class="line">        emptyOpt.ifPresent(user -&gt; System.out.println(<span class="string">"这段代码不会被执行"</span>));</span><br><span class="line"></span><br><span class="line">        <span class="comment">// Java 9+ 的 ifPresentOrElse</span></span><br><span class="line">         userOpt.ifPresentOrElse(</span><br><span class="line">             user -&gt; System.out.println(<span class="string">"用户信息: "</span> + user.getName()), <span class="comment">// 用户信息: Charles</span></span><br><span class="line">             () -&gt; System.out.println(<span class="string">"用户不存在"</span>) <span class="comment">// 若用户不存在，则会执行此 lambda</span></span><br><span class="line">         );</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// 用户信息: Charles</span></span><br></pre></td></tr></tbody></table></figure><hr><h3 id="8-4-语言增强-Java-8-语言及-API-增强"><a href="#8-4-语言增强-Java-8-语言及-API-增强" class="headerlink" title="8.4 [语言增强] [Java 8+] 语言及 API 增强"></a>8.4 [语言增强] [Java 8+] 语言及 API 增强</h3><blockquote><p><strong>本章导读</strong>: 除了像 Stream 和 Optional 这样的大型主题外，Java 8 及后续版本还带来了许多“小而美”的改进。本节将作为这些实用特性的一个合集，我们将学习接口如何通过默认方法和静态方法实现优雅演进，了解如何用一行代码创建不可变集合，并探索 String、Files 等日常核心 API 的便利新方法，以及可重复注解和 Base64 API 等实用工具。</p></blockquote><h4 id="8-4-1-接口的演进-Java-8"><a href="#8-4-1-接口的演进-Java-8" class="headerlink" title="8.4.1 接口的演进 [Java 8]"></a>8.4.1 接口的演进 <code>[Java 8]</code></h4><p>在 Java 8 之前，接口是“纯粹”的，只能包含抽象方法和常量。这种设计的弊端在于，一旦接口发布，就很难再向其中添加新方法，因为这会强制所有已有的实现类都去实现这个新方法，造成大规模的代码破坏。Java 8 通过引入默认方法和静态方法，完美地解决了这个问题。</p><h6 id="1-默认方法-default-在不破坏实现的前提下为接口添加新功能"><a href="#1-默认方法-default-在不破坏实现的前提下为接口添加新功能" class="headerlink" title="1. 默认方法 (default) - 在不破坏实现的前提下为接口添加新功能"></a>1. 默认方法 (<code>default</code>) - 在不破坏实现的前提下为接口添加新功能</h6><p><strong>默认方法</strong>允许我们在接口中提供一个方法的默认实现。实现该接口的类将自动继承这个默认方法，无需强制重写。</p><p>这对于API的向后兼容和优雅演进至关重要。</p><ul><li><strong>常用方法速查表</strong></li></ul><table><thead><tr><th align="left">关键字</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>default</code></td><td align="left">在接口方法声明前使用，用于提供一个默认的方法体实现。</td></tr></tbody></table><p>####### <strong>场景1：为一个 <code>Logger</code> 接口添加新的日志级别方法</strong></p><p><strong>背景</strong>：假设我们有一个已广泛使用的 <code>Logger</code> 接口，现在希望为它增加一个 <code>logWarning</code> 方法，但又不想让所有旧的实现类都报错。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">interface</span> <span class="title class_">Logger</span> {</span><br><span class="line">    <span class="keyword">void</span> <span class="title function_">logInfo</span><span class="params">(String message)</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 使用 default 关键字为接口添加一个新方法</span></span><br><span class="line">    <span class="keyword">default</span> <span class="keyword">void</span> <span class="title function_">logWarning</span><span class="params">(String message)</span> {</span><br><span class="line">        System.out.println(<span class="string">"【Default Warning】: "</span> + message);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// 一个老的实现类，它只实现了 logInfo</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">SimpleFileLogger</span> <span class="keyword">implements</span> <span class="title class_">Logger</span> {</span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">logInfo</span><span class="params">(String message)</span> {</span><br><span class="line">        System.out.println(<span class="string">"【File Info】: "</span> + message);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// 一个新的实现类，它选择重写默认方法</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">ConsoleLogger</span> <span class="keyword">implements</span> <span class="title class_">Logger</span> {</span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">logInfo</span><span class="params">(String message)</span> {</span><br><span class="line">        System.out.println(<span class="string">"【Console Info】: "</span> + message);</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">logWarning</span><span class="params">(String message)</span> {</span><br><span class="line">        System.err.println(<span class="string">"【CONSOLE WARNING】: "</span> + message);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        <span class="type">Logger</span> <span class="variable">fileLogger</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">SimpleFileLogger</span>();</span><br><span class="line">        fileLogger.logInfo(<span class="string">"Application started."</span>);</span><br><span class="line">        <span class="comment">// 老的实现类可以直接调用新的默认方法，而无需修改自身代码</span></span><br><span class="line">        fileLogger.logWarning(<span class="string">"Configuration is missing."</span>);</span><br><span class="line"></span><br><span class="line">        System.out.println(<span class="string">"--------------------"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="type">Logger</span> <span class="variable">consoleLogger</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">ConsoleLogger</span>();</span><br><span class="line">        consoleLogger.logInfo(<span class="string">"Processing data..."</span>);</span><br><span class="line">        <span class="comment">// 新的实现类调用的是它自己重写后的版本</span></span><br><span class="line">        consoleLogger.logWarning(<span class="string">"Disk space is low."</span>);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// 【File Info】: Application started.</span></span><br><span class="line"><span class="comment">// 【Default Warning】: Configuration is missing.</span></span><br><span class="line"><span class="comment">// --------------------</span></span><br><span class="line"><span class="comment">// 【Console Info】: Processing data...</span></span><br><span class="line"><span class="comment">// 【CONSOLE WARNING】: Disk space is low.</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: <strong>默认方法</strong>是 Java API 设计者向库中添加新功能而<strong>不破坏向后兼容性</strong>的强大工具。它使得接口的演进变得平滑和安全。</p></blockquote><h6 id="2-静态方法-static-在接口中定义工具方法"><a href="#2-静态方法-static-在接口中定义工具方法" class="headerlink" title="2. 静态方法 (static) - 在接口中定义工具方法"></a>2. 静态方法 (<code>static</code>) - 在接口中定义工具方法</h6><p>Java 8 还允许在接口中定义<strong>静态方法</strong>。这些方法不属于任何对象实例，而是直接属于接口本身。这使得我们可以将一些相关的工具方法直接组织在接口内部，而不是创建一个单独的 <code>xxxUtils</code> 工具类</p><ul><li><strong>常用方法速查表</strong></li></ul><table><thead><tr><th align="left">关键字</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>static</code></td><td align="left">在接口方法声明前使用，定义一个属于接口本身的静态方法。</td></tr></tbody></table><p>####### <strong>场景2：在 <code>Logger</code> 接口中提供一个工厂方法</strong></p><p><strong>背景</strong>：我们希望提供一个简单的方式来获取 <code>Logger</code> 的实例，可以直接在 <code>Logger</code> 接口中定义一个静态工厂方法。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">interface</span> <span class="title class_">LoggerWithFactory</span> {</span><br><span class="line">    <span class="keyword">void</span> <span class="title function_">log</span><span class="params">(String message)</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 在接口中定义一个静态工厂方法</span></span><br><span class="line">    <span class="keyword">static</span> LoggerWithFactory <span class="title function_">createConsoleLogger</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">ConsoleLoggerImpl</span>();</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">ConsoleLoggerImpl</span> <span class="keyword">implements</span> <span class="title class_">LoggerWithFactory</span> {</span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">log</span><span class="params">(String message)</span> {</span><br><span class="line">        System.out.println(<span class="string">"LOG: "</span> + message);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        <span class="comment">// 直接通过接口名调用静态方法，就像调用普通工具类一样</span></span><br><span class="line">        <span class="type">LoggerWithFactory</span> <span class="variable">logger</span> <span class="operator">=</span> LoggerWithFactory.createConsoleLogger();</span><br><span class="line">        logger.log(<span class="string">"This is a test message."</span>);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// LOG: This is a test message.</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: <strong>接口静态方法</strong>进一步提升了接口作为<strong>代码组织单元</strong>的能力，让接口不仅能定义契约，还能提供相关的辅助工具。</p></blockquote><h4 id="8-4-2-集合工厂方法-Java-9"><a href="#8-4-2-集合工厂方法-Java-9" class="headerlink" title="8.4.2 集合工厂方法 [Java 9+]"></a>8.4.2 集合工厂方法 <code>[Java 9+]</code></h4><p>在 Java 9 之前，创建一个包含少量元素的集合通常需要好几行代码。Java 9 引入了一系列静态工厂方法 <code>of()</code>，极大地简化了<strong>不可变集合</strong>的创建。</p><ul><li><strong>常用方法速查表</strong></li></ul><table><thead><tr><th align="left">方法名</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>List.of(...)</code></td><td align="left"><strong>(主力/推荐)</strong> 创建一个<strong>不可变</strong>的 <code>List</code>。</td></tr><tr><td align="left"><code>Set.of(...)</code></td><td align="left">创建一个<strong>不可变</strong>的 <code>Set</code>，不允许重复元素。</td></tr><tr><td align="left"><code>Map.of(k1, v1, ...)</code></td><td align="left">创建一个<strong>不可变</strong>的 <code>Map</code>，不允许重复的键。</td></tr></tbody></table><blockquote><p><strong>[避坑指南]</strong>: 使用 <code>of()</code> 方法创建的集合是<strong>不可变的</strong> (<code>Immutable</code>)。任何尝试对其进行添加、删除等修改操作的行为，都会抛出 <code>UnsupportedOperationException</code>。此外，它们<strong>不允许</strong>存入 <code>null</code> 元素。</p></blockquote><p>####### <strong>场景3：快速创建只读的配置集合</strong></p><p><strong>背景</strong>：我们需要创建一个包含默认配置项的 <code>Map</code>，这个配置在程序运行期间不应被修改。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"><span class="keyword">import</span> java.util.Set;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        <span class="comment">// 创建不可变 List</span></span><br><span class="line">        List &lt;String&gt; names = List.of(<span class="string">"Alice"</span>, <span class="string">"Bob"</span>, <span class="string">"Charlie"</span>);</span><br><span class="line">        System.out.println(<span class="string">"Immutable List: "</span> + names);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 创建不可变 Set</span></span><br><span class="line">        Set &lt;Integer&gt; numbers = Set.of(<span class="number">10</span>, <span class="number">20</span>, <span class="number">30</span>);</span><br><span class="line">        System.out.println(<span class="string">"Immutable Set: "</span> + numbers);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 创建不可变 Map</span></span><br><span class="line">        Map &lt;String, String&gt; config = Map.of(</span><br><span class="line">            <span class="string">"user"</span>, <span class="string">"admin"</span>,</span><br><span class="line">            <span class="string">"password"</span>, <span class="string">"secret"</span>,</span><br><span class="line">            <span class="string">"timeout"</span>, <span class="string">"3000"</span></span><br><span class="line">        );</span><br><span class="line">        System.out.println(<span class="string">"Immutable Map: "</span> + config);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 尝试修改会抛出异常</span></span><br><span class="line">        <span class="keyword">try</span> {</span><br><span class="line">            names.add(<span class="string">"David"</span>);</span><br><span class="line">        } <span class="keyword">catch</span> (UnsupportedOperationException e) {</span><br><span class="line">            System.out.println(<span class="string">"捕获到异常: "</span> + e.getClass().getSimpleName());</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// Immutable List: [Alice, Bob, Charlie]</span></span><br><span class="line"><span class="comment">// Immutable Set: [10, 20, 30]</span></span><br><span class="line"><span class="comment">// Immutable Map: {user = admin, password = secret, timeout = 3000}</span></span><br><span class="line"><span class="comment">// 捕获到异常: UnsupportedOperationException</span></span><br></pre></td></tr></tbody></table></figure><h4 id="8-4-3-常用-API-增强-Java-11"><a href="#8-4-3-常用-API-增强-Java-11" class="headerlink" title="8.4.3 常用 API 增强 [Java 11+]"></a>8.4.3 常用 API 增强 <code>[Java 11+]</code></h4><p>Java 在后续版本中，也持续对一些我们日常使用最频繁的类（如 <code>String</code>）进行功能增强。</p><ul><li><strong>常用 String API 增强速查表</strong></li></ul><table><thead><tr><th align="left">方法名</th><th align="left">引入版本</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left"><code>isBlank()</code></td><td align="left">Java 11</td><td align="left">判断字符串是否为空白（<code>isEmpty()</code> 或只包含空白字符）。</td></tr><tr><td align="left"><code>lines()</code></td><td align="left">Java 11</td><td align="left">将字符串按行分隔符 <code>\n</code> 拆分为一个 <code>Stream&lt;String&gt;</code>。</td></tr><tr><td align="left"><code>repeat(int)</code></td><td align="left">Java 11</td><td align="left">将字符串重复指定次数。</td></tr><tr><td align="left"><code>strip()</code></td><td align="left">Java 11</td><td align="left">去除字符串首尾的空白字符（比 <code>trim()</code> 更能识别 Unicode 空白符）。</td></tr></tbody></table><p>####### <strong>场景4：处理多行文本并去除空白行</strong></p><p><strong>背景</strong>：我们从外部系统获取了一段文本，其中包含多行内容和一些空白行，需要进行清洗。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.stream.Collectors;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String [] args)</span> {</span><br><span class="line">        <span class="type">String</span> <span class="variable">multilineText</span> <span class="operator">=</span> <span class="string">""</span> <span class="string">"</span></span><br><span class="line"><span class="string">            </span></span><br><span class="line"><span class="string">            First line</span></span><br><span class="line"><span class="string">            Second line</span></span><br><span class="line"><span class="string">            </span></span><br><span class="line"><span class="string">            Third line</span></span><br><span class="line"><span class="string">            "</span><span class="string">" "</span>; <span class="comment">// 这是 Java 15 的文本块语法，此处用于方便地表示多行文本</span></span><br><span class="line"></span><br><span class="line">        System.out.println(<span class="string">"--- 清洗前的文本 ---"</span>);</span><br><span class="line">        System.out.print(multilineText);</span><br><span class="line">        System.out.println(<span class="string">"--- 清洗后的文本 ---"</span>);</span><br><span class="line"></span><br><span class="line">        <span class="type">String</span> <span class="variable">cleanedText</span> <span class="operator">=</span> multilineText.lines()      <span class="comment">// 1. 将文本分割成一个 Stream &lt;String&gt;</span></span><br><span class="line">                .filter(line -&gt; ! line.isBlank())        <span class="comment">// 2. 使用 isBlank() 过滤掉空白行</span></span><br><span class="line">                .map(String:: strip)                     <span class="comment">// 3. 使用 strip() 去除每行首尾的空白</span></span><br><span class="line">                .collect(Collectors.joining(<span class="string">"\n"</span>)); <span class="comment">// 4. 重新组合成一个字符串</span></span><br><span class="line"></span><br><span class="line">        System.out.println(cleanedText);</span><br><span class="line"></span><br><span class="line">        System.out.println(<span class="string">"\n--- 其他 String API ---"</span>);</span><br><span class="line">        System.out.println(<span class="string">"重复三次 'Abc': "</span> + <span class="string">"Abc"</span>.repeat(<span class="number">3</span>));</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// --- 清洗前的文本 ---</span></span><br><span class="line"><span class="comment">// </span></span><br><span class="line"><span class="comment">// First line</span></span><br><span class="line"><span class="comment">// Second line</span></span><br><span class="line"><span class="comment">// </span></span><br><span class="line"><span class="comment">// Third line</span></span><br><span class="line"><span class="comment">// </span></span><br><span class="line"><span class="comment">// --- 清洗后的文本 ---</span></span><br><span class="line"><span class="comment">// First line</span></span><br><span class="line"><span class="comment">// Second line</span></span><br><span class="line"><span class="comment">// Third line</span></span><br><span class="line"><span class="comment">// </span></span><br><span class="line"><span class="comment">// --- 其他 String API ---</span></span><br><span class="line"><span class="comment">// 重复三次 'Abc': AbcAbcAbc</span></span><br></pre></td></tr></tbody></table></figure><p>好的，我们已经完成了 <code>8.4 语言及 API 增强</code> 中多个版本的特性学习。</p><p>接下来，我们进入一个在 Java 10 中引入的、极大提升编码便利性的语法糖——<strong><code>8.5 局部变量类型推断 (var)</code></strong>。</p><hr><h3 id="8-5-语法现代化-Java-10-局部变量类型推断-var"><a href="#8-5-语法现代化-Java-10-局部变量类型推断-var" class="headerlink" title="8.5 [语法现代化] [Java 10+] 局部变量类型推断 (var)"></a>8.5 [语法现代化] [Java 10+] 局部变量类型推断 (var)</h3><blockquote><p><strong>本章导读</strong>: 在 Java 10 之前，我们声明变量时必须在左侧明确写出其类型，即使在右侧的初始化语句中类型已经非常清晰。这种冗余在处理复杂的泛型类型时尤为明显。为了解决这一问题，Java 10 引入了 <code>var</code> 关键字，它允许编译器根据变量的初始化表达式<strong>自动推断</strong>出其类型。这并非引入了动态类型，<code>var</code> 声明的变量仍然是<strong>静态类型</strong>的，它仅仅是为我们省去了手动声明类型的麻烦，让代码更简洁。</p></blockquote><h4 id="8-5-1-var-的使用场景与优势"><a href="#8-5-1-var-的使用场景与优势" class="headerlink" title="8.5.1 var 的使用场景与优势"></a>8.5.1 <code>var</code> 的使用场景与优势</h4><p><code>var</code> 主要的优势在于能够简化代码，尤其是在处理嵌套泛型等复杂类型时，能极大地提升代码的可读性。</p><ul><li><strong><code>var</code> 常用场景速查表</strong></li></ul><table><thead><tr><th align="left">场景</th><th align="left">示例</th><th align="left">优势</th></tr></thead><tbody><tr><td align="left"><strong>简化复杂类型声明</strong></td><td align="left"><code>var userMap = new HashMap&lt;String, List&lt;User&gt;&gt;();</code></td><td align="left"><strong>(核心优势)</strong> 大幅减少冗余的模板代码，让代码更聚焦于变量名和业务逻辑。</td></tr><tr><td align="left"><strong>for-each 循环</strong></td><td align="left"><code>for (var user : userList) { ... }</code></td><td align="left">简化循环变量的声明。</td></tr><tr><td align="left"><strong>try-with-resources</strong></td><td align="left"><code>try (var reader = new BufferedReader(...)) { ... }</code></td><td align="left">简化资源变量的声明，使代码更紧凑。</td></tr></tbody></table><p>####### <strong>场景1：简化复杂 Map 的声明</strong></p><p><strong>背景</strong>：我们需要创建一个 <code>Map</code>，其键是字符串，值是一个存储了 <code>User</code> 对象的 <code>List</code>。我们来对比一下使用 <code>var</code> 前后的代码差异。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.ArrayList;</span><br><span class="line"><span class="keyword">import</span> java.util.HashMap;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">User</span> {</span><br><span class="line">    <span class="keyword">private</span> String name;</span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">User</span><span class="params">(String name)</span> { <span class="built_in">this</span>.name = name; }</span><br><span class="line">    <span class="meta">@Override</span> <span class="keyword">public</span> String <span class="title function_">toString</span><span class="params">()</span> { <span class="keyword">return</span> <span class="string">"User{name='"</span> + name + <span class="string">"'}"</span>; }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// Java 10 之前的写法，类型声明非常冗长</span></span><br><span class="line">        Map&lt;String, List&lt;User&gt;&gt; userMapBefore = <span class="keyword">new</span> <span class="title class_">HashMap</span>&lt;&gt;();</span><br><span class="line">        userMapBefore.put(<span class="string">"groupA"</span>, <span class="keyword">new</span> <span class="title class_">ArrayList</span>&lt;&gt;(List.of(<span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"Alice"</span>))));</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 使用 var 的写法，代码瞬间清爽</span></span><br><span class="line">        <span class="type">var</span> <span class="variable">userMapAfter</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">HashMap</span>&lt;String, List&lt;User&gt;&gt;();</span><br><span class="line">        userMapAfter.put(<span class="string">"groupB"</span>, <span class="keyword">new</span> <span class="title class_">ArrayList</span>&lt;&gt;(List.of(<span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"Bob"</span>))));</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 编译器为 userMapAfter 推断出的类型仍然是 Map&lt;String, List&lt;User&gt;&gt;</span></span><br><span class="line">        System.out.println(<span class="string">"userMapAfter 的类型是 Map: "</span> + (userMapAfter <span class="keyword">instanceof</span> Map));</span><br><span class="line">        System.out.println(<span class="string">"userMapAfter 的内容: "</span> + userMapAfter);</span><br><span class="line">        </span><br><span class="line">        System.out.println(<span class="string">"--- for-each 循环中使用 var ---"</span>);</span><br><span class="line">        <span class="type">var</span> <span class="variable">userList</span> <span class="operator">=</span> List.of(<span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"Charlie"</span>), <span class="keyword">new</span> <span class="title class_">User</span>(<span class="string">"David"</span>));</span><br><span class="line">        <span class="keyword">for</span> (<span class="keyword">var</span> user : userList) {</span><br><span class="line">            <span class="comment">// user 的类型被正确推断为 User</span></span><br><span class="line">            System.out.println(<span class="string">"循环中的用户: "</span> + user);</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// userMapAfter 的类型是 Map: true</span></span><br><span class="line"><span class="comment">// userMapAfter 的内容: {groupB=[User{name='Bob'}]}</span></span><br><span class="line"><span class="comment">// --- for-each 循环中使用 var ---</span></span><br><span class="line"><span class="comment">// 循环中的用户: User{name='Charlie'}</span></span><br><span class="line"><span class="comment">// 循环中的用户: User{name='David'}</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: <code>var</code> 让我们的代码不再被冗长的类型声明所淹没，尤其是在泛型和复杂的类名中。它鼓励我们为变量起一个更有意义的名字，因为类型的上下文已经由初始化表达式提供了。</p></blockquote><h4 id="8-5-2-避坑指南-var-的使用限制"><a href="#8-5-2-避坑指南-var-的使用限制" class="headerlink" title="8.5.2 [避坑指南] var 的使用限制"></a>8.5.2 [避坑指南] <code>var</code> 的使用限制</h4><p><code>var</code> 虽然好用，但它不是“万金油”，只能在特定的上下文中使用。滥用或误用反而会降低代码的可读性。</p><blockquote><p><strong>核心原则</strong>: <strong>所有 <code>var</code> 声明的变量，编译器都必须能在编译时、仅通过其初始化表达式就明确地推断出它的唯一类型。</strong></p></blockquote><p>以下是 <code>var</code> 的主要使用限制：</p><ol><li><p><strong>必须有初始化器</strong>: 不能只声明不赋值。</p><ul><li>错误: <code>var name;</code></li><li>正确: <code>var name = "Alice";</code></li></ul></li><li><p><strong>不能用 <code>null</code> 初始化</strong>: <code>var</code> 无法从 <code>null</code> 推断出具体类型。</p><ul><li>错误: <code>var data = null;</code></li></ul></li><li><p><strong>只能用于局部变量</strong>: 这是最重要的一条规则。<code>var</code> <strong>不能</strong>用于：</p><ul><li>类的成员变量（字段）</li><li>方法的参数</li><li>方法的返回类型</li></ul></li><li><p><strong>Lambda 表达式和方法引用需要显式目标类型</strong>:</p><ul><li>错误: <code>var runnable = () -&gt; System.out.println("Hello");</code></li><li>原因: Lambda 表达式的类型依赖于其上下文的目标接口，仅凭 Lambda 自身无法推断。</li><li>正确: <code>Runnable runnable = () -&gt; System.out.println("Hello");</code></li></ul></li><li><p><strong>小心泛型和菱形操作符 (<code>&lt;&gt;</code>)</strong>:</p><ul><li><code>var list = new ArrayList&lt;&gt;();</code> 这行代码是合法的，但 <code>list</code> 的类型会被推断为 <code>ArrayList&lt;Object&gt;</code>，这可能不是我们想要的。</li><li><strong>最佳实践</strong>: 如果想让 <code>var</code> 推断出正确的泛型类型，应在初始化表达式中明确指定它：<code>var stringList = new ArrayList&lt;String&gt;();</code></li></ul></li></ol><p>####### <strong>场景2：<code>var</code> 的非法使用示例</strong></p><p><strong>背景</strong>：下面的代码展示了几种常见的 <code>var</code> 错误用法，帮助我们加深理解。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.function.Consumer;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="comment">// 错误1：不能用于成员变量</span></span><br><span class="line">    <span class="comment">// private var instanceVariable = "error";</span></span><br><span class="line"></span><br><span class="line">    <span class="comment">// 错误2：不能用于方法返回类型</span></span><br><span class="line">    <span class="comment">// public var getMessage() { return "error"; }</span></span><br><span class="line"></span><br><span class="line">    <span class="comment">// 错误3：不能用于方法参数</span></span><br><span class="line">    <span class="comment">// public void printMessage(var message) { System.out.println(message); }</span></span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// ------------------ 合法用法 ------------------</span></span><br><span class="line">        <span class="type">var</span> <span class="variable">validMessage</span> <span class="operator">=</span> <span class="string">"This is a valid local variable."</span>;</span><br><span class="line">        System.out.println(validMessage);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// ------------------ 非法用法 ------------------</span></span><br><span class="line">        <span class="comment">// 错误4：没有初始化器</span></span><br><span class="line">        <span class="comment">// var name; </span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// 错误5：用 null 初始化</span></span><br><span class="line">        <span class="comment">// var data = null;</span></span><br><span class="line"></span><br><span class="line">        <span class="comment">// 错误6：Lambda 表达式需要显式目标类型</span></span><br><span class="line">        <span class="comment">// var printer = s -&gt; System.out.println(s);</span></span><br><span class="line">        </span><br><span class="line">        <span class="comment">// 正确的 Lambda 用法</span></span><br><span class="line">        Consumer&lt;String&gt; printer = s -&gt; System.out.println(s);</span><br><span class="line">        printer.accept(<span class="string">"Lambda with explicit type is fine."</span>);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// This is a valid local variable.</span></span><br><span class="line"><span class="comment">// Lambda with explicit type is fine.</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: <code>var</code> 是一个强大的便利工具，但绝不能滥用。在类型不明显或者会降低代码清晰度的场景下，我们仍应坚持使用明确的类型声明。<strong>代码首先是写给人看的，其次才是给机器执行的。</strong></p></blockquote><p>好的，我们已经学习了 Java 10 引入的 <code>var</code> 关键字。</p><p>接下来，我们将把目光投向 Java 16 和 17 中两个重要的“现代化 Java”基石，它们共同致力于增强 Java 的数据建模和领域建模能力。我们将把它们合并在 <strong><code>8.6 Record 类与 Sealed 类</code></strong> 这一节中进行讲解。</p><hr><h3 id="8-6-语法现代化-Java-16-17-Record-类与-Sealed-类"><a href="#8-6-语法现代化-Java-16-17-Record-类与-Sealed-类" class="headerlink" title="8.6 [语法现代化] [Java 16/17+] Record 类与 Sealed 类"></a>8.6 [语法现代化] [Java 16/17+] Record 类与 Sealed 类</h3><blockquote><p><strong>本章导读</strong>: 随着应用日益复杂，如何清晰、准确地对业务领域进行建模变得至关重要。<code>Record</code> 类 (JEP 395, Java 16) 和 <code>Sealed</code> 类 (JEP 409, Java 17) 是 Java 在这方面给出的强力回应。<code>Record</code> 类旨在用最少的代码创建不可变的数据载体，彻底终结了传统 POJO/DTO 的冗长样板代码。而 <code>Sealed</code> 类则允许我们创建可控的、封闭的继承体系。这两者结合，特别是在后续的模式匹配中，能够构建出既安全又极具表达力的领域模型。</p></blockquote><h4 id="8-6-1-Record-类：不可变数据载体-Java-16"><a href="#8-6-1-Record-类：不可变数据载体-Java-16" class="headerlink" title="8.6.1 Record 类：不可变数据载体 [Java 16+]"></a>8.6.1 Record 类：不可变数据载体 <code>[Java 16+]</code></h4><p>在 <code>Record</code> 出现之前，创建一个简单的数据类（如一个二维坐标点），需要我们手动编写构造函数、<code>getter</code>、以及 <code>equals()</code>、<code>hashCode()</code>、<code>toString()</code> 方法。这些都是高度重复且容易出错的样板代码。<code>Record</code> 类就是为了解决这个问题而生的。</p><blockquote><p><strong>核心思想</strong>: <strong><code>Record</code> 是一种特殊的类，它专门用于充当不可变数据的“透明载体”。</strong> 我们只需在声明时定义其“组件”，编译器就会自动为我们生成所有必要的成员。</p></blockquote><ul><li><strong><code>record</code> 自动生成内容速查表</strong></li></ul><table><thead><tr><th align="left">当我们声明 <code>record Point(int x, int y) {}</code> 时，编译器自动生成:</th></tr></thead><tbody><tr><td align="left"><code>private final int x;</code> 和 <code>private final int y;</code> (私有 <code>final</code> 字段)</td></tr><tr><td align="left">一个包含所有组件的公共构造器 <code>public Point(int x, int y)</code></td></tr><tr><td align="left">每个组件的公共“访问器”方法，如 <code>public int x()</code> 和 <code>public int y()</code> (注意：不是 <code>getX()</code>)</td></tr><tr><td align="left">一个完整的 <code>public boolean equals(Object o)</code> 实现</td></tr><tr><td align="left">一个完整的 <code>public int hashCode()</code> 实现</td></tr><tr><td align="left">一个完整的 <code>public String toString()</code> 实现</td></tr></tbody></table><p>####### <strong>场景1：使用 <code>record</code> 定义一个坐标点类</strong></p><p><strong>背景</strong>：我们需要一个 <code>Point</code> 类来表示二维坐标。我们来对比一下传统方式和 <code>record</code> 方式的巨大差异。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="comment">// 这是使用 record 的方式，仅需一行！</span></span><br><span class="line"><span class="keyword">record</span> <span class="title class_">Point</span><span class="params">(<span class="type">int</span> x, <span class="type">int</span> y)</span> {</span><br><span class="line">    <span class="comment">// 我们还可以在 record 内部添加静态方法或自定义的实例方法</span></span><br><span class="line">    <span class="keyword">public</span> <span class="type">double</span> <span class="title function_">distanceToOrigin</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">return</span> Math.sqrt(x * x + y * y);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/*</span></span><br><span class="line"><span class="comment">// 如果不用 record，我们需要手写这么多代码：</span></span><br><span class="line"><span class="comment">class PointBeforeRecord {</span></span><br><span class="line"><span class="comment">    private final int x;</span></span><br><span class="line"><span class="comment">    private final int y;</span></span><br><span class="line"><span class="comment"></span></span><br><span class="line"><span class="comment">    public PointBeforeRecord(int x, int y) { this.x = x; this.y = y; }</span></span><br><span class="line"><span class="comment">    public int getX() { return x; }</span></span><br><span class="line"><span class="comment">    public int getY() { return y; }</span></span><br><span class="line"><span class="comment">    // ... 还需要手动实现 equals(), hashCode(), toString() ...</span></span><br><span class="line"><span class="comment">}</span></span><br><span class="line"><span class="comment">*/</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="comment">// 创建 record 实例</span></span><br><span class="line">        <span class="type">Point</span> <span class="variable">p1</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">Point</span>(<span class="number">3</span>, <span class="number">4</span>);</span><br><span class="line">        <span class="type">Point</span> <span class="variable">p2</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">Point</span>(<span class="number">3</span>, <span class="number">4</span>);</span><br><span class="line">        <span class="type">Point</span> <span class="variable">p3</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">Point</span>(<span class="number">5</span>, <span class="number">12</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 1. 访问器方法</span></span><br><span class="line">        System.out.println(<span class="string">"p1 的 x 坐标是: "</span> + p1.x()); <span class="comment">// 注意是 p1.x() 而不是 p1.getX()</span></span><br><span class="line">        System.out.println(<span class="string">"p1 的 y 坐标是: "</span> + p1.y());</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 2. toString()</span></span><br><span class="line">        System.out.println(<span class="string">"p1 的字符串表示: "</span> + p1);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 3. equals()</span></span><br><span class="line">        System.out.println(<span class="string">"p1 和 p2 是否相等? "</span> + p1.equals(p2));</span><br><span class="line">        System.out.println(<span class="string">"p1 和 p3 是否相等? "</span> + p1.equals(p3));</span><br><span class="line">        </span><br><span class="line">        <span class="comment">// 4. hashCode()</span></span><br><span class="line">        System.out.println(<span class="string">"p1 的哈希码: "</span> + p1.hashCode());</span><br><span class="line">        System.out.println(<span class="string">"p2 的哈希码: "</span> + p2.hashCode());</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 5. 调用自定义方法</span></span><br><span class="line">        System.out.printf(<span class="string">"p3 到原点的距离是: %.2f\n"</span>, p3.distanceToOrigin());</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// p1 的 x 坐标是: 3</span></span><br><span class="line"><span class="comment">// p1 的 y 坐标是: 4</span></span><br><span class="line"><span class="comment">// p1 的字符串表示: Point[x=3, y=4]</span></span><br><span class="line"><span class="comment">// p1 和 p2 是否相等? true</span></span><br><span class="line"><span class="comment">// p1 和 p3 是否相等? false</span></span><br><span class="line"><span class="comment">// p1 的哈希码: 100</span></span><br><span class="line"><span class="comment">// p2 的哈希码: 100</span></span><br><span class="line"><span class="comment">// p3 到原点的距离是: 13.00</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: <code>Record</code> 类是创建<strong>不可变数据聚合</strong>的理想选择。它用最少的语法，提供了完整、正确的功能，极大地减少了样板代码，使我们的模型类定义更清晰、更可靠。</p></blockquote><h4 id="8-6-2-Sealed-类-接口：精准的继承控制-Java-17"><a href="#8-6-2-Sealed-类-接口：精准的继承控制-Java-17" class="headerlink" title="8.6.2 Sealed 类/接口：精准的继承控制 [Java 17+]"></a>8.6.2 Sealed 类/接口：精准的继承控制 <code>[Java 17+]</code></h4><p>在面向对象设计中，我们有时希望限制一个类或接口的继承体系，明确地指定“谁可以成为我的子类”。例如，一个 <code>Shape</code>（形状）接口，我们可能只希望它被 <code>Circle</code>（圆形）、<code>Square</code>（方形）和 <code>Triangle</code>（三角形）实现。</p><p><code>Sealed</code> 类/接口正是为此而生，它允许我们创建一个<strong>封闭的、可控的继承层次结构</strong>。</p><blockquote><p><strong>核心语法</strong>: 使用 <code>sealed</code> 关键字修饰类或接口，并使用 <code>permits</code> 关键字列出所有允许继承或实现的直接子类。</p></blockquote><ul><li><strong>子类的规则</strong></li></ul><p>所有被 <code>permits</code> 关键字指定的子类，都必须遵循以下三条规则之一：</p><ol><li>必须声明为 <code>final</code>，表示继承关系到此为止，不能再被继承。</li><li>必须声明为 <code>sealed</code>，表示它可以被继续继承，但同样需要用 <code>permits</code> 指定其子类。</li><li>必须声明为 <code>non-sealed</code>，表示“打开”继承限制，任何类都可以继承它，回归到普通的继承模式。</li></ol><p>####### <strong>场景2：定义一个封闭的图形 (<code>Shape</code>) 继承体系</strong></p><p><strong>背景</strong>：我们正在设计一个绘图程序，需要定义一个 <code>Shape</code> 接口，并确保系统中只存在 <code>Circle</code> 和 <code>Square</code> 这两种形状。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="comment">// 1. 定义一个 sealed 接口，只允许 Circle 和 Square 实现它</span></span><br><span class="line"><span class="keyword">sealed</span> <span class="keyword">interface</span> <span class="title class_">Shape</span> <span class="keyword">permits</span> Circle, Square {</span><br><span class="line">    <span class="type">double</span> <span class="title function_">area</span><span class="params">()</span>; <span class="comment">// 面积</span></span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// 2. Circle 是 Shape 的一个子类，我们将其声明为 final，表示它不能再被继承</span></span><br><span class="line"><span class="keyword">final</span> <span class="keyword">class</span> <span class="title class_">Circle</span> <span class="keyword">implements</span> <span class="title class_">Shape</span> {</span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> <span class="type">double</span> radius;</span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">Circle</span><span class="params">(<span class="type">double</span> radius)</span> { <span class="built_in">this</span>.radius = radius; }</span><br><span class="line">    </span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> <span class="type">double</span> <span class="title function_">area</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">return</span> Math.PI * radius * radius;</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// 3. Square 是 Shape 的另一个子类，我们将其声明为 non-sealed，</span></span><br><span class="line"><span class="comment">//    表示任何其他类（如 SpecialSquare）都可以继承它。</span></span><br><span class="line"><span class="keyword">non-sealed</span> <span class="keyword">class</span> <span class="title class_">Square</span> <span class="keyword">implements</span> <span class="title class_">Shape</span> {</span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> <span class="type">double</span> side;</span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">Square</span><span class="params">(<span class="type">double</span> side)</span> { <span class="built_in">this</span>.side = side; }</span><br><span class="line">    </span><br><span class="line">    <span class="meta">@Override</span></span><br><span class="line">    <span class="keyword">public</span> <span class="type">double</span> <span class="title function_">area</span><span class="params">()</span> {</span><br><span class="line">        <span class="keyword">return</span> side * side;</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// 由于 Square 是 non-sealed, 我们可以自由地继承它</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">SpecialSquare</span> <span class="keyword">extends</span> <span class="title class_">Square</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">SpecialSquare</span><span class="params">(<span class="type">double</span> side)</span> { <span class="built_in">super</span>(side); }</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">void</span> <span class="title function_">doSomethingSpecial</span><span class="params">()</span> {</span><br><span class="line">        System.out.println(<span class="string">"This is a special square!"</span>);</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        <span class="type">Shape</span> <span class="variable">circle</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">Circle</span>(<span class="number">10.0</span>);</span><br><span class="line">        <span class="type">Shape</span> <span class="variable">square</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">Square</span>(<span class="number">10.0</span>);</span><br><span class="line"></span><br><span class="line">        System.out.printf(<span class="string">"圆形的面积是: %.2f\n"</span>, circle.area());</span><br><span class="line">        System.out.printf(<span class="string">"方形的面积是: %.2f\n"</span>, square.area());</span><br><span class="line"></span><br><span class="line">        <span class="type">SpecialSquare</span> <span class="variable">specialSquare</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">SpecialSquare</span>(<span class="number">5</span>);</span><br><span class="line">        specialSquare.doSomethingSpecial();</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// 圆形的面积是: 314.16</span></span><br><span class="line"><span class="comment">// 方形的面积是: 100.00</span></span><br><span class="line"><span class="comment">// This is a special square!</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: <code>Sealed</code> 类和接口为我们的领域建模提供了更强的控制力。它使得类的继承关系从“开放”变为“有意识的设计”，这在与后面要讲的<strong>模式匹配</strong>结合时，威力会得到最大程度的体现，因为编译器能够知晓所有可能的子类型，从而检查 <code>switch</code> 语句是否<strong>穷尽了所有情况</strong>。</p></blockquote><hr><h4 id="高频面试题-Record-VS-Lombok"><a href="#高频面试题-Record-VS-Lombok" class="headerlink" title="[高频面试题] Record VS Lombok"></a><code>[高频面试题]</code> <code>Record</code> VS Lombok</h4><blockquote><p><strong>Q: <code>Record</code> 和 Lombok 是不是一样的？Lombok 的功能不是比 <code>Record</code> 更强吗？</strong></p><p><strong>A:</strong> 这是一个非常经典的问题。虽然 <code>Record</code> 和 Lombok 的 <code>@Data</code>/<code>@Value</code> 注解在<strong>目标</strong>上（即减少数据类的样板代码）有重叠，但它们在<strong>本质、设计哲学和使用方式</strong>上有着根本性的不同。</p><ul><li><strong>一句话总结</strong>：Lombok 就是比Record牛逼</li></ul></blockquote><hr><h3 id="8-7-语法现代化-Java-16-21-模式匹配"><a href="#8-7-语法现代化-Java-16-21-模式匹配" class="headerlink" title="8.7 [语法现代化] [Java 16-21+] 模式匹配"></a>8.7 [语法现代化] [Java 16-21+] 模式匹配</h3><blockquote><p><strong>本章导读</strong>: 在模式匹配出现之前，处理不同类型的对象通常需要遵循一个繁琐的固定流程：<code>instanceof</code> 类型检查 -&gt; 强制类型转换 -&gt; 使用转换后的变量。这个过程不仅冗长，而且容易出错（例如，忘记转换或转换错误）。<strong>模式匹配</strong>旨在彻底改变这一现状，它将类型测试、变量声明和条件提取合并为一步，让我们能以一种更具声明性的方式来表达“如果对象是这种模式，就这么做”。</p></blockquote><h4 id="8-7-1-instanceof-的模式匹配-Java-16"><a href="#8-7-1-instanceof-的模式匹配-Java-16" class="headerlink" title="8.7.1 instanceof 的模式匹配 [Java 16+]"></a>8.7.1 <code>instanceof</code> 的模式匹配 <code>[Java 16+]</code></h4><p>这是模式匹配最基础、最直接的应用，它首先对我们熟悉的 <code>instanceof</code> 运算符进行了增强。</p><blockquote><p><strong>核心思想</strong>: 在 <code>instanceof</code> 类型检查成功后，直接声明一个该类型的变量，省去手动强制转换的步骤。</p></blockquote><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 假设 obj 是一个 Object 类型的变量，可能包含 String, Integer 等</span></span><br><span class="line"><span class="type">Object</span> <span class="variable">obj</span> <span class="operator">=</span> <span class="string">"hello world"</span>;</span><br><span class="line"></span><br><span class="line"><span class="comment">// --- 传统写法 (Java 16 之前) ---</span></span><br><span class="line"><span class="keyword">if</span> (obj <span class="keyword">instanceof</span> String) {</span><br><span class="line">    <span class="type">String</span> <span class="variable">s</span> <span class="operator">=</span> (String) obj; <span class="comment">// 类型检查后，需要显式向下转型</span></span><br><span class="line">    System.out.println(<span class="string">"传统方式打印大写: "</span> + s.toUpperCase());</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// --- instanceof 模式匹配写法 (Java 16+) ---</span></span><br><span class="line"><span class="keyword">if</span> (obj <span class="keyword">instanceof</span> String s) { <span class="comment">// 类型检查和变量声明/绑定一步完成</span></span><br><span class="line">    System.out.println(<span class="string">"模式匹配打印大写: "</span> + s.toUpperCase()); <span class="comment">// 直接使用 s，无需转型</span></span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: <code>instanceof</code> 模式匹配虽然只是一个小改动，但它为更强大的 <code>switch</code> 模式匹配奠定了基础，并培养了我们用模式的思维去替代传统类型判断的习惯。</p></blockquote><h4 id="8-7-2-switch-表达式与模式匹配-Java-21"><a href="#8-7-2-switch-表达式与模式匹配-Java-21" class="headerlink" title="8.7.2 switch 表达式与模式匹配 [Java 21+]"></a>8.7.2 <code>switch</code> 表达式与模式匹配 <code>[Java 21+]</code></h4><p>这是模式匹配的“完全体”，它极大地增强了 <code>switch</code> 语句（和表达式）的能力，使其可以对任意类型的对象进行匹配，并应用更复杂的逻辑。</p><ul><li><strong><code>switch</code> 模式匹配核心增强速查表</strong></li></ul><table><thead><tr><th align="left">新特性</th><th align="left">功能描述</th></tr></thead><tbody><tr><td align="left">**类型模式 **</td><td align="left"><code>case</code> 标签可以直接匹配对象的类型，如 <code>case String s</code>。</td></tr><tr><td align="left"><strong><code>case null</code></strong></td><td align="left"><code>switch</code> 现在可以直接处理 <code>null</code> 情况，无需在外部进行 <code>if (obj == null)</code> 判断。</td></tr><tr><td align="left"><strong>守护模式</strong></td><td align="left">使用 <code>when</code> 关键字为 <code>case</code> 增加一个额外的布尔条件，如 <code>case String s when s.length() &gt; 5</code>。</td></tr><tr><td align="left"><strong>穷尽性检查</strong></td><td align="left"><strong>(安全保障)</strong> 当对 <code>sealed</code> 类或枚举进行 <code>switch</code> 时，编译器会检查是否覆盖了所有可能的子类型，否则会报错。</td></tr></tbody></table><p>####### <strong>场景2：使用 <code>switch</code> 模式匹配和 <code>sealed</code> 接口重构图形面积计算</strong></p><p><strong>背景</strong>：我们重用上一节定义的 <code>sealed interface Shape</code>，编写一个方法，使用 <code>switch</code> 模式匹配来计算不同形状的面积。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example;</span><br><span class="line"></span><br><span class="line"><span class="comment">// --- 复用上一节定义的 Shape 继承体系 ---</span></span><br><span class="line"><span class="keyword">sealed</span> <span class="keyword">interface</span> <span class="title class_">Shape</span> <span class="keyword">permits</span> Circle, Square {}</span><br><span class="line"><span class="keyword">final</span> <span class="keyword">class</span> <span class="title class_">Circle</span> <span class="keyword">implements</span> <span class="title class_">Shape</span> {</span><br><span class="line">    <span class="keyword">final</span> <span class="type">double</span> radius;</span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">Circle</span><span class="params">(<span class="type">double</span> radius)</span> { <span class="built_in">this</span>.radius = radius; }</span><br><span class="line">}</span><br><span class="line"><span class="keyword">non-sealed</span> <span class="keyword">class</span> <span class="title class_">Square</span> <span class="keyword">implements</span> <span class="title class_">Shape</span> {</span><br><span class="line">    <span class="keyword">final</span> <span class="type">double</span> side;</span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">Square</span><span class="params">(<span class="type">double</span> side)</span> { <span class="built_in">this</span>.side = side; }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// -----------------------------------------</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Main</span> {</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="type">double</span> <span class="title function_">getArea</span><span class="params">(Shape shape)</span> {</span><br><span class="line">        <span class="comment">// 使用 switch 表达式进行模式匹配</span></span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">switch</span> (shape) {</span><br><span class="line">            <span class="comment">// 1. case null: 直接处理 null 输入</span></span><br><span class="line">            <span class="keyword">case</span> <span class="literal">null</span> -&gt; <span class="number">0.0</span>;</span><br><span class="line">            </span><br><span class="line">            <span class="comment">// 2. 类型模式: 匹配 Circle 类型，并创建变量 c</span></span><br><span class="line">            <span class="keyword">case</span> Circle c -&gt; Math.PI * c.radius * c.radius;</span><br><span class="line">            </span><br><span class="line">            <span class="comment">// 3. 守护模式: 匹配 Square 类型，但只有当边长大于0时才进入该分支</span></span><br><span class="line">            <span class="keyword">case</span> Square s <span class="keyword">when</span> s.side &gt; <span class="number">0</span> -&gt; s.side * s.side;</span><br><span class="line">            </span><br><span class="line">            <span class="comment">// 4. default 分支: 覆盖所有其他情况（例如，一个边长小于等于0的 Square）</span></span><br><span class="line">            <span class="comment">// 如果不写 default，对于 sealed 接口，编译器会检查是否穷尽所有可能。</span></span><br><span class="line">            <span class="comment">// 在本例中，因为有 'when' 条件，所以必须有 default。</span></span><br><span class="line">            <span class="keyword">default</span> -&gt; <span class="number">0.0</span>;</span><br><span class="line">        };</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> <span class="title function_">main</span><span class="params">(String[] args)</span> {</span><br><span class="line">        System.out.printf(<span class="string">"圆形面积: %.2f\n"</span>, getArea(<span class="keyword">new</span> <span class="title class_">Circle</span>(<span class="number">10</span>)));</span><br><span class="line">        System.out.printf(<span class="string">"方形面积: %.2f\n"</span>, getArea(<span class="keyword">new</span> <span class="title class_">Square</span>(<span class="number">5</span>)));</span><br><span class="line">        System.out.printf(<span class="string">"null 图形面积: %.2f\n"</span>, getArea(<span class="literal">null</span>));</span><br><span class="line">        System.out.printf(<span class="string">"无效方形面积: %.2f\n"</span>, getArea(<span class="keyword">new</span> <span class="title class_">Square</span>(-<span class="number">5</span>)));</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"><span class="comment">// 输出:</span></span><br><span class="line"><span class="comment">// 圆形面积: 314.16</span></span><br><span class="line"><span class="comment">// 方形面积: 25.00</span></span><br><span class="line"><span class="comment">// null 图形面积: 0.00</span></span><br><span class="line"><span class="comment">// 无效方形面积: 0.00</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>小结</strong>: <code>switch</code> 模式匹配是 Java 语法现代化的一个巨大飞跃。它将类型判断、条件逻辑和安全性（通过穷尽性检查）完美结合，使得我们处理复杂、多态的业务逻辑时，代码能变得像查表一样清晰、直观和安全。</p></blockquote></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/14501.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/14501.html&quot;)">Java（八）：8.0 Java新语法总结</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/14501.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/14501.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Java<span class="categoryesPageCount">20</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Java基础知识总汇<span class="tagsPageCount">9</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/64051.html"><img class="prev-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Java（七）：7.0 Java并发编程</div></div></a></div><div class="next-post pull-right"><a href="/posts/43523.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/814899.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Java（9）：9.0 JavaWeb核心知识点速查</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/30645.html" title="Java（一）：1.0 Java语言概述与核心生态"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（一）：1.0 Java语言概述与核心生态</div></div></a></div><div><a href="/posts/43523.html" title="Java（9）：9.0 JavaWeb核心知识点速查"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/814899.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-09</div><div class="title">Java（9）：9.0 JavaWeb核心知识点速查</div></div></a></div><div><a href="/posts/35626.html" title="Java（二）：2.0 Java基础"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（二）：2.0 Java基础</div></div></a></div><div><a href="/posts/6760.html" title="Java（四）：4.0 [核心] Java I/O 流体系与实战"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（四）：4.0 [核心] Java I/O 流体系与实战</div></div></a></div><div><a href="/posts/42235.html" title="Java（三）：3.0 [核心] 面向对象编程"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（三）：3.0 [核心] 面向对象编程</div></div></a></div><div><a href="/posts/62133.html" title="Java（五）：5.0 [元编程] 反射、注解"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-05-08</div><div class="title">Java（五）：5.0 [元编程] 反射、注解</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"Java（八）：8.0 Java新语法总结",date:"2025-05-08 23:13:45",updated:"2025-07-14 15:58:33",tags:["Java基础知识总汇"],categories:["后端技术","Java"],content:'\n## 8.0 Java新语法总结\n\n### 8.1 [核心] [Java 8+] Lambda 表达式与方法引用\n\n> **本章导读**: Java 8 的发布是 Java 发展史上的一个里程碑，其最核心、最具代表性的特性无疑是 **Lambda 表达式**的引入。它将**函数式编程**思想正式带入了 Java 世界，从根本上改变了我们处理**行为参数化** 的方式。在本节中，我们将从 Lambda 的诞生背景出发，深入其核心语法、**函数式接口**，并最终掌握其语法糖——**方法引用**的使用技巧，为我们后续学习 Stream API 等高级特性打下坚实的基础。\n\n![image-20250714145916792](https://cdn.smartcis.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250714145916792.png)\n\n\n\n#### 8.1.1 背景：为什么要引入 Lambda\n\n在 Java 8 之前，如果我们想将一个行为（一段代码逻辑）传递给另一个方法，通常需要依赖**匿名内部类** (`Anonymous Inner Class`)。这种写法不仅语法冗长、可读性差，而且会为每个实例生成一个独立的 `.class` 文件，显得非常笨重。\n\n让我们通过一个简单的例子直观感受一下：对一个字符串列表按长度进行排序。\n\n**背景**：假设我们需要对一个 `List<String>` 集合，按照字符串的长度进行升序排序。\n\n####### **场景1：Java 8 之前的实现方式 - 使用匿名内部类**\n\n```java\npackage com.example;\n\nimport java.util.Arrays;\nimport java.util.Collections;\nimport java.util.Comparator;\nimport java.util.List;\n\npublic class Main {\n    public static void main(String[] args) {\n        List<String> names = Arrays.asList("peter", "anna", "mike", "xenia");\n        // 按照字符串的长度进行升序排序。\n        names.sort(new Comparator<String>() {\n            @Override\n            public int compare(String a, String b) {\n                return a.length() - b.length();\n            }\n        });\n        System.out.println("排序后的列表: " + names);\n\n    }\n}\n// 输出： \n// 排序后的列表: [anna, mike, peter, xenia]\n```\n\n> **小结**: 我们可以看到，为了实现一个简单的比较逻辑，我们不得不编写一个完整的匿名内部类实例，其中真正核心的代码只有一行 `a.length() - b.length()`，其余都是模板化的语法噪音。这种写法显得“头重脚轻”，不够优雅。Lambda 表达式的诞生，正是为了解决这一痛点。\n\n#### 8.1.2 核心：Lambda 语法与函数式接口\n\n###### 1\\. Lambda 表达式的三部分构成：`(参数) -> {方法体}` - 一种更紧凑的代码表示法\n\nLambda 表达式本质上是一个**匿名方法**，它提供了一种清晰、简洁的方式来表示一个函数式接口的实例。其构成如下：\n\n  * **参数列表 `(parameters)`**：与接口中抽象方法的参数列表相对应。可以省略参数类型，编译器会自动推断。如果只有一个参数，可以省略括号 `()`。\n  * **箭头符号 `->`**：将参数列表与方法体分开，读作 "goes to"。\n  * **方法体 `body`**：包含了方法的实现逻辑。如果方法体只有一条语句，可以省略大括号 `{}` 和 `return` 关键字。\n\n####### **场景1：使用 Lambda 表达式重写排序**\n\n**背景**：同样是对字符串列表按长度排序，我们看看使用 Lambda 如何实现。\n\n``` java\npackage com.example;\n\nimport java.util.Arrays;\nimport java.util.List;\n\npublic class Main {\n    public static void main(String [] args) {\n        List <String> names = Arrays.asList("peter", "anna", "mike", "xenia");\n\n        // 1. 带有类型声明的完整语法\n        // names.sort((String a, String b) -> {\n        //     return a.length() - b.length();\n        // });\n\n        // 2. 省略类型声明，由编译器推断 (推荐)\n        // names.sort((a, b) -> {\n        //     return a.length() - b.length();\n        // });\n\n        // 3. 当方法体只有一行时，省略大括号和 return (最简洁，推荐)\n        names.sort((a, b) -> a.length() - b.length());\n\n        System.out.println("使用 Lambda 排序后的列表: " + names);\n    }\n}\n// 输出:\n// 使用 Lambda 排序后的列表: [anna, mike, peter, xenia]\n```\n\n> **小结**: 对比之前的匿名内部类，Lambda 表达式极大地简化了代码，使我们的意图一目了然：**传入两个字符串 `a` 和 `b`，返回它们的长度差**。\n\n###### 2\\. 函数式接口 (`@FunctionalInterface`) - Lambda 的类型基础\n\nLambda 表达式本身没有类型，它必须被赋值给一个**函数式接口**类型的变量。\n\n> **定义**：函数式接口是**有且仅有一个抽象方法**的接口（可以包含多个默认方法或静态方法）。\n\n为了让编译器强制检查一个接口是否为函数式接口，Java 8 引入了 `@FunctionalInterface` 注解。\n\n> **最佳实践**: 我们强烈推荐为所有函数式接口添加 `@FunctionalInterface` 注解，以明确接口意图并利用编译时检查。\n\n  * **常用函数式接口速查表**\n\n`java.util.function` 包中预定义了大量常用的函数式接口，以下是四大核心接口：\n\n| 接口名 | 抽象方法 | 功能描述 |\n| :--- | :--- | :--- |\n| `Predicate<T>` | `boolean test(T t)` | **（主力/推荐）接收一个参数并返回一个布尔值，常用于过滤**。 |\n| `Function<T, R>` | `R apply(T t)` | **（主力/推荐）接收一个参数，返回一个结果，常用于映射/转换**。 |\n| `Supplier<T>` | `T get()` | 不接收参数，返回一个结果，常用于对象的**工厂方法**。 |\n| `Consumer<T>` | `void accept(T t)` | 接收一个参数，没有返回值，常用于对元素执行某种**消费操作**（如打印）。 |\n\n####### **场景2：四大核心函数式接口实战**\n\n**背景**：我们将通过一个简单的程序来演示这四个核心接口在实际代码中的应用。\n\n``` java\npackage com.example;\n\nimport java.util.function.Consumer;\nimport java.util.function.Function;\nimport java.util.function.Predicate;\nimport java.util.function.Supplier;\n\npublic class Main {\n    public static void main(String [] args) {\n        // 1. Predicate <T>: 判断一个整数是否大于 10\n        Predicate <Integer> isGreaterThan10 = x -> x > 10;\n        System.out.println("15 是否大于 10? " + isGreaterThan10.test(15));\n\n        // 2. Function <T, R>: 将字符串转换为其长度\n        Function <String, Integer> stringLengthFunction = s -> s.length();\n        System.out.println("\'hello\' 的长度是: " + stringLengthFunction.apply("hello"));\n\n        // 3. Consumer <T>: 打印传入的字符串\n        Consumer <String> printConsumer = s -> System.out.println("正在消费: " + s);\n        printConsumer.accept("This is a message.");\n\n        // 4. Supplier <T>: 生成一个随机数\n        Supplier <Double> randomSupplier = () -> Math.random();\n        System.out.println("生成的随机数: " + randomSupplier.get());\n    }\n}\n// 输出:\n// 15 是否大于 10? true\n// \'hello\' 的长度是: 5\n// 正在消费: This is a message.\n// 生成的随机数: 0.12345... (每次不同)\n```\n\n> 你可能已经注意到，上面的示例代码中我们直接使用了 Predicate、Function 等接口，但并没有看到 @FunctionalInterface 注解的影子。这是为什么呢？\n\n**关键在于区分“接口的定义者”与“接口的使用者”：**\n\n1. **定义者**：`@FunctionalInterface` 注解是给**接口的创建者**使用的。Java 官方在 设计 Predicate, Function 等接口时，**已经在它们的源码中添加了 `@FunctionalInterface` 注解**。这保证了 JDK 提供的这些接口绝对符合函数式接口的规范。\n2. **使用者**：在我们的示例代码中，我们是这些接口的**使用者**。我们直接利用这些已经由 JDK 定义好并验证过的接口作为 Lambda 表达式的类型，而不需要（也不能）再去重复声明注解。\n\n#### 8.1.3 进阶：方法引用的四种类型 (`::`)\n\n**方法引用** (`Method Reference`) 是 Lambda 表达式的一种特殊语法糖，它允许我们通过方法的名字来直接引用一个已经存在的方法。当 Lambda 表达式的方法体已经有现成的方法可以实现时，使用方法引用会让代码更加简洁易读。\n\n**用更具体的例子来说：**\n\n假设我们要对一个字符串列表进行排序。\n\n*   **不使用方法引用：**\n  \n    ``` java\n    List <String> names = Arrays.asList("张三", "李四", "王五");\n    Collections.sort(names, (s1, s2) -> s1.compareTo(s2)); // Lambda 表达式\n    ```\n这里，`(s1, s2) -> s1.compareTo(s2)` 就是你的 Lambda 表达式，你是在“教” `sort` 方法如何比较两个字符串。\n    \n* **使用方法引用（就像直接用现成的菜）：**\n\n  ``` java\n  List <String> names = Arrays.asList("张三", "李四", "王五");\n  Collections.sort(names, String:: compareTo); // 方法引用\n  ```\n  `String::compareTo` 就是对已经存在的 `String` 类里的 `compareTo` 方法的引用。你不再需要写 `(s1, s2) -> s1.compareTo(s2)`，而是直接说：“用 `String` 类那个叫 `compareTo` 的方法来比吧！”\n\n| 方法引用类型 | 示例语法（实际代码） | Lambda 等价形式（实际代码） | 核心意图 |\n| :--- | :--- | :--- | :--- |\n| **引用静态方法** | `类名::静态方法名` | `参数 -> 类名.静态方法名(参数)` | 调用类的静态方法 |\n| **引用特定类型的任意对象的实例方法** | `类名::实例方法名` | `对象实例, 参数 -> 对象实例.实例方法名(参数)` | 调用对象身上的实例方法（对象是传入的） |\n| **引用特定对象的实例方法** | `具体对象::实例方法名` | `参数 -> 具体对象.实例方法名(参数)` | 调用某个固定对象的实例方法 |\n| **引用构造函数** | `类名::new` | `参数 -> new 类名(参数)` | 创建类的实例 |\n\n**说明：**\n\n*   这里的“参数”是根据实际的函数式接口定义来决定的，可能是一个或多个参数，也可能没有参数。\n*   “对象实例”在“引用特定类型的任意对象的实例方法”中，是作为第一个隐式参数传入的。\n*   “具体对象”在“引用特定对象的实例方法”中，是方法引用语法的一部分，已经固定了。\n####### **方法引用实战场景详解**\n\n**背景**：我们将通过代码示例逐一展示四种方法引用的应用场景。\n\n``` java\npackage com.example;\n\nimport java.util.Arrays;\nimport java.util.List;\nimport java.util.function.Consumer;\nimport java.util.function.Function;\nimport java.util.function.Supplier;\n\npublic class Main {\n\n    public static void main(String [] args) {\n        List <String> strList = Arrays.asList("apple", "banana", "cherry");\n        // 场景 1: 引用静态方法\n        // 将字符串转换为整数\n        Function <String, Integer> parseIntFunc = s -> Integer.parseInt(s.replaceAll("\\\\D+", ""));\n        Function <String, Integer> valueOfFunc = Integer:: valueOf;\n        System.out.println("字符串 \'123\' 转为整数: " + valueOfFunc.apply("123"));\n\n        // 场景 2: 引用特定类型的任意对象的实例方法\n        // 将字符串列表全部转为大写\n        strList.stream().map(String:: toUpperCase).forEach(s -> System.out.print(s + " "));\n\n        // 场景 3: 引用特定对象的实例方法\n        // 使用 System.out 对象的 println 方法\n        Consumer <String> printer = System.out:: println;\n        printer.accept("Hello, Method Reference!");\n\n        // 场景 4: 引用构造函数\n        // 创建一个 Person 对象\n        Supplier <Person> personSupplier = Person:: new;\n        Person person = personSupplier.get();\n        System.out.println("创建了一个新 Person: " + person);\n    }\n}\n\nclass Person {\n    @Override\n    public String toString() {\n        return "A Person instance";\n    }\n}\n\n```\n\n> **小结**: 当 Lambda 的逻辑仅仅是调用一个已存在的方法时，方法引用是最佳选择。它能消除冗余的参数声明，让代码的**“意图”**而非**“实现细节”**凸显出来。\n\n#### 8.1.4 [高频面试题] Lambda 与匿名内部类的区别\n\n> **Q: Lambda 表达式和匿名内部类有什么核心区别？**\n>\n> **A:** 尽管 Lambda 表达式在很多场景下可以替代匿名内部类，但它们在底层实现和行为上存在显著差异。\n>\n> 1.  **`this` 的指向不同 (Scope)**: 这是最核心的区别。\n>* **匿名内部类**: `this` 关键字指向的是匿名内部类**自身的实例**。\n>       * **Lambda 表达式**: `this` 关键字指向的是其**外层封装类**的实例。Lambda 表达式本身没有自己的 `this`，它在词法上是其外层作用域的一部分。\n>       \n>2.  **编译后产物不同**:\n> * **匿名内部类**: 编译器会为每一个匿名内部类生成一个单独的 `.class` 文件，格式通常是 `EnclosingClass$1.class`。\n>      * **Lambda 表达式**: 编译器不会为每个 Lambda 生成一个独立的 `.class` 文件。它会将 Lambda 的逻辑翻译成一个私有的静态方法，并在运行时通过 `invokedynamic` 指令来动态地创建实现函数式接口的对象。这种方式在性能和内存占用上通常更优。\n>       \n> 3.  **功能限制**:\n>* **匿名内部类**: 功能更强大。它可以实现有多个抽象方法的接口，可以继承具体的类，也可以拥有自己的实例变量和方法。\n>       * **Lambda 表达式**: 功能更专注。它只能用于实现**函数式接口**（即只有一个抽象方法的接口）。\n>      \n> 4.  **性能 :\n> * 由于 `invokedynamic` 的机制，JVM 在运行时有更多的优化空间，比如可以对 Lambda 的调用进行内联或者延迟实例化，因此在很多情况下，Lambda 的性能会优于或等于匿名内部类。\n\n\n\n-----\n\n### 8.2 [数据处理革命] [Java 8+] Stream API\n\n> **本章导读**: 如果说 Lambda 表达式是 Java 8 的“引擎”，那么 Stream API 就是搭载了这个强大引擎的“超级跑车”。它提供了一套声明式、可组合、可并行的 API，彻底改变了我们对集合数据的处理方式。告别繁琐的 `for` 循环和临时变量，Stream API 让我们能以一种更优雅、更符合人类思维的“流水线”方式来操作数据。本节，我们将全面探索 Stream 的世界。\n\n#### 8.2.1 核心概念：流的创建与生命周期\n\n一个典型的 Stream 操作流包含三个阶段：\n\n1.  **创建 (Creation)**: 从一个数据源（如集合、数组）获取一个流。\n2.  **中间操作 (Intermediate Operations)**: 对流进行一系列的转换或筛选操作。每个中间操作都会返回一个新的流，这使得操作可以像链条一样串联起来。这些操作是**惰性求值 (Lazy)** 的，也就是说，它们在终端操作被调用前不会真正执行。\n3.  **终端操作 (Terminal Operations)**: 触发流的实际计算，并产生一个最终结果（如一个值、一个集合，或无返回值）。终端操作是**及早求值 (Eager)** 的，一旦执行，流就会被消耗且无法再次使用。\n\n####### **场景1：流的多种创建方式**\n\n**背景**：在开始处理数据前，我们首先需要知道如何从不同的数据源创建流。\n\n``` java\npackage com.example;\n\nimport java.util.Arrays;\nimport java.util.List;\nimport java.util.stream.IntStream;\nimport java.util.stream.Stream;\n\npublic class Main {\n    public static void main(String [] args) {\n        // 1. 从集合创建流 (最常用)\n        List <String> list = Arrays.asList("a", "b", "c");\n        Stream <String> listStream = list.stream();\n        System.out.print("从 List 创建的流: ");\n        listStream.forEach(s -> System.out.print(s + " "));\n        System.out.println();\n\n        // 2. 从数组创建流\n        String [] array = {"x", "y", "z"};\n        Stream <String> arrayStream = Arrays.stream(array);\n        System.out.print("从 Array 创建的流: ");\n        arrayStream.forEach(s -> System.out.print(s + " "));\n        System.out.println();\n\n        // 3. 使用 Stream.of() 静态方法创建流\n        Stream <Integer> ofStream = Stream.of(1, 2, 3, 4, 5);\n        System.out.print("使用 Stream.of() 创建的流: ");\n        ofStream.forEach(s -> System.out.print(s + " "));\n        System.out.println();\n        \n        // 4. 使用 Stream.iterate() 创建无限流\n        Stream <Integer> iterateStream = Stream.iterate(0, n -> n + 2).limit(5); // 必须用 limit 限制\n        System.out.print("使用 Stream.iterate() 创建的无限流 (限制后): ");\n        iterateStream.forEach(s -> System.out.print(s + " "));\n        System.out.println();\n    }\n}\n// 输出:\n// 从 List 创建的流: a b c \n// 从 Array 创建的流: x y z \n// 使用 Stream.of() 创建的流: 1 2 3 4 5 \n// 使用 Stream.iterate() 创建的无限流 (限制后): 0 2 4 6 8 \n```\n\n> **小结**: 创建流是所有操作的第一步。`collection.stream()` 是最常用的方式。对于无限流，务必使用 `limit()` 等中间操作将其变为有限流，否则可能会导致无限循环。\n\n#### 8.2.2 中间操作详解\n\n中间操作是 Stream API 的精髓所在，它们允许我们将复杂逻辑分解为一系列小而清晰的步骤。\n\n###### 1\\. 筛选与切片 - 从流中选取我们需要的元素\n\n  * **常用筛选与切片方法速查表**\n\n| 方法名 | 功能描述 |\n| :--- | :--- |\n| `filter(Predicate<T>)` | **(主力/推荐)** 根据条件过滤流中元素，只保留满足条件的。 |\n| `distinct()` | 去除流中重复的元素（基于元素的 `equals()` 方法）。 |\n| `limit(long)` | 截断流，使其元素数量不超过给定值。 |\n| `skip(long)` | 跳过前 N 个元素，返回一个扔掉了前 N 个元素的新流。 |\n\n####### **场景2：筛选出前两名不重复的偶数**\n\n**背景**：给定一个整数列表，我们需要找到其中不重复的偶数，并只取前两个。\n\n``` java\npackage com.example;\n\nimport java.util.Arrays;\nimport java.util.List;\n\npublic class Main {\n    public static void main(String [] args) {\n        List <Integer> numbers = Arrays.asList(2, 4, 6, 4, 8, 2, 10, 12);\n\n        System.out.print("处理后的结果: ");\n        numbers.stream()\n                .filter(n -> n % 2 == 0)   // 筛选偶数: [2, 4, 6, 4, 8, 2, 10, 12]\n                .distinct()               // 去重: [2, 4, 6, 8, 10, 12]\n                .skip(1)                  // 跳过第一个: [4, 6, 8, 10, 12]\n                .limit(2)                 // 取前两个: [4, 6]\n                .forEach(n -> System.out.print(n + " "));\n        System.out.println();\n    }\n}\n// 输出:\n// 处理后的结果: 4 6 \n```\n\n###### 2\\. 映射 - 将流中元素转换为其他形式或提取信息\n\n  * **常用映射方法速查表**\n\n| 方法名 | 功能描述 |\n| :--- | :--- |\n| `map(Function<T,R>)` | **(主力/推荐)** 将流中每个元素 `T` 转换为另一个元素 `R` (一对一映射)。 |\n| `flatMap(Function<T,Stream<R>>)` | **(进阶/常用)** 将每个元素转换为一个新流，然后将所有子流连接成一个流 (一对多扁平化)。 |\n\n####### **场景3：从单词列表获取所有不同的字符**\n\n**背景**：给定一个单词列表 `["Hello", "World"]`，我们希望得到其中所有字符列表 `[H, e, l, l, o, W, o, r, l, d]`。\n\n``` java\npackage com.example;\n\nimport java.util.Arrays;\nimport java.util.List;\nimport java.util.stream.Collectors;\n\npublic class Main {\n    public static void main(String [] args) {\n        List <String> words = Arrays.asList("Hello", "World");\n        List <String> list = words.stream()\n                .map(word -> word.split("")) // 1. 得到 Stream <String[]>\n                .flatMap(Arrays:: stream)   // 2. 扁平化为 Stream <String>\n                .toList();// 3. 转为 List\n\n        System.out.println(list);\n\n    }\n}\n// 输出：\n// [H, e, l, l, o, W, o, r, l, d]\n\n\n```\n\n> **小结**: 当你的转换逻辑会从**一个元素**产生**多个结果**时（例如，从一个单词产生多个字符），`flatMap` 就是你需要的。\n\n###### 3\\. 排序 - 对流中元素进行排序\n\n  * **常用排序方法速查表**\n\n| 方法名 | 功能描述 |\n| :--- | :--- |\n| `sorted()` | 按自然顺序排序（元素需实现 `Comparable`）。 |\n| `sorted(Comparator<T>)` | **(主力/推荐)** 根据自定义比较器进行排序，表达能力更强。 |\n\n####### **场景4：对自定义对象进行排序**\n\n**背景**：我们有一个 `Product` 列表，需要先按价格降序排序，如果价格相同，再按名称升序排序。\n\n``` java\npackage com.example;\n\nimport lombok.AllArgsConstructor;\nimport lombok.Data;\n\nimport java.util.Arrays;\nimport java.util.Comparator;\nimport java.util.List;\n\npublic class Main {\n    public static void main(String [] args) {\n        List <Product> products = Arrays.asList(\n                new Product("Laptop", 1200), new Product("Mouse", 50),\n                new Product("Keyboard", 100), new Product("MousePad", 50),\n                new Product("Keyboard", 300), new Product("MousePad", 50)\n        );\n        // 原生 Stream 流的实现方式\n        products.stream()\n                .sorted(Comparator.comparing(Product:: getPrice).reversed() // 按价格降序\n                        .thenComparing(Product:: getName))       // 再按名称升序\n                .forEach(System.out:: println);\n\n    }\n}\n\n@Data\n@AllArgsConstructor\nclass Product {\n    private String name;\n    private int price;\n}\n\n\n```\n\n> **小结**: `Comparator` 接口在 Java 8 中也得到了极大的增强，`comparing()`, `thenComparing()`, `reversed()` 等静态和默认方法使得构建复杂的多级排序逻辑变得异常简单和直观。\n\n#### 8.2.3 终端操作详解\n\n终端操作是流处理的最后一步，它会触发所有懒加载的中间操作并生成最终结果。\n\n###### 1\\. 匹配与查找 - 检查流中元素是否满足特定条件\n\n  * **常用匹配与查找方法速查表**\n\n| 方法名 | 功能描述 |\n| :--- | :--- |\n| `anyMatch(Predicate<T>)` | 检查流中是否**至少有一个**元素匹配条件。 |\n| `allMatch(Predicate<T>)` | 检查流中是否**所有**元素都匹配条件。 |\n| `noneMatch(Predicate<T>)`| 检查流中是否**没有**元素匹配条件。 |\n| `findFirst()` | 返回流的第一个元素，用 `Optional` 包装。 |\n| `findAny()` | 返回流中的任意一个元素，用 `Optional` 包装（并行流中更高效）。 |\n\n####### **场景5：检查产品列表中是否存在高价商品**\n\n``` java\npackage com.example;\n\nimport java.util.Arrays;\nimport java.util.List;\nimport java.util.Optional;\n\n\n@Data\n@AllArgsConstructor\nclass Product {\n    private String name;\n    private int price;\n}\n\npublic class Main {\n    public static void main(String [] args) {\n         List <Product> products = Arrays.asList(\n            new Product("Laptop", 1200), new Product("Mouse", 50)\n        );\n        boolean hasExpensiveProduct = products.stream().anyMatch(p -> p.getPrice() > 1000);\n        System.out.println("是否有价格 > 1000 的产品? " + hasExpensiveProduct);\n    }\n}\n// 输出:\n// 是否有价格 > 1000 的产品? true\n```\n\n###### 2\\. 规约 (Reduction) - 将流中所有元素计算，得到一个值\n\n  * **常用规约方法速查表**\n\n| 方法名 | 功能描述 |\n| :--- | :--- |\n| `reduce(T identity, BinaryOperator<T>)` | **(主力/推荐)** 从一个初始值 `identity` 开始，对流中所有元素进行规约操作。 |\n| `reduce(BinaryOperator<T>)` | 无初始值的规约，因为流可能为空，所以返回一个 `Optional`。 |\n\n####### **场景6：计算所有产品价格的总和**\n\n``` java\npackage com.example;\n\nimport java.util.Arrays;\nimport java.util.List;\n\n@Data\n@AllArgsConstructor\nclass Product {\n    private String name;\n    private int price;\n}\npublic class Main {\n    public static void main(String [] args) {\n        List <Product> products = Arrays.asList(\n            new Product("Laptop", 1200), new Product("Mouse", 50)\n        );\n        int totalPrice = products.stream()\n            .map(Product:: getPrice)\n            .reduce(0, Integer:: sum); // 0 是初始值, Integer:: sum 是 (a, b) -> a + b\n        System.out.println("所有产品的总价是: " + totalPrice);\n    }\n}\n// 输出:\n// 所有产品的总价是: 1250\n```\n\n###### 3\\. 收集 (Collect) - 将流中元素转换成集合、Map或其他复杂对象\n\n  * **常用收集器 (`Collectors`) 速查表**\n\n| 收集器 (`Collector`) | 功能描述 |\n| :--- | :--- |\n| `toList()` / `toSet()` | **(最常用)** 将流中元素收集到 `List` 或 `Set`。 |\n| `toMap(keyMapper, valueMapper)` | 将流中元素收集到 `Map`，需注意 key 重复问题。 |\n| `groupingBy(classifier)` | **(主力/推荐)** 根据分类函数对元素进行分组，返回一个 `Map`。 |\n| `joining(delimiter)` | 将流中元素（通常是字符串）连接成一个字符串。 |\n\n####### **场景7：将产品列表按价格分组**\n\n``` java\npackage com.example;\n\nimport lombok.AllArgsConstructor;\nimport lombok.Data;\n\nimport java.util.Arrays;\nimport java.util.List;\nimport java.util.Map;\nimport java.util.stream.Collectors;\n@Data\n@AllArgsConstructor\nclass Product {\n    private String name;\n    private int price;\n}\npublic class Main {\n    public static void main(String [] args) {\n        List <Product> products = Arrays.asList(\n                new Product("Laptop", 1200), new Product("Mouse", 50),\n                new Product("Keyboard", 100), new Product("MousePad", 50)\n        );\n\n        Map <Integer, List<Product> > productsByPrice = products.stream()\n                .collect(Collectors.groupingBy(Product:: getPrice));\n\n        System.out.println("按价格分组的结果:");\n        productsByPrice.forEach((price, list) -> System.out.println("价格: " + price + " -> " + list));\n    }\n}\n// 输出:\n// 按价格分组的结果:\n// 价格: 50 -> [Product{name =\'Mouse\', price = 50}, Product{name =\'MousePad\', price = 50}]\n// 价格: 100 -> [Product{name =\'Keyboard\', price = 100}]\n// 价格: 1200 -> [Product{name =\'Laptop\', price = 1200}]\n```\n\n> **小结**: `Collectors` 工具类提供了极其丰富和强大的收集器，是 `collect` 操作的“弹药库”。熟练掌握 `Collectors` 是高效使用 Stream API 的关键。\n\n\n\n-----\n\n### 8.3 [空指针终结者] [Java 8+] Optional 容器类\n\n> **本章导读**: `NullPointerException` (NPE) 被其创造者称为“价值十亿美元的错误”，它长久以来都是 Java 开发者的噩梦。为了应对这一挑战，Java 8 引入了 `Optional<T>`。它是一个容器类，其核心设计思想并非简单地替代 `null`，而是通过**类型系统**来显式地表达一个值**可能缺失**的情况。这迫使我们作为调用者，必须正视并处理值不存在的可能性，从而将潜在的运行时 NPE 转换为编译时可见的设计考量，让代码更加健壮和优雅。\n\n#### 8.3.1 设计哲学：为什么需要 Optional\n\n在 `Optional` 出现之前，方法通常通过返回 `null` 来表示“没有找到结果”。这种做法存在一个致命缺陷：`null` 的含义是模糊的，并且完全依赖于调用者的“自觉性”去进行非空检查。\n\n  * **返回 `null` 的问题**:\n    1.  **意图不明**: `null` 可能代表“查询无结果”、“操作失败”或“值本身就是空”，容易引起混淆。\n    2.  **契约不清**: 方法签名 `User findById(long id)` 无法告诉调用者它可能会返回 `null`，这是一种隐藏的、脆弱的契约。\n    3.  **风险转嫁**: 将校验 `null` 的责任完全抛给了调用方，一旦忘记检查，`NullPointerException` 就会在运行时不期而至。\n\n`Optional` 通过将“可能没有值”这一情况包装成一个对象，完美地解决了上述问题。一个返回 `Optional<User>` 的方法，其签名本身就在大声宣告：“我返回的可能是一个 `User`，也可能什么都没有，请你务必处理这两种情况！”\n\n#### 8.3.2 核心方法分类使用\n\n###### 1\\. 创建 Optional 对象 - 包装你的值\n\n  * **常用创建方法速查表**\n\n| 方法名 | 功能描述 |\n| :--- | :--- |\n| `of(T value)` | **(谨慎使用)** 为一个**你确定非 null** 的值创建一个 `Optional`。如果 `value` 为 `null`，会立即抛出 `NullPointerException`。 |\n| `ofNullable(T value)` | **(主力/推荐)** 为一个**可能为 null** 的值创建一个 `Optional`。如果 `value` 为 `null`，则返回一个空的 `Optional` 对象。这是最安全、最常用的创建方式。 |\n| `empty()` | 创建一个明确的、空的 `Optional` 实例。 |\n\n####### **场景1：创建 Optional 实例**\n\n**背景**：我们有一个方法，可能返回一个 `User` 对象，也可能返回 `null`。我们需要将这个结果安全地包装起来。\n\n``` java\npackage com.example;\n\nimport lombok.AllArgsConstructor;\nimport lombok.Data;\n\nimport java.util.Optional;\n\npublic class Main {\n    public static void main(String [] args) {\n        User user = new User("John Doe");\n        User nullUser = null;\n\n        // 1. ofNullable: 最安全的方式\n        Optional <User> optUser = Optional.ofNullable(user);\n        Optional <User> optNullUser = Optional.ofNullable(nullUser);\n        System.out.println("ofNullable (有值): " + optUser.isPresent());  // ofNullable (有值): true\n        System.out.println("ofNullable (无值): " + optNullUser.isPresent()); // ofNullable (无值): false\n        // 2. of: 当你确定值不为 null 时使用\n        Optional <User> optUserOf = Optional.of(user);\n        System.out.println("of (有值): " + optUserOf.isPresent()); // of (有值): true\n        // 下面这行会立即抛出 NullPointerException\n        // Optional.of(nullUser);\n\n        // 3. empty: 创建一个空实例\n        Optional <User> emptyOpt = Optional.empty();\n        System.out.println("empty: " + emptyOpt.isPresent()); // empty: false\n\n    }\n}\n@Data @AllArgsConstructor\nclass User {private String name;}\n```\n\n###### 2\\. 获取值（带默认处理） - 安全地取出结果\n\n> **[避坑指南]**: 应尽量避免使用 `get()` 方法。它在 `Optional` 为空时会抛出 `NoSuchElementException`，与我们期望避免运行时异常的初衷背道而驰。只有在万分确定 `Optional` 中有值时（例如，在 `isPresent()` 判断之后），才可使用。\n\n  * **安全获取值方法速查表**\n\n| 方法名 | 功能描述 |\n| :--- | :--- |\n| `orElse(T other)` | **(常用)** 如果 `Optional` 中有值，则返回该值；否则返回指定的默认值 `other`。**注意**：无论 `Optional` 是否为空，`other` 参数都会被求值。|\n| `orElseGet(Supplier<? extends T>)` | **(主力/推荐)** 如果 `Optional` 中有值，则返回该值；否则调用 `Supplier` 函数式接口来生成一个默认值。**优势**：`Supplier` 只在 `Optional` 为空时才会被调用，实现了懒加载，性能更优。|\n| `orElseThrow(Supplier<? extends X>)` | 如果 `Optional` 中有值，则返回该值；否则抛出由 `Supplier` 生成的异常。|\n\n####### **场景2：为可能不存在的用户提供默认名称**\n\n**背景**：我们需要从一个 `Optional<User>` 中获取用户名，如果用户不存在，则返回 "Guest"。\n\n``` java\npackage com.example;\n\nimport lombok.AllArgsConstructor;\nimport lombok.Data;\n\nimport java.util.Optional;\n\n@Data @AllArgsConstructor\nclass User {private String name;}\npublic class Main {\n    public static void main(String [] args) {\n        Optional <User> userOpt = Optional.of(new User("Alice"));\n        Optional <User> emptyOpt = Optional.empty();\n\n        // orElse: 无论 Optional 是否为空，"new User(" Guest ")" 都会被执行\n        String name1 = userOpt.orElse(new User("Guest")).getName();\n        System.out.println("orElse (有值时): " + name1);\n\n        String name2 = emptyOpt.orElse(new User("Guest")).getName();\n        System.out.println("orElse (无值时): " + name2);\n\n        // orElseGet: 只有在 Optional 为空时，lambda 表达式才会执行\n        String name3 = emptyOpt.orElseGet(() -> new User("Default User")).getName();\n        System.out.println("orElseGet (无值时): " + name3);\n    }\n}\n// 输出:\n// orElse (有值时): Alice\n// orElse (无值时): Guest\n// orElseGet (无值时): Default User\n```\n\n###### 3\\. 值的转换与过滤 - 以函数式风格处理值\n\n这些方法让我们可以像操作 Stream 一样，对 `Optional` 内部的值进行链式处理，而无需显式地进行非空判断。\n\n  * **常用转换与过滤方法速查表**\n\n| 方法名 | 功能描述 |\n| :--- | :--- |\n| `map(Function<T, R>)` | **(主力/推荐)** 如果值存在，则对其应用 `Function` 映射，并返回一个包装了新值的 `Optional<R>`。如果值不存在，则直接返回一个空的 `Optional<R>`。|\n| `flatMap(Function<T, Optional<R>>)` | **(进阶/常用)** 与 `map` 类似，但要求映射函数本身返回一个 `Optional`。`flatMap` 会将结果“扁平化”，避免出现 `Optional<Optional<T>>` 这样的嵌套结构。|\n| `filter(Predicate<T>)` | 如果值存在且满足 `Predicate` 条件，则返回包含该值的 `Optional`；否则返回一个空的 `Optional`。|\n\n####### **场景3：获取用户地址的邮政编码（多级可能为空）**\n\n**背景**：`User` 可能没有 `Address`，`Address` 可能没有 `zipCode`。我们需要安全地获取邮编，如果中间任何一环为空，都应返回 "UNKNOWN"。\n\n``` java\npackage com.example;\n\nimport java.util.Optional;\n\nclass Address {\n    private String zipCode;\n    public Address(String zipCode) { this.zipCode = zipCode; }\n    public Optional <String> getZipCode() { return Optional.ofNullable(zipCode); }\n}\nclass UserWithAddress {\n    private Address address;\n    public UserWithAddress(Address address) { this.address = address; }\n    public Optional <Address> getAddress() { return Optional.ofNullable(address); }\n}\npublic class Main {\n    public static void main(String [] args) {\n        // 一个拥有完整地址信息的用户\n        Address address = new Address("100-0001");\n        UserWithAddress user = new UserWithAddress(address);\n        Optional <UserWithAddress> userOpt = Optional.of(user);\n\n        // flatMap 用于处理返回 Optional 的 getter\n        String zip = userOpt\n                .flatMap(UserWithAddress:: getAddress)  // 返回 Optional <Address>\n                .flatMap(Address:: getZipCode)         // 返回 Optional <String>\n                .orElse("UNKNOWN");\n\n        System.out.println("用户的邮政编码是: " + zip); // 用户的邮政编码是: 100-0001\n\n        // 一个没有地址信息的用户\n        UserWithAddress userWithoutAddress = new UserWithAddress(null);\n        Optional <UserWithAddress> userOpt2 = Optional.of(userWithoutAddress);\n        String zip2 = userOpt2\n                .flatMap(UserWithAddress:: getAddress)\n                .flatMap(Address:: getZipCode)\n                .orElse("UNKNOWN");\n        System.out.println("无地址用户的邮政编码是: " + zip2); // 无地址用户的邮政编码是: UNKNOWN\n    }\n}\n// 输出:\n// 用户的邮政编码是: 100-0001\n// 无地址用户的邮政编码是: UNKNOWN\n```\n\n> **小结**: 当你的 `map` 操作返回的是一个 `Optional` 对象时，就应该使用 `flatMap` 来代替 `map`，以保持结构的扁平。\n\n###### 4\\. 判断与消费 - 在值存在时执行操作\n\n  * **常用判断与消费方法速查表**\n\n| 方法名 | 功能描述 |\n| :--- | :--- |\n| `isPresent()` | **(不推荐)** 检查值是否存在。通常可以用函数式方法替代，以避免命令式的 `if` 语句。|\n| `ifPresent(Consumer<T>)` | **(主力/推荐)** 如果值存在，则对该值执行 `Consumer` 操作。这是处理“有值”情况的最常用方式。|\n| `ifPresentOrElse(Consumer<T>, Runnable)` | `[Java 9+]` 如果值存在，执行第一个 `Consumer`；否则，执行第二个 `Runnable` 任务。|\n\n####### **场景4：如果用户存在，就打印其信息**\n\n``` java\npackage com.example;\n\nimport lombok.AllArgsConstructor;\nimport lombok.Data;\n\nimport java.util.Optional;\n\n@Data\n@AllArgsConstructor\nclass User {private String name;}\npublic class Main {\n    public static void main(String [] args) {\n        Optional <User> userOpt = Optional.of(new User("Charles"));\n        Optional <User> emptyOpt = Optional.empty();\n\n        // 只有在 userOpt 有值时，才会执行 lambda\n        userOpt.ifPresent(user -> System.out.println("用户信息: " + user.getName())); // 用户信息: Charles\n\n        // emptyOpt 为空，lambda 不会被执行\n        emptyOpt.ifPresent(user -> System.out.println("这段代码不会被执行"));\n\n        // Java 9+ 的 ifPresentOrElse\n         userOpt.ifPresentOrElse(\n             user -> System.out.println("用户信息: " + user.getName()), // 用户信息: Charles\n             () -> System.out.println("用户不存在") // 若用户不存在，则会执行此 lambda\n         );\n    }\n}\n// 输出:\n// 用户信息: Charles\n```\n\n\n\n-----\n\n### 8.4 [语言增强] [Java 8+] 语言及 API 增强\n\n> **本章导读**: 除了像 Stream 和 Optional 这样的大型主题外，Java 8 及后续版本还带来了许多“小而美”的改进。本节将作为这些实用特性的一个合集，我们将学习接口如何通过默认方法和静态方法实现优雅演进，了解如何用一行代码创建不可变集合，并探索 String、Files 等日常核心 API 的便利新方法，以及可重复注解和 Base64 API 等实用工具。\n\n#### 8.4.1 接口的演进 `[Java 8]`\n\n在 Java 8 之前，接口是“纯粹”的，只能包含抽象方法和常量。这种设计的弊端在于，一旦接口发布，就很难再向其中添加新方法，因为这会强制所有已有的实现类都去实现这个新方法，造成大规模的代码破坏。Java 8 通过引入默认方法和静态方法，完美地解决了这个问题。\n\n###### 1\\. 默认方法 (`default`) - 在不破坏实现的前提下为接口添加新功能\n\n**默认方法**允许我们在接口中提供一个方法的默认实现。实现该接口的类将自动继承这个默认方法，无需强制重写。\n\n这对于API的向后兼容和优雅演进至关重要。\n\n  * **常用方法速查表**\n\n| 关键字 | 功能描述 |\n| :--- | :--- |\n| `default` | 在接口方法声明前使用，用于提供一个默认的方法体实现。 |\n\n####### **场景1：为一个 `Logger` 接口添加新的日志级别方法**\n\n**背景**：假设我们有一个已广泛使用的 `Logger` 接口，现在希望为它增加一个 `logWarning` 方法，但又不想让所有旧的实现类都报错。\n\n``` java\npackage com.example;\n\ninterface Logger {\n    void logInfo(String message);\n\n    // 使用 default 关键字为接口添加一个新方法\n    default void logWarning(String message) {\n        System.out.println("【Default Warning】: " + message);\n    }\n}\n\n// 一个老的实现类，它只实现了 logInfo\nclass SimpleFileLogger implements Logger {\n    @Override\n    public void logInfo(String message) {\n        System.out.println("【File Info】: " + message);\n    }\n}\n\n// 一个新的实现类，它选择重写默认方法\nclass ConsoleLogger implements Logger {\n    @Override\n    public void logInfo(String message) {\n        System.out.println("【Console Info】: " + message);\n    }\n\n    @Override\n    public void logWarning(String message) {\n        System.err.println("【CONSOLE WARNING】: " + message);\n    }\n}\n\npublic class Main {\n    public static void main(String [] args) {\n        Logger fileLogger = new SimpleFileLogger();\n        fileLogger.logInfo("Application started.");\n        // 老的实现类可以直接调用新的默认方法，而无需修改自身代码\n        fileLogger.logWarning("Configuration is missing.");\n\n        System.out.println("--------------------");\n\n        Logger consoleLogger = new ConsoleLogger();\n        consoleLogger.logInfo("Processing data...");\n        // 新的实现类调用的是它自己重写后的版本\n        consoleLogger.logWarning("Disk space is low.");\n    }\n}\n// 输出:\n// 【File Info】: Application started.\n// 【Default Warning】: Configuration is missing.\n// --------------------\n// 【Console Info】: Processing data...\n// 【CONSOLE WARNING】: Disk space is low.\n```\n\n> **小结**: **默认方法**是 Java API 设计者向库中添加新功能而**不破坏向后兼容性**的强大工具。它使得接口的演进变得平滑和安全。\n\n###### 2\\. 静态方法 (`static`) - 在接口中定义工具方法\n\nJava 8 还允许在接口中定义**静态方法**。这些方法不属于任何对象实例，而是直接属于接口本身。这使得我们可以将一些相关的工具方法直接组织在接口内部，而不是创建一个单独的 `xxxUtils` 工具类\n\n  * **常用方法速查表**\n\n| 关键字 | 功能描述 |\n| :--- | :--- |\n| `static` | 在接口方法声明前使用，定义一个属于接口本身的静态方法。 |\n\n####### **场景2：在 `Logger` 接口中提供一个工厂方法**\n\n**背景**：我们希望提供一个简单的方式来获取 `Logger` 的实例，可以直接在 `Logger` 接口中定义一个静态工厂方法。\n\n``` java\npackage com.example;\n\ninterface LoggerWithFactory {\n    void log(String message);\n\n    // 在接口中定义一个静态工厂方法\n    static LoggerWithFactory createConsoleLogger() {\n        return new ConsoleLoggerImpl();\n    }\n}\n\nclass ConsoleLoggerImpl implements LoggerWithFactory {\n    @Override\n    public void log(String message) {\n        System.out.println("LOG: " + message);\n    }\n}\n\npublic class Main {\n    public static void main(String [] args) {\n        // 直接通过接口名调用静态方法，就像调用普通工具类一样\n        LoggerWithFactory logger = LoggerWithFactory.createConsoleLogger();\n        logger.log("This is a test message.");\n    }\n}\n// 输出:\n// LOG: This is a test message.\n```\n\n> **小结**: **接口静态方法**进一步提升了接口作为**代码组织单元**的能力，让接口不仅能定义契约，还能提供相关的辅助工具。\n\n#### 8.4.2 集合工厂方法 `[Java 9+]`\n\n在 Java 9 之前，创建一个包含少量元素的集合通常需要好几行代码。Java 9 引入了一系列静态工厂方法 `of()`，极大地简化了**不可变集合**的创建。\n\n  * **常用方法速查表**\n\n| 方法名 | 功能描述 |\n| :--- | :--- |\n| `List.of(...)` | **(主力/推荐)** 创建一个**不可变**的 `List`。 |\n| `Set.of(...)` | 创建一个**不可变**的 `Set`，不允许重复元素。 |\n| `Map.of(k1, v1, ...)` | 创建一个**不可变**的 `Map`，不允许重复的键。 |\n\n> **[避坑指南]**: 使用 `of()` 方法创建的集合是**不可变的** (`Immutable`)。任何尝试对其进行添加、删除等修改操作的行为，都会抛出 `UnsupportedOperationException`。此外，它们**不允许**存入 `null` 元素。\n\n####### **场景3：快速创建只读的配置集合**\n\n**背景**：我们需要创建一个包含默认配置项的 `Map`，这个配置在程序运行期间不应被修改。\n\n``` java\npackage com.example;\n\nimport java.util.List;\nimport java.util.Map;\nimport java.util.Set;\n\npublic class Main {\n    public static void main(String [] args) {\n        // 创建不可变 List\n        List <String> names = List.of("Alice", "Bob", "Charlie");\n        System.out.println("Immutable List: " + names);\n\n        // 创建不可变 Set\n        Set <Integer> numbers = Set.of(10, 20, 30);\n        System.out.println("Immutable Set: " + numbers);\n\n        // 创建不可变 Map\n        Map <String, String> config = Map.of(\n            "user", "admin",\n            "password", "secret",\n            "timeout", "3000"\n        );\n        System.out.println("Immutable Map: " + config);\n\n        // 尝试修改会抛出异常\n        try {\n            names.add("David");\n        } catch (UnsupportedOperationException e) {\n            System.out.println("捕获到异常: " + e.getClass().getSimpleName());\n        }\n    }\n}\n// 输出:\n// Immutable List: [Alice, Bob, Charlie]\n// Immutable Set: [10, 20, 30]\n// Immutable Map: {user = admin, password = secret, timeout = 3000}\n// 捕获到异常: UnsupportedOperationException\n```\n\n#### 8.4.3 常用 API 增强 `[Java 11+]`\n\nJava 在后续版本中，也持续对一些我们日常使用最频繁的类（如 `String`）进行功能增强。\n\n  * **常用 String API 增强速查表**\n\n| 方法名 | 引入版本 | 功能描述 |\n| :--- | :--- | :--- |\n| `isBlank()` | Java 11 | 判断字符串是否为空白（`isEmpty()` 或只包含空白字符）。 |\n| `lines()` | Java 11 | 将字符串按行分隔符 `\\n` 拆分为一个 `Stream<String>`。 |\n| `repeat(int)` | Java 11 | 将字符串重复指定次数。 |\n| `strip()` | Java 11 | 去除字符串首尾的空白字符（比 `trim()` 更能识别 Unicode 空白符）。 |\n\n####### **场景4：处理多行文本并去除空白行**\n\n**背景**：我们从外部系统获取了一段文本，其中包含多行内容和一些空白行，需要进行清洗。\n\n``` java\npackage com.example;\n\nimport java.util.stream.Collectors;\n\npublic class Main {\n    public static void main(String [] args) {\n        String multilineText = "" "\n            \n            First line\n            Second line\n            \n            Third line\n            "" "; // 这是 Java 15 的文本块语法，此处用于方便地表示多行文本\n\n        System.out.println("--- 清洗前的文本 ---");\n        System.out.print(multilineText);\n        System.out.println("--- 清洗后的文本 ---");\n\n        String cleanedText = multilineText.lines()      // 1. 将文本分割成一个 Stream <String>\n                .filter(line -> ! line.isBlank())        // 2. 使用 isBlank() 过滤掉空白行\n                .map(String:: strip)                     // 3. 使用 strip() 去除每行首尾的空白\n                .collect(Collectors.joining("\\n")); // 4. 重新组合成一个字符串\n\n        System.out.println(cleanedText);\n\n        System.out.println("\\n--- 其他 String API ---");\n        System.out.println("重复三次 \'Abc\': " + "Abc".repeat(3));\n    }\n}\n// 输出:\n// --- 清洗前的文本 ---\n// \n// First line\n// Second line\n// \n// Third line\n// \n// --- 清洗后的文本 ---\n// First line\n// Second line\n// Third line\n// \n// --- 其他 String API ---\n// 重复三次 \'Abc\': AbcAbcAbc\n```\n\n\n好的，我们已经完成了 `8.4 语言及 API 增强` 中多个版本的特性学习。\n\n接下来，我们进入一个在 Java 10 中引入的、极大提升编码便利性的语法糖——**`8.5 局部变量类型推断 (var)`**。\n\n-----\n\n### 8.5 [语法现代化] [Java 10+] 局部变量类型推断 (var)\n\n> **本章导读**: 在 Java 10 之前，我们声明变量时必须在左侧明确写出其类型，即使在右侧的初始化语句中类型已经非常清晰。这种冗余在处理复杂的泛型类型时尤为明显。为了解决这一问题，Java 10 引入了 `var` 关键字，它允许编译器根据变量的初始化表达式**自动推断**出其类型。这并非引入了动态类型，`var` 声明的变量仍然是**静态类型**的，它仅仅是为我们省去了手动声明类型的麻烦，让代码更简洁。\n\n#### 8.5.1 `var` 的使用场景与优势\n\n`var` 主要的优势在于能够简化代码，尤其是在处理嵌套泛型等复杂类型时，能极大地提升代码的可读性。\n\n  * **`var` 常用场景速查表**\n\n| 场景 | 示例 | 优势 |\n| :--- | :--- | :--- |\n| **简化复杂类型声明** | `var userMap = new HashMap<String, List<User>>();` | **(核心优势)** 大幅减少冗余的模板代码，让代码更聚焦于变量名和业务逻辑。 |\n| **for-each 循环** | `for (var user : userList) { ... }` | 简化循环变量的声明。 |\n| **try-with-resources** | `try (var reader = new BufferedReader(...)) { ... }` | 简化资源变量的声明，使代码更紧凑。 |\n\n####### **场景1：简化复杂 Map 的声明**\n\n**背景**：我们需要创建一个 `Map`，其键是字符串，值是一个存储了 `User` 对象的 `List`。我们来对比一下使用 `var` 前后的代码差异。\n\n```java\npackage com.example;\n\nimport java.util.ArrayList;\nimport java.util.HashMap;\nimport java.util.List;\nimport java.util.Map;\n\nclass User {\n    private String name;\n    public User(String name) { this.name = name; }\n    @Override public String toString() { return "User{name=\'" + name + "\'}"; }\n}\n\npublic class Main {\n    public static void main(String[] args) {\n        // Java 10 之前的写法，类型声明非常冗长\n        Map<String, List<User>> userMapBefore = new HashMap<>();\n        userMapBefore.put("groupA", new ArrayList<>(List.of(new User("Alice"))));\n\n        // 使用 var 的写法，代码瞬间清爽\n        var userMapAfter = new HashMap<String, List<User>>();\n        userMapAfter.put("groupB", new ArrayList<>(List.of(new User("Bob"))));\n\n        // 编译器为 userMapAfter 推断出的类型仍然是 Map<String, List<User>>\n        System.out.println("userMapAfter 的类型是 Map: " + (userMapAfter instanceof Map));\n        System.out.println("userMapAfter 的内容: " + userMapAfter);\n        \n        System.out.println("--- for-each 循环中使用 var ---");\n        var userList = List.of(new User("Charlie"), new User("David"));\n        for (var user : userList) {\n            // user 的类型被正确推断为 User\n            System.out.println("循环中的用户: " + user);\n        }\n    }\n}\n// 输出:\n// userMapAfter 的类型是 Map: true\n// userMapAfter 的内容: {groupB=[User{name=\'Bob\'}]}\n// --- for-each 循环中使用 var ---\n// 循环中的用户: User{name=\'Charlie\'}\n// 循环中的用户: User{name=\'David\'}\n```\n\n> **小结**: `var` 让我们的代码不再被冗长的类型声明所淹没，尤其是在泛型和复杂的类名中。它鼓励我们为变量起一个更有意义的名字，因为类型的上下文已经由初始化表达式提供了。\n\n#### 8.5.2 [避坑指南] `var` 的使用限制\n\n`var` 虽然好用，但它不是“万金油”，只能在特定的上下文中使用。滥用或误用反而会降低代码的可读性。\n\n> **核心原则**: **所有 `var` 声明的变量，编译器都必须能在编译时、仅通过其初始化表达式就明确地推断出它的唯一类型。**\n\n以下是 `var` 的主要使用限制：\n\n1.  **必须有初始化器**: 不能只声明不赋值。\n\n      * 错误: `var name;`\n      * 正确: `var name = "Alice";`\n\n2.  **不能用 `null` 初始化**: `var` 无法从 `null` 推断出具体类型。\n\n      * 错误: `var data = null;`\n\n3.  **只能用于局部变量**: 这是最重要的一条规则。`var` **不能**用于：\n\n      * 类的成员变量（字段）\n      * 方法的参数\n      * 方法的返回类型\n\n4.  **Lambda 表达式和方法引用需要显式目标类型**:\n\n      * 错误: `var runnable = () -> System.out.println("Hello");`\n      * 原因: Lambda 表达式的类型依赖于其上下文的目标接口，仅凭 Lambda 自身无法推断。\n      * 正确: `Runnable runnable = () -> System.out.println("Hello");`\n\n5.  **小心泛型和菱形操作符 (`<>`)**:\n\n      * `var list = new ArrayList<>();` 这行代码是合法的，但 `list` 的类型会被推断为 `ArrayList<Object>`，这可能不是我们想要的。\n      * **最佳实践**: 如果想让 `var` 推断出正确的泛型类型，应在初始化表达式中明确指定它：`var stringList = new ArrayList<String>();`\n\n####### **场景2：`var` 的非法使用示例**\n\n**背景**：下面的代码展示了几种常见的 `var` 错误用法，帮助我们加深理解。\n\n```java\npackage com.example;\n\nimport java.util.function.Consumer;\n\npublic class Main {\n    // 错误1：不能用于成员变量\n    // private var instanceVariable = "error";\n\n    // 错误2：不能用于方法返回类型\n    // public var getMessage() { return "error"; }\n\n    // 错误3：不能用于方法参数\n    // public void printMessage(var message) { System.out.println(message); }\n\n    public static void main(String[] args) {\n        // ------------------ 合法用法 ------------------\n        var validMessage = "This is a valid local variable.";\n        System.out.println(validMessage);\n\n        // ------------------ 非法用法 ------------------\n        // 错误4：没有初始化器\n        // var name; \n\n        // 错误5：用 null 初始化\n        // var data = null;\n\n        // 错误6：Lambda 表达式需要显式目标类型\n        // var printer = s -> System.out.println(s);\n        \n        // 正确的 Lambda 用法\n        Consumer<String> printer = s -> System.out.println(s);\n        printer.accept("Lambda with explicit type is fine.");\n    }\n}\n// 输出:\n// This is a valid local variable.\n// Lambda with explicit type is fine.\n```\n\n> **小结**: `var` 是一个强大的便利工具，但绝不能滥用。在类型不明显或者会降低代码清晰度的场景下，我们仍应坚持使用明确的类型声明。**代码首先是写给人看的，其次才是给机器执行的。**\n\n\n\n好的，我们已经学习了 Java 10 引入的 `var` 关键字。\n\n接下来，我们将把目光投向 Java 16 和 17 中两个重要的“现代化 Java”基石，它们共同致力于增强 Java 的数据建模和领域建模能力。我们将把它们合并在 **`8.6 Record 类与 Sealed 类`** 这一节中进行讲解。\n\n-----\n\n### 8.6 [语法现代化] [Java 16/17+] Record 类与 Sealed 类\n\n> **本章导读**: 随着应用日益复杂，如何清晰、准确地对业务领域进行建模变得至关重要。`Record` 类 (JEP 395, Java 16) 和 `Sealed` 类 (JEP 409, Java 17) 是 Java 在这方面给出的强力回应。`Record` 类旨在用最少的代码创建不可变的数据载体，彻底终结了传统 POJO/DTO 的冗长样板代码。而 `Sealed` 类则允许我们创建可控的、封闭的继承体系。这两者结合，特别是在后续的模式匹配中，能够构建出既安全又极具表达力的领域模型。\n\n#### 8.6.1 Record 类：不可变数据载体 `[Java 16+]`\n\n在 `Record` 出现之前，创建一个简单的数据类（如一个二维坐标点），需要我们手动编写构造函数、`getter`、以及 `equals()`、`hashCode()`、`toString()` 方法。这些都是高度重复且容易出错的样板代码。`Record` 类就是为了解决这个问题而生的。\n\n> **核心思想**: **`Record` 是一种特殊的类，它专门用于充当不可变数据的“透明载体”。** 我们只需在声明时定义其“组件”，编译器就会自动为我们生成所有必要的成员。\n\n  * **`record` 自动生成内容速查表**\n\n| 当我们声明 `record Point(int x, int y) {}` 时，编译器自动生成: |\n| :--- |\n| `private final int x;` 和 `private final int y;` (私有 `final` 字段) |\n| 一个包含所有组件的公共构造器 `public Point(int x, int y)` |\n| 每个组件的公共“访问器”方法，如 `public int x()` 和 `public int y()` (注意：不是 `getX()`) |\n| 一个完整的 `public boolean equals(Object o)` 实现 |\n| 一个完整的 `public int hashCode()` 实现 |\n| 一个完整的 `public String toString()` 实现 |\n\n####### **场景1：使用 `record` 定义一个坐标点类**\n\n**背景**：我们需要一个 `Point` 类来表示二维坐标。我们来对比一下传统方式和 `record` 方式的巨大差异。\n\n```java\npackage com.example;\n\n// 这是使用 record 的方式，仅需一行！\nrecord Point(int x, int y) {\n    // 我们还可以在 record 内部添加静态方法或自定义的实例方法\n    public double distanceToOrigin() {\n        return Math.sqrt(x * x + y * y);\n    }\n}\n\n/*\n// 如果不用 record，我们需要手写这么多代码：\nclass PointBeforeRecord {\n    private final int x;\n    private final int y;\n\n    public PointBeforeRecord(int x, int y) { this.x = x; this.y = y; }\n    public int getX() { return x; }\n    public int getY() { return y; }\n    // ... 还需要手动实现 equals(), hashCode(), toString() ...\n}\n*/\n\npublic class Main {\n    public static void main(String[] args) {\n        // 创建 record 实例\n        Point p1 = new Point(3, 4);\n        Point p2 = new Point(3, 4);\n        Point p3 = new Point(5, 12);\n\n        // 1. 访问器方法\n        System.out.println("p1 的 x 坐标是: " + p1.x()); // 注意是 p1.x() 而不是 p1.getX()\n        System.out.println("p1 的 y 坐标是: " + p1.y());\n\n        // 2. toString()\n        System.out.println("p1 的字符串表示: " + p1);\n\n        // 3. equals()\n        System.out.println("p1 和 p2 是否相等? " + p1.equals(p2));\n        System.out.println("p1 和 p3 是否相等? " + p1.equals(p3));\n        \n        // 4. hashCode()\n        System.out.println("p1 的哈希码: " + p1.hashCode());\n        System.out.println("p2 的哈希码: " + p2.hashCode());\n\n        // 5. 调用自定义方法\n        System.out.printf("p3 到原点的距离是: %.2f\\n", p3.distanceToOrigin());\n    }\n}\n// 输出:\n// p1 的 x 坐标是: 3\n// p1 的 y 坐标是: 4\n// p1 的字符串表示: Point[x=3, y=4]\n// p1 和 p2 是否相等? true\n// p1 和 p3 是否相等? false\n// p1 的哈希码: 100\n// p2 的哈希码: 100\n// p3 到原点的距离是: 13.00\n```\n\n> **小结**: `Record` 类是创建**不可变数据聚合**的理想选择。它用最少的语法，提供了完整、正确的功能，极大地减少了样板代码，使我们的模型类定义更清晰、更可靠。\n\n#### 8.6.2 Sealed 类/接口：精准的继承控制 `[Java 17+]`\n\n在面向对象设计中，我们有时希望限制一个类或接口的继承体系，明确地指定“谁可以成为我的子类”。例如，一个 `Shape`（形状）接口，我们可能只希望它被 `Circle`（圆形）、`Square`（方形）和 `Triangle`（三角形）实现。\n\n`Sealed` 类/接口正是为此而生，它允许我们创建一个**封闭的、可控的继承层次结构**。\n\n> **核心语法**: 使用 `sealed` 关键字修饰类或接口，并使用 `permits` 关键字列出所有允许继承或实现的直接子类。\n\n  * **子类的规则**\n\n所有被 `permits` 关键字指定的子类，都必须遵循以下三条规则之一：\n\n1.  必须声明为 `final`，表示继承关系到此为止，不能再被继承。\n2.  必须声明为 `sealed`，表示它可以被继续继承，但同样需要用 `permits` 指定其子类。\n3.  必须声明为 `non-sealed`，表示“打开”继承限制，任何类都可以继承它，回归到普通的继承模式。\n\n####### **场景2：定义一个封闭的图形 (`Shape`) 继承体系**\n\n**背景**：我们正在设计一个绘图程序，需要定义一个 `Shape` 接口，并确保系统中只存在 `Circle` 和 `Square` 这两种形状。\n\n```java\npackage com.example;\n\n// 1. 定义一个 sealed 接口，只允许 Circle 和 Square 实现它\nsealed interface Shape permits Circle, Square {\n    double area(); // 面积\n}\n\n// 2. Circle 是 Shape 的一个子类，我们将其声明为 final，表示它不能再被继承\nfinal class Circle implements Shape {\n    private final double radius;\n    public Circle(double radius) { this.radius = radius; }\n    \n    @Override\n    public double area() {\n        return Math.PI * radius * radius;\n    }\n}\n\n// 3. Square 是 Shape 的另一个子类，我们将其声明为 non-sealed，\n//    表示任何其他类（如 SpecialSquare）都可以继承它。\nnon-sealed class Square implements Shape {\n    private final double side;\n    public Square(double side) { this.side = side; }\n    \n    @Override\n    public double area() {\n        return side * side;\n    }\n}\n\n// 由于 Square 是 non-sealed, 我们可以自由地继承它\nclass SpecialSquare extends Square {\n    public SpecialSquare(double side) { super(side); }\n    public void doSomethingSpecial() {\n        System.out.println("This is a special square!");\n    }\n}\n\npublic class Main {\n    public static void main(String[] args) {\n        Shape circle = new Circle(10.0);\n        Shape square = new Square(10.0);\n\n        System.out.printf("圆形的面积是: %.2f\\n", circle.area());\n        System.out.printf("方形的面积是: %.2f\\n", square.area());\n\n        SpecialSquare specialSquare = new SpecialSquare(5);\n        specialSquare.doSomethingSpecial();\n    }\n}\n// 输出:\n// 圆形的面积是: 314.16\n// 方形的面积是: 100.00\n// This is a special square!\n```\n\n> **小结**: `Sealed` 类和接口为我们的领域建模提供了更强的控制力。它使得类的继承关系从“开放”变为“有意识的设计”，这在与后面要讲的**模式匹配**结合时，威力会得到最大程度的体现，因为编译器能够知晓所有可能的子类型，从而检查 `switch` 语句是否**穷尽了所有情况**。\n\n\n\n-----\n\n#### `[高频面试题]` `Record` VS Lombok\n\n> **Q: `Record` 和 Lombok 是不是一样的？Lombok 的功能不是比 `Record` 更强吗？**\n>\n> **A:** 这是一个非常经典的问题。虽然 `Record` 和 Lombok 的 `@Data`/`@Value` 注解在**目标**上（即减少数据类的样板代码）有重叠，但它们在**本质、设计哲学和使用方式**上有着根本性的不同。\n>\n>   * **一句话总结**：Lombok 就是比Record牛逼\n>\n\n\n\n\n\n-----\n\n### 8.7 [语法现代化] [Java 16-21+] 模式匹配\n\n> **本章导读**: 在模式匹配出现之前，处理不同类型的对象通常需要遵循一个繁琐的固定流程：`instanceof` 类型检查 -\\> 强制类型转换 -\\> 使用转换后的变量。这个过程不仅冗长，而且容易出错（例如，忘记转换或转换错误）。**模式匹配**旨在彻底改变这一现状，它将类型测试、变量声明和条件提取合并为一步，让我们能以一种更具声明性的方式来表达“如果对象是这种模式，就这么做”。\n\n#### 8.7.1 `instanceof` 的模式匹配 `[Java 16+]`\n\n这是模式匹配最基础、最直接的应用，它首先对我们熟悉的 `instanceof` 运算符进行了增强。\n\n> **核心思想**: 在 `instanceof` 类型检查成功后，直接声明一个该类型的变量，省去手动强制转换的步骤。\n\n```java\n// 假设 obj 是一个 Object 类型的变量，可能包含 String, Integer 等\nObject obj = "hello world";\n\n// --- 传统写法 (Java 16 之前) ---\nif (obj instanceof String) {\n    String s = (String) obj; // 类型检查后，需要显式向下转型\n    System.out.println("传统方式打印大写: " + s.toUpperCase());\n}\n\n// --- instanceof 模式匹配写法 (Java 16+) ---\nif (obj instanceof String s) { // 类型检查和变量声明/绑定一步完成\n    System.out.println("模式匹配打印大写: " + s.toUpperCase()); // 直接使用 s，无需转型\n}\n```\n\n> **小结**: `instanceof` 模式匹配虽然只是一个小改动，但它为更强大的 `switch` 模式匹配奠定了基础，并培养了我们用模式的思维去替代传统类型判断的习惯。\n\n#### 8.7.2 `switch` 表达式与模式匹配 `[Java 21+]`\n\n这是模式匹配的“完全体”，它极大地增强了 `switch` 语句（和表达式）的能力，使其可以对任意类型的对象进行匹配，并应用更复杂的逻辑。\n\n  * **`switch` 模式匹配核心增强速查表**\n\n| 新特性 | 功能描述 |\n| :--- | :--- |\n| **类型模式 ** | `case` 标签可以直接匹配对象的类型，如 `case String s`。 |\n| **`case null`** | `switch` 现在可以直接处理 `null` 情况，无需在外部进行 `if (obj == null)` 判断。 |\n| **守护模式** | 使用 `when` 关键字为 `case` 增加一个额外的布尔条件，如 `case String s when s.length() > 5`。 |\n| **穷尽性检查** | **(安全保障)** 当对 `sealed` 类或枚举进行 `switch` 时，编译器会检查是否覆盖了所有可能的子类型，否则会报错。 |\n\n####### **场景2：使用 `switch` 模式匹配和 `sealed` 接口重构图形面积计算**\n\n**背景**：我们重用上一节定义的 `sealed interface Shape`，编写一个方法，使用 `switch` 模式匹配来计算不同形状的面积。\n\n```java\npackage com.example;\n\n// --- 复用上一节定义的 Shape 继承体系 ---\nsealed interface Shape permits Circle, Square {}\nfinal class Circle implements Shape {\n    final double radius;\n    public Circle(double radius) { this.radius = radius; }\n}\nnon-sealed class Square implements Shape {\n    final double side;\n    public Square(double side) { this.side = side; }\n}\n// -----------------------------------------\n\npublic class Main {\n    \n    public static double getArea(Shape shape) {\n        // 使用 switch 表达式进行模式匹配\n        return switch (shape) {\n            // 1. case null: 直接处理 null 输入\n            case null -> 0.0;\n            \n            // 2. 类型模式: 匹配 Circle 类型，并创建变量 c\n            case Circle c -> Math.PI * c.radius * c.radius;\n            \n            // 3. 守护模式: 匹配 Square 类型，但只有当边长大于0时才进入该分支\n            case Square s when s.side > 0 -> s.side * s.side;\n            \n            // 4. default 分支: 覆盖所有其他情况（例如，一个边长小于等于0的 Square）\n            // 如果不写 default，对于 sealed 接口，编译器会检查是否穷尽所有可能。\n            // 在本例中，因为有 \'when\' 条件，所以必须有 default。\n            default -> 0.0;\n        };\n    }\n\n    public static void main(String[] args) {\n        System.out.printf("圆形面积: %.2f\\n", getArea(new Circle(10)));\n        System.out.printf("方形面积: %.2f\\n", getArea(new Square(5)));\n        System.out.printf("null 图形面积: %.2f\\n", getArea(null));\n        System.out.printf("无效方形面积: %.2f\\n", getArea(new Square(-5)));\n    }\n}\n// 输出:\n// 圆形面积: 314.16\n// 方形面积: 25.00\n// null 图形面积: 0.00\n// 无效方形面积: 0.00\n```\n\n> **小结**: `switch` 模式匹配是 Java 语法现代化的一个巨大飞跃。它将类型判断、条件逻辑和安全性（通过穷尽性检查）完美结合，使得我们处理复杂、多态的业务逻辑时，代码能变得像查表一样清晰、直观和安全。'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#8-0-Java%E6%96%B0%E8%AF%AD%E6%B3%95%E6%80%BB%E7%BB%93"><span class="toc-number">1.</span> <span class="toc-text">8.0 Java新语法总结</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#8-1-%E6%A0%B8%E5%BF%83-Java-8-Lambda-%E8%A1%A8%E8%BE%BE%E5%BC%8F%E4%B8%8E%E6%96%B9%E6%B3%95%E5%BC%95%E7%94%A8"><span class="toc-number">1.1.</span> <span class="toc-text">8.1 [核心] [Java 8+] Lambda 表达式与方法引用</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-1-%E8%83%8C%E6%99%AF%EF%BC%9A%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E5%BC%95%E5%85%A5-Lambda"><span class="toc-number">1.1.1.</span> <span class="toc-text">8.1.1 背景：为什么要引入 Lambda</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-2-%E6%A0%B8%E5%BF%83%EF%BC%9ALambda-%E8%AF%AD%E6%B3%95%E4%B8%8E%E5%87%BD%E6%95%B0%E5%BC%8F%E6%8E%A5%E5%8F%A3"><span class="toc-number">1.1.2.</span> <span class="toc-text">8.1.2 核心：Lambda 语法与函数式接口</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-Lambda-%E8%A1%A8%E8%BE%BE%E5%BC%8F%E7%9A%84%E4%B8%89%E9%83%A8%E5%88%86%E6%9E%84%E6%88%90%EF%BC%9A-%E5%8F%82%E6%95%B0-%E6%96%B9%E6%B3%95%E4%BD%93-%E4%B8%80%E7%A7%8D%E6%9B%B4%E7%B4%A7%E5%87%91%E7%9A%84%E4%BB%A3%E7%A0%81%E8%A1%A8%E7%A4%BA%E6%B3%95"><span class="toc-number">1.1.2.0.1.</span> <span class="toc-text">1. Lambda 表达式的三部分构成：(参数) -&gt; {方法体} - 一种更紧凑的代码表示法</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E5%87%BD%E6%95%B0%E5%BC%8F%E6%8E%A5%E5%8F%A3-FunctionalInterface-Lambda-%E7%9A%84%E7%B1%BB%E5%9E%8B%E5%9F%BA%E7%A1%80"><span class="toc-number">1.1.2.0.2.</span> <span class="toc-text">2. 函数式接口 (@FunctionalInterface) - Lambda 的类型基础</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-3-%E8%BF%9B%E9%98%B6%EF%BC%9A%E6%96%B9%E6%B3%95%E5%BC%95%E7%94%A8%E7%9A%84%E5%9B%9B%E7%A7%8D%E7%B1%BB%E5%9E%8B"><span class="toc-number">1.1.3.</span> <span class="toc-text">8.1.3 进阶：方法引用的四种类型 (::)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-1-4-%E9%AB%98%E9%A2%91%E9%9D%A2%E8%AF%95%E9%A2%98-Lambda-%E4%B8%8E%E5%8C%BF%E5%90%8D%E5%86%85%E9%83%A8%E7%B1%BB%E7%9A%84%E5%8C%BA%E5%88%AB"><span class="toc-number">1.1.4.</span> <span class="toc-text">8.1.4 [高频面试题] Lambda 与匿名内部类的区别</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-2-%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86%E9%9D%A9%E5%91%BD-Java-8-Stream-API"><span class="toc-number">1.2.</span> <span class="toc-text">8.2 [数据处理革命] [Java 8+] Stream API</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-1-%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%EF%BC%9A%E6%B5%81%E7%9A%84%E5%88%9B%E5%BB%BA%E4%B8%8E%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F"><span class="toc-number">1.2.1.</span> <span class="toc-text">8.2.1 核心概念：流的创建与生命周期</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-2-%E4%B8%AD%E9%97%B4%E6%93%8D%E4%BD%9C%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.2.2.</span> <span class="toc-text">8.2.2 中间操作详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E7%AD%9B%E9%80%89%E4%B8%8E%E5%88%87%E7%89%87-%E4%BB%8E%E6%B5%81%E4%B8%AD%E9%80%89%E5%8F%96%E6%88%91%E4%BB%AC%E9%9C%80%E8%A6%81%E7%9A%84%E5%85%83%E7%B4%A0"><span class="toc-number">1.2.2.0.1.</span> <span class="toc-text">1. 筛选与切片 - 从流中选取我们需要的元素</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E6%98%A0%E5%B0%84-%E5%B0%86%E6%B5%81%E4%B8%AD%E5%85%83%E7%B4%A0%E8%BD%AC%E6%8D%A2%E4%B8%BA%E5%85%B6%E4%BB%96%E5%BD%A2%E5%BC%8F%E6%88%96%E6%8F%90%E5%8F%96%E4%BF%A1%E6%81%AF"><span class="toc-number">1.2.2.0.2.</span> <span class="toc-text">2. 映射 - 将流中元素转换为其他形式或提取信息</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E6%8E%92%E5%BA%8F-%E5%AF%B9%E6%B5%81%E4%B8%AD%E5%85%83%E7%B4%A0%E8%BF%9B%E8%A1%8C%E6%8E%92%E5%BA%8F"><span class="toc-number">1.2.2.0.3.</span> <span class="toc-text">3. 排序 - 对流中元素进行排序</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-2-3-%E7%BB%88%E7%AB%AF%E6%93%8D%E4%BD%9C%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.2.3.</span> <span class="toc-text">8.2.3 终端操作详解</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E5%8C%B9%E9%85%8D%E4%B8%8E%E6%9F%A5%E6%89%BE-%E6%A3%80%E6%9F%A5%E6%B5%81%E4%B8%AD%E5%85%83%E7%B4%A0%E6%98%AF%E5%90%A6%E6%BB%A1%E8%B6%B3%E7%89%B9%E5%AE%9A%E6%9D%A1%E4%BB%B6"><span class="toc-number">1.2.3.0.1.</span> <span class="toc-text">1. 匹配与查找 - 检查流中元素是否满足特定条件</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E8%A7%84%E7%BA%A6-Reduction-%E5%B0%86%E6%B5%81%E4%B8%AD%E6%89%80%E6%9C%89%E5%85%83%E7%B4%A0%E8%AE%A1%E7%AE%97%EF%BC%8C%E5%BE%97%E5%88%B0%E4%B8%80%E4%B8%AA%E5%80%BC"><span class="toc-number">1.2.3.0.2.</span> <span class="toc-text">2. 规约 (Reduction) - 将流中所有元素计算，得到一个值</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E6%94%B6%E9%9B%86-Collect-%E5%B0%86%E6%B5%81%E4%B8%AD%E5%85%83%E7%B4%A0%E8%BD%AC%E6%8D%A2%E6%88%90%E9%9B%86%E5%90%88%E3%80%81Map%E6%88%96%E5%85%B6%E4%BB%96%E5%A4%8D%E6%9D%82%E5%AF%B9%E8%B1%A1"><span class="toc-number">1.2.3.0.3.</span> <span class="toc-text">3. 收集 (Collect) - 将流中元素转换成集合、Map或其他复杂对象</span></a></li></ol></li></ol><li class="toc-item toc-level-3"><a class="toc-link" href="#8-3-%E7%A9%BA%E6%8C%87%E9%92%88%E7%BB%88%E7%BB%93%E8%80%85-Java-8-Optional-%E5%AE%B9%E5%99%A8%E7%B1%BB"><span class="toc-number">1.3.</span> <span class="toc-text">8.3 [空指针终结者] [Java 8+] Optional 容器类</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-3-1-%E8%AE%BE%E8%AE%A1%E5%93%B2%E5%AD%A6%EF%BC%9A%E4%B8%BA%E4%BB%80%E4%B9%88%E9%9C%80%E8%A6%81-Optional"><span class="toc-number">1.3.1.</span> <span class="toc-text">8.3.1 设计哲学：为什么需要 Optional</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-3-2-%E6%A0%B8%E5%BF%83%E6%96%B9%E6%B3%95%E5%88%86%E7%B1%BB%E4%BD%BF%E7%94%A8"><span class="toc-number">1.3.2.</span> <span class="toc-text">8.3.2 核心方法分类使用</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E5%88%9B%E5%BB%BA-Optional-%E5%AF%B9%E8%B1%A1-%E5%8C%85%E8%A3%85%E4%BD%A0%E7%9A%84%E5%80%BC"><span class="toc-number">1.3.2.0.1.</span> <span class="toc-text">1. 创建 Optional 对象 - 包装你的值</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E8%8E%B7%E5%8F%96%E5%80%BC%EF%BC%88%E5%B8%A6%E9%BB%98%E8%AE%A4%E5%A4%84%E7%90%86%EF%BC%89-%E5%AE%89%E5%85%A8%E5%9C%B0%E5%8F%96%E5%87%BA%E7%BB%93%E6%9E%9C"><span class="toc-number">1.3.2.0.2.</span> <span class="toc-text">2. 获取值（带默认处理） - 安全地取出结果</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#3-%E5%80%BC%E7%9A%84%E8%BD%AC%E6%8D%A2%E4%B8%8E%E8%BF%87%E6%BB%A4-%E4%BB%A5%E5%87%BD%E6%95%B0%E5%BC%8F%E9%A3%8E%E6%A0%BC%E5%A4%84%E7%90%86%E5%80%BC"><span class="toc-number">1.3.2.0.3.</span> <span class="toc-text">3. 值的转换与过滤 - 以函数式风格处理值</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#4-%E5%88%A4%E6%96%AD%E4%B8%8E%E6%B6%88%E8%B4%B9-%E5%9C%A8%E5%80%BC%E5%AD%98%E5%9C%A8%E6%97%B6%E6%89%A7%E8%A1%8C%E6%93%8D%E4%BD%9C"><span class="toc-number">1.3.2.0.4.</span> <span class="toc-text">4. 判断与消费 - 在值存在时执行操作</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-4-%E8%AF%AD%E8%A8%80%E5%A2%9E%E5%BC%BA-Java-8-%E8%AF%AD%E8%A8%80%E5%8F%8A-API-%E5%A2%9E%E5%BC%BA"><span class="toc-number">1.4.</span> <span class="toc-text">8.4 [语言增强] [Java 8+] 语言及 API 增强</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-4-1-%E6%8E%A5%E5%8F%A3%E7%9A%84%E6%BC%94%E8%BF%9B-Java-8"><span class="toc-number">1.4.1.</span> <span class="toc-text">8.4.1 接口的演进 [Java 8]</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#1-%E9%BB%98%E8%AE%A4%E6%96%B9%E6%B3%95-default-%E5%9C%A8%E4%B8%8D%E7%A0%B4%E5%9D%8F%E5%AE%9E%E7%8E%B0%E7%9A%84%E5%89%8D%E6%8F%90%E4%B8%8B%E4%B8%BA%E6%8E%A5%E5%8F%A3%E6%B7%BB%E5%8A%A0%E6%96%B0%E5%8A%9F%E8%83%BD"><span class="toc-number">1.4.1.0.1.</span> <span class="toc-text">1. 默认方法 (default) - 在不破坏实现的前提下为接口添加新功能</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#2-%E9%9D%99%E6%80%81%E6%96%B9%E6%B3%95-static-%E5%9C%A8%E6%8E%A5%E5%8F%A3%E4%B8%AD%E5%AE%9A%E4%B9%89%E5%B7%A5%E5%85%B7%E6%96%B9%E6%B3%95"><span class="toc-number">1.4.1.0.2.</span> <span class="toc-text">2. 静态方法 (static) - 在接口中定义工具方法</span></a></li></ol></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-4-2-%E9%9B%86%E5%90%88%E5%B7%A5%E5%8E%82%E6%96%B9%E6%B3%95-Java-9"><span class="toc-number">1.4.2.</span> <span class="toc-text">8.4.2 集合工厂方法 [Java 9+]</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-4-3-%E5%B8%B8%E7%94%A8-API-%E5%A2%9E%E5%BC%BA-Java-11"><span class="toc-number">1.4.3.</span> <span class="toc-text">8.4.3 常用 API 增强 [Java 11+]</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-5-%E8%AF%AD%E6%B3%95%E7%8E%B0%E4%BB%A3%E5%8C%96-Java-10-%E5%B1%80%E9%83%A8%E5%8F%98%E9%87%8F%E7%B1%BB%E5%9E%8B%E6%8E%A8%E6%96%AD-var"><span class="toc-number">1.5.</span> <span class="toc-text">8.5 [语法现代化] [Java 10+] 局部变量类型推断 (var)</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-5-1-var-%E7%9A%84%E4%BD%BF%E7%94%A8%E5%9C%BA%E6%99%AF%E4%B8%8E%E4%BC%98%E5%8A%BF"><span class="toc-number">1.5.1.</span> <span class="toc-text">8.5.1 var 的使用场景与优势</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-5-2-%E9%81%BF%E5%9D%91%E6%8C%87%E5%8D%97-var-%E7%9A%84%E4%BD%BF%E7%94%A8%E9%99%90%E5%88%B6"><span class="toc-number">1.5.2.</span> <span class="toc-text">8.5.2 [避坑指南] var 的使用限制</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-6-%E8%AF%AD%E6%B3%95%E7%8E%B0%E4%BB%A3%E5%8C%96-Java-16-17-Record-%E7%B1%BB%E4%B8%8E-Sealed-%E7%B1%BB"><span class="toc-number">1.6.</span> <span class="toc-text">8.6 [语法现代化] [Java 16/17+] Record 类与 Sealed 类</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-6-1-Record-%E7%B1%BB%EF%BC%9A%E4%B8%8D%E5%8F%AF%E5%8F%98%E6%95%B0%E6%8D%AE%E8%BD%BD%E4%BD%93-Java-16"><span class="toc-number">1.6.1.</span> <span class="toc-text">8.6.1 Record 类：不可变数据载体 [Java 16+]</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-6-2-Sealed-%E7%B1%BB-%E6%8E%A5%E5%8F%A3%EF%BC%9A%E7%B2%BE%E5%87%86%E7%9A%84%E7%BB%A7%E6%89%BF%E6%8E%A7%E5%88%B6-Java-17"><span class="toc-number">1.6.2.</span> <span class="toc-text">8.6.2 Sealed 类/接口：精准的继承控制 [Java 17+]</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E9%AB%98%E9%A2%91%E9%9D%A2%E8%AF%95%E9%A2%98-Record-VS-Lombok"><span class="toc-number">1.6.3.</span> <span class="toc-text">[高频面试题] Record VS Lombok</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#8-7-%E8%AF%AD%E6%B3%95%E7%8E%B0%E4%BB%A3%E5%8C%96-Java-16-21-%E6%A8%A1%E5%BC%8F%E5%8C%B9%E9%85%8D"><span class="toc-number">1.7.</span> <span class="toc-text">8.7 [语法现代化] [Java 16-21+] 模式匹配</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#8-7-1-instanceof-%E7%9A%84%E6%A8%A1%E5%BC%8F%E5%8C%B9%E9%85%8D-Java-16"><span class="toc-number">1.7.1.</span> <span class="toc-text">8.7.1 instanceof 的模式匹配 [Java 16+]</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#8-7-2-switch-%E8%A1%A8%E8%BE%BE%E5%BC%8F%E4%B8%8E%E6%A8%A1%E5%BC%8F%E5%8C%B9%E9%85%8D-Java-21"><span class="toc-number">1.7.2.</span> <span class="toc-text">8.7.2 switch 表达式与模式匹配 [Java 21+]</span></a></li></ol></li></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>