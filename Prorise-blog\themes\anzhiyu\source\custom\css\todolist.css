/* Todolist 现代化扁平设计 - v4.0 */

/* ==========================================================================
   基础布局与容器
   ========================================================================== */
   body[data-type="todolist"] #web_bg {
    background: var(--anzhiyu-background);
  }
  
  body[data-type="todolist"] #page {
    border: 0;
    box-shadow: none !important;
    padding: 0 !important;
    background: transparent !important;
  }
  
  body[data-type="todolist"] #page .page-title {
    display: none;
  }
  
  #todolist-box {
    margin: 0 auto;
    max-width: 1200px;
    padding: 0 15px;
  }
  
  /* 顶部内容区 */
  .author-content.todolist {
    margin-bottom: 20px;
  }
  .author-content .tips {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--anzhiyu-fontcolor);
    opacity: 0.75;
  }
  .author-content .tips i {
    color: var(--anzhiyu-theme);
    font-size: 0.8rem;
  }
  
  /* ==========================================================================
     筛选器
     ========================================================================== */
  #todolist-filter {
    margin: 20px 0;
    padding: 15px;
    background: var(--anzhiyu-card-bg);
    border-radius: 12px;
    box-shadow: var(--anzhiyu-shadow-border);
  }
  
  .filter-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 12px;
    color: var(--anzhiyu-fontcolor);
    font-weight: 600;
  }
  
  .filter-title i {
    color: var(--anzhiyu-theme);
  }
  
  .filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .filter-btn {
    background: var(--anzhiyu-card-bg);
    border: 1px solid var(--anzhiyu-card-border);
    color: var(--anzhiyu-fontcolor);
    border-radius: 8px; /* 更方正的圆角 */
    padding: 6px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
  }
  
  .filter-btn:hover {
    transform: translateY(-2px);
    border-color: var(--anzhiyu-theme);
    background: var(--anzhiyu-card-bg);
  }
  
  .filter-btn.active {
    background: var(--anzhiyu-theme);
    color: white;
    border-color: var(--anzhiyu-theme);
  }
  
  /* ==========================================================================
     主体布局与卡片 (核心重构区域)
     ========================================================================== */
  #todolist-main {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
  }
  
  /* 卡片容器 - 简化 */
  .todolist-item {
    background: var(--anzhiyu-card-bg);
    border-radius: 12px;
    padding: 20px 25px;
    box-shadow: var(--anzhiyu-shadow-border);
    transition: all 0.3s ease;
    animation: fadeIn 0.5s ease forwards;
  }
  
  .todolist-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--anzhiyu-shadow-main);
  }
  
  /* 标题样式 - 简化 */
  h3.todolist-title {
    margin: 0 0 1rem 0 !important;
    padding-bottom: 0.8rem;
    font-size: 1.2rem;
    color: var(--anzhiyu-fontcolor);
    border-bottom: 1px solid var(--anzhiyu-card-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  /* 隐藏原标题图标 */
  h3.todolist-title i {
    display: none;
  }
  
  /* 任务计数器 */
  .task-count {
    background: var(--anzhiyu-secondbg);
    color: var(--anzhiyu-fontcolor);
    padding: 2px 8px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
  }
  
  /* 移除进度条 */
  .progress-bar {
    display: none;
  }
  
  /* ==========================================================================
     清单列表项 (全新 Checkbox 样式)
     ========================================================================== */
  .todolist-ul {
    margin: 0;
    padding: 0;
  }
  
  .todolist-ul li {
    list-style: none;
    padding: 10px 0;
    margin: 0;
    display: grid;
    grid-template-columns: 20px auto; /* 复选框 | 文本 */
    align-items: center;
    gap: 15px;
    cursor: pointer;
    position: relative;
  }
  
  /* 隐藏原列表图标 */
  .todolist-ul li i {
    display: none;
  }
  
  /* 任务文本 */
  .todolist-ul li span {
    color: var(--anzhiyu-fontcolor);
    position: relative;
    transition: color 0.3s ease;
    line-height: 1.5;
  }
  
  /* 使用 ::before 伪元素创建自定义复选框的“框” */
  .todolist-ul li::before {
    content: "";
    width: 16px;
    height: 16px;
    border: 2px solid var(--anzhiyu-fontcolor);
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
  }
  
  /* 使用 ::after 伪元素创建自定义复选框的“勾” */
  .todolist-ul li::after {
    content: "";
    position: absolute;
    left: 6px;
    top: 13px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  /* ==========================================================================
     已完成状态 (交互动画)
     ========================================================================== */
  .todolist-ul li.todolist-li-done span {
    color: var(--anzhiyu-secondtext);
  }
  
  /* 复选框（框）的完成状态 */
  .todolist-ul li.todolist-li-done::before {
    background: var(--anzhiyu-blue);
    border-color: var(--anzhiyu-blue);
    animation: check-box 0.3s ease;
  }
  
  /* 复选框（勾）的完成状态 */
  .todolist-ul li.todolist-li-done::after {
    opacity: 1;
  }
  
  /* 文本删除线动画 */
  .todolist-ul li span::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--anzhiyu-secondtext);
    transition: width 0.4s ease;
  }
  
  .todolist-ul li.todolist-li-done span::before {
    width: 100%;
  }
  
  
  /* ==========================================================================
     分页与响应式
     ========================================================================== */
  #todolist-pagination {
    display: flex;
    justify-content: center;
    margin: 30px 0;
  }
  .pagination-container {
    display: flex;
    align-items: center;
    gap: 5px;
  }
  .page-btn, .page-number {
    min-width: 36px;
    height: 36px;
    border: 1px solid var(--anzhiyu-card-border);
    background: var(--anzhiyu-card-bg);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--anzhiyu-fontcolor);
  }
  .page-btn:hover:not(:disabled), .page-number:hover:not(.active) {
    border-color: var(--anzhiyu-theme);
    color: var(--anzhiyu-theme);
  }
  .page-number.active {
    background: var(--anzhiyu-theme);
    color: white;
    border-color: var(--anzhiyu-theme);
  }
  .page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  #page-numbers {
    display: flex;
    gap: 5px;
  }
  @media screen and (max-width: 768px) {
    #todolist-box {
      margin: 0;
      padding: 0 10px;
    }
    .todolist-item {
      padding: 15px;
    }
    h3.todolist-title {
      font-size: 1.1rem;
    }
    .filter-buttons {
      overflow-x: auto;
      padding-bottom: 5px;
      flex-wrap: nowrap;
    }
  }
  
  /* ==========================================================================
     动画定义
     ========================================================================== */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  @keyframes check-box {
    0% { transform: scale(0.8); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }