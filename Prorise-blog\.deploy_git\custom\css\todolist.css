/* build time:Sun Jul 27 2025 21:07:30 GMT+0800 (中国标准时间)*/
body[data-type=todolist] #web_bg{background:var(--anzhiyu-background)}body[data-type=todolist] #page{border:0;box-shadow:none!important;padding:0!important;background:0 0!important}body[data-type=todolist] #page .page-title{display:none}#todolist-box{margin:0 auto;max-width:1200px;padding:0 15px}.author-content.todolist{margin-bottom:20px}.author-content .tips{display:flex;align-items:center;gap:.5rem;color:var(--anzhiyu-fontcolor);opacity:.75}.author-content .tips i{color:var(--anzhiyu-theme);font-size:.8rem}#todolist-filter{margin:20px 0;padding:15px;background:var(--anzhiyu-card-bg);border-radius:12px;box-shadow:var(--anzhiyu-shadow-border)}.filter-title{display:flex;align-items:center;gap:.5rem;margin-bottom:12px;color:var(--anzhiyu-fontcolor);font-weight:600}.filter-title i{color:var(--anzhiyu-theme)}.filter-buttons{display:flex;flex-wrap:wrap;gap:8px}.filter-btn{background:var(--anzhiyu-card-bg);border:1px solid var(--anzhiyu-card-border);color:var(--anzhiyu-fontcolor);border-radius:8px;padding:6px 15px;cursor:pointer;transition:all .3s ease;font-size:.9rem;pointer-events:auto;user-select:none}.filter-btn:hover{transform:translateY(-2px);border-color:var(--anzhiyu-theme);background:var(--anzhiyu-card-bg)}.filter-btn.active{background:var(--anzhiyu-theme);color:#fff;border-color:var(--anzhiyu-theme)}#todolist-main{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:20px;margin:20px 0}.todolist-item{background:var(--anzhiyu-card-bg);border-radius:12px;padding:20px 25px;box-shadow:var(--anzhiyu-shadow-border);transition:all .3s ease;animation:fadeIn .5s ease forwards}.todolist-item:hover{transform:translateY(-5px);box-shadow:var(--anzhiyu-shadow-main)}h3.todolist-title{margin:0 0 1rem 0!important;padding-bottom:.8rem;font-size:1.2rem;color:var(--anzhiyu-fontcolor);border-bottom:1px solid var(--anzhiyu-card-border);display:flex;align-items:center;justify-content:space-between}h3.todolist-title i{display:none}.task-count{background:var(--anzhiyu-secondbg);color:var(--anzhiyu-fontcolor);padding:2px 8px;border-radius:6px;font-size:.8rem;font-weight:500}.progress-bar{display:none}.todolist-ul{margin:0;padding:0}.todolist-ul li{list-style:none;padding:10px 0;margin:0;display:grid;grid-template-columns:20px auto;align-items:center;gap:15px;cursor:pointer;position:relative;pointer-events:auto;user-select:none}.todolist-ul li i{display:none}.todolist-ul li span{color:var(--anzhiyu-fontcolor);position:relative;transition:color .3s ease;line-height:1.5}.todolist-ul li::before{content:"";width:16px;height:16px;border:2px solid var(--anzhiyu-fontcolor);border-radius:4px;transition:all .3s ease;position:relative}.todolist-ul li::after{content:"";position:absolute;left:6px;top:13px;width:4px;height:8px;border:solid #fff;border-width:0 2px 2px 0;transform:rotate(45deg);opacity:0;transition:opacity .3s ease}.todolist-ul li.todolist-li-done span{color:var(--anzhiyu-secondtext)}.todolist-ul li.todolist-li-done::before{background:var(--anzhiyu-blue);border-color:var(--anzhiyu-blue);animation:check-box .3s ease}.todolist-ul li.todolist-li-done::after{opacity:1}.todolist-ul li span::before{content:"";position:absolute;top:50%;left:0;width:0;height:2px;background:var(--anzhiyu-secondtext);transition:width .4s ease}.todolist-ul li.todolist-li-done span::before{width:100%}#todolist-pagination{display:flex;justify-content:center;margin:30px 0}.pagination-container{display:flex;align-items:center;gap:5px}.page-btn,.page-number{min-width:36px;height:36px;border:1px solid var(--anzhiyu-card-border);background:var(--anzhiyu-card-bg);border-radius:4px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;color:var(--anzhiyu-fontcolor)}.page-btn:hover:not(:disabled),.page-number:hover:not(.active){border-color:var(--anzhiyu-theme);color:var(--anzhiyu-theme)}.page-number.active{background:var(--anzhiyu-theme);color:#fff;border-color:var(--anzhiyu-theme)}.page-btn:disabled{opacity:.5;cursor:not-allowed}#page-numbers{display:flex;gap:5px}@media screen and (max-width:768px){#todolist-box{margin:0;padding:0 10px}.todolist-item{padding:15px}h3.todolist-title{font-size:1.1rem}.filter-buttons{overflow-x:auto;padding-bottom:5px;flex-wrap:nowrap}}@keyframes fadeIn{from{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes check-box{0%{transform:scale(.8)}50%{transform:scale(1.1)}100%{transform:scale(1)}}
/* rebuild by neat */