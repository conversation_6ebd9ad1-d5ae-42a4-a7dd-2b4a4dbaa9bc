<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>关于我 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="关于我"><meta name="application-name" content="关于我"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="website"><meta property="og:title" content="关于我"><meta property="og:url" content="https://prorise666.site/about/index.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="心路历程 回望这段人生，与其称之为履历，不如视作一场漫长的自我救赎。它始于一片灰色的浓雾，日子沉闷如南方绵长的梅雨，未来则是一条被预设却黯淡无光的轨道。我曾是雾中徘徊的游魂，以为世界的尽头不过小城里那条闭眼也能走完的街道，人生的剧本也早已写好：毕业、工作、重复，循环往复。  我是一个来自广东潮州的中"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://bu.dusays.com/2025/06/15/684e5ba422dc2.png?_r_=383c8b31-5e7e-ba26-ccae-44e67c88494a"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://bu.dusays.com/2025/06/15/684e5ba422dc2.png?_r_=383c8b31-5e7e-ba26-ccae-44e67c88494a"><meta name="description" content="心路历程 回望这段人生，与其称之为履历，不如视作一场漫长的自我救赎。它始于一片灰色的浓雾，日子沉闷如南方绵长的梅雨，未来则是一条被预设却黯淡无光的轨道。我曾是雾中徘徊的游魂，以为世界的尽头不过小城里那条闭眼也能走完的街道，人生的剧本也早已写好：毕业、工作、重复，循环往复。  我是一个来自广东潮州的中"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/about/"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"关于我",postAI:"",pageFillDescription:"心路历程心路历程回望这段人生与其称之为履历不如视作一场漫长的自我救赎它始于一片灰色的浓雾日子沉闷如南方绵长的梅雨未来则是一条被预设却黯淡无光的轨道我曾是雾中徘徊的游魂以为世界的尽头不过小城里那条闭眼也能走完的街道人生的剧本也早已写好毕业工作重复循环往复我是一个来自广东潮州的中职生无可否认我曾是浑噩度日的一员初中时我体会不到教育的意义也感受不到亲情的温度父母仿佛永远缺席唯有年迈的爷爷奶奶是他们陪伴我走过了整个童年那时我的志向渺小得可怜觉得读完初中就出去打工便好反正父亲母亲也是如此在这个小城镇里跨越阶层似乎是个无人能及的神话当同龄人畅谈理想时我渐渐退到人群最后将自己沉溺在虚拟的网络世界也落得一身病痛初中的我是个满脸痘痕体重一百八十斤的胖子在同龄人眼中无异于一个遭人嘲笑的哥布林无论是学业技能还是运动我都沾不上边跑操请假生病请假体育课请假这些事对我来说习以为常家里人对此也漠不关心只是淡淡地说你自己想做什么都行不出所料我来到了职校职校的生活是命运惯性的无情延续周遭的一切似乎都在不断加深我的平庸感在中职学校能学到的东西屈指可数课程内容常让我觉得过于浅显诸如如何使用百度如何打字之类的课堂一节课四十分钟显得格外漫长那段日子上课时玩手机盼着下课下课后回宿舍倒头就睡日复一日常听人抱怨家人管束太多太多而我却无比渴望有人能真正理理我能注意到那个蜷缩在阴暗角落里的自己然而恰恰是在这片看似贫瘠的土壤里一颗名为不甘的种子悄然破土我开始笨拙地尝试改变在宿舍的角落我举起那几公斤重的哑铃动作生涩而笨拙忍受着室友们或好奇或不解的目光每个夜晚在宿舍的喧哗吵闹中甚至在深夜突如其来的灯光秀里我都只能默不作声相比起来我才是那个格格不入的人这样的日子一直持续到中职二年级的尾声学校开设了升大班也就是面向考大专的升学班那时的我依然懵懂迷茫只是看到朋友们都去了便也抱着试试看的心态报了名结果显而易见这个所谓的升大班几乎没有门槛只要你愿意就能进我被分到了第七班的第十一号命运的轨迹似乎就在那一刻悄然拐向了另一条岔路即便在这里也远没有普通高中的学习氛围但老师们都格外尽责生平第一次我体会到晚自习的教室里原来真的有人在学习尽管人数寥寥看着他们专注的身影一股莫名的冲动驱使我想要加入可我的基础太差了在很多人眼中如同常识的乘除法因式分解甚至简单的英语单词我都认不全一次课间身后的一位女孩轻轻点了点我的背问我是否知道某道数学题怎么解我只能尴尬地回答不会就是这样一个瞬间看着她转向班里那些厉害的同学并获得解答时一个念头在我心底扎下了根如果我会就好了哪怕仅仅是一道关于集合的基础题那个假期带着一种近乎耻辱的决心我打开电脑搜索四年级乘除法中职数学基础小学英语单词对于一个十六岁即将十七岁的人来说这无疑是一种难言的羞耻但我必须尝试在那段时间里我加上了那个女孩的微信以请教学习为名我甚至问出了一个倒着的三角形为什么它的底在上边上边不应该叫高吗这样暴露智商的问题出乎意料的是她依然耐心地为我讲解从那一刻起我开始一点一滴地戒掉很多东西沉迷的游戏放纵的饮食以及那个浑噩度日的自己除了必要的吃饭时间我几乎从未停下每一个放学的午后空旷的教室里仿佛只剩下我一个人我爱极了这种感觉在无人注视无人竞争的寂静里默默地积蓄力量我要跨过去我要走进那个世界不是为了任何人而是为了给自己一场彻底的新生于是我的朝圣之旅正式启程美好的时间总是短暂的渐渐地我们的关系从她指导我变成了共同讨论甚至偶尔我竟能扮演起指导者的角色我敏锐地察觉到了这种变化内心无数次警醒能力的鸿沟依然真实存在然而她看我的眼神已然不同也许是我故态复萌又戴上了虚伪的面具的确在相当长的一段时间里我误以为自己真的掌握了许多知识每逢小考我就翻看笔记坚信只要能在最终的大考派上用场就好我一直在欺骗自己在此期间我甚至开始梦想我们起点虽低但若能一同努力或许可以考入同一所大学我试过鼓起勇气去追求这份微光遗憾的是我失败了我们的关系急转直下从朋友到同学再到如今的音讯全无还记得毕业聚会前她的宿舍五人关系已势同水火聚会自然无人出席得知消息班级群里充斥着这样的声音毕业会不来大家筹钱准备的活动一点同学情谊都没有了太自私了吧说我自欺欺人也好说我只能做到这样也罢我交了聚会费但最终没有去参加那天下午放学大家热热闹闹地准备着聚会我却看见她和几位舍友沉默地结伴走向宿舍楼我独自背着书包回宿舍那一刻我觉得自己的缺席是值得的只是无法苟同那种随波逐流的合群罢了聚会前的那个下午她主动来找我问问题我凭着半桶水的知识与她交流那是我记忆中为数不多感到真正开心的午后时光之后便是紧锣密鼓的备考冲刺临考前十天她和舍友的关系依旧紧张四位舍友甚至决定搬回家住等到考试当天再返校独留她一人在宿舍于是她也会常常待在教室对我来说下午放学后的教室终于不再只有我孤身一人我们的关系其实并不算好就在那年四月我曾试图向她袒露心迹表达我对她那份能在淤泥中奋力绽放的毅力的欣赏可我深知自己还远远不够尽管那时已成功减重至一百二十多斤连初中老同学见了都惊呼真是大变样啊完全不像你了但我始终是我那个内核未曾改变的我在那最后的十天里她主动找到我希望能一起复习备考或许是我在模拟考中作弊的侥幸给了她错觉哈哈但我终究还是靠着那半桶水的知识陪她渡过了这至关重要的十天那十天里一切都仿佛在朝着光明的方向迈进走进真正的考场时我带着前所未有的自信一扫模拟考时的紧张第一场语文考试结束我甚至押中了作文题目欣喜若狂地第一时间与她分享然而她的状态却截然不同她的作文没能写完尽管如此我仍尽力安慰她鼓励她调整心态迎接下一场考试那一晚我没有询问她第二天的考试情况当一切尘埃落定她整个人已然崩溃朋友圈的签名整个人的状态后来才听说考试前一晚她因抱怨舍友打呼噜竟被狠狠责骂了很久我能做的唯有苍白的安慰却收效甚微我们之间的关系远未达到能共情的深度一切不过是我单方面的徒劳输出放学铃响了考试结束了那个曾满怀梦想的小孩也死在了自我欺骗的路上带着他未曾实现的憧憬永远地离开了如今我依然习惯独坐到深夜当世界陷入万籁俱寂耳畔不再有游戏的喧嚣唯有键盘敲击的清脆声响那是我与这个世界对话的方式是我与孤独和解的独特旋律每一次按下敲下的既是代码的指令也是我人生崭新的下一行我不再追问学习是否真有出路因为行走在这条路上本身便是我的最终归宿",isPost:!1,isHome:!1,isHighlightShrink:!1,isToc:!1,postUpdate:"2025-06-13 21:08:59",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="page" id="body-wrap"><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout hide-aside" id="content-inner"><div id="page"><h1 class="page-title">关于我</h1><div id="about-page"><div class="author-box"><div class="author-tag-left"><span class="author-tag">😉 全栈开发工程师</span><span class="author-tag">🤝 分享与热心帮助</span><span class="author-tag">🏠 前端架构设计师</span><span class="author-tag">🔨 技术栈之多面手</span></div><div class="author-img"><img class="no-lightbox" src="https://bu.dusays.com/2025/06/16/684f745b722d7.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;" alt="avatar"></div><div class="author-tag-right"><span class="author-tag">专修交互与设计🤝</span><span class="author-tag">脚踏实地行动派🏃</span><span class="author-tag">团队小组发动机🧱</span><span class="author-tag">执行力属性爆棚💢</span></div></div><p class="p center logo large">关于我</p><p class="p center small">代码构建世界，思想驱动未来✨</p><div class="author-content"><div class="author-content-item myInfoAndSayHello"><div class="title1">你好，很高兴认识你👋</div><div class="title2">我叫 <span class="inline-word">Prorise</span></div><div class="title1">是一名 超全栈工程师、独立开发者、博主</div></div><div class="aboutsiteTips author-content-item"><div class="author-content-item-tips">目标</div><h2>源于<br>热爱而去 感受<div class="mask"><span class="first-tips">学习</span><span>生活</span><span data-up="">程序</span> <span data-show="">体验</span></div></h2></div></div><div class="hello-about"><div class="cursor" style="translate:none;rotate:none;scale:none;transform:translate(721px,180px)"></div><div class="shapes"><div class="shape shape-1" style="translate:none;rotate:none;scale:none;transform:translate(721px,180px)"></div><div class="shape shape-2" style="translate:none;rotate:none;scale:none;transform:translate(721px,180px)"></div><div class="shape shape-3" style="translate:none;rotate:none;scale:none;transform:translate(721px,180px)"></div></div><div class="content"><h1>Prorise</h1></div></div><div class="author-content"><div class="author-content-item skills"><div class="card-content"><div class="author-content-item-tips">技能</div><span class="author-content-item-title">开启创造力</span><div class="skills-style-group"><div id="skills-tags-group-all"><div class="tags-group-wrapper"><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#e44d26"><img class="no-lightbox" title="HTML5/CSS3" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/html5/html5-original.svg" size="60px" alt="HTML5/CSS3"></div><div class="tags-group-icon" style="background:#f7df1e"><img class="no-lightbox" title="JavaScript" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg" size="60px" alt="JavaScript"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#3178c6"><img class="no-lightbox" title="TypeScript" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg" size="60px" alt="TypeScript"></div><div class="tags-group-icon" style="background:#4fc08d"><img class="no-lightbox" title="Vue.js" src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/vuejs/vuejs-original.svg" size="60px" alt="Vue.js"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="React" src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/react/react-original-wordmark.svg" size="60px" alt="React"></div><div class="tags-group-icon" style="background:#09b83e"><img class="no-lightbox" title="小程序开发" src="https://res.wx.qq.com/a/wx_fed/assets/res/OTE0YTAw.png" size="60px" alt="小程序开发"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Sass" src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/sass/sass-original.svg" size="60px" alt="Sass"></div><div class="tags-group-icon" style="background:#fefef7"><img class="no-lightbox" title="uniapp" src="https://ts2.tc.mm.bing.net/th/id/ODLS.e63c536b-f08d-4550-b4cd-f6ca9d84b67e?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" size="60px" alt="uniapp"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#8dd6f9"><img class="no-lightbox" title="Webpack" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/webpack/webpack-original.svg" size="60px" alt="Webpack"></div><div class="tags-group-icon" style="background:#646cff"><img class="no-lightbox" title="Vite" src="https://vitejs.dev/logo.svg" size="60px" alt="Vite"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Canvas动画" src="https://ts4.tc.mm.bing.net/th/id/ODLS.97013f0b-ae3b-41b4-8c13-ae3ac47fd06d?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" size="60px" alt="Canvas动画"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="GSAP动画库" src="https://ts4.tc.mm.bing.net/th/id/ODLS.827513d4-9077-440d-92b0-1f3d73888d03?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" size="60px" alt="GSAP动画库"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Electron" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/electron/electron-original.svg" size="60px" alt="Electron"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="TailwindCSS" src="https://ts3.tc.mm.bing.net/th/id/ODLS.095f9b22-a70b-47ed-bdb1-070466f08dc4?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" size="60px" alt="TailwindCSS"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#7952b3"><img class="no-lightbox" title="Bootstrap" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/bootstrap/bootstrap-original.svg" size="60px" alt="Bootstrap"></div><div class="tags-group-icon" style="background:#409eff"><img class="no-lightbox" title="Element Plus" src="https://ts3.tc.mm.bing.net/th/id/ODLS.57304398-66ea-459d-8d9c-4647aea8751b?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" size="60px" alt="Element Plus"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#e44d26"><img class="no-lightbox" title="HTML5/CSS3" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/html5/html5-original.svg" size="60px" alt="HTML5/CSS3"></div><div class="tags-group-icon" style="background:#f7df1e"><img class="no-lightbox" title="JavaScript" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg" size="60px" alt="JavaScript"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#3178c6"><img class="no-lightbox" title="TypeScript" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg" size="60px" alt="TypeScript"></div><div class="tags-group-icon" style="background:#4fc08d"><img class="no-lightbox" title="Vue.js" src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/vuejs/vuejs-original.svg" size="60px" alt="Vue.js"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="React" src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/react/react-original-wordmark.svg" size="60px" alt="React"></div><div class="tags-group-icon" style="background:#09b83e"><img class="no-lightbox" title="小程序开发" src="https://res.wx.qq.com/a/wx_fed/assets/res/OTE0YTAw.png" size="60px" alt="小程序开发"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Sass" src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/sass/sass-original.svg" size="60px" alt="Sass"></div><div class="tags-group-icon" style="background:#fefef7"><img class="no-lightbox" title="uniapp" src="https://ts2.tc.mm.bing.net/th/id/ODLS.e63c536b-f08d-4550-b4cd-f6ca9d84b67e?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" size="60px" alt="uniapp"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#8dd6f9"><img class="no-lightbox" title="Webpack" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/webpack/webpack-original.svg" size="60px" alt="Webpack"></div><div class="tags-group-icon" style="background:#646cff"><img class="no-lightbox" title="Vite" src="https://vitejs.dev/logo.svg" size="60px" alt="Vite"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Canvas动画" src="https://ts4.tc.mm.bing.net/th/id/ODLS.97013f0b-ae3b-41b4-8c13-ae3ac47fd06d?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" size="60px" alt="Canvas动画"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="GSAP动画库" src="https://ts4.tc.mm.bing.net/th/id/ODLS.827513d4-9077-440d-92b0-1f3d73888d03?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" size="60px" alt="GSAP动画库"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Electron" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/electron/electron-original.svg" size="60px" alt="Electron"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="TailwindCSS" src="https://ts3.tc.mm.bing.net/th/id/ODLS.095f9b22-a70b-47ed-bdb1-070466f08dc4?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" size="60px" alt="TailwindCSS"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#7952b3"><img class="no-lightbox" title="Bootstrap" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/bootstrap/bootstrap-original.svg" size="60px" alt="Bootstrap"></div><div class="tags-group-icon" style="background:#409eff"><img class="no-lightbox" title="Element Plus" src="https://ts3.tc.mm.bing.net/th/id/ODLS.57304398-66ea-459d-8d9c-4647aea8751b?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" size="60px" alt="Element Plus"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Java" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/java/java-original.svg" size="60px" alt="Java"></div><div class="tags-group-icon" style="background:#3776ab"><img class="no-lightbox" title="Python" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg" size="60px" alt="Python"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#777bb4"><img class="no-lightbox" title="PHP" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/php/php-original.svg" size="60px" alt="PHP"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Node.js" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg" size="60px" alt="Node.js"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Java" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/java/java-original.svg" size="60px" alt="Java"></div><div class="tags-group-icon" style="background:#3776ab"><img class="no-lightbox" title="Python" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg" size="60px" alt="Python"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#777bb4"><img class="no-lightbox" title="PHP" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/php/php-original.svg" size="60px" alt="PHP"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Node.js" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg" size="60px" alt="Node.js"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Spring Boot" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg" size="60px" alt="Spring Boot"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Spring Framework" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg" size="60px" alt="Spring Framework"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Spring MVC" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg" size="60px" alt="Spring MVC"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Spring Security" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg" size="60px" alt="Spring Security"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Spring Boot" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg" size="60px" alt="Spring Boot"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Spring Framework" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg" size="60px" alt="Spring Framework"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Spring MVC" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg" size="60px" alt="Spring MVC"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Spring Security" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg" size="60px" alt="Spring Security"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="MyBatis" src="https://ts4.tc.mm.bing.net/th/id/ODLS.513057f8-5234-45b9-92f8-da60ba13023d?w=32&amp;h=32&amp;qlt=93&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" size="60px" alt="MyBatis"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="MyBatis-Plus" src="https://ts1.tc.mm.bing.net/th/id/ODLS.b5a2813e-eac8-4f6d-8c4a-3955777e3300?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" size="60px" alt="MyBatis-Plus"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Django" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/django/django-plain.svg" size="60px" alt="Django"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Flask" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/flask/flask-original.svg" size="60px" alt="Flask"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="FastAPI" src="https://ts2.tc.mm.bing.net/th/id/ODLS.f4c3d716-9b6f-42a3-9099-8af0c6828d50?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" size="60px" alt="FastAPI"></div><div class="tags-group-icon" style="background:#60a839"><img class="no-lightbox" title="Scrapy" src="https://ts1.tc.mm.bing.net/th/id/ODLS.7c59a4c2-1c1a-47a1-b5c0-a9a7a345bbfb?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" size="60px" alt="Scrapy"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="MyBatis" src="https://ts4.tc.mm.bing.net/th/id/ODLS.513057f8-5234-45b9-92f8-da60ba13023d?w=32&amp;h=32&amp;qlt=93&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" size="60px" alt="MyBatis"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="MyBatis-Plus" src="https://ts1.tc.mm.bing.net/th/id/ODLS.b5a2813e-eac8-4f6d-8c4a-3955777e3300?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" size="60px" alt="MyBatis-Plus"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Django" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/django/django-plain.svg" size="60px" alt="Django"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Flask" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/flask/flask-original.svg" size="60px" alt="Flask"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="FastAPI" src="https://ts2.tc.mm.bing.net/th/id/ODLS.f4c3d716-9b6f-42a3-9099-8af0c6828d50?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" size="60px" alt="FastAPI"></div><div class="tags-group-icon" style="background:#60a839"><img class="no-lightbox" title="Scrapy" src="https://ts1.tc.mm.bing.net/th/id/ODLS.7c59a4c2-1c1a-47a1-b5c0-a9a7a345bbfb?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" size="60px" alt="Scrapy"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="MySQL" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mysql/mysql-original.svg" size="60px" alt="MySQL"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="MongoDB" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg" size="60px" alt="MongoDB"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#dc382d"><img class="no-lightbox" title="Redis" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/redis/redis-original.svg" size="60px" alt="Redis"></div><div class="tags-group-icon" style="background:#fcc624"><img class="no-lightbox" title="Linux" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/linux/linux-original.svg" size="60px" alt="Linux"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Docker" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg" size="60px" alt="Docker"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Nginx" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nginx/nginx-original.svg" size="60px" alt="Nginx"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="MySQL" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mysql/mysql-original.svg" size="60px" alt="MySQL"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="MongoDB" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg" size="60px" alt="MongoDB"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#dc382d"><img class="no-lightbox" title="Redis" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/redis/redis-original.svg" size="60px" alt="Redis"></div><div class="tags-group-icon" style="background:#fcc624"><img class="no-lightbox" title="Linux" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/linux/linux-original.svg" size="60px" alt="Linux"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Docker" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg" size="60px" alt="Docker"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="Nginx" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nginx/nginx-original.svg" size="60px" alt="Nginx"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="提示词工程" src="https://upload.wikimedia.org/wikipedia/commons/0/04/ChatGPT_logo.svg" size="60px" alt="提示词工程"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="UI设计" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/figma/figma-original.svg" size="60px" alt="UI设计"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="提示词工程" src="https://upload.wikimedia.org/wikipedia/commons/0/04/ChatGPT_logo.svg" size="60px" alt="提示词工程"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="UI设计" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/figma/figma-original.svg" size="60px" alt="UI设计"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="RuoYi框架" src="https://ts2.tc.mm.bing.net/th/id/ODLS.5715a21c-7c5f-4781-ae97-2867ef553257?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2g" size="60px" alt="RuoYi框架"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="GoEazy网络框架" src="https://ts3.tc.mm.bing.net/th/id/ODLS.d7f53ebd-3c0c-415f-943b-197fa248b289?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" size="60px" alt="GoEazy网络框架"></div></div><div class="tags-group-icon-pair"><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="RuoYi框架" src="https://ts2.tc.mm.bing.net/th/id/ODLS.5715a21c-7c5f-4781-ae97-2867ef553257?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2g" size="60px" alt="RuoYi框架"></div><div class="tags-group-icon" style="background:#fff"><img class="no-lightbox" title="GoEazy网络框架" src="https://ts3.tc.mm.bing.net/th/id/ODLS.d7f53ebd-3c0c-415f-943b-197fa248b289?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" size="60px" alt="GoEazy网络框架"></div></div></div></div><div class="skills-list"><div class="skill-category"><div class="skill-category-title">前端开发</div><div class="skill-category-content"><div class="skill-info"><div class="skill-icon" style="background:#e44d26"><img class="no-lightbox" title="HTML5/CSS3" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/html5/html5-original.svg" alt="HTML5/CSS3"></div><div class="skill-name"><span>HTML5/CSS3</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#f7df1e"><img class="no-lightbox" title="JavaScript" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg" alt="JavaScript"></div><div class="skill-name"><span>JavaScript</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#3178c6"><img class="no-lightbox" title="TypeScript" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg" alt="TypeScript"></div><div class="skill-name"><span>TypeScript</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#4fc08d"><img class="no-lightbox" title="Vue.js" src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/vuejs/vuejs-original.svg" alt="Vue.js"></div><div class="skill-name"><span>Vue.js</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="React" src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/react/react-original-wordmark.svg" alt="React"></div><div class="skill-name"><span>React</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#09b83e"><img class="no-lightbox" title="小程序开发" src="https://res.wx.qq.com/a/wx_fed/assets/res/OTE0YTAw.png" alt="小程序开发"></div><div class="skill-name"><span>小程序开发</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Sass" src="https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/sass/sass-original.svg" alt="Sass"></div><div class="skill-name"><span>Sass</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fefef7"><img class="no-lightbox" title="uniapp" src="https://ts2.tc.mm.bing.net/th/id/ODLS.e63c536b-f08d-4550-b4cd-f6ca9d84b67e?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" alt="uniapp"></div><div class="skill-name"><span>uniapp</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#8dd6f9"><img class="no-lightbox" title="Webpack" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/webpack/webpack-original.svg" alt="Webpack"></div><div class="skill-name"><span>Webpack</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#646cff"><img class="no-lightbox" title="Vite" src="https://vitejs.dev/logo.svg" alt="Vite"></div><div class="skill-name"><span>Vite</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Canvas动画" src="https://ts4.tc.mm.bing.net/th/id/ODLS.97013f0b-ae3b-41b4-8c13-ae3ac47fd06d?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" alt="Canvas动画"></div><div class="skill-name"><span>Canvas动画</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="GSAP动画库" src="https://ts4.tc.mm.bing.net/th/id/ODLS.827513d4-9077-440d-92b0-1f3d73888d03?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" alt="GSAP动画库"></div><div class="skill-name"><span>GSAP动画库</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Electron" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/electron/electron-original.svg" alt="Electron"></div><div class="skill-name"><span>Electron</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="TailwindCSS" src="https://ts3.tc.mm.bing.net/th/id/ODLS.095f9b22-a70b-47ed-bdb1-070466f08dc4?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" alt="TailwindCSS"></div><div class="skill-name"><span>TailwindCSS</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#7952b3"><img class="no-lightbox" title="Bootstrap" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/bootstrap/bootstrap-original.svg" alt="Bootstrap"></div><div class="skill-name"><span>Bootstrap</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#409eff"><img class="no-lightbox" title="Element Plus" src="https://ts3.tc.mm.bing.net/th/id/ODLS.57304398-66ea-459d-8d9c-4647aea8751b?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" alt="Element Plus"></div><div class="skill-name"><span>Element Plus</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="DaisyUI" src="https://ts2.tc.mm.bing.net/th/id/ODLS.abfe53b0-a009-4cb9-9eb6-0e088c68c907?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" alt="DaisyUI"></div><div class="skill-name"><span>DaisyUI</span></div></div></div></div><div class="skill-category"><div class="skill-category-title">后端编程语言</div><div class="skill-category-content"><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Java" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/java/java-original.svg" alt="Java"></div><div class="skill-name"><span>Java</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#3776ab"><img class="no-lightbox" title="Python" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg" alt="Python"></div><div class="skill-name"><span>Python</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#777bb4"><img class="no-lightbox" title="PHP" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/php/php-original.svg" alt="PHP"></div><div class="skill-name"><span>PHP</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Node.js" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg" alt="Node.js"></div><div class="skill-name"><span>Node.js</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#00599c"><img class="no-lightbox" title="C/C++" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/cplusplus/cplusplus-original.svg" alt="C/C++"></div><div class="skill-name"><span>C/C++</span></div></div></div></div><div class="skill-category"><div class="skill-category-title">Spring全家桶</div><div class="skill-category-content"><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Spring Boot" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg" alt="Spring Boot"></div><div class="skill-name"><span>Spring Boot</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Spring Framework" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg" alt="Spring Framework"></div><div class="skill-name"><span>Spring Framework</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Spring MVC" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg" alt="Spring MVC"></div><div class="skill-name"><span>Spring MVC</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Spring Security" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg" alt="Spring Security"></div><div class="skill-name"><span>Spring Security</span></div></div></div></div><div class="skill-category"><div class="skill-category-title">后端框架与库</div><div class="skill-category-content"><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="MyBatis" src="https://ts4.tc.mm.bing.net/th/id/ODLS.513057f8-5234-45b9-92f8-da60ba13023d?w=32&amp;h=32&amp;qlt=93&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" alt="MyBatis"></div><div class="skill-name"><span>MyBatis</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="MyBatis-Plus" src="https://ts1.tc.mm.bing.net/th/id/ODLS.b5a2813e-eac8-4f6d-8c4a-3955777e3300?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" alt="MyBatis-Plus"></div><div class="skill-name"><span>MyBatis-Plus</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Django" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/django/django-plain.svg" alt="Django"></div><div class="skill-name"><span>Django</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Flask" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/flask/flask-original.svg" alt="Flask"></div><div class="skill-name"><span>Flask</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="FastAPI" src="https://ts2.tc.mm.bing.net/th/id/ODLS.f4c3d716-9b6f-42a3-9099-8af0c6828d50?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" alt="FastAPI"></div><div class="skill-name"><span>FastAPI</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#60a839"><img class="no-lightbox" title="Scrapy" src="https://ts1.tc.mm.bing.net/th/id/ODLS.7c59a4c2-1c1a-47a1-b5c0-a9a7a345bbfb?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" alt="Scrapy"></div><div class="skill-name"><span>Scrapy</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="ThinkPHP" src="https://ts3.tc.mm.bing.net/th/id/ODLS.06dc16eb-e885-4f1a-890e-25dc17a3483b?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffc&amp;o=6&amp;pid=1.2" alt="ThinkPHP"></div><div class="skill-name"><span>ThinkPHP</span></div></div></div></div><div class="skill-category"><div class="skill-category-title">数据库与运维</div><div class="skill-category-content"><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="MySQL" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mysql/mysql-original.svg" alt="MySQL"></div><div class="skill-name"><span>MySQL</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="MongoDB" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg" alt="MongoDB"></div><div class="skill-name"><span>MongoDB</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#dc382d"><img class="no-lightbox" title="Redis" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/redis/redis-original.svg" alt="Redis"></div><div class="skill-name"><span>Redis</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fcc624"><img class="no-lightbox" title="Linux" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/linux/linux-original.svg" alt="Linux"></div><div class="skill-name"><span>Linux</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Docker" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg" alt="Docker"></div><div class="skill-name"><span>Docker</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Nginx" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nginx/nginx-original.svg" alt="Nginx"></div><div class="skill-name"><span>Nginx</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="Git" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/git/git-original.svg" alt="Git"></div><div class="skill-name"><span>Git</span></div></div></div></div><div class="skill-category"><div class="skill-category-title">AI与设计</div><div class="skill-category-content"><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="提示词工程" src="https://upload.wikimedia.org/wikipedia/commons/0/04/ChatGPT_logo.svg" alt="提示词工程"></div><div class="skill-name"><span>提示词工程</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="UI设计" src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/figma/figma-original.svg" alt="UI设计"></div><div class="skill-name"><span>UI设计</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#9c27b0"><img class="no-lightbox" title="UE设计" src="https://upload.wikimedia.org/wikipedia/commons/c/c2/Adobe_XD_CC_icon.svg" alt="UE设计"></div><div class="skill-name"><span>UE设计</span></div></div></div></div><div class="skill-category"><div class="skill-category-title">开发框架</div><div class="skill-category-content"><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="RuoYi框架" src="https://ts2.tc.mm.bing.net/th/id/ODLS.5715a21c-7c5f-4781-ae97-2867ef553257?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2g" alt="RuoYi框架"></div><div class="skill-name"><span>RuoYi框架</span></div></div><div class="skill-info"><div class="skill-icon" style="background:#fff"><img class="no-lightbox" title="GoEazy网络框架" src="https://ts3.tc.mm.bing.net/th/id/ODLS.d7f53ebd-3c0c-415f-943b-197fa248b289?w=32&amp;h=32&amp;qlt=90&amp;pcl=fffffa&amp;o=6&amp;pid=1.2" alt="GoEazy网络框架"></div><div class="skill-name"><span>GoEazy网络框架</span></div></div></div></div><div class="etc">...</div></div></div></div></div><div class="author-content-item careers"><div class="card-content"><div class="author-content-item-tips">生涯</div><span class="author-content-item-title">无限进步</span><div class="careers-group"><div class="career-item"><div class="circle" style="background:#357ef5"></div><div class="name">广东计算机科学与技术</div></div><div class="career-item"><div class="circle" style="background:#357ef5"></div><div class="name">超全栈开发工程师</div></div></div><img class="author-content-img no-lightbox" alt="生涯" src="https://bu.dusays.com/2023/04/21/644287166329b.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"></div></div></div><div class="author-content"><div class="author-content-item-group column mapAndInfo"><style>.author-content-item.map{background-image:url(https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/TerO/1474X484/8d3acb37-2cb0-4b18-a6c4-a28e2894aba1.png)}[data-theme=dark] .author-content-item.map{background-image:url(https://bu.dusays.com/2023/07/05/64a4c63495ac5.jpg)}</style><div class="author-content-item map single"><span class="map-title">我现在住在<b>广东省潮州市</b></span></div><div class="author-content-item selfInfo single"><div><span class="selfInfo-title">生于</span> <span class="selfInfo-content" id="selfInfo-content-year" style="color:#43a6c6">2005</span></div><div><span class="selfInfo-title">就业于</span> <span class="selfInfo-content" style="color:#c69043">网络茫茫大海</span></div><div><span class="selfInfo-title">现在职业</span> <span class="selfInfo-content" style="color:#b04fe6">全栈工程师</span></div></div></div></div><div class="author-content"><div class="author-content-item personalities"><div class="author-content-item-tips">性格</div><span class="author-content-item-title">INFJ-T</span><div class="title2" style="color:#ac899c">提倡者</div><div class="post-tips">在 <a href="https://www.16personalities.com/" target="_blank" rel="noopener nofollow">16personalities</a> 了解更多关于 <a target="_blank" rel="noopener external nofollow" href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/sdwO/DM-20250613173929-001.svg">INFJ-T</a></div><div class="image"><img class="no-lightbox" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/m2uo/DM-20250613174238-001.svg" alt="人格"></div></div><div class="author-content-item myphoto"><img class="author-content-img no-lightbox" alt="自拍" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/sdwO/DM-20250613173929-001.svg"></div></div><div class="author-content"><div class="author-content-item maxim"><div class="author-content-item-tips">座右铭</div><span class="maxim-title"><span style="opacity:.6;margin-bottom:8px">保持饥饿，</span> <span>保持愚蠢。</span></span></div><div class="author-content-item buff"><div class="card-content"></div><div class="author-content-item-tips">特长</div><span class="buff-title"><span style="opacity:.6;margin-bottom:8px">不会聊天的 技术宅</span> <span>学习指数 MAX</span></span><div class="card-background-icon"><i class="anzhiyufont anzhiyu-icon-dice-d20"></i></div></div></div><div class="author-content"><div class="author-content-item game-yuanshen" style="background:url(https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/oYRd/1192X703/f836eb3c-f02b-42be-9eda-e7854e8de265.png) top/cover no-repeat"><div class="card-content"><div class="author-content-item-tips">爱好游戏</div><span class="author-content-item-title">英雄联盟</span><div class="content-bottom"><div class="icon-group"><div class="loading-bar" role="presentation" aria-hidden="true" style="display:none"></div></div><div class="tips game-yuanshen-uid">游戏ID: Prorise</div></div></div></div><div class="author-content-item comic-content"><div class="card-content"><div class="author-content-item-tips">爱好番剧</div><div class="author-content-item-title">追番</div><div class="comic-box"><a class="comic-item" href="https://img02.anheyu.com/adminuploads/1/2022/12/13/63988658aa1b1.webp" rel="external nofollow noreferrer" target="_blank" title="约定的梦幻岛"><div class="comic-item-cover"><img src="https://img02.anheyu.com/adminuploads/1/2022/12/13/63988658aa1b1.webp" alt="约定的梦幻岛"></div></a><a class="comic-item" href="https://www.bilibili.com/bangumi/media/md28229899/?spm_id_from=666.25.b_6d656469615f6d6f64756c65.1" rel="external nofollow noreferrer" target="_blank" title="咒术回战"><div class="comic-item-cover"><img src="https://img02.anheyu.com/adminuploads/1/2022/12/13/6398864e572ed.webp" alt="咒术回战"></div></a><a class="comic-item" href="https://www.bilibili.com/bangumi/media/md8892/?spm_id_from=666.25.b_6d656469615f6d6f64756c65.1" rel="external nofollow noreferrer" target="_blank" title="紫罗兰永恒花园"><div class="comic-item-cover"><img src="https://img02.anheyu.com/adminuploads/1/2022/12/13/639886315d658.webp" alt="紫罗兰永恒花园"></div></a><a class="comic-item" href="https://www.bilibili.com/bangumi/media/md22718131/?spm_id_from=666.25.b_6d656469615f6d6f64756c65.1" rel="external nofollow noreferrer" target="_blank" title="未闻花名"><div class="comic-item-cover"><img src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/LXVm/474X663/226eae35-d054-4419-9cac-02a822b19913.png" alt="未闻花名"></div></a><a class="comic-item" href="https://www.bilibili.com/bangumi/media/md135652/?spm_id_from=666.25.b_6d656469615f6d6f64756c65.1" rel="external nofollow noreferrer" target="_blank" title="星游记"><div class="comic-item-cover"><img src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250613/XVRo/770X1080/bd2b1fe4-4a01-40c3-99cd-34a566b6fec9.png" alt="星游记"></div></a></div></div></div></div><div class="author-content"><div class="author-content-item like-technology" style="background:url(https://bu.dusays.com/2022/12/06/638f5f05ce1f7.jpg) top/cover no-repeat"><div class="card-content"><div class="author-content-item-tips">关注偏好</div><span class="author-content-item-title">新型软件 &amp; 开源项目</span><div class="content-bottom"><div class="tips">手机、电脑软硬件</div></div></div></div><div class="author-content-item like-music" style="background:url(https://p2.music.126.net/Mrg1i7DwcwjWBvQPIMt_Mg==/79164837213438.jpg) top/cover no-repeat"><div class="card-content"><div class="author-content-item-tips">音乐偏好</div><span class="author-content-item-title">纯音、轻音乐、美音</span><div class="content-bottom"><div class="tips">跟 Prorise 一起欣赏更多音乐</div></div><div class="banner-button-group"><a class="banner-button" onclick="pjax.loadUrl(&quot;/music/&quot;)"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i> <span class="banner-button-text">更多推荐</span></a></div></div></div></div><div class="author-content"><div class="create-site-post author-content-item single"><h2 id="心路历程"><a href="#心路历程" class="headerlink" title="心路历程"></a>心路历程</h2><blockquote><p>回望这段人生，与其称之为履历，不如视作一场漫长的自我救赎。它始于一片灰色的浓雾，日子沉闷如南方绵长的梅雨，未来则是一条被预设却黯淡无光的轨道。我曾是雾中徘徊的游魂，以为世界的尽头不过小城里那条闭眼也能走完的街道，人生的剧本也早已写好：<em>毕业、工作、重复</em>，循环往复。</p></blockquote><p>我是一个来自广东潮州的中职生。无可否认，我曾是浑噩度日的一员。初中时，我体会不到教育的意义，也感受不到亲情的温度。父母仿佛永远缺席，唯有年迈的爷爷奶奶，是他们陪伴我走过了整个童年。那时，我的志向渺小得可怜，觉得读完初中就出去打工便好——反正父亲母亲也是如此。在这个小城镇里，跨越阶层似乎是个无人能及的神话。当同龄人畅谈理想时，我渐渐退到人群最后，将自己沉溺在虚拟的网络世界，也落得一身病痛。</p><p>初中的我，是个满脸痘痕、体重一百八十斤的胖子，在同龄人眼中，无异于一个遭人嘲笑的“哥布林”。无论是学业、技能还是运动，我都沾不上边。跑操请假、生病请假、体育课请假……这些事对我来说习以为常，家里人对此也漠不关心，只是淡淡地说：“你自己想做什么都行。”</p><p>不出所料，我来到了职校，职校的生活，是命运惯性的无情延续。周遭的一切似乎都在不断加深我的平庸感。在中职学校，能学到的东西屈指可数，课程内容常让我觉得过于浅显……诸如“如何使用百度”、“如何打字”之类的课堂，一节课四十分钟显得格外漫长。那段日子，上课时玩手机盼着下课，下课后回宿舍倒头就睡，日复一日。</p><blockquote><p>常听人抱怨家人管束太多……太多，而我却无比渴望有人能真正“理理”我，能注意到那个蜷缩在阴暗角落里的自己。</p></blockquote><p>然而，恰恰是在这片看似贫瘠的土壤里，一颗名为“不甘”的种子悄然破土。我开始笨拙地尝试改变。在宿舍的角落，我举起那几公斤重的哑铃，动作生涩而笨拙，忍受着室友们或好奇或不解的目光。每个夜晚，在宿舍的喧哗吵闹中，甚至在深夜突如其来的“灯光秀”里，我都只能默不作声——相比起来，我才是那个格格不入的人。这样的日子一直持续到中职二年级的尾声。学校开设了“升大班”，也就是面向考大专的升学班。那时的我依然懵懂迷茫，只是看到朋友们都去了，便也抱着“试试看”的心态报了名。结果显而易见，这个所谓的“升大班”几乎没有门槛，只要你愿意，就能进。</p><blockquote><p>我被分到了第七班的第十一号，命运的轨迹，似乎就在那一刻悄然拐向了另一条岔路。</p></blockquote><p>即便在这里，也远没有普通高中的学习氛围。但老师们都格外尽责。生平第一次，我体会到晚自习的教室里，原来真的有人在学习——尽管人数寥寥。看着他们专注的身影，一股莫名的冲动驱使我想要加入。可我的基础太差了，在很多人眼中如同常识的乘除法、因式分解，甚至简单的英语单词，我都认不全……</p><p>一次课间，身后的一位女孩轻轻点了点我的背，问我是否知道某道数学题怎么解。我只能尴尬地回答“不会”。就是这样一个瞬间，看着她转向班里那些“厉害”的同学并获得解答时，一个念头在我心底扎下了根：如果我会就好了，哪怕仅仅是一道关于集合的基础题。那个假期，带着一种近乎耻辱的决心，我打开电脑，搜索“四年级乘除法”、“中职数学基础”、“小学英语单词”……对于一个十六岁、即将十七岁的人来说，这无疑是一种难言的羞耻。但我必须尝试。在那段时间里，我加上了那个女孩的微信，以请教学习为名。我甚至问出了“一个倒着的三角形，为什么它的‘底’在上边？上边不应该叫‘高’吗？”这样暴露智商的问题。出乎意料的是，她依然耐心地为我讲解。</p><blockquote><p>从那一刻起，我开始一点一滴地戒掉很多东西：沉迷的游戏、放纵的饮食……以及那个浑噩度日的自己。</p><p>除了必要的吃饭时间，我几乎从未停下。每一个放学的午后，空旷的教室里仿佛只剩下我一个人。我爱极了这种感觉——在无人注视、无人竞争的寂静里，默默地积蓄力量。</p></blockquote><div class="note simple"><p>我要跨过去。我要走进那个世界，不是为了任何人，而是为了给自己一场彻底的新生。</p></div><p>于是，我的朝圣之旅，正式启程。</p><details class="folding-tag"><summary>美好的时间总是短暂的</summary><div class="content"><p>渐渐地，我们的关系从她指导我，变成了共同讨论，甚至偶尔我竟能扮演起“指导者”的角色。我敏锐地察觉到了这种变化，内心无数次警醒：能力的鸿沟依然真实存在。然而，她看我的眼神已然不同。也许是我故态复萌，又戴上了虚伪的面具？的确，在相当长的一段时间里，我误以为自己真的掌握了许多知识。每逢小考，我就翻看笔记，坚信只要能在最终的大考派上用场就好。我一直在欺骗自己。在此期间，我甚至开始梦想：我们起点虽低，但若能一同努力，或许可以考入同一所大学？我试过鼓起勇气去追求这份微光，遗憾的是，我失败了……我们的关系急转直下，从朋友到同学，再到如今的音讯全无。</p><p>还记得毕业聚会前，她的宿舍五人关系已势同水火，聚会自然无人出席。得知消息，班级群里充斥着这样的声音：“毕业会不来？大家筹钱准备的活动，一点同学情谊都没有了？太自私了吧！”说我自欺欺人也好，说我只能做到这样也罢——我交了聚会费，但最终没有去参加。那天下午放学，大家热热闹闹地准备着聚会，我却看见她和几位舍友沉默地结伴走向宿舍楼。我独自背着书包回宿舍。那一刻，我觉得自己的缺席是值得的，只是无法苟同那种随波逐流的“合群”罢了。聚会前的那个下午，她主动来找我问问题。我凭着半桶水的知识与她交流，那是我记忆中为数不多感到真正开心的午后时光。之后，便是紧锣密鼓的备考冲刺。</p></div></details><p>临考前十天，她和舍友的关系依旧紧张。四位舍友甚至决定搬回家住，等到考试当天再返校，独留她一人在宿舍。于是，她也会常常待在教室。对我来说，下午放学后的教室终于不再只有我孤身一人。我们的关系其实并不算好。就在那年四月，我曾试图向她袒露心迹，表达我对她那份能在淤泥中奋力绽放的毅力的欣赏。可我深知自己还远远不够——尽管那时已成功减重至一百二十多斤，连初中老同学见了都惊呼：“真是大变样啊，完全不像你了！”</p><p>但我始终是我，那个内核未曾改变的我。在那最后的十天里，她主动找到我，希望能一起复习备考。或许是我在模拟考中作弊的侥幸给了她错觉？哈哈……但我终究还是靠着那半桶水的知识，陪她渡过了这至关重要的十天。那十天里，一切都仿佛在朝着光明的方向迈进。</p><p>走进真正的考场时，我带着前所未有的自信，一扫模拟考时的紧张。第一场语文考试结束，我甚至押中了作文题目，欣喜若狂地第一时间与她分享。然而，她的状态却截然不同——她的作文没能写完。尽管如此，我仍尽力安慰她，鼓励她调整心态迎接下一场考试。那一晚，我没有询问她第二天的考试情况。当一切尘埃落定，她整个人已然崩溃。朋友圈的签名、整个人的状态……后来才听说，考试前一晚，她因抱怨舍友打呼噜，竟被狠狠责骂了很久。我能做的，唯有苍白的安慰，却收效甚微。我们之间的关系，远未达到能共情的深度，一切不过是我单方面的徒劳输出。</p><p>放学铃响了……考试结束了。那个曾满怀梦想的小孩，也死在了自我欺骗的路上，带着他未曾实现的憧憬，永远地离开了。</p><p>如今，我依然习惯独坐到深夜。当世界陷入万籁俱寂，耳畔不再有游戏的喧嚣，唯有键盘敲击的清脆声响——那是我与这个世界对话的方式，是我与孤独和解的独特旋律。每一次按下 <kbd>Enter</kbd>, 敲下的既是代码的指令，也是我人生崭新的下一行。</p><p>我不再追问学习是否真有出路，因为<strong>行走在这条路上本身，便是我的最终归宿</strong>。</p><hr></div></div><div class="author-content"><div class="author-content-item single reward" id="about-reward"><div class="author-content-item-tips">致谢</div><span class="author-content-item-title">赞赏名单</span><div class="author-content-item-description">感谢因为有你们，让我更加有创作的动力。<div class="reward-list-all"><div class="reward-list-item"><div class="reward-list-item-name">感谢B</div><div class="reward-list-bottom-group"><div class="reward-list-item-money" style="background:var(--anzhiyu-yellow)">¥66.6</div><div class="datatime reward-list-item-time" datatime="2025-05-20T00:00:00.000Z">2025-05-20</div></div></div><div class="reward-list-item"><div class="reward-list-item-name">感谢A</div><div class="reward-list-bottom-group"><div class="reward-list-item-money">¥8.8</div><div class="datatime reward-list-item-time" datatime="2025-06-01T00:00:00.000Z">2025-06-01</div></div></div></div><div class="reward-list-updateDate">最新更新时间： <time class="datatime reward-list-updateDate-time" datatime="2025-06-01T00:00:00.000Z">2025-06-01</time></div></div><div class="about-reward"><div id="con"></div><div id="TA-con" onclick="anzhiyu.rewardShowConsole()"><div id="text-con"><div id="linght"></div><div id="TA">为TA充电</div></div></div><div id="tube-con"><svg viewBox="0 0 1028 385" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 77H234.226L307.006 24H790" stroke="#e5e9ef" stroke-width="20"></path><path d="M0 140H233.035L329.72 71H1028" stroke="#e5e9ef" stroke-width="20"></path><path d="M1 255H234.226L307.006 307H790" stroke="#e5e9ef" stroke-width="20"></path><path d="M0 305H233.035L329.72 375H1028" stroke="#e5e9ef" stroke-width="20"></path><rect y="186" width="236" height="24" fill="#e5e9ef"></rect><ellipse cx="790" cy="25.5" rx="25" ry="25.5" fill="#e5e9ef"></ellipse><circle r="14" transform="matrix(1 0 0 -1 790 25)" fill="white"></circle><ellipse cx="790" cy="307.5" rx="25" ry="25.5" fill="#e5e9ef"></ellipse><circle r="14" transform="matrix(1 0 0 -1 790 308)" fill="white"></circle></svg><div id="mask"><svg viewBox="0 0 1028 385" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 77H234.226L307.006 24H790" stroke="#f25d8e" stroke-width="20"></path><path d="M0 140H233.035L329.72 71H1028" stroke="#f25d8e" stroke-width="20"></path><path d="M1 255H234.226L307.006 307H790" stroke="#f25d8e" stroke-width="20"></path><path d="M0 305H233.035L329.72 375H1028" stroke="#f25d8e" stroke-width="20"></path><rect y="186" width="236" height="24" fill="#f25d8e"></rect><ellipse cx="790" cy="25.5" rx="25" ry="25.5" fill="#f25d8e"></ellipse><circle r="14" transform="matrix(1 0 0 -1 790 25)" fill="white"></circle><ellipse cx="790" cy="307.5" rx="25" ry="25.5" fill="#f25d8e"></ellipse><circle r="14" transform="matrix(1 0 0 -1 790 308)" fill="white"></circle></svg></div><div id="orange-mask"><svg viewBox="0 0 1028 385" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 77H234.226L307.006 24H790" stroke="#ffd52b" stroke-width="20"></path><path d="M0 140H233.035L329.72 71H1028" stroke="#ffd52b" stroke-width="20"></path><path d="M1 255H234.226L307.006 307H790" stroke="#ffd52b" stroke-width="20"></path><path d="M0 305H233.035L329.72 375H1028" stroke="#ffd52b" stroke-width="20"></path><rect y="186" width="236" height="24" fill="#ffd52b"></rect><ellipse cx="790" cy="25.5" rx="25" ry="25.5" fill="#ffd52b"></ellipse><circle r="14" transform="matrix(1 0 0 -1 790 25)" fill="white"></circle><ellipse cx="790" cy="307.5" rx="25" ry="25.5" fill="#ffd52b"></ellipse><circle r="14" transform="matrix(1 0 0 -1 790 308)" fill="white"></circle></svg></div><p id="people">共<b>2</b>人</p></div></div></div></div></div><script src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/countup/countup.js"></script><script defer="">function initAboutPage() {
  fetch("https://v6-widget.51.la/v6//quote.js")
    .then(res => res.text())
    .then(data => {
      let title = ["最近活跃", "今日人数", "今日访问", "昨日人数", "昨日访问", "本月访问", "总访问量"];
      let num = data.match(/(<\/span><span>).*?(\/span><\/p>)/g);

      num = num.map(el => {
        let val = el.replace(/(<\/span><span>)/g, "");
        let str = val.replace(/(<\/span><\/p>)/g, "");
        return str;
      });

      let statisticEl = document.getElementById("statistic");

      // 自定义不显示哪个或者显示哪个，如下为不显示 最近活跃访客 和 总访问量
      let statistic = [];
      for (let i = 0; i < num.length; i++) {
        if (!statisticEl) return;
        if (i == 0) continue;
        statisticEl.innerHTML +=
          "<div><span>" + title[i] + "</span><span id=" + title[i] + ">" + num[i] + "</span></div>";
        queueMicrotask(() => {
          statistic.push(
            new CountUp(title[i], 0, num[i], 0, 2, {
              useEasing: true,
              useGrouping: true,
              separator: ",",
              decimal: ".",
              prefix: "",
              suffix: "",
            })
          );
        });
      }

      let statisticElement = document.querySelector(".about-statistic.author-content-item");
      function statisticUP() {
        if (!statisticElement) return;

        const callback = (entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              for (let i = 0; i < num.length; i++) {
                if (i == 0) continue;
                queueMicrotask(() => {
                  statistic[i - 1].start();
                });
              }
              observer.disconnect(); // 停止观察元素，因为不再需要触发此回调
            }
          });
        };

        const options = {
          root: null,
          rootMargin: "0px",
          threshold: 0
        };
        const observer = new IntersectionObserver(callback, options);
        observer.observe(statisticElement);
      }

      const selfInfoContentYear = new CountUp("selfInfo-content-year", 0, 2005, 0, 2, {
        useEasing: true,
        useGrouping: false,
        separator: ",",
        decimal: ".",
        prefix: "",
        suffix: "",
      });

      let selfInfoContentYearElement = document.querySelector(".author-content-item.selfInfo.single");
      function selfInfoContentYearUp() {
        if (!selfInfoContentYearElement) return;

        const callback = (entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              selfInfoContentYear.start();
              observer.disconnect(); // 停止观察元素，因为不再需要触发此回调
            }
          });
        };

        const options = {
          root: null,
          rootMargin: "0px",
          threshold: 0
        };
        const observer = new IntersectionObserver(callback, options);
        observer.observe(selfInfoContentYearElement);
      }

      selfInfoContentYearUp();
      statisticUP()
    });

  var pursuitInterval = null;
  pursuitInterval = setInterval(function () {
    const show = document.querySelector("span[data-show]");
    const next = show.nextElementSibling || document.querySelector(".first-tips");
    const up = document.querySelector("span[data-up]");

    if (up) {
      up.removeAttribute("data-up");
    }

    show.removeAttribute("data-show");
    show.setAttribute("data-up", "");

    next.setAttribute("data-show", "");
  }, 2000);

  document.addEventListener("pjax:send", function () {
    pursuitInterval && clearInterval(pursuitInterval);
  });

  var helloAboutEl = document.querySelector(".hello-about");
  helloAboutEl.addEventListener("mousemove", evt => {
    const mouseX = evt.offsetX;
    const mouseY = evt.offsetY;
    gsap.set(".cursor", {
      x: mouseX,
      y: mouseY,
    });

    gsap.to(".shape", {
      x: mouseX,
      y: mouseY,
      stagger: -0.1,
    });
  });
}
if (typeof gsap === "object") {
  initAboutPage()
} else {
  getScript("https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/gsap/gsap.min.js").then(initAboutPage);
}</script><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="about"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>