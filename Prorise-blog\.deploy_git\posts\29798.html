<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>Python（十一）：第十章： 模块与包 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="Python基础知识总汇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="Python（十一）：第十章： 模块与包"><meta name="application-name" content="Python（十一）：第十章： 模块与包"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="Python（十一）：第十章： 模块与包"><meta property="og:url" content="https://prorise666.site/posts/29798.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="第十章： 模块与包模块是 Python 中组织代码的基本单位，本质上是一个包含 Python 定义和语句的文件。本文将深入探讨模块与包的概念、使用方法以及高级应用技巧，结合 PyCharm 中的包管理最佳实践。 10.1 模块分类在 Python 生态系统中，模块可以分为三大类：    模块类型 说"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta name="description" content="第十章： 模块与包模块是 Python 中组织代码的基本单位，本质上是一个包含 Python 定义和语句的文件。本文将深入探讨模块与包的概念、使用方法以及高级应用技巧，结合 PyCharm 中的包管理最佳实践。 10.1 模块分类在 Python 生态系统中，模块可以分为三大类：    模块类型 说"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/29798.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"Python（十一）：第十章： 模块与包",postAI:"true",pageFillDescription:"第十章： 模块与包, 10.1 模块分类, 10.2 模块导入方式, 10.2.1 导入整个模块, 10.2.2 从模块导入特定内容, 10.2.3 导入时重命名, 10.2.4 导入所有内容（不推荐）, 10.3 控制模块导入, 10.4 模块的特殊属性, 10.4.1 __name__ 属性, 10.4.2 From 模块无法识别问题, 10.5 包的概念与使用, 10.5.1 包的结构示例, 10.5.2 __init__.py 文件的作用, 10.5.3 包的导入方式, ******** 导入包中的模块, ******** 导入包中特定内容, 10.5.4 相对导入与绝对导入, ******** 绝对导入, ******** 相对导入, 10.6 高级应用技巧, 10.6.1 动态导入, 10.6.2 延迟导入, 10.6.3 使用 __slots__ 优化内存, 10.7 包的发布与安装, 10.7.1 创建 setup.py 文件, 10.7.2 打包与上传, 10.7.3 安装包, 10.8 PyCharm 中的包管理, 10.8.1 使用 Python Packages 工具, 10.8.2 更改 PyCharm 的 pip 源, 10.8.3 使用 Project Interpreter 管理包, 10.8.4 导出和导入项目依赖, ******** 导出依赖, ******** 导入依赖, 10.8.5 使用虚拟环境, ******** 虚拟环境工具对比, 10.9 模块开发最佳实践, 10.9.1 模块组织, 10.9.2 导入顺序, 10.9.3 文档化模块和包第十章模块与包模块是中组织代码的基本单位本质上是一个包含定义和语句的文件本文将深入探讨模块与包的概念使用方法以及高级应用技巧结合中的包管理最佳实践模块分类在生态系统中模块可以分为三大类模块类型说明示例内置模块解释器自带的标准库模块第三方模块社区开发者创建的模块自定义模块开发者自己创建的模块项目中自定义的文件重要提示首次导入自定义模块时会执行该模块中的所有顶层代码每个模块都有自己的名称空间模块中定义的变量属于该模块的名称空间模块导入方式导入整个模块使用模块中的内容从模块导入特定内容直接使用无需模块名前缀工作原理使用方式导入时被导入的对象会直接引用模块中对应变量的内存地址可以直接使用而无需模块前缀导入时重命名模块重命名函数重命名导入所有内容不推荐注意这种方式可能导致命名冲突不利于代码可读性和维护性在大型项目中应避免使用控制模块导入我们可以在每一个模块的文件中使用如下的操作可以使用列表来控制语句导入的内容在模块文件中定义不在中的变量和函数使用时不会被导入这个变量不会被导入模块的特殊属性属性每个文件都有一个属性当直接运行该文件时的值为当作为模块被导入时的值为模块名这个特性可用于编写既可作为模块导入又可独立运行的代码模块内的代码执行主函数逻辑辅助函数这部分代码只在直接运行文件时执行作为模块导入时不会执行运行模块自测试模块无法识别问题在导入模块时会按照一定顺序搜索模块文件在有些情况下我们自己定义的模块不一定会被检测到如下列图片例如当我们的模型层期望用到另外一个包的代码时往往会这样引入但这样是无法被识别到的我们应该是需要这样做标记外层的包为根包去掉前缀这样就会检测到我们是在这个包下进行操作的即可识别到从我们的根包出发也就是图片中蓝色的包这个是需要在手动标注的包的概念与使用包是一种特殊的模块它是一个包含文件的目录用于组织相关模块包可以包含子包和模块形成层次结构包的结构示例使目录成为包的文件模块模块子包文件的作用标识目录为包将包含的目录视为包初始化包在导入包时执行初始化代码定义包的公共接口通过列表指定时导入的内容自动导入子模块可以在中导入子模块使它们在导入包时可用示例从子模块导入主要函数使它们在导入包时可用定义包导出的符号包初始化代码已加载包的导入方式导入包中的模块完整路径导入导入特定模块导入子包中的模块导入包中特定内容相对导入与绝对导入绝对导入从项目的顶级包开始导入相对导入使用点号表示相对位置当前包中的模块父包中的模块祖父包中的模块在中导入同级模块导入同级模块导入父包中的模块导入父包中的模块导入父包中模块的特定函数注意相对导入只能在包内使用不能在顶级模块中使用相对导入基于当前模块的属性而直接运行的脚本的总是高级应用技巧动态导入在运行时根据条件动态导入模块方法使用方法使用推荐根据名称动态导入模块无法导入模块示例根据条件选择不同的模块根据数据库类型动态选择数据库模块延迟导入推迟导入耗时模块直到真正需要时才导入可以加快程序启动速度只在函数被调用时导入图像处理函数仅在需要时导入只在需要处理图像时才导入处理图像使用优化内存在模块级别的类中使用限制属性提高内存效率使用优化内存的数据点类只允许这些属性计算到原点的距离包的发布与安装创建自己的包并发布到创建文件打包与上传安装打包工具构建分发包上传到安装包中的包管理提供了强大的图形界面来管理包让包的安装和管理变得简单高效使用工具在中管理包的最简单方法是使用内置的工具点击底部的标签打开包管理器在搜索框中输入要安装的包名称点击包右侧的按钮安装包已安装的包会显示在标签下可以查看版本并进行升级或卸载操作更改的源默认的源在国内访问可能较慢可以更换为国内镜像以提高下载速度在界面点击左上角的齿轮图标点击按钮添加新的软件源输入国内镜像源地址例如阿里云清华中国科技大学豆瓣使用管理包除了工具外还可以通过设置管理包进入点击按钮添加新包在弹出窗口中搜索并安装需要的包导出和导入项目依赖在团队开发中共享项目依赖非常重要提供了方便的方式来管理文件导出依赖推荐使用工具导出仅项目使用的依赖包安装在项目根目录执行提示参数会强制覆盖已存在的文件确保使用编码处理文件导入依赖在的中执行或者指定国内镜像源使用虚拟环境支持多种虚拟环境管理工具如和创建新项目时选择虚拟环境类型对于现有项目可以在中配置点击齿轮图标选择然后选择合适的虚拟环境类型虚拟环境工具对比工具优点缺点适用场景轻量级易于使用需要手动维护简单项目自动管理依赖有锁文件比慢中型团队项目同时管理版本和包占用空间大数据科学项目模块开发最佳实践模块组织相关功能放在同一个模块中单个模块不要过大保持在行以内使用子模块和子包组织复杂功能使用清晰的命名约定避免与标准库和流行第三方库冲突导入顺序遵循建议的导入顺序标准库导入相关第三方库导入本地应用库特定导入文档化模块和包为模块类和函数编写清晰的文档字符串模块名称这个模块提供了处理数据的实用函数主要功能数据清洗特征工程数据转换清洗输入的参数需要清洗的数据帧返回清洗后的数据帧示例函数实现",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-13 22:13:01",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E7%AB%A0%EF%BC%9A-%E6%A8%A1%E5%9D%97%E4%B8%8E%E5%8C%85"><span class="toc-text">第十章： 模块与包</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-%E6%A8%A1%E5%9D%97%E5%88%86%E7%B1%BB"><span class="toc-text">10.1 模块分类</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-%E6%A8%A1%E5%9D%97%E5%AF%BC%E5%85%A5%E6%96%B9%E5%BC%8F"><span class="toc-text">10.2 模块导入方式</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-1-%E5%AF%BC%E5%85%A5%E6%95%B4%E4%B8%AA%E6%A8%A1%E5%9D%97"><span class="toc-text">10.2.1 导入整个模块</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-2-%E4%BB%8E%E6%A8%A1%E5%9D%97%E5%AF%BC%E5%85%A5%E7%89%B9%E5%AE%9A%E5%86%85%E5%AE%B9"><span class="toc-text">10.2.2 从模块导入特定内容</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-3-%E5%AF%BC%E5%85%A5%E6%97%B6%E9%87%8D%E5%91%BD%E5%90%8D"><span class="toc-text">10.2.3 导入时重命名</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-4-%E5%AF%BC%E5%85%A5%E6%89%80%E6%9C%89%E5%86%85%E5%AE%B9%EF%BC%88%E4%B8%8D%E6%8E%A8%E8%8D%90%EF%BC%89"><span class="toc-text">10.2.4 导入所有内容（不推荐）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-%E6%8E%A7%E5%88%B6%E6%A8%A1%E5%9D%97%E5%AF%BC%E5%85%A5"><span class="toc-text">10.3 控制模块导入</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-%E6%A8%A1%E5%9D%97%E7%9A%84%E7%89%B9%E6%AE%8A%E5%B1%9E%E6%80%A7"><span class="toc-text">10.4 模块的特殊属性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-4-1-name-%E5%B1%9E%E6%80%A7"><span class="toc-text">10.4.1 __name__ 属性</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-4-2-From-%E6%A8%A1%E5%9D%97%E6%97%A0%E6%B3%95%E8%AF%86%E5%88%AB%E9%97%AE%E9%A2%98"><span class="toc-text">10.4.2 From 模块无法识别问题</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-%E5%8C%85%E7%9A%84%E6%A6%82%E5%BF%B5%E4%B8%8E%E4%BD%BF%E7%94%A8"><span class="toc-text">10.5 包的概念与使用</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-5-1-%E5%8C%85%E7%9A%84%E7%BB%93%E6%9E%84%E7%A4%BA%E4%BE%8B"><span class="toc-text">10.5.1 包的结构示例</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-5-2-init-py-%E6%96%87%E4%BB%B6%E7%9A%84%E4%BD%9C%E7%94%A8"><span class="toc-text">10.5.2 __init__.py 文件的作用</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-5-3-%E5%8C%85%E7%9A%84%E5%AF%BC%E5%85%A5%E6%96%B9%E5%BC%8F"><span class="toc-text">10.5.3 包的导入方式</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#10-5-3-1-%E5%AF%BC%E5%85%A5%E5%8C%85%E4%B8%AD%E7%9A%84%E6%A8%A1%E5%9D%97"><span class="toc-text">******** 导入包中的模块</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#10-5-3-2-%E5%AF%BC%E5%85%A5%E5%8C%85%E4%B8%AD%E7%89%B9%E5%AE%9A%E5%86%85%E5%AE%B9"><span class="toc-text">******** 导入包中特定内容</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-5-4-%E7%9B%B8%E5%AF%B9%E5%AF%BC%E5%85%A5%E4%B8%8E%E7%BB%9D%E5%AF%B9%E5%AF%BC%E5%85%A5"><span class="toc-text">10.5.4 相对导入与绝对导入</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#10-5-4-1-%E7%BB%9D%E5%AF%B9%E5%AF%BC%E5%85%A5"><span class="toc-text">******** 绝对导入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#10-5-4-2-%E7%9B%B8%E5%AF%B9%E5%AF%BC%E5%85%A5"><span class="toc-text">******** 相对导入</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-6-%E9%AB%98%E7%BA%A7%E5%BA%94%E7%94%A8%E6%8A%80%E5%B7%A7"><span class="toc-text">10.6 高级应用技巧</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-6-1-%E5%8A%A8%E6%80%81%E5%AF%BC%E5%85%A5"><span class="toc-text">10.6.1 动态导入</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-6-2-%E5%BB%B6%E8%BF%9F%E5%AF%BC%E5%85%A5"><span class="toc-text">10.6.2 延迟导入</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-6-3-%E4%BD%BF%E7%94%A8-slots-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98"><span class="toc-text">10.6.3 使用 __slots__ 优化内存</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-7-%E5%8C%85%E7%9A%84%E5%8F%91%E5%B8%83%E4%B8%8E%E5%AE%89%E8%A3%85"><span class="toc-text">10.7 包的发布与安装</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-7-1-%E5%88%9B%E5%BB%BA-setup-py-%E6%96%87%E4%BB%B6"><span class="toc-text">10.7.1 创建 setup.py 文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-7-2-%E6%89%93%E5%8C%85%E4%B8%8E%E4%B8%8A%E4%BC%A0"><span class="toc-text">10.7.2 打包与上传</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-7-3-%E5%AE%89%E8%A3%85%E5%8C%85"><span class="toc-text">10.7.3 安装包</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-8-PyCharm-%E4%B8%AD%E7%9A%84%E5%8C%85%E7%AE%A1%E7%90%86"><span class="toc-text">10.8 PyCharm 中的包管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-8-1-%E4%BD%BF%E7%94%A8-Python-Packages-%E5%B7%A5%E5%85%B7"><span class="toc-text">10.8.1 使用 Python Packages 工具</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-8-2-%E6%9B%B4%E6%94%B9-PyCharm-%E7%9A%84-pip-%E6%BA%90"><span class="toc-text">10.8.2 更改 PyCharm 的 pip 源</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-8-3-%E4%BD%BF%E7%94%A8-Project-Interpreter-%E7%AE%A1%E7%90%86%E5%8C%85"><span class="toc-text">10.8.3 使用 Project Interpreter 管理包</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-8-4-%E5%AF%BC%E5%87%BA%E5%92%8C%E5%AF%BC%E5%85%A5%E9%A1%B9%E7%9B%AE%E4%BE%9D%E8%B5%96"><span class="toc-text">10.8.4 导出和导入项目依赖</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#10-8-4-1-%E5%AF%BC%E5%87%BA%E4%BE%9D%E8%B5%96"><span class="toc-text">******** 导出依赖</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#10-8-4-2-%E5%AF%BC%E5%85%A5%E4%BE%9D%E8%B5%96"><span class="toc-text">******** 导入依赖</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-8-5-%E4%BD%BF%E7%94%A8%E8%99%9A%E6%8B%9F%E7%8E%AF%E5%A2%83"><span class="toc-text">10.8.5 使用虚拟环境</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#10-8-5-1-%E8%99%9A%E6%8B%9F%E7%8E%AF%E5%A2%83%E5%B7%A5%E5%85%B7%E5%AF%B9%E6%AF%94"><span class="toc-text">******** 虚拟环境工具对比</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-9-%E6%A8%A1%E5%9D%97%E5%BC%80%E5%8F%91%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5"><span class="toc-text">10.9 模块开发最佳实践</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-9-1-%E6%A8%A1%E5%9D%97%E7%BB%84%E7%BB%87"><span class="toc-text">10.9.1 模块组织</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-9-2-%E5%AF%BC%E5%85%A5%E9%A1%BA%E5%BA%8F"><span class="toc-text">10.9.2 导入顺序</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-9-3-%E6%96%87%E6%A1%A3%E5%8C%96%E6%A8%A1%E5%9D%97%E5%92%8C%E5%8C%85"><span class="toc-text">10.9.3 文档化模块和包</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Python基础知识总汇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">Python（十一）：第十章： 模块与包</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-04-18T18:13:45.000Z" title="发表于 2025-04-19 02:13:45">2025-04-19</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.526Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">2.9k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>11分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="Python（十一）：第十章： 模块与包"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/29798.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/29798.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url">Python基础知识总汇</a><h1 id="CrawlerTitle" itemprop="name headline">Python（十一）：第十章： 模块与包</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-04-18T18:13:45.000Z" title="发表于 2025-04-19 02:13:45">2025-04-19</time><time itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.526Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></header><div id="postchat_postcontent"><h2 id="第十章：-模块与包"><a href="#第十章：-模块与包" class="headerlink" title="第十章： 模块与包"></a>第十章： 模块与包</h2><p>模块是 Python 中组织代码的基本单位，本质上是一个包含 Python 定义和语句的文件。本文将深入探讨模块与包的概念、使用方法以及高级应用技巧，结合 PyCharm 中的包管理最佳实践。</p><h3 id="10-1-模块分类"><a href="#10-1-模块分类" class="headerlink" title="10.1 模块分类"></a>10.1 模块分类</h3><p>在 Python 生态系统中，模块可以分为三大类：</p><table><thead><tr><th>模块类型</th><th>说明</th><th>示例</th></tr></thead><tbody><tr><td><strong>内置模块</strong></td><td>Python 解释器自带的标准库模块</td><td>os, sys, math, datetime</td></tr><tr><td><strong>第三方模块</strong></td><td>社区开发者创建的模块</td><td>numpy, pandas, requests</td></tr><tr><td><strong>自定义模块</strong></td><td>开发者自己创建的模块</td><td>项目中自定义的.py 文件</td></tr></tbody></table><blockquote><p><strong>重要提示</strong>：首次导入自定义模块时，Python 会执行该模块中的所有顶层代码。每个模块都有自己的名称空间，模块中定义的变量属于该模块的名称空间。</p></blockquote><h3 id="10-2-模块导入方式"><a href="#10-2-模块导入方式" class="headerlink" title="10.2 模块导入方式"></a>10.2 模块导入方式</h3><h4 id="10-2-1-导入整个模块"><a href="#10-2-1-导入整个模块" class="headerlink" title="10.2.1 导入整个模块"></a>10.2.1 导入整个模块</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> module_name</span><br><span class="line"></span><br><span class="line"><span class="comment">## 使用模块中的内容</span></span><br><span class="line">module_name.function_name()</span><br><span class="line">module_name.variable_name</span><br></pre></td></tr></tbody></table></figure><h4 id="10-2-2-从模块导入特定内容"><a href="#10-2-2-从模块导入特定内容" class="headerlink" title="10.2.2 从模块导入特定内容"></a>10.2.2 从模块导入特定内容</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> module_name <span class="keyword">import</span> function_name, variable_name</span><br><span class="line"></span><br><span class="line"><span class="comment">## 直接使用，无需模块名前缀</span></span><br><span class="line">function_name()</span><br><span class="line"><span class="built_in">print</span>(variable_name)</span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>工作原理</strong>：使用 <code>from</code> 方式导入时，被导入的对象会直接引用模块中对应变量的内存地址，可以直接使用而无需模块前缀。</p></blockquote><h4 id="10-2-3-导入时重命名"><a href="#10-2-3-导入时重命名" class="headerlink" title="10.2.3 导入时重命名"></a>10.2.3 导入时重命名</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">## 模块重命名</span></span><br><span class="line"><span class="keyword">import</span> module_name <span class="keyword">as</span> alias</span><br><span class="line">alias.function_name()</span><br><span class="line"></span><br><span class="line"><span class="comment">## 函数重命名</span></span><br><span class="line"><span class="keyword">from</span> module_name <span class="keyword">import</span> function_name <span class="keyword">as</span> fn</span><br><span class="line">fn()</span><br></pre></td></tr></tbody></table></figure><h4 id="10-2-4-导入所有内容（不推荐）"><a href="#10-2-4-导入所有内容（不推荐）" class="headerlink" title="10.2.4 导入所有内容（不推荐）"></a>10.2.4 导入所有内容（不推荐）</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> module_name <span class="keyword">import</span> *</span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>注意</strong>：这种方式可能导致命名冲突，不利于代码可读性和维护性。在大型项目中应避免使用。</p></blockquote><h3 id="10-3-控制模块导入"><a href="#10-3-控制模块导入" class="headerlink" title="10.3 控制模块导入"></a>10.3 控制模块导入</h3><p>我们可以在每一个模块的 <code>__init__</code> 文件中使用如下的操作</p><p>可以使用 <code>__all__</code> 列表来控制 <code>from module import *</code> 语句导入的内容：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">## 在模块文件中定义</span></span><br><span class="line">__all__ = [<span class="string">'function1'</span>, <span class="string">'function2'</span>, <span class="string">'CONSTANT1'</span>]</span><br><span class="line"></span><br><span class="line"><span class="comment">## 不在__all__中的变量和函数，使用from module import *时不会被导入</span></span><br><span class="line">_private_variable = <span class="string">"这个变量不会被导入"</span></span><br></pre></td></tr></tbody></table></figure><h3 id="10-4-模块的特殊属性"><a href="#10-4-模块的特殊属性" class="headerlink" title="10.4 模块的特殊属性"></a>10.4 模块的特殊属性</h3><h4 id="10-4-1-name-属性"><a href="#10-4-1-name-属性" class="headerlink" title="10.4.1 __name__ 属性"></a>10.4.1 <code>__name__</code> 属性</h4><p>每个 Python 文件都有一个 <code>__name__</code> 属性：</p><ul><li>当直接运行该文件时，<code>__name__</code> 的值为 <code>'__main__'</code></li><li>当作为模块被导入时，<code>__name__</code> 的值为模块名</li></ul><p>这个特性可用于编写既可作为模块导入，又可独立运行的代码：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">## 模块内的代码</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">main_function</span>():</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"执行主函数逻辑"</span>)</span><br><span class="line">    </span><br><span class="line"><span class="keyword">def</span> <span class="title function_">helper_function</span>():</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"辅助函数"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    <span class="comment"># 这部分代码只在直接运行文件时执行</span></span><br><span class="line">    <span class="comment"># 作为模块导入时不会执行</span></span><br><span class="line">    main_function()</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"运行模块自测试..."</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="10-4-2-From-模块无法识别问题"><a href="#10-4-2-From-模块无法识别问题" class="headerlink" title="10.4.2 From 模块无法识别问题"></a>10.4.2 From 模块无法识别问题</h4><p>Python 在导入模块时会按照一定顺序搜索模块文件，在有些情况下我们自己定义的模块不一定会被检测到<br>如下列图片：</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://jsd.cdn.zzko.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250426173806981.png" alt="image-20250426173806981"></p><p>例如，当我们的模型层期望用到另外一个 <code>包</code> 的代码时，往往会这样引入：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> ecommerce_system.ecommerce.interfaces.payment <span class="keyword">import</span> PaymentProcessor</span><br><span class="line"><span class="keyword">from</span> ecommerce_system.ecommerce.interfaces.shipping <span class="keyword">import</span> ShippingMethod, Address</span><br></pre></td></tr></tbody></table></figure><p>但这样是无法被识别到的，我们应该是需要这样做：</p><ul><li>1.标记外层的包为根包</li><li>2.去掉 ecommerce_system 前缀</li></ul><p>这样 Pycharm 就会检测到我们是在这个包下进行操作的，即可识别到</p><p>从我们的根包出发，也就是图片中蓝色的包（这个是需要在 IDEA）手动标注的</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://jsd.cdn.zzko.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250426174310742.png" alt="image-20250426174310742"></p><h3 id="10-5-包的概念与使用"><a href="#10-5-包的概念与使用" class="headerlink" title="10.5 包的概念与使用"></a>10.5 包的概念与使用</h3><p>包是一种特殊的模块，它是一个包含 <code>__init__.py</code> 文件的目录，用于组织相关模块。包可以包含子包和模块，形成层次结构。</p><h4 id="10-5-1-包的结构示例"><a href="#10-5-1-包的结构示例" class="headerlink" title="10.5.1 包的结构示例"></a>10.5.1 包的结构示例</h4><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line">mypackage/</span><br><span class="line">    __init__.py     # 使目录成为包的文件</span><br><span class="line">    module1.py      # 模块1</span><br><span class="line">    module2.py      # 模块2</span><br><span class="line">    subpackage/     # 子包</span><br><span class="line">        __init__.py</span><br><span class="line">        module3.py</span><br></pre></td></tr></tbody></table></figure><h4 id="10-5-2-init-py-文件的作用"><a href="#10-5-2-init-py-文件的作用" class="headerlink" title="10.5.2 __init__.py 文件的作用"></a>10.5.2 <code>__init__.py</code> 文件的作用</h4><ol><li><strong>标识目录为包</strong>：Python 将包含 <code>__init__.py</code> 的目录视为包</li><li><strong>初始化包</strong>：在导入包时执行初始化代码</li><li><strong>定义包的公共接口</strong>：通过 <code>__all__</code> 列表指定 <code>from package import *</code> 时导入的内容</li><li><strong>自动导入子模块</strong>：可以在 <code>__init__.py</code> 中导入子模块，使它们在导入包时可用</li></ol><p><strong>示例 <code>__init__.py</code></strong>：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">## mypackage/__init__.py</span></span><br><span class="line"></span><br><span class="line"><span class="comment">## 从子模块导入主要函数，使它们在导入包时可用</span></span><br><span class="line"><span class="keyword">from</span> .module1 <span class="keyword">import</span> function1</span><br><span class="line"><span class="keyword">from</span> .module2 <span class="keyword">import</span> function2</span><br><span class="line"></span><br><span class="line"><span class="comment">## 定义包导出的符号</span></span><br><span class="line">__all__ = [<span class="string">'function1'</span>, <span class="string">'function2'</span>]</span><br><span class="line"></span><br><span class="line"><span class="comment">## 包初始化代码</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"mypackage 已加载"</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="10-5-3-包的导入方式"><a href="#10-5-3-包的导入方式" class="headerlink" title="10.5.3 包的导入方式"></a>10.5.3 包的导入方式</h4><h5 id="10-5-3-1-导入包中的模块"><a href="#10-5-3-1-导入包中的模块" class="headerlink" title="******** 导入包中的模块"></a>******** 导入包中的模块</h5><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">## 完整路径导入</span></span><br><span class="line"><span class="keyword">import</span> mypackage.module1</span><br><span class="line">mypackage.module1.function1()</span><br><span class="line"></span><br><span class="line"><span class="comment">## 导入特定模块</span></span><br><span class="line"><span class="keyword">from</span> mypackage <span class="keyword">import</span> module1</span><br><span class="line">module1.function1()</span><br><span class="line"></span><br><span class="line"><span class="comment">## 导入子包中的模块</span></span><br><span class="line"><span class="keyword">from</span> mypackage.subpackage <span class="keyword">import</span> module3</span><br><span class="line">module3.function3()</span><br></pre></td></tr></tbody></table></figure><h5 id="10-5-3-2-导入包中特定内容"><a href="#10-5-3-2-导入包中特定内容" class="headerlink" title="******** 导入包中特定内容"></a>******** 导入包中特定内容</h5><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> mypackage.module1 <span class="keyword">import</span> function1</span><br><span class="line">function1()</span><br></pre></td></tr></tbody></table></figure><h4 id="10-5-4-相对导入与绝对导入"><a href="#10-5-4-相对导入与绝对导入" class="headerlink" title="10.5.4 相对导入与绝对导入"></a>10.5.4 相对导入与绝对导入</h4><h5 id="10-5-4-1-绝对导入"><a href="#10-5-4-1-绝对导入" class="headerlink" title="******** 绝对导入"></a>******** 绝对导入</h5><p>从项目的顶级包开始导入：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> package_name.module_name <span class="keyword">import</span> function_name</span><br></pre></td></tr></tbody></table></figure><h5 id="10-5-4-2-相对导入"><a href="#10-5-4-2-相对导入" class="headerlink" title="******** 相对导入"></a>******** 相对导入</h5><p>使用点号表示相对位置：</p><ul><li><code>.module_name</code>：当前包中的模块</li><li><code>..module_name</code>：父包中的模块</li><li><code>...module_name</code>：祖父包中的模块</li></ul><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">## 在mypackage.subpackage.module3中导入同级模块</span></span><br><span class="line"><span class="keyword">from</span> . <span class="keyword">import</span> another_module  <span class="comment"># 导入同级模块</span></span><br><span class="line"></span><br><span class="line"><span class="comment">## 导入父包中的模块</span></span><br><span class="line"><span class="keyword">from</span> .. <span class="keyword">import</span> module1  <span class="comment"># 导入父包中的模块</span></span><br><span class="line"></span><br><span class="line"><span class="comment">## 导入父包中模块的特定函数</span></span><br><span class="line"><span class="keyword">from</span> ..module2 <span class="keyword">import</span> function2</span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>注意</strong>：相对导入只能在包内使用，不能在顶级模块中使用。相对导入基于当前模块的 <code>__name__</code> 属性，而直接运行的脚本的 <code>__name__</code> 总是 <code>'__main__'</code>。</p></blockquote><h3 id="10-6-高级应用技巧"><a href="#10-6-高级应用技巧" class="headerlink" title="10.6 高级应用技巧"></a>10.6 高级应用技巧</h3><h4 id="10-6-1-动态导入"><a href="#10-6-1-动态导入" class="headerlink" title="10.6.1 动态导入"></a>10.6.1 动态导入</h4><p>在运行时根据条件动态导入模块：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">## 方法1：使用__import__</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">import_module_by_name</span>(<span class="params">module_name</span>):</span><br><span class="line">    <span class="keyword">return</span> <span class="built_in">__import__</span>(module_name)</span><br><span class="line"></span><br><span class="line"><span class="comment">## 方法2：使用importlib（推荐）</span></span><br><span class="line"><span class="keyword">import</span> importlib</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">get_module</span>(<span class="params">module_name</span>):</span><br><span class="line">    <span class="string">"""根据名称动态导入模块"""</span></span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="keyword">return</span> importlib.import_module(module_name)</span><br><span class="line">    <span class="keyword">except</span> ImportError <span class="keyword">as</span> e:</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"无法导入模块 <span class="subst">{module_name}</span>: <span class="subst">{e}</span>"</span>)</span><br><span class="line">        <span class="keyword">return</span> <span class="literal">None</span></span><br><span class="line"></span><br><span class="line"><span class="comment">## 示例：根据条件选择不同的模块</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">get_database_module</span>(<span class="params">db_type</span>):</span><br><span class="line">    <span class="string">"""根据数据库类型动态选择数据库模块"""</span></span><br><span class="line">    <span class="keyword">if</span> db_type.lower() == <span class="string">'mysql'</span>:</span><br><span class="line">        <span class="keyword">return</span> importlib.import_module(<span class="string">'mysql.connector'</span>)</span><br><span class="line">    <span class="keyword">elif</span> db_type.lower() == <span class="string">'postgresql'</span>:</span><br><span class="line">        <span class="keyword">return</span> importlib.import_module(<span class="string">'psycopg2'</span>)</span><br><span class="line">    <span class="keyword">else</span>:</span><br><span class="line">        <span class="keyword">return</span> importlib.import_module(<span class="string">'sqlite3'</span>)</span><br></pre></td></tr></tbody></table></figure><h4 id="10-6-2-延迟导入"><a href="#10-6-2-延迟导入" class="headerlink" title="10.6.2 延迟导入"></a>10.6.2 延迟导入</h4><p>推迟导入耗时模块，直到真正需要时才导入，可以加快程序启动速度：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">def</span> <span class="title function_">function_that_needs_numpy</span>():</span><br><span class="line">    <span class="string">"""只在函数被调用时导入numpy"""</span></span><br><span class="line">    <span class="keyword">import</span> numpy <span class="keyword">as</span> np</span><br><span class="line">    <span class="keyword">return</span> np.array([<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>])</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">process_image</span>(<span class="params">image_path</span>):</span><br><span class="line">    <span class="string">"""图像处理函数，仅在需要时导入PIL"""</span></span><br><span class="line">    <span class="comment"># 只在需要处理图像时才导入PIL</span></span><br><span class="line">    <span class="keyword">from</span> PIL <span class="keyword">import</span> Image</span><br><span class="line">    </span><br><span class="line">    img = Image.<span class="built_in">open</span>(image_path)</span><br><span class="line">    <span class="comment"># 处理图像...</span></span><br><span class="line">    <span class="keyword">return</span> img</span><br></pre></td></tr></tbody></table></figure><h4 id="10-6-3-使用-slots-优化内存"><a href="#10-6-3-使用-slots-优化内存" class="headerlink" title="10.6.3 使用 __slots__ 优化内存"></a>10.6.3 使用 <code>__slots__</code> 优化内存</h4><p>在模块级别的类中使用 <code>__slots__</code> 限制属性，提高内存效率：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">class</span> <span class="title class_">DataPoint</span>:</span><br><span class="line">    <span class="string">"""使用__slots__优化内存的数据点类"""</span></span><br><span class="line">    __slots__ = [<span class="string">'x'</span>, <span class="string">'y'</span>, <span class="string">'z'</span>]  <span class="comment"># 只允许这些属性</span></span><br><span class="line">    </span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__init__</span>(<span class="params">self, x, y, z</span>):</span><br><span class="line">        <span class="variable language_">self</span>.x = x</span><br><span class="line">        <span class="variable language_">self</span>.y = y</span><br><span class="line">        <span class="variable language_">self</span>.z = z</span><br><span class="line">        </span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">distance_from_origin</span>(<span class="params">self</span>):</span><br><span class="line">        <span class="string">"""计算到原点的距离"""</span></span><br><span class="line">        <span class="keyword">return</span> (<span class="variable language_">self</span>.x**<span class="number">2</span> + <span class="variable language_">self</span>.y**<span class="number">2</span> + <span class="variable language_">self</span>.z**<span class="number">2</span>) ** <span class="number">0.5</span></span><br></pre></td></tr></tbody></table></figure><h3 id="10-7-包的发布与安装"><a href="#10-7-包的发布与安装" class="headerlink" title="10.7 包的发布与安装"></a>10.7 包的发布与安装</h3><p>创建自己的包并发布到 PyPI：</p><h4 id="10-7-1-创建-setup-py-文件"><a href="#10-7-1-创建-setup-py-文件" class="headerlink" title="10.7.1 创建 setup.py 文件"></a>10.7.1 创建 <code>setup.py</code> 文件</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> setuptools <span class="keyword">import</span> setup, find_packages</span><br><span class="line"></span><br><span class="line">setup(</span><br><span class="line">    name=<span class="string">"mypackage"</span>,</span><br><span class="line">    version=<span class="string">"0.1.0"</span>,</span><br><span class="line">    author=<span class="string">"Your Name"</span>,</span><br><span class="line">    author_email=<span class="string">"<EMAIL>"</span>,</span><br><span class="line">    description=<span class="string">"A short description of the package"</span>,</span><br><span class="line">    long_description=<span class="built_in">open</span>(<span class="string">"README.md"</span>).read(),</span><br><span class="line">    long_description_content_type=<span class="string">"text/markdown"</span>,</span><br><span class="line">    url=<span class="string">"https://github.com/yourusername/mypackage"</span>,</span><br><span class="line">    packages=find_packages(),</span><br><span class="line">    classifiers=[</span><br><span class="line">        <span class="string">"Programming Language :: Python :: 3"</span>,</span><br><span class="line">        <span class="string">"License :: OSI Approved :: MIT License"</span>,</span><br><span class="line">        <span class="string">"Operating System :: OS Independent"</span>,</span><br><span class="line">    ],</span><br><span class="line">    python_requires=<span class="string">'&gt;=3.6'</span>,</span><br><span class="line">    install_requires=[</span><br><span class="line">        <span class="string">'dependency1&gt;=1.0.0'</span>,</span><br><span class="line">        <span class="string">'dependency2&gt;=2.0.0'</span>,</span><br><span class="line">    ],</span><br><span class="line">)</span><br></pre></td></tr></tbody></table></figure><h4 id="10-7-2-打包与上传"><a href="#10-7-2-打包与上传" class="headerlink" title="10.7.2 打包与上传"></a>10.7.2 打包与上传</h4><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">## 安装打包工具</span></span><br><span class="line">pip install --upgrade setuptools wheel twine</span><br><span class="line"></span><br><span class="line"><span class="comment">## 构建分发包</span></span><br><span class="line">python setup.py sdist bdist_wheel</span><br><span class="line"></span><br><span class="line"><span class="comment">## 上传到PyPI</span></span><br><span class="line">twine upload dist/*</span><br></pre></td></tr></tbody></table></figure><h4 id="10-7-3-安装包"><a href="#10-7-3-安装包" class="headerlink" title="10.7.3 安装包"></a>10.7.3 安装包</h4><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">pip install mypackage</span><br></pre></td></tr></tbody></table></figure><h3 id="10-8-PyCharm-中的包管理"><a href="#10-8-PyCharm-中的包管理" class="headerlink" title="10.8 PyCharm 中的包管理"></a>10.8 PyCharm 中的包管理</h3><p>PyCharm 提供了强大的图形界面来管理 Python 包，让包的安装和管理变得简单高效。</p><h4 id="10-8-1-使用-Python-Packages-工具"><a href="#10-8-1-使用-Python-Packages-工具" class="headerlink" title="10.8.1 使用 Python Packages 工具"></a>10.8.1 使用 Python Packages 工具</h4><p>在 PyCharm 中管理包的最简单方法是使用内置的 Python Packages 工具：</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i-blog.csdnimg.cn/direct/00b70a4bd0534549b0bb79bdfdd75d3f.png" alt="PyCharm Python Packages 界面"></p><ol><li>点击底部的 <strong>Python Packages</strong> 标签打开包管理器</li><li>在搜索框中输入要安装的包名称</li><li>点击包右侧的 <strong>Install</strong> 按钮安装包</li><li>已安装的包会显示在 <strong>Installed</strong> 标签下，可以查看版本并进行升级或卸载操作</li></ol><h4 id="10-8-2-更改-PyCharm-的-pip-源"><a href="#10-8-2-更改-PyCharm-的-pip-源" class="headerlink" title="10.8.2 更改 PyCharm 的 pip 源"></a>10.8.2 更改 PyCharm 的 pip 源</h4><p>默认的 PyPI 源在国内访问可能较慢，可以更换为国内镜像以提高下载速度：</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://img2023.cnblogs.com/blog/1756102/202306/1756102-20230620182228379-**********.png" alt="PyCharm 更改 pip 源"></p><ol><li>在 Python Packages 界面点击左上角的齿轮图标</li><li>点击 “+” 按钮添加新的软件源</li><li>输入国内镜像源地址，例如：<ul><li>阿里云：<a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly9taXJyb3JzLmFsaXl1bi5jb20vcHlwaS9zaW1wbGUv">https://mirrors.aliyun.com/pypi/simple/</a></li><li>清华：<a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly9weXBpLnR1bmEudHNpbmdodWEuZWR1LmNuL3NpbXBsZS8">https://pypi.tuna.tsinghua.edu.cn/simple/</a></li><li>中国科技大学：<a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cHM6Ly9weXBpLm1pcnJvcnMudXN0Yy5lZHUuY24vc2ltcGxlLw">https://pypi.mirrors.ustc.edu.cn/simple/</a></li><li>豆瓣：<a target="_blank" rel="external nofollow noopener noreferrer" href="/go.html?u=aHR0cDovL3B5cGkuZG91YmFuLmNvbS9zaW1wbGUv">http://pypi.douban.com/simple/</a></li></ul></li></ol><h4 id="10-8-3-使用-Project-Interpreter-管理包"><a href="#10-8-3-使用-Project-Interpreter-管理包" class="headerlink" title="10.8.3 使用 Project Interpreter 管理包"></a>10.8.3 使用 Project Interpreter 管理包</h4><p>除了 Python Packages 工具外，还可以通过 Project Interpreter 设置管理包：</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i-blog.csdnimg.cn/blog_migrate/9bd22f4d41e33fbc711d1c53a17c8892.png" alt="PyCharm Project Interpreter"></p><ol><li>进入 <strong>File &gt; Settings &gt; Project &gt; Python Interpreter</strong></li><li>点击 “+” 按钮添加新包</li><li>在弹出窗口中搜索并安装需要的包</li></ol><h4 id="10-8-4-导出和导入项目依赖"><a href="#10-8-4-导出和导入项目依赖" class="headerlink" title="10.8.4 导出和导入项目依赖"></a>10.8.4 导出和导入项目依赖</h4><p>在团队开发中，共享项目依赖非常重要。PyCharm 提供了方便的方式来管理 requirements.txt 文件：</p><h5 id="10-8-4-1-导出依赖"><a href="#10-8-4-1-导出依赖" class="headerlink" title="******** 导出依赖"></a>******** 导出依赖</h5><p>推荐使用 pipreqs 工具导出仅项目使用的依赖包：</p><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">## 安装pipreqs</span></span><br><span class="line">pip install pipreqs</span><br><span class="line"></span><br><span class="line"><span class="comment">## 在项目根目录执行</span></span><br><span class="line">pipreqs . --encoding=utf8 --force</span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>提示</strong>：<code>--force</code> 参数会强制覆盖已存在的 requirements.txt 文件，<code>--encoding=utf8</code> 确保使用 UTF-8 编码处理文件。</p></blockquote><h5 id="10-8-4-2-导入依赖"><a href="#10-8-4-2-导入依赖" class="headerlink" title="******** 导入依赖"></a>******** 导入依赖</h5><p>在 PyCharm 的 Terminal 中执行：</p><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">pip install -r requirements.txt</span><br></pre></td></tr></tbody></table></figure><p>或者指定国内镜像源：</p><figure class="highlight bash"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/</span><br></pre></td></tr></tbody></table></figure><h4 id="10-8-5-使用虚拟环境"><a href="#10-8-5-使用虚拟环境" class="headerlink" title="10.8.5 使用虚拟环境"></a>10.8.5 使用虚拟环境</h4><p>PyCharm 支持多种虚拟环境管理工具，如 Virtualenv、Pipenv 和 Conda：</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://i-blog.csdnimg.cn/blog_migrate/5e33e43c899366b78dfd0b1f7bc395c7.png" alt="PyCharm 虚拟环境设置"></p><ol><li>创建新项目时选择虚拟环境类型</li><li>对于现有项目，可以在 <strong>File &gt; Settings &gt; Project &gt; Python Interpreter</strong> 中配置</li><li>点击齿轮图标，选择 “Add…”，然后选择合适的虚拟环境类型</li></ol><h5 id="10-8-5-1-虚拟环境工具对比"><a href="#10-8-5-1-虚拟环境工具对比" class="headerlink" title="******** 虚拟环境工具对比"></a>******** 虚拟环境工具对比</h5><table><thead><tr><th>工具</th><th>优点</th><th>缺点</th><th>适用场景</th></tr></thead><tbody><tr><td>Virtualenv</td><td>轻量级，易于使用</td><td>需要手动维护 requirements.txt</td><td>简单项目</td></tr><tr><td>Pipenv</td><td>自动管理依赖，有锁文件</td><td>比 Virtualenv 慢</td><td>中型团队项目</td></tr><tr><td>Conda</td><td>同时管理 Python 版本和包</td><td>占用空间大</td><td>数据科学项目</td></tr></tbody></table><h3 id="10-9-模块开发最佳实践"><a href="#10-9-模块开发最佳实践" class="headerlink" title="10.9 模块开发最佳实践"></a>10.9 模块开发最佳实践</h3><h4 id="10-9-1-模块组织"><a href="#10-9-1-模块组织" class="headerlink" title="10.9.1 模块组织"></a>10.9.1 模块组织</h4><ul><li>相关功能放在同一个模块中</li><li>单个模块不要过大，保持在 1000 行以内</li><li>使用子模块和子包组织复杂功能</li><li>使用清晰的命名约定，避免与标准库和流行第三方库冲突</li></ul><h4 id="10-9-2-导入顺序"><a href="#10-9-2-导入顺序" class="headerlink" title="10.9.2 导入顺序"></a>10.9.2 导入顺序</h4><p>遵循 PEP8 建议的导入顺序：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">## 1. 标准库导入</span></span><br><span class="line"><span class="keyword">import</span> os</span><br><span class="line"><span class="keyword">import</span> sys</span><br><span class="line"></span><br><span class="line"><span class="comment">## 2. 相关第三方库导入</span></span><br><span class="line"><span class="keyword">import</span> numpy <span class="keyword">as</span> np</span><br><span class="line"><span class="keyword">import</span> pandas <span class="keyword">as</span> pd</span><br><span class="line"></span><br><span class="line"><span class="comment">## 3. 本地应用/库特定导入</span></span><br><span class="line"><span class="keyword">from</span> mypackage <span class="keyword">import</span> module1</span><br><span class="line"><span class="keyword">from</span> .utils <span class="keyword">import</span> helper_function</span><br></pre></td></tr></tbody></table></figure><h4 id="10-9-3-文档化模块和包"><a href="#10-9-3-文档化模块和包" class="headerlink" title="10.9.3 文档化模块和包"></a>10.9.3 文档化模块和包</h4><p>为模块、类和函数编写清晰的文档字符串：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br></pre></td><td class="code"><pre><span class="line"><span class="string">"""</span></span><br><span class="line"><span class="string">模块名称: data_processing</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">这个模块提供了处理数据的实用函数。</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">主要功能:</span></span><br><span class="line"><span class="string">    * 数据清洗</span></span><br><span class="line"><span class="string">    * 特征工程</span></span><br><span class="line"><span class="string">    * 数据转换</span></span><br><span class="line"><span class="string">"""</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">clean_data</span>(<span class="params">df</span>):</span><br><span class="line">    <span class="string">"""</span></span><br><span class="line"><span class="string">    清洗输入的DataFrame。</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    参数:</span></span><br><span class="line"><span class="string">        df (pandas.DataFrame): 需要清洗的数据帧</span></span><br><span class="line"><span class="string">        </span></span><br><span class="line"><span class="string">    返回:</span></span><br><span class="line"><span class="string">        pandas.DataFrame: 清洗后的数据帧</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    示例:</span></span><br><span class="line"><span class="string">        &gt;&gt;&gt; import pandas as pd</span></span><br><span class="line"><span class="string">        &gt;&gt;&gt; df = pd.DataFrame({'A': [1, None, 3], 'B': [4, 5, None]})</span></span><br><span class="line"><span class="string">        &gt;&gt;&gt; clean_data(df)</span></span><br><span class="line"><span class="string">           A    B</span></span><br><span class="line"><span class="string">        0  1.0  4.0</span></span><br><span class="line"><span class="string">        2  3.0  5.0</span></span><br><span class="line"><span class="string">    """</span></span><br><span class="line">    <span class="comment"># 函数实现...</span></span><br></pre></td></tr></tbody></table></figure></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/29798.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/29798.html&quot;)">Python（十一）：第十章： 模块与包</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/29798.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/29798.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Python<span class="categoryesPageCount">22</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Python基础知识总汇<span class="tagsPageCount">22</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/15831.html"><img class="prev-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Python（十）：第九章：迭代器、生成器与推导式</div></div></a></div><div class="next-post pull-right"><a href="/posts/52396.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Python（十二）：第十一章：面向对象编程</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/45310.html" title="Python（七）：第六章：条件循环分支"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（七）：第六章：条件循环分支</div></div></a></div><div><a href="/posts/8019.html" title="Python（三）：第二章：转义字符"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（三）：第二章：转义字符</div></div></a></div><div><a href="/posts/56572.html" title="Python（九）：第八章： 函数知识总结"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（九）：第八章： 函数知识总结</div></div></a></div><div><a href="/posts/55902.html" title="Python（二十一）：第二十章：Python 语法新特性总结"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（二十一）：第二十章：Python 语法新特性总结</div></div></a></div><div><a href="/posts/2501.html" title="Python（二）：第一章：字符串打印格式化与PyCharm模板变量"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（二）：第一章：字符串打印格式化与PyCharm模板变量</div></div></a></div><div><a href="/posts/43091.html" title="Python（二十二）：第二十一章：项目结构规范与最佳实践"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（二十二）：第二十一章：项目结构规范与最佳实践</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"Python（十一）：第十章： 模块与包",date:"2025-04-19 02:13:45",updated:"2025-07-13 22:13:01",tags:["Python基础知识总汇"],categories:["后端技术","Python"],content:'\n## 第十章： 模块与包\n\n模块是 Python 中组织代码的基本单位，本质上是一个包含 Python 定义和语句的文件。本文将深入探讨模块与包的概念、使用方法以及高级应用技巧，结合 PyCharm 中的包管理最佳实践。\n\n### 10.1 模块分类\n\n在 Python 生态系统中，模块可以分为三大类：\n\n| 模块类型       | 说明                          | 示例                    |\n| -------------- | ----------------------------- | ----------------------- |\n| **内置模块**   | Python 解释器自带的标准库模块 | os, sys, math, datetime |\n| **第三方模块** | 社区开发者创建的模块          | numpy, pandas, requests |\n| **自定义模块** | 开发者自己创建的模块          | 项目中自定义的.py 文件  |\n\n> **重要提示**：首次导入自定义模块时，Python 会执行该模块中的所有顶层代码。每个模块都有自己的名称空间，模块中定义的变量属于该模块的名称空间。\n\n### 10.2 模块导入方式\n\n#### 10.2.1 导入整个模块\n\n```python\nimport module_name\n\n## 使用模块中的内容\nmodule_name.function_name()\nmodule_name.variable_name\n```\n\n#### 10.2.2 从模块导入特定内容\n\n```python\nfrom module_name import function_name, variable_name\n\n## 直接使用，无需模块名前缀\nfunction_name()\nprint(variable_name)\n```\n\n> **工作原理**：使用 `from` 方式导入时，被导入的对象会直接引用模块中对应变量的内存地址，可以直接使用而无需模块前缀。\n\n#### 10.2.3 导入时重命名\n\n```python\n## 模块重命名\nimport module_name as alias\nalias.function_name()\n\n## 函数重命名\nfrom module_name import function_name as fn\nfn()\n```\n\n#### 10.2.4 导入所有内容（不推荐）\n\n```python\nfrom module_name import *\n```\n\n> **注意**：这种方式可能导致命名冲突，不利于代码可读性和维护性。在大型项目中应避免使用。\n\n### 10.3 控制模块导入\n\n我们可以在每一个模块的 `__init__` 文件中使用如下的操作\n\n可以使用 `__all__` 列表来控制 `from module import *` 语句导入的内容：\n\n```python\n## 在模块文件中定义\n__all__ = [\'function1\', \'function2\', \'CONSTANT1\']\n\n## 不在__all__中的变量和函数，使用from module import *时不会被导入\n_private_variable = "这个变量不会被导入"\n```\n\n### 10.4 模块的特殊属性\n\n#### 10.4.1 `__name__` 属性\n\n每个 Python 文件都有一个 `__name__` 属性：\n- 当直接运行该文件时，`__name__` 的值为 `\'__main__\'`\n- 当作为模块被导入时，`__name__` 的值为模块名\n\n这个特性可用于编写既可作为模块导入，又可独立运行的代码：\n\n```python\n## 模块内的代码\ndef main_function():\n    print("执行主函数逻辑")\n    \ndef helper_function():\n    print("辅助函数")\n\nif __name__ == \'__main__\':\n    # 这部分代码只在直接运行文件时执行\n    # 作为模块导入时不会执行\n    main_function()\n    print("运行模块自测试...")\n```\n\n#### 10.4.2 From 模块无法识别问题\n\nPython 在导入模块时会按照一定顺序搜索模块文件，在有些情况下我们自己定义的模块不一定会被检测到\n如下列图片：\n\n![image-20250426173806981](https://jsd.cdn.zzko.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250426173806981.png)\n\n例如，当我们的模型层期望用到另外一个 `包` 的代码时，往往会这样引入：\n\n```python\nfrom ecommerce_system.ecommerce.interfaces.payment import PaymentProcessor\nfrom ecommerce_system.ecommerce.interfaces.shipping import ShippingMethod, Address\n```\n\n但这样是无法被识别到的，我们应该是需要这样做：\n\n- 1.标记外层的包为根包\n- 2.去掉 ecommerce_system 前缀\n\n这样 Pycharm 就会检测到我们是在这个包下进行操作的，即可识别到\n\n从我们的根包出发，也就是图片中蓝色的包（这个是需要在 IDEA）手动标注的\n\n![image-20250426174310742](https://jsd.cdn.zzko.cn/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250426174310742.png)\n\n### 10.5 包的概念与使用\n\n包是一种特殊的模块，它是一个包含 `__init__.py` 文件的目录，用于组织相关模块。包可以包含子包和模块，形成层次结构。\n\n#### 10.5.1 包的结构示例\n\n```\nmypackage/\n    __init__.py     # 使目录成为包的文件\n    module1.py      # 模块1\n    module2.py      # 模块2\n    subpackage/     # 子包\n        __init__.py\n        module3.py\n```\n\n#### 10.5.2 `__init__.py` 文件的作用\n\n1. **标识目录为包**：Python 将包含 `__init__.py` 的目录视为包\n2. **初始化包**：在导入包时执行初始化代码\n3. **定义包的公共接口**：通过 `__all__` 列表指定 `from package import *` 时导入的内容\n4. **自动导入子模块**：可以在 `__init__.py` 中导入子模块，使它们在导入包时可用\n\n**示例 `__init__.py`**：\n\n```python\n## mypackage/__init__.py\n\n## 从子模块导入主要函数，使它们在导入包时可用\nfrom .module1 import function1\nfrom .module2 import function2\n\n## 定义包导出的符号\n__all__ = [\'function1\', \'function2\']\n\n## 包初始化代码\nprint("mypackage 已加载")\n```\n\n#### 10.5.3 包的导入方式\n\n##### ******** 导入包中的模块\n\n```python\n## 完整路径导入\nimport mypackage.module1\nmypackage.module1.function1()\n\n## 导入特定模块\nfrom mypackage import module1\nmodule1.function1()\n\n## 导入子包中的模块\nfrom mypackage.subpackage import module3\nmodule3.function3()\n```\n\n##### ******** 导入包中特定内容\n\n```python\nfrom mypackage.module1 import function1\nfunction1()\n```\n\n#### 10.5.4 相对导入与绝对导入\n\n##### ******** 绝对导入\n\n从项目的顶级包开始导入：\n\n```python\nfrom package_name.module_name import function_name\n```\n\n##### ******** 相对导入\n\n使用点号表示相对位置：\n- `.module_name`：当前包中的模块\n- `..module_name`：父包中的模块\n- `...module_name`：祖父包中的模块\n\n```python\n## 在mypackage.subpackage.module3中导入同级模块\nfrom . import another_module  # 导入同级模块\n\n## 导入父包中的模块\nfrom .. import module1  # 导入父包中的模块\n\n## 导入父包中模块的特定函数\nfrom ..module2 import function2\n```\n\n> **注意**：相对导入只能在包内使用，不能在顶级模块中使用。相对导入基于当前模块的 `__name__` 属性，而直接运行的脚本的 `__name__` 总是 `\'__main__\'`。\n\n### 10.6 高级应用技巧\n\n#### 10.6.1 动态导入\n\n在运行时根据条件动态导入模块：\n\n```python\n## 方法1：使用__import__\ndef import_module_by_name(module_name):\n    return __import__(module_name)\n\n## 方法2：使用importlib（推荐）\nimport importlib\n\ndef get_module(module_name):\n    """根据名称动态导入模块"""\n    try:\n        return importlib.import_module(module_name)\n    except ImportError as e:\n        print(f"无法导入模块 {module_name}: {e}")\n        return None\n\n## 示例：根据条件选择不同的模块\ndef get_database_module(db_type):\n    """根据数据库类型动态选择数据库模块"""\n    if db_type.lower() == \'mysql\':\n        return importlib.import_module(\'mysql.connector\')\n    elif db_type.lower() == \'postgresql\':\n        return importlib.import_module(\'psycopg2\')\n    else:\n        return importlib.import_module(\'sqlite3\')\n```\n\n#### 10.6.2 延迟导入\n\n推迟导入耗时模块，直到真正需要时才导入，可以加快程序启动速度：\n\n```python\ndef function_that_needs_numpy():\n    """只在函数被调用时导入numpy"""\n    import numpy as np\n    return np.array([1, 2, 3])\n\ndef process_image(image_path):\n    """图像处理函数，仅在需要时导入PIL"""\n    # 只在需要处理图像时才导入PIL\n    from PIL import Image\n    \n    img = Image.open(image_path)\n    # 处理图像...\n    return img\n```\n\n#### 10.6.3 使用 `__slots__` 优化内存\n\n在模块级别的类中使用 `__slots__` 限制属性，提高内存效率：\n\n```python\nclass DataPoint:\n    """使用__slots__优化内存的数据点类"""\n    __slots__ = [\'x\', \'y\', \'z\']  # 只允许这些属性\n    \n    def __init__(self, x, y, z):\n        self.x = x\n        self.y = y\n        self.z = z\n        \n    def distance_from_origin(self):\n        """计算到原点的距离"""\n        return (self.x**2 + self.y**2 + self.z**2) ** 0.5\n```\n\n### 10.7 包的发布与安装\n\n创建自己的包并发布到 PyPI：\n\n#### 10.7.1 创建 `setup.py` 文件\n\n```python\nfrom setuptools import setup, find_packages\n\nsetup(\n    name="mypackage",\n    version="0.1.0",\n    author="Your Name",\n    author_email="<EMAIL>",\n    description="A short description of the package",\n    long_description=open("README.md").read(),\n    long_description_content_type="text/markdown",\n    url="https://github.com/yourusername/mypackage",\n    packages=find_packages(),\n    classifiers=[\n        "Programming Language :: Python :: 3",\n        "License :: OSI Approved :: MIT License",\n        "Operating System :: OS Independent",\n    ],\n    python_requires=\'>=3.6\',\n    install_requires=[\n        \'dependency1>=1.0.0\',\n        \'dependency2>=2.0.0\',\n    ],\n)\n```\n\n#### 10.7.2 打包与上传\n\n```bash\n## 安装打包工具\npip install --upgrade setuptools wheel twine\n\n## 构建分发包\npython setup.py sdist bdist_wheel\n\n## 上传到PyPI\ntwine upload dist/*\n```\n\n#### 10.7.3 安装包\n\n```bash\npip install mypackage\n```\n\n### 10.8 PyCharm 中的包管理\n\nPyCharm 提供了强大的图形界面来管理 Python 包，让包的安装和管理变得简单高效。\n\n#### 10.8.1 使用 Python Packages 工具\n\n在 PyCharm 中管理包的最简单方法是使用内置的 Python Packages 工具：\n\n![PyCharm Python Packages 界面](https://i-blog.csdnimg.cn/direct/00b70a4bd0534549b0bb79bdfdd75d3f.png)\n\n1. 点击底部的 **Python Packages** 标签打开包管理器\n2. 在搜索框中输入要安装的包名称\n3. 点击包右侧的 **Install** 按钮安装包\n4. 已安装的包会显示在 **Installed** 标签下，可以查看版本并进行升级或卸载操作\n\n#### 10.8.2 更改 PyCharm 的 pip 源\n\n默认的 PyPI 源在国内访问可能较慢，可以更换为国内镜像以提高下载速度：\n\n![PyCharm 更改 pip 源](https://img2023.cnblogs.com/blog/1756102/202306/1756102-20230620182228379-**********.png)\n\n1. 在 Python Packages 界面点击左上角的齿轮图标\n2. 点击 "+" 按钮添加新的软件源\n3. 输入国内镜像源地址，例如：\n   - 阿里云：https://mirrors.aliyun.com/pypi/simple/\n   - 清华：https://pypi.tuna.tsinghua.edu.cn/simple/\n   - 中国科技大学：https://pypi.mirrors.ustc.edu.cn/simple/\n   - 豆瓣：http://pypi.douban.com/simple/\n\n#### 10.8.3 使用 Project Interpreter 管理包\n\n除了 Python Packages 工具外，还可以通过 Project Interpreter 设置管理包：\n\n![PyCharm Project Interpreter](https://i-blog.csdnimg.cn/blog_migrate/9bd22f4d41e33fbc711d1c53a17c8892.png)\n\n1. 进入 **File > Settings > Project > Python Interpreter**\n2. 点击 "+" 按钮添加新包\n3. 在弹出窗口中搜索并安装需要的包\n\n#### 10.8.4 导出和导入项目依赖\n\n在团队开发中，共享项目依赖非常重要。PyCharm 提供了方便的方式来管理 requirements.txt 文件：\n\n##### ******** 导出依赖\n\n推荐使用 pipreqs 工具导出仅项目使用的依赖包：\n\n```bash\n## 安装pipreqs\npip install pipreqs\n\n## 在项目根目录执行\npipreqs . --encoding=utf8 --force\n```\n\n> **提示**：`--force` 参数会强制覆盖已存在的 requirements.txt 文件，`--encoding=utf8` 确保使用 UTF-8 编码处理文件。\n\n##### ******** 导入依赖\n\n在 PyCharm 的 Terminal 中执行：\n\n```bash\npip install -r requirements.txt\n```\n\n或者指定国内镜像源：\n\n```bash\npip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/\n```\n\n#### 10.8.5 使用虚拟环境\n\nPyCharm 支持多种虚拟环境管理工具，如 Virtualenv、Pipenv 和 Conda：\n\n![PyCharm 虚拟环境设置](https://i-blog.csdnimg.cn/blog_migrate/5e33e43c899366b78dfd0b1f7bc395c7.png)\n\n1. 创建新项目时选择虚拟环境类型\n2. 对于现有项目，可以在 **File > Settings > Project > Python Interpreter** 中配置\n3. 点击齿轮图标，选择 "Add..."，然后选择合适的虚拟环境类型\n\n##### ******** 虚拟环境工具对比\n\n| 工具       | 优点                     | 缺点                          | 适用场景     |\n| ---------- | ------------------------ | ----------------------------- | ------------ |\n| Virtualenv | 轻量级，易于使用         | 需要手动维护 requirements.txt | 简单项目     |\n| Pipenv     | 自动管理依赖，有锁文件   | 比 Virtualenv 慢              | 中型团队项目 |\n| Conda      | 同时管理 Python 版本和包 | 占用空间大                    | 数据科学项目 |\n\n### 10.9 模块开发最佳实践\n\n#### 10.9.1 模块组织\n\n- 相关功能放在同一个模块中\n- 单个模块不要过大，保持在 1000 行以内\n- 使用子模块和子包组织复杂功能\n- 使用清晰的命名约定，避免与标准库和流行第三方库冲突\n\n#### 10.9.2 导入顺序\n\n遵循 PEP8 建议的导入顺序：\n\n```python\n## 1. 标准库导入\nimport os\nimport sys\n\n## 2. 相关第三方库导入\nimport numpy as np\nimport pandas as pd\n\n## 3. 本地应用/库特定导入\nfrom mypackage import module1\nfrom .utils import helper_function\n```\n\n#### 10.9.3 文档化模块和包\n\n为模块、类和函数编写清晰的文档字符串：\n\n```python\n"""\n模块名称: data_processing\n\n这个模块提供了处理数据的实用函数。\n\n主要功能:\n    * 数据清洗\n    * 特征工程\n    * 数据转换\n"""\n\ndef clean_data(df):\n    """\n    清洗输入的DataFrame。\n    \n    参数:\n        df (pandas.DataFrame): 需要清洗的数据帧\n        \n    返回:\n        pandas.DataFrame: 清洗后的数据帧\n    \n    示例:\n        >>> import pandas as pd\n        >>> df = pd.DataFrame({\'A\': [1, None, 3], \'B\': [4, 5, None]})\n        >>> clean_data(df)\n           A    B\n        0  1.0  4.0\n        2  3.0  5.0\n    """\n    # 函数实现...\n```'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E5%8D%81%E7%AB%A0%EF%BC%9A-%E6%A8%A1%E5%9D%97%E4%B8%8E%E5%8C%85"><span class="toc-number">1.</span> <span class="toc-text">第十章： 模块与包</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#10-1-%E6%A8%A1%E5%9D%97%E5%88%86%E7%B1%BB"><span class="toc-number">1.1.</span> <span class="toc-text">10.1 模块分类</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-2-%E6%A8%A1%E5%9D%97%E5%AF%BC%E5%85%A5%E6%96%B9%E5%BC%8F"><span class="toc-number">1.2.</span> <span class="toc-text">10.2 模块导入方式</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-1-%E5%AF%BC%E5%85%A5%E6%95%B4%E4%B8%AA%E6%A8%A1%E5%9D%97"><span class="toc-number">1.2.1.</span> <span class="toc-text">10.2.1 导入整个模块</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-2-%E4%BB%8E%E6%A8%A1%E5%9D%97%E5%AF%BC%E5%85%A5%E7%89%B9%E5%AE%9A%E5%86%85%E5%AE%B9"><span class="toc-number">1.2.2.</span> <span class="toc-text">10.2.2 从模块导入特定内容</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-3-%E5%AF%BC%E5%85%A5%E6%97%B6%E9%87%8D%E5%91%BD%E5%90%8D"><span class="toc-number">1.2.3.</span> <span class="toc-text">10.2.3 导入时重命名</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-2-4-%E5%AF%BC%E5%85%A5%E6%89%80%E6%9C%89%E5%86%85%E5%AE%B9%EF%BC%88%E4%B8%8D%E6%8E%A8%E8%8D%90%EF%BC%89"><span class="toc-number">1.2.4.</span> <span class="toc-text">10.2.4 导入所有内容（不推荐）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-3-%E6%8E%A7%E5%88%B6%E6%A8%A1%E5%9D%97%E5%AF%BC%E5%85%A5"><span class="toc-number">1.3.</span> <span class="toc-text">10.3 控制模块导入</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-4-%E6%A8%A1%E5%9D%97%E7%9A%84%E7%89%B9%E6%AE%8A%E5%B1%9E%E6%80%A7"><span class="toc-number">1.4.</span> <span class="toc-text">10.4 模块的特殊属性</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-4-1-name-%E5%B1%9E%E6%80%A7"><span class="toc-number">1.4.1.</span> <span class="toc-text">10.4.1 __name__ 属性</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-4-2-From-%E6%A8%A1%E5%9D%97%E6%97%A0%E6%B3%95%E8%AF%86%E5%88%AB%E9%97%AE%E9%A2%98"><span class="toc-number">1.4.2.</span> <span class="toc-text">10.4.2 From 模块无法识别问题</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-5-%E5%8C%85%E7%9A%84%E6%A6%82%E5%BF%B5%E4%B8%8E%E4%BD%BF%E7%94%A8"><span class="toc-number">1.5.</span> <span class="toc-text">10.5 包的概念与使用</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-5-1-%E5%8C%85%E7%9A%84%E7%BB%93%E6%9E%84%E7%A4%BA%E4%BE%8B"><span class="toc-number">1.5.1.</span> <span class="toc-text">10.5.1 包的结构示例</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-5-2-init-py-%E6%96%87%E4%BB%B6%E7%9A%84%E4%BD%9C%E7%94%A8"><span class="toc-number">1.5.2.</span> <span class="toc-text">10.5.2 __init__.py 文件的作用</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-5-3-%E5%8C%85%E7%9A%84%E5%AF%BC%E5%85%A5%E6%96%B9%E5%BC%8F"><span class="toc-number">1.5.3.</span> <span class="toc-text">10.5.3 包的导入方式</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#10-5-3-1-%E5%AF%BC%E5%85%A5%E5%8C%85%E4%B8%AD%E7%9A%84%E6%A8%A1%E5%9D%97"><span class="toc-number">1.5.3.1.</span> <span class="toc-text">******** 导入包中的模块</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#10-5-3-2-%E5%AF%BC%E5%85%A5%E5%8C%85%E4%B8%AD%E7%89%B9%E5%AE%9A%E5%86%85%E5%AE%B9"><span class="toc-number">1.5.3.2.</span> <span class="toc-text">******** 导入包中特定内容</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-5-4-%E7%9B%B8%E5%AF%B9%E5%AF%BC%E5%85%A5%E4%B8%8E%E7%BB%9D%E5%AF%B9%E5%AF%BC%E5%85%A5"><span class="toc-number">1.5.4.</span> <span class="toc-text">10.5.4 相对导入与绝对导入</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#10-5-4-1-%E7%BB%9D%E5%AF%B9%E5%AF%BC%E5%85%A5"><span class="toc-number">1.5.4.1.</span> <span class="toc-text">******** 绝对导入</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#10-5-4-2-%E7%9B%B8%E5%AF%B9%E5%AF%BC%E5%85%A5"><span class="toc-number">1.5.4.2.</span> <span class="toc-text">******** 相对导入</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-6-%E9%AB%98%E7%BA%A7%E5%BA%94%E7%94%A8%E6%8A%80%E5%B7%A7"><span class="toc-number">1.6.</span> <span class="toc-text">10.6 高级应用技巧</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-6-1-%E5%8A%A8%E6%80%81%E5%AF%BC%E5%85%A5"><span class="toc-number">1.6.1.</span> <span class="toc-text">10.6.1 动态导入</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-6-2-%E5%BB%B6%E8%BF%9F%E5%AF%BC%E5%85%A5"><span class="toc-number">1.6.2.</span> <span class="toc-text">10.6.2 延迟导入</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-6-3-%E4%BD%BF%E7%94%A8-slots-%E4%BC%98%E5%8C%96%E5%86%85%E5%AD%98"><span class="toc-number">1.6.3.</span> <span class="toc-text">10.6.3 使用 __slots__ 优化内存</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-7-%E5%8C%85%E7%9A%84%E5%8F%91%E5%B8%83%E4%B8%8E%E5%AE%89%E8%A3%85"><span class="toc-number">1.7.</span> <span class="toc-text">10.7 包的发布与安装</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-7-1-%E5%88%9B%E5%BB%BA-setup-py-%E6%96%87%E4%BB%B6"><span class="toc-number">1.7.1.</span> <span class="toc-text">10.7.1 创建 setup.py 文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-7-2-%E6%89%93%E5%8C%85%E4%B8%8E%E4%B8%8A%E4%BC%A0"><span class="toc-number">1.7.2.</span> <span class="toc-text">10.7.2 打包与上传</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-7-3-%E5%AE%89%E8%A3%85%E5%8C%85"><span class="toc-number">1.7.3.</span> <span class="toc-text">10.7.3 安装包</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-8-PyCharm-%E4%B8%AD%E7%9A%84%E5%8C%85%E7%AE%A1%E7%90%86"><span class="toc-number">1.8.</span> <span class="toc-text">10.8 PyCharm 中的包管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-8-1-%E4%BD%BF%E7%94%A8-Python-Packages-%E5%B7%A5%E5%85%B7"><span class="toc-number">1.8.1.</span> <span class="toc-text">10.8.1 使用 Python Packages 工具</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-8-2-%E6%9B%B4%E6%94%B9-PyCharm-%E7%9A%84-pip-%E6%BA%90"><span class="toc-number">1.8.2.</span> <span class="toc-text">10.8.2 更改 PyCharm 的 pip 源</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-8-3-%E4%BD%BF%E7%94%A8-Project-Interpreter-%E7%AE%A1%E7%90%86%E5%8C%85"><span class="toc-number">1.8.3.</span> <span class="toc-text">10.8.3 使用 Project Interpreter 管理包</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-8-4-%E5%AF%BC%E5%87%BA%E5%92%8C%E5%AF%BC%E5%85%A5%E9%A1%B9%E7%9B%AE%E4%BE%9D%E8%B5%96"><span class="toc-number">1.8.4.</span> <span class="toc-text">10.8.4 导出和导入项目依赖</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#10-8-4-1-%E5%AF%BC%E5%87%BA%E4%BE%9D%E8%B5%96"><span class="toc-number">*******.</span> <span class="toc-text">******** 导出依赖</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#10-8-4-2-%E5%AF%BC%E5%85%A5%E4%BE%9D%E8%B5%96"><span class="toc-number">*******.</span> <span class="toc-text">******** 导入依赖</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-8-5-%E4%BD%BF%E7%94%A8%E8%99%9A%E6%8B%9F%E7%8E%AF%E5%A2%83"><span class="toc-number">1.8.5.</span> <span class="toc-text">10.8.5 使用虚拟环境</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#10-8-5-1-%E8%99%9A%E6%8B%9F%E7%8E%AF%E5%A2%83%E5%B7%A5%E5%85%B7%E5%AF%B9%E6%AF%94"><span class="toc-number">1.8.5.1.</span> <span class="toc-text">******** 虚拟环境工具对比</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#10-9-%E6%A8%A1%E5%9D%97%E5%BC%80%E5%8F%91%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5"><span class="toc-number">1.9.</span> <span class="toc-text">10.9 模块开发最佳实践</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#10-9-1-%E6%A8%A1%E5%9D%97%E7%BB%84%E7%BB%87"><span class="toc-number">1.9.1.</span> <span class="toc-text">10.9.1 模块组织</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-9-2-%E5%AF%BC%E5%85%A5%E9%A1%BA%E5%BA%8F"><span class="toc-number">1.9.2.</span> <span class="toc-text">10.9.2 导入顺序</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#10-9-3-%E6%96%87%E6%A1%A3%E5%8C%96%E6%A8%A1%E5%9D%97%E5%92%8C%E5%8C%85"><span class="toc-number">1.9.3.</span> <span class="toc-text">10.9.3 文档化模块和包</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>