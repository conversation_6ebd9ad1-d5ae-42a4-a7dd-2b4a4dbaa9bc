//- 技术栈展示组件
//- 使用方式：在文章中使用 {% include_code tech-stack %}

.tech-stack-container
  each category in site.data.creativity
    .tech-category
      h4.category-title= category.category_name
      .tech-items
        each tech in category.creativity_list
          .tech-item(style=`border-left: 3px solid ${tech.color};`)
            img.tech-icon(src=tech.icon alt=tech.name)
            span.tech-name= tech.name

style.
  .tech-stack-container {
    margin: 2rem 0;
  }

  .tech-category {
    margin-bottom: 2rem;
    background: var(--anzhiyu-card-bg);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--anzhiyu-shadow-border);
  }

  .category-title {
    color: var(--anzhiyu-main);
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--anzhiyu-main);
  }

  .tech-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.8rem;
  }

  .tech-item {
    display: flex;
    align-items: center;
    padding: 0.8rem;
    background: var(--anzhiyu-secondbg);
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .tech-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }

  .tech-icon {
    width: 24px;
    height: 24px;
    margin-right: 0.8rem;
    border-radius: 4px;
  }

  .tech-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--anzhiyu-fontcolor);
  }

  @media (max-width: 768px) {
    .tech-items {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 0.6rem;
    }
    
    .tech-item {
      padding: 0.6rem;
    }
    
    .tech-name {
      font-size: 0.8rem;
    }
  }
