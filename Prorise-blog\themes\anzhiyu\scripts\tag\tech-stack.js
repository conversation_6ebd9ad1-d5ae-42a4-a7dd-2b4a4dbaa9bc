'use strict';

/**
 * 技术栈展示标签插件
 * 使用方式：{% techstack %}
 * 自动从 creativity.yml 读取数据并生成技术栈展示
 */

function techStackTag(args) {
  const hexo = this;
  const creativity = hexo.locals.get('data').creativity;
  
  if (!creativity || !Array.isArray(creativity)) {
    return '<div class="tech-stack-error">技术栈数据未找到</div>';
  }

  let html = '<div class="tech-stack-container">';
  
  creativity.forEach(category => {
    html += `<div class="tech-category">`;
    html += `<h4 class="category-title">${category.category_name}</h4>`;
    html += `<div class="tech-items">`;
    
    if (category.creativity_list && Array.isArray(category.creativity_list)) {
      category.creativity_list.forEach(tech => {
        html += `<div class="tech-item" style="border-left: 3px solid ${tech.color};">`;
        html += `<img src="${tech.icon}" alt="${tech.name}" class="tech-icon">`;
        html += `<span class="tech-name">${tech.name}</span>`;
        html += `</div>`;
      });
    }
    
    html += `</div>`;
    html += `</div>`;
  });
  
  html += '</div>';
  
  // 添加CSS样式
  html += `
<style>
.tech-stack-container {
  margin: 2rem 0;
}

.tech-category {
  margin-bottom: 2rem;
  background: var(--anzhiyu-card-bg);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--anzhiyu-shadow-border);
}

.category-title {
  color: var(--anzhiyu-main);
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--anzhiyu-main);
}

.tech-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.8rem;
}

.tech-item {
  display: flex;
  align-items: center;
  padding: 0.8rem;
  background: var(--anzhiyu-secondbg);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.tech-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.tech-icon {
  width: 24px;
  height: 24px;
  margin-right: 0.8rem;
  border-radius: 4px;
}

.tech-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--anzhiyu-fontcolor);
}

@media (max-width: 768px) {
  .tech-items {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.6rem;
  }
  
  .tech-item {
    padding: 0.6rem;
  }
  
  .tech-name {
    font-size: 0.8rem;
  }
}

.tech-stack-error {
  color: #ff6b6b;
  text-align: center;
  padding: 1rem;
  background: #ffe0e0;
  border-radius: 8px;
  margin: 1rem 0;
}
</style>`;
  
  return html;
}

hexo.extend.tag.register('techstack', techStackTag);
