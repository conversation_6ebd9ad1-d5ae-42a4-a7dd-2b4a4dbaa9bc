/* index */

#home-media-container {
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1; /* 确保在所有内容之下 */
  pointer-events: none; /* 防止阻挡用户交互 */


  /* 添加底部向上渐变遮罩 */
  -webkit-mask-image: linear-gradient(to top, transparent 0%, black 0%);
  mask-image: linear-gradient(to top, transparent 0%, black 0%);
}

.home-media {
  position: fixed; /* 同步改为固定定位 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  
  /* 添加透明度过渡 */
  transition: opacity 0.5s ease;
  opacity: 1;
}

/* 自定义加载动画容器 */
.custom-loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10; /* 确保在视频上方 */
  pointer-events: none; /* 防止阻挡视频交互 */
  transition: opacity 0.5s ease; /* 淡出动画 */
}

/* 加载动画元素 - 修复尺寸问题 */
.loader-animation {
  /* 修复：改为全屏覆盖模式 */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover; /* 改为cover确保铺满屏幕 */
  background-position: center;
  background-repeat: no-repeat;
  animation: pulse 1.5s infinite ease-in-out;
}

/* 移动端优化 */
@media screen and (max-width: 768px) {
  .loader-animation {
    background-size: cover; /* 移动端也保持cover模式 */
  }
}

/* 呼吸动画效果 */
@keyframes pulse {
  0% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.02); opacity: 1; }
  100% { transform: scale(1); opacity: 0.8; }
}