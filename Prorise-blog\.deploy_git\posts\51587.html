<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理入门（七）：第七章：用户端设计 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理入门（七）：第七章：用户端设计"><meta name="application-name" content="产品经理入门（七）：第七章：用户端设计"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="产品经理入门（七）：第七章：用户端设计"><meta property="og:url" content="https://prorise666.site/posts/51587.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="第七章：用户端设计在这一章，我们将进入一个完整的实战设计流程。我们将从用户第一次打开App的瞬间开始，一步步地设计出内容产品用户端的每一个核心模块，将我们之前学到的所有理论知识，全部应用到实践中。 7.1 引导页 &amp;amp; 启动页 &amp;amp; 闪屏页当一个新用户满怀期待地下载并首次打开我们的App"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta name="description" content="第七章：用户端设计在这一章，我们将进入一个完整的实战设计流程。我们将从用户第一次打开App的瞬间开始，一步步地设计出内容产品用户端的每一个核心模块，将我们之前学到的所有理论知识，全部应用到实践中。 7.1 引导页 &amp;amp; 启动页 &amp;amp; 闪屏页当一个新用户满怀期待地下载并首次打开我们的App"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/51587.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"产品经理入门（七）：第七章：用户端设计",postAI:"true",pageFillDescription:"第七章：用户端设计, 7.1 引导页 amp 启动页 amp 闪屏页, 7.1.1 学习目标, 7.1.2 引导页设计, 1. 引导页概念, 2. 引导页作用, 7.1.3 启动页设计, 1. 启动页概念, 2. 启动页作用, 7.1.4 闪屏页设计, 1. 闪屏页概念, 2. 闪屏页作用, 7.1.5 本节小结, 7.2 用户端设计思路, 7.2.1 学习目标, 7.2.2 需求背景分析, 7.2.3 需求分析（角色提炼）, 7.2.4 需求分析（用户场景）, 7.2.5 用户端核心功能设计, 7.2.6 本节小结, 7.3 注册登录, 7.3.1 学习目标, 7.3.2 注册登录的目的, 7.3.3 常见注册登录方式介绍, 1. 手机号+验证码, 2. 手机号+验证码+密码, 3. 第三方注册登录, 7.3.4 注册登录安全验证设计, 7.3.5 本节小结, 7.4 内容发布, 7.4.1 学习目标, 7.4.2 图文内容的展现形式, 7.4.3 内容发布页的设计, 1. 图文分离形式设计要点, 2. 图文混排形式设计要点, 7.5 内容列表及内容详情, 7.5.1 学习目标, 7.5.2 内容列表页设计, 1. 列表页元素设计, 2. 列表页设计要点, 7.5.3 内容详情页设计, 1. 图文混排详情页设计要点, 2. 图文分离详情页设计要点, 7.5.4 内容互动设计, 1. 互动行为的重要性, 2. 常见互动行为设计要点, 7.5.5 本节小结, 7.6 内容分发, 7.6.1 学习目标, 7.6.2 算法分发设计, 1. 算法分发定义与要素, 2. 用户画像介绍, 3. 标签分类及应用, 4. 算法分发设计逻辑, 7.6.3 热度排序设计, 1. 热度排序逻辑与设计要点, 2. 热度排序规则示例, 7.6.4 订阅分发设计, 1. 订阅分发的核心逻辑, 2. 订阅分发实现的关键功能, 7.6.5 本节小结, 7.7 个人中心, 7.7.1 学习目标, 7.7.2 个人资料页设计, 1. 常见个人信息展示, 7.7.3 个人中心常见功能, 1. 登录与未登录状态区别, 2. 常见功能模块介绍, 7.7.4 本节小结, 7.8 本章总结第七章用户端设计在这一章我们将进入一个完整的实战设计流程我们将从用户第一次打开的瞬间开始一步步地设计出内容产品用户端的每一个核心模块将我们之前学到的所有理论知识全部应用到实践中引导页启动页闪屏页当一个新用户满怀期待地下载并首次打开我们的时我们有且仅有一次机会来给他留下一个完美的第一印象这个第一印象通常是由三个不同的页面共同构成的我必须清晰地辨别它们并为它们设计好各自的使命学习目标在本节中我的目标是带大家清晰地区分引导页启动页闪屏页这三个极易混淆的概念我将拆解它们各自的定义和核心作用确保我们在产品设计中能为用户的第一次做出最合理的安排引导页设计引导页概念引导页是我为首次安装并打开我们的用户专门准备的一套欢迎手册它通常是由张可滑动的页面构成引导页作用我设计引导页通常是为了达成以下三个目的产品功能介绍用最简洁的图文向用户展示我们最核心最吸引人的个功能产品亮点说明传达我们产品的核心价值主张告诉用户我们是谁我们能为你带来什么独特的好处推广品宣通过精美的设计和文案在用户接触产品的最初几秒钟就建立起我们产品的品牌调性和情感链接启动页设计启动页概念启动页是用户每一次打开时都会看到的第一个页面它是一个短暂展示的静态页面主要目的是在后台加载资源时给用户一个优雅的等待界面避免出现白屏或黑屏启动页作用启动页的作用非常纯粹主要是品牌和信息的展示品牌宣传在页面中心清晰地展示我们产品的和品牌口号在每一次启动中反复加深用户的品牌认知版权声明与版本号在页面底部通常会标注公司的版权信息和当前的版本号闪屏页设计闪屏页概念闪屏页是一个可选的通常在启动页之后首页之前出现的页面它本质上是一个全屏的通常带有跳过按钮的广告或运营活动页面一般会展示秒闪屏页作用闪屏页的作用完全是商业化和运营导向的广告曝光开屏广告是中非常重要的一个广告位能为我们带来商业收入活动推广我可以用它来推广平台级的重要的运营活动为活动进行预热和导流内容推荐对于内容产品我也可以用它来推荐平台级的重磅内容吸引用户点击本节小结这三个页面共同构成了用户打开的三部曲我将它们的核心区别总结在了下面的表格里来帮助我们加深记忆页面类型出现时机核心目的我的设计思考引导页仅在首次安装启动时教育用户介绍核心功能与价值内容要少而精突出核心亮点让用户快速了解启动页每一次启动时加载期间品牌展示传递与界面要极简干净加载速度一定要快闪屏页每一次启动时加载后首页前商业运营广告曝光与活动推广必须提供清晰的跳过按钮不能强制用户观看我的设计哲学是用户的最终目的是进入使用核心功能因此这个开门的过程必须尽可能地快速流畅任何不必要的停留都可能造成用户的流失用户端设计思路在动手画任何一个具体页面之前我必须先建立起整个产品的设计蓝图这个蓝图就是我的用户端设计思路它是一个从宏观到微观从战略到执行的逻辑推演过程能确保我后续所有的设计决策都是有据可依浑然一体的学习目标在本节中我的目标是带大家完整地走一遍这个设计蓝图的推演过程我们将学习如何从一个需求背景出发提炼出产品的核心角色分析他们的用户场景并最终推导出我们需要设计的核心功能需求背景分析我们假设通过前期的市场分析和需求收集我们已经为我们即将开发的图文类内容产品确定了版本的四大核心策略这就是我们一切设计的出发点和宪法生产模式我们将采用专业生产内容用户生产内容的双引擎模式来保证内容的专业度和丰富度审核方式我们将采用自动审核人工审核的方式来平衡审核的效率和准确性分发方式我们将采用算法分发订阅分发的方式来兼顾用户发现新内容和关注老作者的需求消费模式在版本我们将采用免费消费的模式以最大化地吸引早期用户需求分析角色提炼基于上述的背景我首先要提炼出在这个生态中到底有哪几类玩家因为有所以必然有内容的生产者我称之为自媒体因为有内容消费所以必然有内容的消费者我称之为普通用户因为有审核和分发所以必然有管理者和运营者我称之为平台在本章我们聚焦的用户端设计主要就是为了服务好普通用户和自媒体这两大核心外部角色需求分析用户场景明确了我们要服务的角色之后我开始思考这两类用户在使用我们产品的过程中会经历哪些最核心最典型的场景我将它们归纳为四大场景获取身份无论是想看个性化推荐的普通用户还是想发表内容的自媒体他们都需要先在我们的平台上拥有一个自己的身份这就是注册登录的场景发布内容自媒体角色的核心诉求就是将自己的图文作品发布到平台上与大家分享这就是内容发布的场景浏览互动内容普通用户的核心诉求是发现阅读自己感兴趣的文章并对内容和作者表达自己的喜好这就是内容消费与互动的场景个人中心所有用户都需要一个地方来管理自己的个人信息查看自己发布或收藏过的内容这就是个人中心管理的场景用户端核心功能设计最后一步也是从分析到设计最关键的一步就是将上述的用户场景映射为我们产品需要提供的核心功能模块为了支撑获取身份场景我们需要设计注册登录功能为了支撑发布内容场景我们需要设计内容发布页为了支撑浏览互动内容场景我们需要设计内容列表页和内容详情页为了支撑个人中心场景我们需要设计个人中心模块这五大核心功能就构成了我们内容产品用户端的骨架在接下来的小节中我们将逐一地对这个骨架进行添肉画皮完成每一个模块的详细设计本节小结我将这个设计思路的推演过程总结为下面这张表格思考步骤核心产出我的目的背景分析四大产品决策确立项目的宪法是所有设计的最高准则角色提炼三大核心角色明确我们到底要为谁服务场景分析四大核心场景梳理出用户的完整旅程和核心诉求功能设计五大核心功能将用户诉求转化为具体可设计的产品模块注册登录在我们的设计思路中获取身份是所有用户要经历的第一个场景注册登录功能就是支撑这个场景我们为用户开启的第一扇门在我看来这扇门的设计至关重要一个好的注册登录体验应该像酒店的自动门一样让用户安全无感顺畅地通过而一个糟糕的设计则像一道生锈的铁门会在用户进入前就把他们拒之门外从本质上讲我设计的所有注册登录流程都是为了完成两件事身份识别你是谁和门槛验证如何证明你是你学习目标在本节中我的目标是带大家掌握现代中最主流的三种注册登录方式的设计我们将深入分析它们的实现逻辑优缺点并探讨如何通过安全验证设计来保障我们产品的大门既方便又安全注册登录的目的在设计之前我总会先思考我们为什么需要用户注册登录从用户的角度为了获得身份一个注册后的身份意味着用户在我们的产品里有了一个专属的数字资产账户这能帮助他们记录跟随可以保存自己的浏览历史收藏发布的文章等获得个性化服务可以接收到我们为他量身定制的内容推荐积累个人资产可以拥有自己的积分等级虚拟财产从平台的角度为了区分用户用户的注册能帮助我们平台更好地运营精细化运营可以针对不同用户群体推送不同的内容或活动信息记录可以更好地掌握平台的用户构成和自媒体信息信息分发能够针对用户的身份和喜好进行更精准的内容分发常见注册登录方式介绍明确了目的我们来看实现门槛验证的三种主流方式手机号验证码这是目前国内互联网产品最主流最便捷的方式它的核心逻辑是手机在手身份我有核心逻辑将注册和登录合二为一用户输入手机号接收并回填验证码系统验证通过后若该手机号未注册则自动为其创建账户并登录若已注册则直接登录优点方便快捷用户无需记忆复杂的密码操作路径最短缺点有一定账户信息风险如手机号丢失且平台需要承担较高的短信成本手机号验证码密码这是最传统也是账户体系最稳固的一种方式核心逻辑注册和登录是分离的用户首次使用时需要通过手机号验证码验证身份并设置一个密码来完成注册后续登录时主要使用手机号密码的方式优点安全性更高登录不受运营商短信通道影响缺点注册流程更长操作成本相对较高可能会流失一部分没有耐心的用户第三方注册登录这是借助巨人的力量让用户快速进入我们产品的方式核心逻辑用户授权我们去获取他在某个第三方大平台如微信微博上的基本公开信息如昵称头像作为身份标识从而完成注册或登录优点门槛极低用户一键授权即可体验非常流畅能有效提升新用户的注册转化率缺点我们能获取的用户信息非常有限不利于后续的精细化运营同时账户的安全性依赖于第三方平台注册登录安全验证设计为了防止被机器人恶意批量地注册或登录俗称刷接口也为了保护我们宝贵的短信费用我必须在注册登录流程中加入一道安全验证的屏障这道屏障通常出现在用户输入完手机号点击获取验证码按钮之后常见的验证形式有智能验证如我不是机器人勾选框文字点选验证要求用户点选图中指定的文字拼图验证要求用户拖动滑块完成拼图此外对于已注册用户为了提供更便捷的登录体验我还会设计支持指纹面容等生物识别验证方式本节小结在实际设计中我很少只提供一种登录方式而是采用组合策略我将这三种方式的选择思路总结如下登录方式核心逻辑我的设计策略手机号验证码便捷性优先作为默认和首选的登录方式最大化地降低用户操作成本手机号验证码密码安全性优先作为一种可选的账户升级或安全设置让注重安全的用户可以绑定密码第三方登录借力信任度优先作为一种重要的补充登录方式并排放在主登录按钮下方给用户多一种便捷选择内容发布当我们的自媒体用户也就是内容创作者想要把他们的想法和作品分享出来时他们就需要一个强大易用的内容发布功能这是连接创作者与平台的桥梁这个桥梁的体验直接决定了我们平台内容的数量和质量学习目标在本节中我的目标是带大家深入研究内容发布页的设计我们将探讨图文内容常见的两种展现形式并拆解这两种形式下内容发布页各自的设计要点和核心元素图文内容的展现形式在设计发布页之前我首先要明确我们平台的内容最终将以什么样的形式呈现给用户这通常决定了我们发布器的形态最常见的两种形式是图文分离展现形式图片和文字是分开展示的通常是上方为图片或视频下方为独立的大段的文字描述图文混排展现形式图片可以自由地插入到文章的任意位置形成我们常说的富文本效果内容发布页的设计图文分离形式设计要点这种形式的发布器设计上更追求简洁快速我通常会关注以下几个设计要点核心元素必须包含文本输入区图片视频上传入口通常是一个号按钮以及发布取消按钮字符长度限制需要明确告知用户正文最多可以输入多少字图片数量限制需要明确告知用户最多可以上传多少张图片发布状态变化当用户未输入任何内容时发布按钮应为置灰不可用状态以避免发布空内容草稿箱功能当用户意外退出时我需要设计一个草稿箱功能自动保存用户未发布的内容防止心血白费图文混排形式设计要点这种形式的发布器功能更强大类似于一个移动端的编辑器除了包含图文分离形式的所有要点外我还会特别关注标题输入通常会提供一个独立的标题输入框并有字数限制富文本编辑支持在正文的任意位置插入图片或视频并提供基础的排版功能如加粗对齐等发布与取消这两个按钮必须始终清晰可见图片视频上传提供清晰的上传入口和进度反馈内容列表及内容详情当内容被成功发布后它就需要被呈现给普通用户这个呈现的过程主要由两个核心页面来承载内容列表页和内容详情页学习目标在本节中我的目标是带大家掌握内容列表页和详情页的设计精髓我们将学习如何设计一个信息丰富吸引眼球的列表页一个沉浸易读的详情页以及如何通过巧妙的互动设计来提升用户的参与感和社区的活跃度内容列表页设计内容列表页是我为用户打造的内容超市它的核心使命是让用户能在这个超市里快速地高效地发现自己可能感兴趣的商品内容列表页元素设计一个标准的内容卡片通常包含三类信息内容基本信息标题封面图如果有内容摘要发布者信息作者的头像昵称互动信息点赞数评论数分享数等列表页设计要点图文权重分配我需要根据产品定位来决定图片和文字的权重左侧的列表形式更注重文字信息的传递而右侧的瀑布流形式则更强调图片的视觉冲击力内容排列规则列表的排序规则是什么是按照发布时间倒序还是按照热度排序我必须定义清晰的规则内容详情页设计当用户在列表页对某项内容产生兴趣并点击后就进入了内容详情页这是用户进行沉浸式阅读和深度消费的核心场所图文混排详情页设计要点核心元素必须清晰地展示导航栏内容标题作者信息发布时间正文图文混排设计要点支持分段长文章必须分段以提升可读性图片可交互图片通常需要支持点击查看大图视觉权重正文的视觉权重最高其他辅助信息如作者时间则相对弱化敏感字过滤需要对评论区等内容进行敏感词过滤图文分离详情页设计要点核心元素与混排类似但图片区和正文区是明确分开的设计要点与混排页的设计要点基本一致同样需要关注分段图片交互视觉权重和敏感字过滤内容互动设计互动行为的重要性在我看来互动是内容产品的灵魂它不仅仅是一些按钮而是连接用户作者内容平台四方的桥梁对于消费者互动是表达情绪的方式对于生产者互动是对自己创作的激励对于内容互动是区分其质量和热度的标尺对于平台互动是口碑营销和用户监督的手段常见互动行为设计要点核心互动点赞和分享是用户成本最低我们最希望用户去做的行为因此这两个功能的按钮在页面上必须非常突出和便捷主要分享渠道分享功能我通常会优先支持微信朋友圈和微博这几个主流渠道次要互动对于一些不常用的功能比如删除作者可见举报用户可见我通常会将它们收纳在右上角的更多按钮中避免干扰主界面的信息本节小结页面模块我的核心设计思考内容发布页根据图文分离混排的展现形式来决定发布器的复杂度和设计要点内容列表页核心是信息卡片的设计需要平衡好图文权重和信息密度内容详情页核心是提供沉浸易读的消费体验并引导用户进行互动内容互动设计突出核心互动点赞分享将次要互动收纳起来保持界面简洁内容分发我们的内容已经通过发布功能进入了平台的内容库详情页也为用户提供了沉浸式的消费体验但现在一个核心问题摆在面前在海量的内容库里我们应该把哪些内容在什么时候以什么方式呈现在用户面前这就是内容分发系统需要解决的问题它是连接海量内容与个性化用户的桥梁学习目标在本节中我的目标是带大家深入了解内容产品背后最主流的三种分发模式的设计我们将重点拆解算法分发的核心三要素学习用户画像和标签的概念并了解热度排序和订阅分发的设计逻辑算法分发设计算法分发定义与要素算法分发是我认为的现代内容产品的发动机我把它定义为一套能根据用户数据自动地个性化地为用户推荐其可能感兴趣的内容的系统要让这台发动机运转起来我必须为它准备好三个核心要素用户画像深入地理解我的用户知道他是谁他喜欢什么内容画像深入地理解我的内容知道它是什么它讲了什么算法模型建立一套高效的匹配和排序规则将最合适的内容推荐给最合适的用户用户画像介绍算法分发的前提是了解用户的喜好用户画像就是我用来了解用户的工具我把它定义为根据用户各维度的真实数据抽象出的一个标签化的用户模型我通常会从以下四个维度来为用户构建画像基本属性如姓名性别年龄地域等社会属性如职业收入公司文化等行为属性这是最重要的包括用户在我们产品里的登录活跃评论点赞等一切行为消费属性如果产品有付费点还包括用户的消费金额次数等标签分类及应用用户画像和内容画像都是通过标签来具体实现的标签就是对目标的量化标识和描述我们的核心工作就是为内容和用户打上同一套标签体系从而实现精准匹配我把用户标签分为两大类静态标签指那些在较长时间内保持稳定不变的标签通常具有先天性比如用户的性别年龄星座地域等动态标签指根据用户的实时操作行为动态变化的标签比如用户刚刚搜索了什么点赞了什么购买了什么这些动态标签更能反映用户当下的即时的兴趣一个完整的用户画像是静态标签和动态标签的结合体算法分发设计逻辑有了用户画像和内容画像我的算法推荐逻辑通常遵循四步走权重设置我会为用户的不同行为赋予不同的权重比如分享行为的权重一定高于点赞贴标签系统自动为内容和用户打上标签匹配推荐算法模型开始工作为用户匹配出与他自身标签相符的内容排序对所有匹配出的内容根据一个热度质量分公式进行排序决定最终呈现给用户的顺序我们来看一个具体的算法分发规则案例计算维度与分值我们可以定义一个内容的总分值公式比如内容总分分享数评论数点赞数收藏数推送规则优先匹配该用户标签权重最高的的标签内容根据内容总分值排序分页推送每页条下拉刷新时推送新产生的内容已经推荐过的内容不再重复推荐热度排序设计前面我们谈的算法分发完全是根据人内容的个性化匹配进行推荐的但这里面可能会存在一个问题如果推荐出的内容本身质量不高怎么办为了过滤掉低质量内容并让用户感知到大家都在看什么我需要引入一种全局性的排序机制热度排序热度排序逻辑与设计要点我设计热度排序的逻辑通常遵循四步走梳理维度首先我会梳理出所有能够反映内容质量和受欢迎程度的用户行为维度比如分享点赞评论收藏等权重设置其次我会基于这些维度为不同的行为设置不同的权重比如我认为分享和评论比点赞更能代表用户的认可度因此会给予更高的分值标准计算然后系统会根据用户产生的实时行为数据套入我们设定的计分公式为每一篇内容动态地计算出一个热度分值排序最后系统根据计算出的热度分值进行排序这里需要特别注意为了避免热度榜被少数爆款内容长期霸占固化我通常会在公式中加入时间衰减因子让新发布的内容有更多的曝光机会热度排序规则示例我们来看一个更具体更复杂的规则案例它巧妙地融合了个性化推荐和热度排序内容总分值公式分享数点赞数评论数收藏数推送规则规则筛选出小时内发布的所有内容规则筛选出小时至小时前发布的内容中热度分值的全部内容排序与分发逻辑在信息流中优先推送满足规则的内容确保新鲜度按发布时间由近到远排列当规则的内容不足时再推送满足规则的内容补充高质量老内容按热度分值由高到低排列每次下拉刷新时推送新产生的内容每次最多推送条订阅分发设计订阅分发的核心逻辑订阅分发是将内容的选择权完全交还给用户的一种方式它的逻辑非常简单我只看我关注的人发布的内容这是一种基于人的强关系的分发模式它的核心业务流程是自媒体发布内容用户在看到后选择关注该自媒体系统此后会自动将该自媒体的新内容分发到该用户的关注信息流中用户随时可以查看订阅分发实现的关键功能要实现这个逻辑我的设计通常会围绕以下三个要点展开连接我必须为用户和作者建立连接的桥梁这通常是在作者的个人主页内容详情页等位置提供一个清晰的关注功能按钮推荐内容我需要为用户提供一个专门的消费场所也就是一个独立的关注信息流这个信息流里只包含用户已关注作者发布的内容排序这个信息流的排序规则通常非常简单就是严格按照内容发布的时间倒序排列确保用户看到的永远是最新的内容本节小结我将这三种核心的分发方式总结如下在我的产品设计中我通常会将它们组合使用来满足用户不同的内容发现需求分发方式核心逻辑我的设计要点算法分发人内容匹配定义清晰的用户画像内容标签推荐与排序规则内容总分分享数评论数点赞数收藏数热度排序内容热度值计算定义合理的热度计算公式并考虑时间衰减避免榜单固化分享数点赞数评论数收藏数订阅分发用户主动关注设计好关注取关功能并提供独立的关注信息流个人中心当用户在我们的产品里消费互动创作留下了一系列数字足迹之后他们需要一个家来安放和管理这些属于自己的信息和资产这个家就是个人中心对我来说个人中心是提升用户归属感和粘性的关键模块它承载了用户的个人身份也聚合了产品中与个人相关的各种高阶功能学习目标在本节我的目标是带大家掌握个人中心模块的完整设计我们将学习如何设计用户的名片个人资料页并重点拆解个人中心页在登录与未登录两种状态下的差异化设计以及其中常见的功能模块应该如何组织个人资料页设计个人资料页是用户在我们的产品里对外展示自己形象的个人名片它的核心是允许用户自定义和编辑自己的个人信息常见个人信息展示我设计个人资料页时会根据产品定位来决定需要提供哪些信息字段对于一个内容产品最常见的核心字段包括头像用户最具识别性的标识支持用户从相册上传或拍照昵称用户在社区中行走江湖的代号简介一段个性化的自我介绍性别生日地区在设计注册流程时我有一个重要原则渐进式信息收集即在最初的注册环节我只要求用户提供最核心的信息比如仅需要手机号验证而将这些详细的个人资料引导用户在后续有空时再来个人中心慢慢完善这能最大化地降低新用户的注册门槛个人中心常见功能个人中心这个页面它的设计比较特殊因为我必须同时考虑游客和主人两种完全不同的状态登录与未登录状态区别未登录状态当用户未登录时个人中心这个页面的核心设计目标只有一个引导用户去登录或注册正如案例图所示此时的页面我会隐藏掉所有个性化的信息和数据用一个通用的图标和提示文案如点击登录来占据视觉中心大部分功能入口如我的收藏历史记录也会被隐藏或置灰用户点击后会直接跳转到登录页面登录状态当用户登录后页面则会完全变身为他专属的个人空间此时的设计核心是清晰的个人信息展示和便捷的功能入口聚合页面的顶部会展示用户的头像昵称和核心数据如作品数关注数粉丝数下方则会罗列出所有与他相关的功能常见功能模块介绍对于登录后的用户我会把个人中心的功能入口按照相关性进行逻辑分组让用户能快速找到自己想要的功能核心资产类这是用户最关心的他们在我们平台沉淀下的数字资产通常包括我的收藏浏览历史我的作品针对创作者消息与互动类消息通知包括系统通知评论点赞等账户与安全类实名认证账号与安全设置通用类用户反馈系统设置里面通常还包含关于我们退出登录等本节小结模块我的核心设计思考个人资料页提供头像昵称简介等基础字段的编辑功能遵循渐进式信息收集原则个人中心未登录设计核心是引导登录注册隐藏个性化信息简化功能入口个人中心已登录设计核心是个人信息展示和功能入口聚合将功能按逻辑分组如资产类账户类通用类本章总结到这里我们已经完整地设计出了一个内容产品用户端的所有核心模块让我们最后回顾一下本章的整个设计旅程开门三板斧我们首先设计了引导页启动页和闪屏页为用户打造了完美的第一印象确立设计思路我们通过背景分析角色提炼用户场景核心功能的推演确立了整个产品的设计骨架设计核心模块我们逐一设计了注册登录内容发布内容列表与详情内容分发和个人中心这几个核心功能模块为骨架添上了血肉通过这一章的实战我们已经将之前学到的所有理论都转化为了具体可视的产品设计方案",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-21 14:52:07",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%83%E7%AB%A0%EF%BC%9A%E7%94%A8%E6%88%B7%E7%AB%AF%E8%AE%BE%E8%AE%A1"><span class="toc-text">第七章：用户端设计</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#7-1-%E5%BC%95%E5%AF%BC%E9%A1%B5-%E5%90%AF%E5%8A%A8%E9%A1%B5-%E9%97%AA%E5%B1%8F%E9%A1%B5"><span class="toc-text">7.1 引导页 &amp; 启动页 &amp; 闪屏页</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">7.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-2-%E5%BC%95%E5%AF%BC%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-text">7.1.2 引导页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%BC%95%E5%AF%BC%E9%A1%B5%E6%A6%82%E5%BF%B5"><span class="toc-text">1. 引导页概念</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%BC%95%E5%AF%BC%E9%A1%B5%E4%BD%9C%E7%94%A8"><span class="toc-text">2. 引导页作用</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-3-%E5%90%AF%E5%8A%A8%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-text">7.1.3 启动页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%90%AF%E5%8A%A8%E9%A1%B5%E6%A6%82%E5%BF%B5"><span class="toc-text">1. 启动页概念</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%90%AF%E5%8A%A8%E9%A1%B5%E4%BD%9C%E7%94%A8"><span class="toc-text">2. 启动页作用</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-4-%E9%97%AA%E5%B1%8F%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-text">7.1.4 闪屏页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%97%AA%E5%B1%8F%E9%A1%B5%E6%A6%82%E5%BF%B5"><span class="toc-text">1. 闪屏页概念</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E9%97%AA%E5%B1%8F%E9%A1%B5%E4%BD%9C%E7%94%A8"><span class="toc-text">2. 闪屏页作用</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-text">7.1.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-2-%E7%94%A8%E6%88%B7%E7%AB%AF%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-text">7.2 用户端设计思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">7.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-2-%E9%9C%80%E6%B1%82%E8%83%8C%E6%99%AF%E5%88%86%E6%9E%90"><span class="toc-text">7.2.2 需求背景分析</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-3-%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%EF%BC%88%E8%A7%92%E8%89%B2%E6%8F%90%E7%82%BC%EF%BC%89"><span class="toc-text">7.2.3 需求分析（角色提炼）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-4-%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%EF%BC%88%E7%94%A8%E6%88%B7%E5%9C%BA%E6%99%AF%EF%BC%89"><span class="toc-text">7.2.4 需求分析（用户场景）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-5-%E7%94%A8%E6%88%B7%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-text">7.2.5 用户端核心功能设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-6-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-text">7.2.6 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-3-%E6%B3%A8%E5%86%8C%E7%99%BB%E5%BD%95"><span class="toc-text">7.3 注册登录</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">7.3.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-2-%E6%B3%A8%E5%86%8C%E7%99%BB%E5%BD%95%E7%9A%84%E7%9B%AE%E7%9A%84"><span class="toc-text">7.3.2 注册登录的目的</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-3-%E5%B8%B8%E8%A7%81%E6%B3%A8%E5%86%8C%E7%99%BB%E5%BD%95%E6%96%B9%E5%BC%8F%E4%BB%8B%E7%BB%8D"><span class="toc-text">7.3.3 常见注册登录方式介绍</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%89%8B%E6%9C%BA%E5%8F%B7-%E9%AA%8C%E8%AF%81%E7%A0%81"><span class="toc-text">1. 手机号+验证码</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%89%8B%E6%9C%BA%E5%8F%B7-%E9%AA%8C%E8%AF%81%E7%A0%81-%E5%AF%86%E7%A0%81"><span class="toc-text">2. 手机号+验证码+密码</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%AC%AC%E4%B8%89%E6%96%B9%E6%B3%A8%E5%86%8C%E7%99%BB%E5%BD%95"><span class="toc-text">3. 第三方注册登录</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-4-%E6%B3%A8%E5%86%8C%E7%99%BB%E5%BD%95%E5%AE%89%E5%85%A8%E9%AA%8C%E8%AF%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">7.3.4 注册登录安全验证设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-text">7.3.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-4-%E5%86%85%E5%AE%B9%E5%8F%91%E5%B8%83"><span class="toc-text">7.4 内容发布</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-4-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">7.4.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-4-2-%E5%9B%BE%E6%96%87%E5%86%85%E5%AE%B9%E7%9A%84%E5%B1%95%E7%8E%B0%E5%BD%A2%E5%BC%8F"><span class="toc-text">7.4.2 图文内容的展现形式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-4-3-%E5%86%85%E5%AE%B9%E5%8F%91%E5%B8%83%E9%A1%B5%E7%9A%84%E8%AE%BE%E8%AE%A1"><span class="toc-text">7.4.3 内容发布页的设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%9B%BE%E6%96%87%E5%88%86%E7%A6%BB%E5%BD%A2%E5%BC%8F%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-text">1. 图文分离形式设计要点</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%9B%BE%E6%96%87%E6%B7%B7%E6%8E%92%E5%BD%A2%E5%BC%8F%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-text">2. 图文混排形式设计要点</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-5-%E5%86%85%E5%AE%B9%E5%88%97%E8%A1%A8%E5%8F%8A%E5%86%85%E5%AE%B9%E8%AF%A6%E6%83%85"><span class="toc-text">7.5 内容列表及内容详情</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-5-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">7.5.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-5-2-%E5%86%85%E5%AE%B9%E5%88%97%E8%A1%A8%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-text">7.5.2 内容列表页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%88%97%E8%A1%A8%E9%A1%B5%E5%85%83%E7%B4%A0%E8%AE%BE%E8%AE%A1"><span class="toc-text">1. 列表页元素设计</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%88%97%E8%A1%A8%E9%A1%B5%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-text">2. 列表页设计要点</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-5-3-%E5%86%85%E5%AE%B9%E8%AF%A6%E6%83%85%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-text">7.5.3 内容详情页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%9B%BE%E6%96%87%E6%B7%B7%E6%8E%92%E8%AF%A6%E6%83%85%E9%A1%B5%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-text">1. 图文混排详情页设计要点</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%9B%BE%E6%96%87%E5%88%86%E7%A6%BB%E8%AF%A6%E6%83%85%E9%A1%B5%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-text">2. 图文分离详情页设计要点</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-5-4-%E5%86%85%E5%AE%B9%E4%BA%92%E5%8A%A8%E8%AE%BE%E8%AE%A1"><span class="toc-text">7.5.4 内容互动设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%BA%92%E5%8A%A8%E8%A1%8C%E4%B8%BA%E7%9A%84%E9%87%8D%E8%A6%81%E6%80%A7"><span class="toc-text">1. 互动行为的重要性</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B8%B8%E8%A7%81%E4%BA%92%E5%8A%A8%E8%A1%8C%E4%B8%BA%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-text">2. 常见互动行为设计要点</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-5-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-text">7.5.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-6-%E5%86%85%E5%AE%B9%E5%88%86%E5%8F%91"><span class="toc-text">7.6 内容分发</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-6-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">7.6.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-6-2-%E7%AE%97%E6%B3%95%E5%88%86%E5%8F%91%E8%AE%BE%E8%AE%A1"><span class="toc-text">7.6.2 算法分发设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%AE%97%E6%B3%95%E5%88%86%E5%8F%91%E5%AE%9A%E4%B9%89%E4%B8%8E%E8%A6%81%E7%B4%A0"><span class="toc-text">1. 算法分发定义与要素</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%94%A8%E6%88%B7%E7%94%BB%E5%83%8F%E4%BB%8B%E7%BB%8D"><span class="toc-text">2. 用户画像介绍</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E6%A0%87%E7%AD%BE%E5%88%86%E7%B1%BB%E5%8F%8A%E5%BA%94%E7%94%A8"><span class="toc-text">3. 标签分类及应用</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E7%AE%97%E6%B3%95%E5%88%86%E5%8F%91%E8%AE%BE%E8%AE%A1%E9%80%BB%E8%BE%91"><span class="toc-text">4. 算法分发设计逻辑</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-6-3-%E7%83%AD%E5%BA%A6%E6%8E%92%E5%BA%8F%E8%AE%BE%E8%AE%A1"><span class="toc-text">7.6.3 热度排序设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%83%AD%E5%BA%A6%E6%8E%92%E5%BA%8F%E9%80%BB%E8%BE%91%E4%B8%8E%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-text">1. 热度排序逻辑与设计要点</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%83%AD%E5%BA%A6%E6%8E%92%E5%BA%8F%E8%A7%84%E5%88%99%E7%A4%BA%E4%BE%8B"><span class="toc-text">2. 热度排序规则示例</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-6-4-%E8%AE%A2%E9%98%85%E5%88%86%E5%8F%91%E8%AE%BE%E8%AE%A1"><span class="toc-text">7.6.4 订阅分发设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%AE%A2%E9%98%85%E5%88%86%E5%8F%91%E7%9A%84%E6%A0%B8%E5%BF%83%E9%80%BB%E8%BE%91"><span class="toc-text">1. 订阅分发的核心逻辑</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%AE%A2%E9%98%85%E5%88%86%E5%8F%91%E5%AE%9E%E7%8E%B0%E7%9A%84%E5%85%B3%E9%94%AE%E5%8A%9F%E8%83%BD"><span class="toc-text">2. 订阅分发实现的关键功能</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-6-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-text">7.6.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-7-%E4%B8%AA%E4%BA%BA%E4%B8%AD%E5%BF%83"><span class="toc-text">7.7 个人中心</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-7-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">7.7.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-7-2-%E4%B8%AA%E4%BA%BA%E8%B5%84%E6%96%99%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-text">7.7.2 个人资料页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%B8%B8%E8%A7%81%E4%B8%AA%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%B1%95%E7%A4%BA"><span class="toc-text">1. 常见个人信息展示</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-7-3-%E4%B8%AA%E4%BA%BA%E4%B8%AD%E5%BF%83%E5%B8%B8%E8%A7%81%E5%8A%9F%E8%83%BD"><span class="toc-text">7.7.3 个人中心常见功能</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%99%BB%E5%BD%95%E4%B8%8E%E6%9C%AA%E7%99%BB%E5%BD%95%E7%8A%B6%E6%80%81%E5%8C%BA%E5%88%AB"><span class="toc-text">1. 登录与未登录状态区别</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B8%B8%E8%A7%81%E5%8A%9F%E8%83%BD%E6%A8%A1%E5%9D%97%E4%BB%8B%E7%BB%8D"><span class="toc-text">2. 常见功能模块介绍</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-7-4-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-text">7.7.4 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-8-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-text">7.8 本章总结</span></a></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理入门（七）：第七章：用户端设计</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-20T14:13:45.000Z" title="发表于 2025-07-20 22:13:45">2025-07-20</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-21T06:52:07.808Z" title="更新于 2025-07-21 14:52:07">2025-07-21</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">9.1k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>26分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理入门（七）：第七章：用户端设计"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/51587.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/51587.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理入门（七）：第七章：用户端设计</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-20T14:13:45.000Z" title="发表于 2025-07-20 22:13:45">2025-07-20</time><time itemprop="dateCreated datePublished" datetime="2025-07-21T06:52:07.808Z" title="更新于 2025-07-21 14:52:07">2025-07-21</time></header><div id="postchat_postcontent"><h1 id="第七章：用户端设计"><a href="#第七章：用户端设计" class="headerlink" title="第七章：用户端设计"></a>第七章：用户端设计</h1><p>在这一章，我们将进入一个完整的实战设计流程。我们将从用户第一次打开App的瞬间开始，一步步地设计出内容产品用户端的每一个核心模块，将我们之前学到的所有理论知识，全部应用到实践中。</p><h2 id="7-1-引导页-启动页-闪屏页"><a href="#7-1-引导页-启动页-闪屏页" class="headerlink" title="7.1 引导页 &amp; 启动页 &amp; 闪屏页"></a>7.1 引导页 &amp; 启动页 &amp; 闪屏页</h2><p>当一个新用户满怀期待地下载并首次打开我们的App时，我们有且仅有一次机会，来给他留下一个完美的“第一印象”。这个第一印象，通常是由三个不同的页面共同构成的。我必须清晰地辨别它们，并为它们设计好各自的使命。</p><h3 id="7-1-1-学习目标"><a href="#7-1-1-学习目标" class="headerlink" title="7.1.1 学习目标"></a>7.1.1 学习目标</h3><p>在本节中，我的目标是，带大家清晰地区分<strong>引导页、启动页、闪屏页</strong>这三个极易混淆的概念。我将拆解它们各自的定义和核心作用，确保我们在产品设计中，能为用户的“第一次”，做出最合理的安排。</p><h3 id="7-1-2-引导页设计"><a href="#7-1-2-引导页设计" class="headerlink" title="7.1.2 引导页设计"></a>7.1.2 引导页设计</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720211813703.png" alt="image-20250720211813703"></p><h4 id="1-引导页概念"><a href="#1-引导页概念" class="headerlink" title="1. 引导页概念"></a>1. 引导页概念</h4><p><strong>引导页（Onboarding Screens）</strong>，是我为<strong>首次</strong>安装并打开我们App的用户，专门准备的一套“欢迎手册”。它通常是由3-5张可滑动的页面构成。</p><h4 id="2-引导页作用"><a href="#2-引导页作用" class="headerlink" title="2. 引导页作用"></a>2. 引导页作用</h4><p>我设计引导页，通常是为了达成以下三个目的：</p><ul><li><strong>产品功能介绍</strong>：用最简洁的图文，向用户展示我们App最核心、最吸引人的1-3个功能。</li><li><strong>产品亮点说明</strong>：传达我们产品的核心价值主张，告诉用户“我们是谁，我们能为你带来什么独特的好处”。</li><li><strong>推广品宣</strong>：通过精美的设计和文案，在用户接触产品的最初几秒钟，就建立起我们产品的品牌调性和情感链接。</li></ul><h3 id="7-1-3-启动页设计"><a href="#7-1-3-启动页设计" class="headerlink" title="7.1.3 启动页设计"></a>7.1.3 启动页设计</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720211951423.png" alt="image-20250720211951423"></p><h4 id="1-启动页概念"><a href="#1-启动页概念" class="headerlink" title="1. 启动页概念"></a>1. 启动页概念</h4><p><strong>启动页（Launch Screen）</strong>，是用户<strong>每一次</strong>打开App时，都会看到的第一个页面。它是一个短暂展示的静态页面，主要目的是在App后台加载资源时，给用户一个优雅的等待界面，避免出现白屏或黑屏。</p><h4 id="2-启动页作用"><a href="#2-启动页作用" class="headerlink" title="2. 启动页作用"></a>2. 启动页作用</h4><p>启动页的作用非常纯粹，主要是品牌和信息的展示：</p><ul><li><strong>品牌宣传</strong>：在页面中心，清晰地展示我们产品的Logo和Slogan（品牌口号），在每一次启动中，反复加深用户的品牌认知。</li><li><strong>版权声明与版本号</strong>：在页面底部，通常会标注公司的版权信息和当前App的版本号。</li></ul><h3 id="7-1-4-闪屏页设计"><a href="#7-1-4-闪屏页设计" class="headerlink" title="7.1.4 闪屏页设计"></a>7.1.4 闪屏页设计</h3><h4 id="1-闪屏页概念"><a href="#1-闪屏页概念" class="headerlink" title="1. 闪屏页概念"></a>1. 闪屏页概念</h4><p><strong>闪屏页（Splash Screen / Splash Ad）</strong>，是一个可选的、通常在<strong>启动页之后，首页之前</strong>出现的页面。它本质上是一个全屏的、通常带有“跳过”按钮的广告或运营活动页面，一般会展示3-5秒。</p><h4 id="2-闪屏页作用"><a href="#2-闪屏页作用" class="headerlink" title="2. 闪屏页作用"></a>2. 闪屏页作用</h4><p>闪屏页的作用完全是商业化和运营导向的：</p><ul><li><strong>广告曝光</strong>：“开屏广告”是App中非常重要的一个广告位，能为我们带来商业收入。</li><li><strong>活动推广</strong>：我可以用它来推广平台级的、重要的运营活动，为活动进行预热和导流。</li><li><strong>内容推荐</strong>：对于内容产品，我也可以用它来推荐平台S级的重磅内容，吸引用户点击。</li></ul><h3 id="7-1-5-本节小结"><a href="#7-1-5-本节小结" class="headerlink" title="7.1.5 本节小结"></a>7.1.5 本节小结</h3><p>这三个页面，共同构成了用户打开App的“三部曲”。我将它们的核心区别，总结在了下面的表格里，来帮助我们加深记忆。</p><table><thead><tr><th align="left"><strong>页面类型</strong></th><th align="left"><strong>出现时机</strong></th><th align="left"><strong>核心目的</strong></th><th align="left"><strong>我的设计思考</strong></th></tr></thead><tbody><tr><td align="left"><strong>引导页</strong></td><td align="left"><strong>仅在首次</strong>安装启动时</td><td align="left"><strong>教育用户</strong>、介绍核心功能与价值</td><td align="left">内容要少而精，突出核心亮点，让用户快速了解。</td></tr><tr><td align="left"><strong>启动页</strong></td><td align="left"><strong>每一次</strong>启动时（加载期间）</td><td align="left"><strong>品牌展示</strong>、传递Logo与Slogan</td><td align="left">界面要极简、干净，加载速度一定要快。</td></tr><tr><td align="left"><strong>闪屏页</strong></td><td align="left"><strong>每一次</strong>启动时（加载后，首页前）</td><td align="left"><strong>商业运营</strong>、广告曝光与活动推广</td><td align="left">必须提供清晰的“跳过”按钮，不能强制用户观看。</td></tr></tbody></table><p>我的设计哲学是，用户的最终目的是进入App使用核心功能。因此，这个“开门”的过程，必须尽可能地快速、流畅。任何不必要的停留，都可能造成用户的流失。</p><hr><h2 id="7-2-用户端设计思路"><a href="#7-2-用户端设计思路" class="headerlink" title="7.2 用户端设计思路"></a>7.2 用户端设计思路</h2><p>在动手画任何一个具体页面之前，我必须先建立起整个产品的“设计蓝图”。这个蓝图，就是我的用户端设计思路。它是一个从宏观到微观，从战略到执行的逻辑推演过程，能确保我后续所有的设计决策，都是有据可依、浑然一体的。</p><h3 id="7-2-1-学习目标"><a href="#7-2-1-学习目标" class="headerlink" title="7.2.1 学习目标"></a>7.2.1 学习目标</h3><p>在本节中，我的目标是带大家完整地走一遍这个“设计蓝图”的推演过程。我们将学习如何从一个<strong>需求背景</strong>出发，提炼出产品的核心<strong>角色</strong>，分析他们的<strong>用户场景</strong>，并最终推导出我们需要设计的<strong>核心功能</strong>。</p><h3 id="7-2-2-需求背景分析"><a href="#7-2-2-需求背景分析" class="headerlink" title="7.2.2 需求背景分析"></a>7.2.2 需求背景分析</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212444403.png" alt="image-20250720212444403"></p><p>我们假设，通过前期的市场分析和需求收集，我们已经为我们即将开发的“图文类内容产品”，确定了V1.0版本的四大核心策略，这就是我们一切设计的出发点和“宪法”。</p><ol><li><strong>生产模式</strong>：我们将采用 <strong>PGC (专业生产内容) + UGC (用户生产内容)</strong> 的双引擎模式，来保证内容的专业度和丰富度。</li><li><strong>审核方式</strong>：我们将采用 <strong>自动审核 + 人工审核</strong> 的方式，来平衡审核的效率和准确性。</li><li><strong>分发方式</strong>：我们将采用 <strong>算法分发 + 订阅分发</strong> 的方式，来兼顾用户发现新内容和关注老作者的需求。</li><li><strong>消费模式</strong>：在V1.0版本，我们将采用<strong>免费消费</strong>的模式，以最大化地吸引早期用户。</li></ol><h3 id="7-2-3-需求分析（角色提炼）"><a href="#7-2-3-需求分析（角色提炼）" class="headerlink" title="7.2.3 需求分析（角色提炼）"></a>7.2.3 需求分析（角色提炼）</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212511146.png" alt="image-20250720212511146"></p><p>基于上述的背景，我首先要提炼出，在这个生态中，到底有哪几类“玩家”？</p><ul><li>因为有UGC，所以必然有内容的<strong>生产者</strong>，我称之为“<strong>自媒体</strong>”。</li><li>因为有内容消费，所以必然有内容的<strong>消费者</strong>，我称之为“<strong>普通用户</strong>”。</li><li>因为有审核和分发，所以必然有<strong>管理者和运营者</strong>，我称之为“<strong>平台</strong>”。</li></ul><p>在本章，我们聚焦的用户端设计，主要就是为了服务好“<strong>普通用户</strong>”和“<strong>自媒体</strong>”这两大核心外部角色。</p><h3 id="7-2-4-需求分析（用户场景）"><a href="#7-2-4-需求分析（用户场景）" class="headerlink" title="7.2.4 需求分析（用户场景）"></a>7.2.4 需求分析（用户场景）</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212618884.png" alt="image-20250720212618884"></p><p>明确了我们要服务的角色之后，我开始思考：这两类用户，在使用我们产品的过程中，会经历哪些最核心、最典型的场景？我将它们归纳为四大场景：</p><ol><li><strong>获取身份</strong>：无论是想看个性化推荐的“普通用户”，还是想发表内容的“自媒体”，他们都需要先在我们的平台上，拥有一个自己的身份。这就是<strong>注册/登录</strong>的场景。</li><li><strong>发布内容</strong>：“自媒体”角色的核心诉求，就是将自己的图文作品发布到平台上，与大家分享。这就是<strong>内容发布</strong>的场景。</li><li><strong>浏览&amp;互动内容</strong>：“普通用户”的核心诉求，是发现、阅读自己感兴趣的文章，并对内容和作者表达自己的喜好。这就是<strong>内容消费与互动</strong>的场景。</li><li><strong>个人中心</strong>：所有用户，都需要一个地方来管理自己的个人信息、查看自己发布或收藏过的内容。这就是<strong>个人中心管理</strong>的场景。</li></ol><h3 id="7-2-5-用户端核心功能设计"><a href="#7-2-5-用户端核心功能设计" class="headerlink" title="7.2.5 用户端核心功能设计"></a>7.2.5 用户端核心功能设计</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212728816.png" alt="image-20250720212728816"></p><p>最后一步，也是从“分析”到“设计”最关键的一步，就是将上述的用户场景，映射为我们产品需要提供的<strong>核心功能模块</strong>。</p><ul><li>为了支撑“获取身份”场景，我们需要设计 <strong>注册登录</strong> 功能。</li><li>为了支撑“发布内容”场景，我们需要设计 <strong>内容发布页</strong>。</li><li>为了支撑“浏览&amp;互动内容”场景，我们需要设计 <strong>内容列表页</strong> 和 <strong>内容详情页</strong>。</li><li>为了支撑“个人中心”场景，我们需要设计 <strong>个人中心</strong> 模块。</li></ul><p>这五大核心功能，就构成了我们内容产品用户端的“骨架”。在接下来的小节中，我们将逐一地，对这个骨架进行“添肉画皮”，完成每一个模块的详细设计。</p><h3 id="7-2-6-本节小结"><a href="#7-2-6-本节小结" class="headerlink" title="7.2.6 本节小结"></a>7.2.6 本节小结</h3><p>我将这个设计思路的推演过程，总结为下面这张表格：</p><table><thead><tr><th align="left"><strong>思考步骤</strong></th><th align="left"><strong>核心产出</strong></th><th align="left"><strong>我的目的</strong></th></tr></thead><tbody><tr><td align="left"><strong>背景分析</strong></td><td align="left">四大产品决策</td><td align="left">确立项目的“宪法”，是所有设计的最高准则。</td></tr><tr><td align="left"><strong>角色提炼</strong></td><td align="left">三大核心角色</td><td align="left">明确我们到底要为谁服务。</td></tr><tr><td align="left"><strong>场景分析</strong></td><td align="left">四大核心场景</td><td align="left">梳理出用户的完整旅程和核心诉求。</td></tr><tr><td align="left"><strong>功能设计</strong></td><td align="left">五大核心功能</td><td align="left">将用户诉求，转化为具体、可设计的产品模块。</td></tr></tbody></table><hr><h2 id="7-3-注册登录"><a href="#7-3-注册登录" class="headerlink" title="7.3 注册登录"></a>7.3 注册登录</h2><p>在我们的设计思路中，“获取身份”是所有用户要经历的第一个场景。<strong>注册登录</strong>功能，就是支撑这个场景、我们为用户开启的“第一扇门”。</p><p>在我看来，这扇门的设计至关重要。一个好的注册登录体验，应该像酒店的自动门一样，让用户安全、无感、顺畅地通过；</p><p>而一个糟糕的设计，则像一道生锈的铁门，会在用户进入前，就把他们拒之门外。</p><p>从本质上讲，我设计的所有注册登录流程，都是为了完成两件事：<strong>身份识别</strong>（你是谁？）和<strong>门槛验证</strong>（如何证明你是你？）。</p><h3 id="7-3-1-学习目标"><a href="#7-3-1-学习目标" class="headerlink" title="7.3.1 学习目标"></a>7.3.1 学习目标</h3><p>在本节中，我的目标是带大家掌握现代App中最主流的三种注册登录方式的设计。我们将深入分析它们的实现逻辑、优缺点，并探讨如何通过安全验证设计，来保障我们产品的“大门”既方便又安全。</p><h3 id="7-3-2-注册登录的目的"><a href="#7-3-2-注册登录的目的" class="headerlink" title="7.3.2 注册登录的目的"></a>7.3.2 注册登录的目的</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720213340787.png" alt="image-20250720213340787"></p><p>在设计之前，我总会先思考：我们为什么需要用户注册登录？</p><ul><li><p><strong>从用户的角度：为了“获得身份”</strong><br>一个注册后的身份，意味着用户在我们的产品里，有了一个专属的“数字资产”账户。这能帮助他们：</p><ul><li><strong>记录跟随</strong>：可以保存自己的浏览历史、收藏、发布的文章等。</li><li><strong>获得个性化服务</strong>：可以接收到我们为他量身定制的内容推荐。</li><li><strong>积累个人资产</strong>：可以拥有自己的积分、等级、虚拟财产。</li></ul></li><li><p><strong>从平台的角度：为了“区分用户”</strong><br>用户的注册，能帮助我们平台更好地运营：</p><ul><li><strong>精细化运营</strong>：可以针对不同用户群体，推送不同的内容或活动。</li><li><strong>信息记录</strong>：可以更好地掌握平台的用户构成和自媒体信息。</li><li><strong>信息分发</strong>：能够针对用户的身份和喜好，进行更精准的内容分发。</li></ul></li></ul><h3 id="7-3-3-常见注册登录方式介绍"><a href="#7-3-3-常见注册登录方式介绍" class="headerlink" title="7.3.3 常见注册登录方式介绍"></a>7.3.3 常见注册登录方式介绍</h3><p>明确了目的，我们来看实现“门槛验证”的三种主流方式。</p><h4 id="1-手机号-验证码"><a href="#1-手机号-验证码" class="headerlink" title="1. 手机号+验证码"></a>1. 手机号+验证码</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720215110314.png" alt="image-20250720215110314"></p><p>这是目前国内互联网产品最主流、最便捷的方式，它的核心逻辑是“<strong>手机在手，身份我有</strong>”。</p><ul><li><strong>核心逻辑</strong>：将注册和登录合二为一。用户输入手机号，接收并回填验证码，系统验证通过后，若该手机号未注册，则自动为其创建账户并登录；若已注册，则直接登录。</li><li><strong>优点</strong>：<strong>方便快捷</strong>，用户无需记忆复杂的密码，操作路径最短。</li><li><strong>缺点</strong>：有一定<strong>账户信息风险</strong>（如手机号丢失），且平台需要承担较高的<strong>短信成本</strong>。</li></ul><h4 id="2-手机号-验证码-密码"><a href="#2-手机号-验证码-密码" class="headerlink" title="2. 手机号+验证码+密码"></a>2. 手机号+验证码+密码</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720215834551.png" alt="image-20250720215834551"></p><p>这是最传统，也是账户体系最稳固的一种方式。</p><ul><li><strong>核心逻辑</strong>：注册和登录是分离的。用户首次使用时，需要通过“手机号+验证码”验证身份，并<strong>设置一个密码</strong>来完成注册。后续登录时，主要使用“手机号+密码”的方式。</li><li><strong>优点</strong>：<strong>安全性更高</strong>，登录不受运营商短信通道影响。</li><li><strong>缺点</strong>：注册流程更长，<strong>操作成本相对较高</strong>，可能会流失一部分没有耐心的用户。</li></ul><h4 id="3-第三方注册登录"><a href="#3-第三方注册登录" class="headerlink" title="3. 第三方注册登录"></a>3. 第三方注册登录</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720220120919.png" alt="image-20250720220120919"></p><p>这是借助“巨人”的力量，让用户快速进入我们产品的方式。</p><ul><li><strong>核心逻辑</strong>：用户授权我们App，去获取他在某个第三方大平台（如微信、QQ、微博）上的基本公开信息（如昵称、头像）作为身份标识，从而完成注册或登录。</li><li><strong>优点</strong>：<strong>门槛极低</strong>，用户一键授权即可，体验非常流畅，能有效提升新用户的注册转化率。</li><li><strong>缺点</strong>：我们能<strong>获取的用户信息非常有限</strong>，不利于后续的精细化运营。同时，账户的安全性依赖于第三方平台。</li></ul><h3 id="7-3-4-注册登录安全验证设计"><a href="#7-3-4-注册登录安全验证设计" class="headerlink" title="7.3.4 注册登录安全验证设计"></a>7.3.4 注册登录安全验证设计</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720220200195.png" alt="image-20250720220200195"></p><p>为了防止被机器人恶意、批量地注册或登录（俗称“刷接口”），也为了保护我们宝贵的短信费用，我必须在注册登录流程中，加入一道“<strong>安全验证</strong>”的屏障。</p><p>这道屏障，通常出现在用户输入完手机号、点击“获取验证码”按钮之后。常见的验证形式有：</p><ul><li><strong>智能验证</strong>（如“我不是机器人”勾选框）</li><li><strong>文字点选验证</strong>（要求用户点选图中指定的文字）</li><li><strong>拼图验证</strong>（要求用户拖动滑块完成拼图）</li></ul><p>此外，对于已注册用户，为了提供更便捷的登录体验，我还会设计支持<strong>指纹、面容ID</strong>等生物识别验证方式。</p><h3 id="7-3-5-本节小结"><a href="#7-3-5-本节小结" class="headerlink" title="7.3.5 本节小结"></a>7.3.5 本节小结</h3><p>在实际设计中，我很少只提供一种登录方式，而是采用组合策略。我将这三种方式的选择思路总结如下：</p><table><thead><tr><th align="left"><strong>登录方式</strong></th><th align="left"><strong>核心逻辑</strong></th><th align="left"><strong>我的设计策略</strong></th></tr></thead><tbody><tr><td align="left"><strong>手机号+验证码</strong></td><td align="left">便捷性优先</td><td align="left">作为默认和首选的登录方式，最大化地降低用户操作成本。</td></tr><tr><td align="left"><strong>手机号+验证码+密码</strong></td><td align="left">安全性优先</td><td align="left">作为一种可选的账户升级或安全设置，让注重安全的用户可以绑定密码。</td></tr><tr><td align="left"><strong>第三方登录</strong></td><td align="left">借力，信任度优先</td><td align="left">作为一种重要的补充登录方式，并排放在主登录按钮下方，给用户多一种便捷选择。</td></tr></tbody></table><hr><h2 id="7-4-内容发布"><a href="#7-4-内容发布" class="headerlink" title="7.4 内容发布"></a>7.4 内容发布</h2><p>当我们的“自媒体”用户，也就是内容创作者，想要把他们的想法和作品分享出来时，他们就需要一个强大、易用的<strong>内容发布</strong>功能。这是连接“创作者”与“平台”的桥梁，这个桥梁的体验，直接决定了我们平台内容的数量和质量。</p><h3 id="7-4-1-学习目标"><a href="#7-4-1-学习目标" class="headerlink" title="7.4.1 学习目标"></a>7.4.1 学习目标</h3><p>在本节中，我的目标是带大家深入研究内容发布页的设计。我们将探讨图文内容常见的两种展现形式，并拆解这两种形式下，内容发布页各自的设计要点和核心元素。</p><h3 id="7-4-2-图文内容的展现形式"><a href="#7-4-2-图文内容的展现形式" class="headerlink" title="7.4.2 图文内容的展现形式"></a>7.4.2 图文内容的展现形式</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720220845581.png" alt="image-20250720220845581"></p><p>在设计发布页之前，我首先要明确，我们平台的内容，最终将以什么样的形式呈现给用户。这通常决定了我们发布器的形态。最常见的两种形式是：</p><ol><li><strong>图文分离展现形式</strong>：图片和文字是分开展示的。通常是上方为图片（或视频），下方为独立的、大段的文字描述。</li><li><strong>图文混排展现形式</strong>：图片可以自由地插入到文章的任意位置，形成我们常说的“富文本”效果。</li></ol><h3 id="7-4-3-内容发布页的设计"><a href="#7-4-3-内容发布页的设计" class="headerlink" title="7.4.3 内容发布页的设计"></a>7.4.3 内容发布页的设计</h3><h4 id="1-图文分离形式设计要点"><a href="#1-图文分离形式设计要点" class="headerlink" title="1. 图文分离形式设计要点"></a>1. 图文分离形式设计要点</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221014583.png" alt="image-20250720221014583"></p><p>这种形式的发布器，设计上更追求简洁、快速。我通常会关注以下几个设计要点：</p><ul><li><strong>核心元素</strong>：必须包含<strong>文本输入区</strong>、<strong>图片/视频上传入口</strong>（通常是一个“+”号按钮），以及<strong>发布/取消按钮</strong>。</li><li><strong>字符长度限制</strong>：需要明确告知用户，正文最多可以输入多少字。</li><li><strong>图片数量限制</strong>：需要明确告知用户，最多可以上传多少张图片。</li><li><strong>发布状态变化</strong>：当用户未输入任何内容时，“发布”按钮应为置灰不可用状态，以避免发布空内容。</li><li><strong>草稿箱功能</strong>：当用户意外退出时，我需要设计一个草稿箱功能，自动保存用户未发布的内容，防止心血白费。</li></ul><h4 id="2-图文混排形式设计要点"><a href="#2-图文混排形式设计要点" class="headerlink" title="2. 图文混排形式设计要点"></a>2. 图文混排形式设计要点</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221124889.png" alt="image-20250720221124889"></p><p>这种形式的发布器，功能更强大，类似于一个移动端的“Word编辑器”。除了包含图文分离形式的所有要点外，我还会特别关注：</p><ul><li><strong>标题输入</strong>：通常会提供一个独立的标题输入框，并有字数限制。</li><li><strong>富文本编辑</strong>：支持在正文的任意位置插入图片或视频，并提供基础的排版功能，如加粗、对齐等。</li><li><strong>发布与取消</strong>：这两个按钮必须始终清晰可见。</li><li><strong>图片/视频上传</strong>：提供清晰的上传入口和进度反馈。</li></ul><hr><h2 id="7-5-内容列表及内容详情"><a href="#7-5-内容列表及内容详情" class="headerlink" title="7.5 内容列表及内容详情"></a>7.5 内容列表及内容详情</h2><p>当内容被成功发布后，它就需要被呈现给“普通用户”。这个呈现的过程，主要由两个核心页面来承载：<strong>内容列表页</strong>和<strong>内容详情页</strong>。</p><h3 id="7-5-1-学习目标"><a href="#7-5-1-学习目标" class="headerlink" title="7.5.1 学习目标"></a>7.5.1 学习目标</h3><p>在本节中，我的目标是带大家掌握内容列表页和详情页的设计精髓。我们将学习如何设计一个信息丰富、吸引眼球的列表页，一个沉浸、易读的详情页，以及如何通过巧妙的互动设计，来提升用户的参与感和社区的活跃度。</p><h3 id="7-5-2-内容列表页设计"><a href="#7-5-2-内容列表页设计" class="headerlink" title="7.5.2 内容列表页设计"></a>7.5.2 内容列表页设计</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221204736.png" alt="image-20250720221204736"></p><p>内容列表页，是我为用户打造的“内容超市”。它的核心使命，是让用户能在这个超市里，快速地、高效地发现自己可能感兴趣的“商品”（内容）。</p><h4 id="1-列表页元素设计"><a href="#1-列表页元素设计" class="headerlink" title="1. 列表页元素设计"></a>1. 列表页元素设计</h4><p>一个标准的内容卡片，通常包含三类信息：</p><ul><li><strong>内容基本信息</strong>：标题、封面图（如果有）、内容摘要。</li><li><strong>发布者信息</strong>：作者的头像、昵称。</li><li><strong>互动信息</strong>：点赞数、评论数、分享数等。</li></ul><h4 id="2-列表页设计要点"><a href="#2-列表页设计要点" class="headerlink" title="2. 列表页设计要点"></a>2. 列表页设计要点</h4><ul><li><strong>图文权重分配</strong>：我需要根据产品定位，来决定图片和文字的权重。左侧的列表形式，更注重文字信息的传递；而右侧的瀑布流形式，则更强调图片的视觉冲击力。</li><li><strong>内容排列规则</strong>：列表的排序规则是什么？是按照发布时间倒序？还是按照热度排序？我必须定义清晰的规则。</li></ul><h3 id="7-5-3-内容详情页设计"><a href="#7-5-3-内容详情页设计" class="headerlink" title="7.5.3 内容详情页设计"></a>7.5.3 内容详情页设计</h3><p>当用户在列表页对某项内容产生兴趣并点击后，就进入了<strong>内容详情页</strong>。这是用户进行沉浸式阅读和深度消费的核心场所。</p><h4 id="1-图文混排详情页设计要点"><a href="#1-图文混排详情页设计要点" class="headerlink" title="1. 图文混排详情页设计要点"></a>1. 图文混排详情页设计要点</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221259547.png" alt="image-20250720221259547"></p><ul><li><strong>核心元素</strong>：必须清晰地展示<strong>导航栏、内容标题、作者信息、发布时间、正文（图文混排）</strong>。</li><li><strong>设计要点</strong>：<ul><li><strong>支持分段</strong>：长文章必须分段，以提升可读性。</li><li><strong>图片可交互</strong>：图片通常需要支持点击查看大图。</li><li><strong>视觉权重</strong>：正文的视觉权重最高，其他辅助信息（如作者、时间）则相对弱化。</li><li><strong>敏感字过滤</strong>：需要对评论区等UGC内容进行敏感词过滤。</li></ul></li></ul><h4 id="2-图文分离详情页设计要点"><a href="#2-图文分离详情页设计要点" class="headerlink" title="2. 图文分离详情页设计要点"></a>2. 图文分离详情页设计要点</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221401530.png" alt="image-20250720221401530"></p><ul><li><strong>核心元素</strong>：与混排类似，但<strong>图片区</strong>和<strong>正文区</strong>是明确分开的。</li><li><strong>设计要点</strong>：与混排页的设计要点基本一致，同样需要关注分段、图片交互、视觉权重和敏感字过滤。</li></ul><h3 id="7-5-4-内容互动设计"><a href="#7-5-4-内容互动设计" class="headerlink" title="7.5.4 内容互动设计"></a>7.5.4 内容互动设计</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221435264.png" alt="image-20250720221435264"></p><h4 id="1-互动行为的重要性"><a href="#1-互动行为的重要性" class="headerlink" title="1. 互动行为的重要性"></a>1. 互动行为的重要性</h4><p>在我看来，互动是内容产品的“灵魂”。它不仅仅是一些按钮，而是连接<strong>用户、作者、内容、平台</strong>四方的桥梁。</p><ul><li>对于<strong>消费者</strong>，互动是表达情绪的方式。</li><li>对于<strong>生产者</strong>，互动是对自己创作的激励。</li><li>对于<strong>内容</strong>，互动是区分其质量和热度的标尺。</li><li>对于<strong>平台</strong>，互动是口碑营销和用户监督的手段。</li></ul><h4 id="2-常见互动行为设计要点"><a href="#2-常见互动行为设计要点" class="headerlink" title="2. 常见互动行为设计要点"></a>2. 常见互动行为设计要点</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221636401.png" alt="image-20250720221636401"></p><ul><li><strong>核心互动</strong>：<strong>点赞</strong>和<strong>分享</strong>，是用户成本最低、我们最希望用户去做的行为。因此，这两个功能的按钮，在页面上必须非常突出和便捷。</li><li><strong>主要分享渠道</strong>：分享功能，我通常会优先支持微信、朋友圈、QQ和微博这几个主流渠道。</li><li><strong>次要互动</strong>：对于一些不常用的功能，比如<strong>删除</strong>（作者可见）、<strong>举报</strong>（用户可见），我通常会将它们收纳在右上角的“更多”按钮中，避免干扰主界面的信息。</li></ul><h3 id="7-5-5-本节小结"><a href="#7-5-5-本节小结" class="headerlink" title="7.5.5 本节小结"></a>7.5.5 本节小结</h3><table><thead><tr><th align="left"><strong>页面/模块</strong></th><th align="left"><strong>我的核心设计思考</strong></th></tr></thead><tbody><tr><td align="left"><strong>内容发布页</strong></td><td align="left">根据<strong>图文分离/混排</strong>的展现形式，来决定发布器的复杂度和设计要点。</td></tr><tr><td align="left"><strong>内容列表页</strong></td><td align="left">核心是<strong>信息卡片</strong>的设计，需要平衡好图文权重和信息密度。</td></tr><tr><td align="left"><strong>内容详情页</strong></td><td align="left">核心是提供<strong>沉浸、易读</strong>的消费体验，并引导用户进行互动。</td></tr><tr><td align="left"><strong>内容互动设计</strong></td><td align="left"><strong>突出核心互动（点赞/分享）</strong>，将次要互动收纳起来，保持界面简洁。</td></tr></tbody></table><hr><h2 id="7-6-内容分发"><a href="#7-6-内容分发" class="headerlink" title="7.6 内容分发"></a>7.6 内容分发</h2><p>我们的内容，已经通过发布功能进入了平台的“内容库”，详情页也为用户提供了沉浸式的消费体验。但现在，一个核心问题摆在面前：<strong>在海量的内容库里，我们应该把哪些内容，在什么时候，以什么方式，呈现在用户面前？</strong></p><p>这就是内容分发系统需要解决的问题。它是连接“海量内容”与“个性化用户”的桥梁。</p><h3 id="7-6-1-学习目标"><a href="#7-6-1-学习目标" class="headerlink" title="7.6.1 学习目标"></a>7.6.1 学习目标</h3><p>在本节中，我的目标是带大家深入了解内容产品背后最主流的三种分发模式的设计。我们将重点拆解<strong>算法分发</strong>的核心三要素，学习<strong>用户画像</strong>和<strong>标签</strong>的概念，并了解<strong>热度排序</strong>和<strong>订阅分发</strong>的设计逻辑。</p><h3 id="7-6-2-算法分发设计"><a href="#7-6-2-算法分发设计" class="headerlink" title="7.6.2 算法分发设计"></a>7.6.2 算法分发设计</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084124586.png" alt="image-20250721084124586"></p><h4 id="1-算法分发定义与要素"><a href="#1-算法分发定义与要素" class="headerlink" title="1. 算法分发定义与要素"></a>1. 算法分发定义与要素</h4><p><strong>算法分发</strong>，是我认为的现代内容产品的“发动机”。我把它定义为：<strong>一套能根据用户数据，自动地、个性化地为用户推荐其可能感兴趣的内容的系统</strong>。</p><p>要让这台发动机运转起来，我必须为它准备好三个核心要素：</p><ul><li><strong>用户画像 (User Persona)</strong>：深入地理解我的用户，知道“他是谁，他喜欢什么”。</li><li><strong>内容画像 (Content Profile)</strong>：深入地理解我的内容，知道“它是什么，它讲了什么”。</li><li><strong>算法模型 (Algorithm Model)</strong>：建立一套高效的匹配和排序规则，将最合适的内容，推荐给最合适的用户。</li></ul><h4 id="2-用户画像介绍"><a href="#2-用户画像介绍" class="headerlink" title="2. 用户画像介绍"></a>2. 用户画像介绍</h4><p>算法分发的前提，是了解用户的喜好。<strong>用户画像</strong>，就是我用来“了解”用户的工具。</p><p>我把它定义为：<strong>根据用户各维度的真实数据，抽象出的一个标签化的用户模型</strong>。<br>我通常会从以下四个维度，来为用户构建画像：</p><ul><li><strong>基本属性</strong>：如姓名、性别、年龄、地域等。</li><li><strong>社会属性</strong>：如职业、收入、公司、文化等。</li><li><strong>行为属性</strong>：这是最重要的，包括用户在我们产品里的登录、活跃、评论、点赞等一切行为。</li><li><strong>消费属性</strong>：如果产品有付费点，还包括用户的消费金额、次数等。</li></ul><h4 id="3-标签分类及应用"><a href="#3-标签分类及应用" class="headerlink" title="3. 标签分类及应用"></a>3. 标签分类及应用</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084235993.png" alt="image-20250721084235993"></p><p>用户画像和内容画像，都是通过“<strong>标签</strong>”来具体实现的。标签，就是对目标的量化标识和描述。我们的核心工作，就是<strong>为内容和用户，打上同一套标签体系</strong>，从而实现精准匹配。</p><p>我把用户标签，分为两大类：</p><ul><li><strong>静态标签</strong>：指那些在较长时间内，保持稳定不变的标签，通常具有“先天性”。比如用户的<strong>性别、年龄、星座、地域</strong>等。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084734425.png" alt="image-20250721084734425"></p><ul><li><strong>动态标签</strong>：指根据用户的<strong>实时操作行为</strong>，动态变化的标签。比如用户刚刚搜索了什么、点赞了什么、购买了什么。这些动态标签，更能反映用户当下的、即时的兴趣。一个完整的用户画像，是静态标签和动态标签的结合体。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084743710.png" alt="image-20250721084743710"></p><h4 id="4-算法分发设计逻辑"><a href="#4-算法分发设计逻辑" class="headerlink" title="4. 算法分发设计逻辑"></a>4. 算法分发设计逻辑</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084704681.png" alt="image-20250721084704681"></p><p>有了用户画像和内容画像，我的算法推荐逻辑通常遵循四步走：</p><ol><li><strong>权重设置</strong>：我会为用户的不同行为，赋予不同的权重。比如，“分享”行为的权重，一定高于“点赞”。</li><li><strong>贴标签</strong>：系统自动为内容和用户打上标签。</li><li><strong>匹配推荐</strong>：算法模型开始工作，为用户匹配出，与他自身标签相符的内容。</li><li><strong>排序</strong>：对所有匹配出的内容，根据一个“热度/质量分”公式，进行排序，决定最终呈现给用户的顺序。</li></ol><p>我们来看一个具体的<strong>算法分发规则案例</strong>：</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084719114.png" alt="image-20250721084719114"></p><ul><li><strong>计算维度与分值</strong>：我们可以定义一个内容的总分值公式，比如：<code>内容总分 = 分享数*2 + 评论数*2 + 点赞数*1 + 收藏数*1</code>。</li><li><strong>推送规则</strong>：<ol><li>优先匹配该用户标签权重最高的TOP5的标签内容。</li><li>根据内容总分值排序，分页推送，每页8条。</li><li>下拉刷新时，推送新产生的内容。</li><li>已经推荐过的内容，不再重复推荐。</li></ol></li></ul><hr><h3 id="7-6-3-热度排序设计"><a href="#7-6-3-热度排序设计" class="headerlink" title="7.6.3 热度排序设计"></a>7.6.3 热度排序设计</h3><p>前面我们谈的算法分发，完全是根据“人-内容”的个性化匹配进行推荐的。但这里面可能会存在一个问题：如果推荐出的内容本身质量不高怎么办？</p><p>为了过滤掉低质量内容，并让用户感知到“大家都在看什么”，我需要引入一种全局性的排序机制——<strong>热度排序</strong>。</p><h4 id="1-热度排序逻辑与设计要点"><a href="#1-热度排序逻辑与设计要点" class="headerlink" title="1. 热度排序逻辑与设计要点"></a>1. 热度排序逻辑与设计要点</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085440264.png" alt="image-20250721085440264"></p><p>我设计热度排序的逻辑，通常遵循四步走：</p><ol><li><strong>梳理维度</strong>：首先，我会梳理出所有能够反映内容质量和受欢迎程度的用户行为维度，比如<strong>分享、点赞、评论、收藏</strong>等。</li><li><strong>权重设置</strong>：其次，我会基于这些维度，为不同的行为设置不同的权重。比如，我认为“分享”和“评论”比“点赞”更能代表用户的认可度，因此会给予更高的分值。</li><li><strong>标准计算</strong>：然后，系统会根据用户产生的实时行为数据，套入我们设定的计分公式，为每一篇内容动态地计算出一个“热度分值”。</li><li><strong>排序</strong>：最后，系统根据计算出的“热度分值”进行排序。这里需要特别注意，为了<strong>避免热度榜被少数爆款内容长期霸占（固化）</strong>，我通常会在公式中加入“时间衰减”因子，让新发布的内容有更多的曝光机会。</li></ol><h4 id="2-热度排序规则示例"><a href="#2-热度排序规则示例" class="headerlink" title="2. 热度排序规则示例"></a>2. 热度排序规则示例</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085555753.png" alt="image-20250721085555753"></p><p>我们来看一个更具体、更复杂的规则案例，它巧妙地融合了<strong>个性化推荐</strong>和<strong>热度排序</strong>。</p><ul><li><strong>内容总分值公式</strong>：<code>S = 分享数(A)*2 + 点赞数(B)*1 + 评论数(C)*2 + 收藏数(D)*1</code></li><li><strong>推送规则</strong>：<ul><li><strong>规则1</strong>：筛选出<strong>24小时内</strong>发布的所有内容。</li><li><strong>规则2</strong>：筛选出<strong>24小时至72小时前</strong>发布的内容中，热度分值<strong>S&gt;=30</strong>的全部内容。</li></ul></li><li><strong>排序与分发逻辑</strong>：<ol><li>在信息流中，优先推送满足“规则1”的内容（确保新鲜度），按发布时间由近到远排列；当“规则1”的内容不足时，再推送满足“规则2”的内容（补充高质量老内容），按热度分值由高到低排列。</li><li>每次下拉刷新时，推送新产生的内容，每次最多推送8条。</li></ol></li></ul><h3 id="7-6-4-订阅分发设计"><a href="#7-6-4-订阅分发设计" class="headerlink" title="7.6.4 订阅分发设计"></a>7.6.4 订阅分发设计</h3><h4 id="1-订阅分发的核心逻辑"><a href="#1-订阅分发的核心逻辑" class="headerlink" title="1. 订阅分发的核心逻辑"></a>1. 订阅分发的核心逻辑</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085717235.png" alt="image-20250721085717235"></p><p><strong>订阅分发</strong>，是将内容的选择权，完全交还给用户的一种方式。它的逻辑非常简单：“<strong>我只看我关注的人发布的内容</strong>”。这是一种基于“人”的、强关系的分发模式。</p><p>它的核心业务流程是：自媒体发布内容 → 用户在看到后选择关注该自媒体 → 系统此后会自动将该自媒体的新内容，分发到该用户的“关注”信息流中 → 用户随时可以查看。</p><h4 id="2-订阅分发实现的关键功能"><a href="#2-订阅分发实现的关键功能" class="headerlink" title="2. 订阅分发实现的关键功能"></a>2. 订阅分发实现的关键功能</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085801010.png" alt="image-20250721085801010"></p><p>要实现这个逻辑，我的设计通常会围绕以下三个要点展开：</p><ul><li><strong>连接 (Connection)</strong>：我必须为用户和作者建立“连接”的桥梁。这通常是在作者的个人主页、内容详情页等位置，提供一个清晰的“<strong>关注</strong>”功能按钮。</li><li><strong>推荐内容 (Content Delivery)</strong>：我需要为用户提供一个专门的消费场所，也就是一个独立的“<strong>关注</strong>”信息流（Feed）。这个信息流里，只包含用户已关注作者发布的内容。</li><li><strong>排序 (Ranking)</strong>：这个信息流的排序规则通常非常简单，就是严格按照<strong>内容发布的时间倒序排列</strong>，确保用户看到的永远是最新的内容。</li></ul><h3 id="7-6-5-本节小结"><a href="#7-6-5-本节小结" class="headerlink" title="7.6.5 本节小结"></a>7.6.5 本节小结</h3><p>我将这三种核心的分发方式总结如下，在我的产品设计中，我通常会将它们组合使用，来满足用户不同的内容发现需求。</p><table><thead><tr><th align="left"><strong>分发方式</strong></th><th align="left"><strong>核心逻辑</strong></th><th align="left"><strong>我的设计要点</strong></th></tr></thead><tbody><tr><td align="left"><strong>算法分发</strong></td><td align="left"><strong>人-内容匹配</strong></td><td align="left">定义清晰的用户画像、内容标签、推荐与排序规则<br><code>内容总分 = 分享数*2 + 评论数*2 + 点赞数*1 + 收藏数*1</code>。</td></tr><tr><td align="left"><strong>热度排序</strong></td><td align="left"><strong>内容热度值计算</strong></td><td align="left">定义合理的热度计算公式，并考虑时间衰减，避免榜单固化。<br>S = 分享数(A)*2 + 点赞数(B)*1 + 评论数(C)*2 + 收藏数(D)*1</td></tr><tr><td align="left"><strong>订阅分发</strong></td><td align="left"><strong>用户主动关注</strong></td><td align="left">设计好关注/取关功能，并提供独立的“关注”信息流。</td></tr></tbody></table><hr><h2 id="7-7-个人中心"><a href="#7-7-个人中心" class="headerlink" title="7.7 个人中心"></a>7.7 个人中心</h2><p>当用户在我们的产品里消费、互动、创作，留下了一系列数字足迹之后，他们需要一个“家”，来安放和管理这些属于自己的信息和资产。这个“家”，就是<strong>个人中心</strong>。</p><p>对我来说，个人中心是提升用户归属感和粘性的关键模块，它承载了用户的个人身份，也聚合了产品中与“个人”相关的各种高阶功能。</p><h3 id="7-7-1-学习目标"><a href="#7-7-1-学习目标" class="headerlink" title="7.7.1 学习目标"></a>7.7.1 学习目标</h3><p>在本节，我的目标是带大家掌握个人中心模块的完整设计。我们将学习如何设计用户的“名片”——<strong>个人资料页</strong>，并重点拆解个人中心页在<strong>登录与未登录</strong>两种状态下的差异化设计，以及其中常见的功能模块应该如何组织。</p><h3 id="7-7-2-个人资料页设计"><a href="#7-7-2-个人资料页设计" class="headerlink" title="7.7.2 个人资料页设计"></a>7.7.2 个人资料页设计</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721101941163.png" alt="image-20250721101941163"></p><p>个人资料页，是用户在我们的产品里，对外展示自己形象的“个人名片”。它的核心是允许用户自定义和编辑自己的个人信息。</p><h4 id="1-常见个人信息展示"><a href="#1-常见个人信息展示" class="headerlink" title="1. 常见个人信息展示"></a>1. 常见个人信息展示</h4><p>我设计个人资料页时，会根据产品定位，来决定需要提供哪些信息字段。对于一个内容产品，最常见的核心字段包括：</p><ul><li><strong>头像</strong>：用户最具识别性的标识，支持用户从相册上传或拍照。</li><li><strong>昵称</strong>：用户在社区中行走江湖的“代号”。</li><li><strong>简介</strong>：一段个性化的自我介绍。</li><li><strong>性别</strong></li><li><strong>生日</strong></li><li><strong>地区</strong></li></ul><p>在设计注册流程时，我有一个重要原则：<strong>渐进式信息收集</strong>。即，在最初的注册环节，我只要求用户提供最核心的信息（比如仅需要手机号验证），而将这些详细的个人资料，引导用户在后续有空时，再来个人中心慢慢完善。这能最大化地降低新用户的注册门槛。</p><h3 id="7-7-3-个人中心常见功能"><a href="#7-7-3-个人中心常见功能" class="headerlink" title="7.7.3 个人中心常见功能"></a>7.7.3 个人中心常见功能</h3><p>个人中心这个页面，它的设计比较特殊，因为我必须同时考虑“游客”和“主人”两种完全不同的状态。</p><h4 id="1-登录与未登录状态区别"><a href="#1-登录与未登录状态区别" class="headerlink" title="1. 登录与未登录状态区别"></a>1. 登录与未登录状态区别</h4><ul><li><p><strong>未登录状态</strong><br>当用户未登录时，个人中心这个页面的核心设计目标只有一个：<strong>引导用户去登录或注册</strong>。<br>正如案例图所示，此时的页面，我会隐藏掉所有个性化的信息和数据，用一个通用的图标和提示文案（如“点击登录”），来占据视觉中心。大部分功能入口（如“我的收藏”、“历史记录”）也会被隐藏或置灰，用户点击后，会直接跳转到登录页面。</p></li><li><p><strong>登录状态</strong><br>当用户登录后，页面则会完全“变身”为他专属的个人空间。此时的设计核心，是<strong>清晰的个人信息展示</strong>和<strong>便捷的功能入口聚合</strong>。页面的顶部，会展示用户的头像、昵称和核心数据（如作品数、关注数、粉丝数），下方则会罗列出所有与他相关的功能。</p></li></ul><h4 id="2-常见功能模块介绍"><a href="#2-常见功能模块介绍" class="headerlink" title="2. 常见功能模块介绍"></a>2. 常见功能模块介绍</h4><p>对于登录后的用户，我会把个人中心的功能入口，按照相关性进行逻辑分组，让用户能快速找到自己想要的功能。</p><ul><li><p><strong>核心资产类</strong>：这是用户最关心的，他们在我们平台沉淀下的“数字资产”。通常包括：</p><ul><li><strong>我的收藏</strong></li><li><strong>浏览历史</strong></li><li><strong>我的作品</strong>（针对创作者）</li></ul></li><li><p><strong>消息与互动类</strong>：</p><ul><li><strong>消息通知</strong>（包括系统通知、评论、点赞等）</li></ul></li><li><p><strong>账户与安全类</strong>：</p><ul><li><strong>实名认证</strong></li><li>账号与安全设置</li></ul></li><li><p><strong>App通用类</strong>：</p><ul><li><strong>用户反馈</strong></li><li><strong>系统设置</strong>（里面通常还包含“关于我们”、“退出登录”等）</li></ul></li></ul><h3 id="7-7-4-本节小结"><a href="#7-7-4-本节小结" class="headerlink" title="7.7.4 本节小结"></a>7.7.4 本节小结</h3><table><thead><tr><th align="left"><strong>模块</strong></th><th align="left"><strong>我的核心设计思考</strong></th></tr></thead><tbody><tr><td align="left"><strong>个人资料页</strong></td><td align="left">提供<strong>头像、昵称、简介</strong>等基础字段的编辑功能，遵循<strong>渐进式</strong>信息收集原则。</td></tr><tr><td align="left"><strong>个人中心（未登录）</strong></td><td align="left">设计核心是<strong>引导登录/注册</strong>，隐藏个性化信息，简化功能入口。</td></tr><tr><td align="left"><strong>个人中心（已登录）</strong></td><td align="left">设计核心是<strong>个人信息展示</strong>和<strong>功能入口聚合</strong>，将功能按逻辑分组（如资产类、账户类、通用类）。</td></tr></tbody></table><hr><h2 id="7-8-本章总结"><a href="#7-8-本章总结" class="headerlink" title="7.8 本章总结"></a>7.8 本章总结</h2><p>到这里，我们已经完整地设计出了一个内容产品用户端的所有核心模块。让我们最后回顾一下本章的整个设计旅程：</p><ul><li><strong>开门三板斧</strong>：我们首先设计了<code>引导页</code>、<code>启动页</code>和<code>闪屏页</code>，为用户打造了完美的“第一印象”。</li><li><strong>确立设计思路</strong>：我们通过<code>背景分析</code>→<code>角色提炼</code>→<code>用户场景</code>→<code>核心功能</code>的推演，确立了整个产品的设计“骨架”。</li><li><strong>设计核心模块</strong>：我们逐一设计了<code>注册登录</code>、<code>内容发布</code>、<code>内容列表与详情</code>、<code>内容分发</code>和<code>个人中心</code>这几个核心功能模块，为骨架“添上了血肉”。</li></ul><p>通过这一章的实战，我们已经将之前学到的所有理论，都转化为了具体、可视的产品设计方案。</p><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/51587.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/51587.html&quot;)">产品经理入门（七）：第七章：用户端设计</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/51587.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/51587.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/8024.html"><img class="prev-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理入门（六）：第六章：产品需求文档（PRD）撰写</div></div></a></div><div class="next-post pull-right"><a href="/posts/11780.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div><div><a href="/posts/11780.html" title="产品经理入门（八）：第八章：内容产品自媒体端设计"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理入门（七）：第七章：用户端设计",date:"2025-07-20 22:13:45",updated:"2025-07-21 14:52:07",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第七章：用户端设计\n\n在这一章，我们将进入一个完整的实战设计流程。我们将从用户第一次打开App的瞬间开始，一步步地设计出内容产品用户端的每一个核心模块，将我们之前学到的所有理论知识，全部应用到实践中。\n\n## 7.1 引导页 & 启动页 & 闪屏页\n\n当一个新用户满怀期待地下载并首次打开我们的App时，我们有且仅有一次机会，来给他留下一个完美的“第一印象”。这个第一印象，通常是由三个不同的页面共同构成的。我必须清晰地辨别它们，并为它们设计好各自的使命。\n\n### 7.1.1 学习目标\n\n在本节中，我的目标是，带大家清晰地区分**引导页、启动页、闪屏页**这三个极易混淆的概念。我将拆解它们各自的定义和核心作用，确保我们在产品设计中，能为用户的“第一次”，做出最合理的安排。\n\n### 7.1.2 引导页设计\n\n![image-20250720211813703](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720211813703.png)\n\n#### 1. 引导页概念\n\n**引导页（Onboarding Screens）**，是我为**首次**安装并打开我们App的用户，专门准备的一套“欢迎手册”。它通常是由3-5张可滑动的页面构成。\n\n#### 2. 引导页作用\n\n我设计引导页，通常是为了达成以下三个目的：\n* **产品功能介绍**：用最简洁的图文，向用户展示我们App最核心、最吸引人的1-3个功能。\n* **产品亮点说明**：传达我们产品的核心价值主张，告诉用户“我们是谁，我们能为你带来什么独特的好处”。\n* **推广品宣**：通过精美的设计和文案，在用户接触产品的最初几秒钟，就建立起我们产品的品牌调性和情感链接。\n\n### 7.1.3 启动页设计\n\n![image-20250720211951423](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720211951423.png)\n\n#### 1. 启动页概念\n\n**启动页（Launch Screen）**，是用户**每一次**打开App时，都会看到的第一个页面。它是一个短暂展示的静态页面，主要目的是在App后台加载资源时，给用户一个优雅的等待界面，避免出现白屏或黑屏。\n\n#### 2. 启动页作用\n\n启动页的作用非常纯粹，主要是品牌和信息的展示：\n* **品牌宣传**：在页面中心，清晰地展示我们产品的Logo和Slogan（品牌口号），在每一次启动中，反复加深用户的品牌认知。\n* **版权声明与版本号**：在页面底部，通常会标注公司的版权信息和当前App的版本号。\n\n### 7.1.4 闪屏页设计\n\n#### 1. 闪屏页概念\n\n**闪屏页（Splash Screen / Splash Ad）**，是一个可选的、通常在**启动页之后，首页之前**出现的页面。它本质上是一个全屏的、通常带有“跳过”按钮的广告或运营活动页面，一般会展示3-5秒。\n\n#### 2. 闪屏页作用\n\n闪屏页的作用完全是商业化和运营导向的：\n* **广告曝光**：“开屏广告”是App中非常重要的一个广告位，能为我们带来商业收入。\n* **活动推广**：我可以用它来推广平台级的、重要的运营活动，为活动进行预热和导流。\n* **内容推荐**：对于内容产品，我也可以用它来推荐平台S级的重磅内容，吸引用户点击。\n\n### 7.1.5 本节小结\n\n这三个页面，共同构成了用户打开App的“三部曲”。我将它们的核心区别，总结在了下面的表格里，来帮助我们加深记忆。\n\n| **页面类型** | **出现时机** | **核心目的** | **我的设计思考** |\n| :--- | :--- | :--- | :--- |\n| **引导页** | **仅在首次**安装启动时 | **教育用户**、介绍核心功能与价值 | 内容要少而精，突出核心亮点，让用户快速了解。 |\n| **启动页** | **每一次**启动时（加载期间） | **品牌展示**、传递Logo与Slogan | 界面要极简、干净，加载速度一定要快。 |\n| **闪屏页** | **每一次**启动时（加载后，首页前） | **商业运营**、广告曝光与活动推广 | 必须提供清晰的“跳过”按钮，不能强制用户观看。 |\n\n我的设计哲学是，用户的最终目的是进入App使用核心功能。因此，这个“开门”的过程，必须尽可能地快速、流畅。任何不必要的停留，都可能造成用户的流失。\n\n\n---\n\n## 7.2 用户端设计思路\n\n在动手画任何一个具体页面之前，我必须先建立起整个产品的“设计蓝图”。这个蓝图，就是我的用户端设计思路。它是一个从宏观到微观，从战略到执行的逻辑推演过程，能确保我后续所有的设计决策，都是有据可依、浑然一体的。\n\n### 7.2.1 学习目标\n\n在本节中，我的目标是带大家完整地走一遍这个“设计蓝图”的推演过程。我们将学习如何从一个**需求背景**出发，提炼出产品的核心**角色**，分析他们的**用户场景**，并最终推导出我们需要设计的**核心功能**。\n\n### 7.2.2 需求背景分析\n\n![image-20250720212444403](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212444403.png)\n\n我们假设，通过前期的市场分析和需求收集，我们已经为我们即将开发的“图文类内容产品”，确定了V1.0版本的四大核心策略，这就是我们一切设计的出发点和“宪法”。\n\n1.  **生产模式**：我们将采用 **PGC (专业生产内容) + UGC (用户生产内容)** 的双引擎模式，来保证内容的专业度和丰富度。\n2.  **审核方式**：我们将采用 **自动审核 + 人工审核** 的方式，来平衡审核的效率和准确性。\n3.  **分发方式**：我们将采用 **算法分发 + 订阅分发** 的方式，来兼顾用户发现新内容和关注老作者的需求。\n4.  **消费模式**：在V1.0版本，我们将采用**免费消费**的模式，以最大化地吸引早期用户。\n\n### 7.2.3 需求分析（角色提炼）\n\n![image-20250720212511146](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212511146.png)\n\n基于上述的背景，我首先要提炼出，在这个生态中，到底有哪几类“玩家”？\n\n* 因为有UGC，所以必然有内容的**生产者**，我称之为“**自媒体**”。\n* 因为有内容消费，所以必然有内容的**消费者**，我称之为“**普通用户**”。\n* 因为有审核和分发，所以必然有**管理者和运营者**，我称之为“**平台**”。\n\n在本章，我们聚焦的用户端设计，主要就是为了服务好“**普通用户**”和“**自媒体**”这两大核心外部角色。\n\n### 7.2.4 需求分析（用户场景）\n\n![image-20250720212618884](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212618884.png)\n\n明确了我们要服务的角色之后，我开始思考：这两类用户，在使用我们产品的过程中，会经历哪些最核心、最典型的场景？我将它们归纳为四大场景：\n\n1.  **获取身份**：无论是想看个性化推荐的“普通用户”，还是想发表内容的“自媒体”，他们都需要先在我们的平台上，拥有一个自己的身份。这就是**注册/登录**的场景。\n2.  **发布内容**：“自媒体”角色的核心诉求，就是将自己的图文作品发布到平台上，与大家分享。这就是**内容发布**的场景。\n3.  **浏览&互动内容**：“普通用户”的核心诉求，是发现、阅读自己感兴趣的文章，并对内容和作者表达自己的喜好。这就是**内容消费与互动**的场景。\n4.  **个人中心**：所有用户，都需要一个地方来管理自己的个人信息、查看自己发布或收藏过的内容。这就是**个人中心管理**的场景。\n\n### 7.2.5 用户端核心功能设计\n\n![image-20250720212728816](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720212728816.png)\n\n最后一步，也是从“分析”到“设计”最关键的一步，就是将上述的用户场景，映射为我们产品需要提供的**核心功能模块**。\n\n* 为了支撑“获取身份”场景，我们需要设计 **注册登录** 功能。\n* 为了支撑“发布内容”场景，我们需要设计 **内容发布页**。\n* 为了支撑“浏览&互动内容”场景，我们需要设计 **内容列表页** 和 **内容详情页**。\n* 为了支撑“个人中心”场景，我们需要设计 **个人中心** 模块。\n\n这五大核心功能，就构成了我们内容产品用户端的“骨架”。在接下来的小节中，我们将逐一地，对这个骨架进行“添肉画皮”，完成每一个模块的详细设计。\n\n### 7.2.6 本节小结\n\n我将这个设计思路的推演过程，总结为下面这张表格：\n\n| **思考步骤** | **核心产出** | **我的目的** |\n| :--- | :--- | :--- |\n| **背景分析** | 四大产品决策 | 确立项目的“宪法”，是所有设计的最高准则。 |\n| **角色提炼** | 三大核心角色 | 明确我们到底要为谁服务。 |\n| **场景分析** | 四大核心场景 | 梳理出用户的完整旅程和核心诉求。 |\n| **功能设计** | 五大核心功能 | 将用户诉求，转化为具体、可设计的产品模块。 |\n\n\n\n---\n\n## 7.3 注册登录\n\n在我们的设计思路中，“获取身份”是所有用户要经历的第一个场景。**注册登录**功能，就是支撑这个场景、我们为用户开启的“第一扇门”。\n\n在我看来，这扇门的设计至关重要。一个好的注册登录体验，应该像酒店的自动门一样，让用户安全、无感、顺畅地通过；\n\n而一个糟糕的设计，则像一道生锈的铁门，会在用户进入前，就把他们拒之门外。\n\n从本质上讲，我设计的所有注册登录流程，都是为了完成两件事：**身份识别**（你是谁？）和**门槛验证**（如何证明你是你？）。\n\n### 7.3.1 学习目标\n\n在本节中，我的目标是带大家掌握现代App中最主流的三种注册登录方式的设计。我们将深入分析它们的实现逻辑、优缺点，并探讨如何通过安全验证设计，来保障我们产品的“大门”既方便又安全。\n\n### 7.3.2 注册登录的目的\n\n![image-20250720213340787](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720213340787.png)\n\n在设计之前，我总会先思考：我们为什么需要用户注册登录？\n\n* **从用户的角度：为了“获得身份”**\n    一个注册后的身份，意味着用户在我们的产品里，有了一个专属的“数字资产”账户。这能帮助他们：\n    * **记录跟随**：可以保存自己的浏览历史、收藏、发布的文章等。\n    * **获得个性化服务**：可以接收到我们为他量身定制的内容推荐。\n    * **积累个人资产**：可以拥有自己的积分、等级、虚拟财产。\n\n* **从平台的角度：为了“区分用户”**\n    用户的注册，能帮助我们平台更好地运营：\n    * **精细化运营**：可以针对不同用户群体，推送不同的内容或活动。\n    * **信息记录**：可以更好地掌握平台的用户构成和自媒体信息。\n    * **信息分发**：能够针对用户的身份和喜好，进行更精准的内容分发。\n\n### 7.3.3 常见注册登录方式介绍\n\n明确了目的，我们来看实现“门槛验证”的三种主流方式。\n\n#### 1. 手机号+验证码\n\n![image-20250720215110314](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720215110314.png)\n\n这是目前国内互联网产品最主流、最便捷的方式，它的核心逻辑是“**手机在手，身份我有**”。\n\n* **核心逻辑**：将注册和登录合二为一。用户输入手机号，接收并回填验证码，系统验证通过后，若该手机号未注册，则自动为其创建账户并登录；若已注册，则直接登录。\n* **优点**：**方便快捷**，用户无需记忆复杂的密码，操作路径最短。\n* **缺点**：有一定**账户信息风险**（如手机号丢失），且平台需要承担较高的**短信成本**。\n\n#### 2. 手机号+验证码+密码\n\n![image-20250720215834551](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720215834551.png)\n\n这是最传统，也是账户体系最稳固的一种方式。\n\n* **核心逻辑**：注册和登录是分离的。用户首次使用时，需要通过“手机号+验证码”验证身份，并**设置一个密码**来完成注册。后续登录时，主要使用“手机号+密码”的方式。\n* **优点**：**安全性更高**，登录不受运营商短信通道影响。\n* **缺点**：注册流程更长，**操作成本相对较高**，可能会流失一部分没有耐心的用户。\n\n#### 3. 第三方注册登录\n\n![image-20250720220120919](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720220120919.png)\n\n这是借助“巨人”的力量，让用户快速进入我们产品的方式。\n\n* **核心逻辑**：用户授权我们App，去获取他在某个第三方大平台（如微信、QQ、微博）上的基本公开信息（如昵称、头像）作为身份标识，从而完成注册或登录。\n* **优点**：**门槛极低**，用户一键授权即可，体验非常流畅，能有效提升新用户的注册转化率。\n* **缺点**：我们能**获取的用户信息非常有限**，不利于后续的精细化运营。同时，账户的安全性依赖于第三方平台。\n\n### 7.3.4 注册登录安全验证设计\n\n![image-20250720220200195](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720220200195.png)\n\n为了防止被机器人恶意、批量地注册或登录（俗称“刷接口”），也为了保护我们宝贵的短信费用，我必须在注册登录流程中，加入一道“**安全验证**”的屏障。\n\n这道屏障，通常出现在用户输入完手机号、点击“获取验证码”按钮之后。常见的验证形式有：\n* **智能验证**（如“我不是机器人”勾选框）\n* **文字点选验证**（要求用户点选图中指定的文字）\n* **拼图验证**（要求用户拖动滑块完成拼图）\n\n此外，对于已注册用户，为了提供更便捷的登录体验，我还会设计支持**指纹、面容ID**等生物识别验证方式。\n\n### 7.3.5 本节小结\n\n在实际设计中，我很少只提供一种登录方式，而是采用组合策略。我将这三种方式的选择思路总结如下：\n\n| **登录方式** | **核心逻辑** | **我的设计策略** |\n| :--- | :--- | :--- |\n| **手机号+验证码** | 便捷性优先 | 作为默认和首选的登录方式，最大化地降低用户操作成本。 |\n| **手机号+验证码+密码** | 安全性优先 | 作为一种可选的账户升级或安全设置，让注重安全的用户可以绑定密码。 |\n| **第三方登录** | 借力，信任度优先 | 作为一种重要的补充登录方式，并排放在主登录按钮下方，给用户多一种便捷选择。 |\n\n\n\n\n\n---\n\n## 7.4 内容发布\n\n当我们的“自媒体”用户，也就是内容创作者，想要把他们的想法和作品分享出来时，他们就需要一个强大、易用的**内容发布**功能。这是连接“创作者”与“平台”的桥梁，这个桥梁的体验，直接决定了我们平台内容的数量和质量。\n\n### 7.4.1 学习目标\n\n在本节中，我的目标是带大家深入研究内容发布页的设计。我们将探讨图文内容常见的两种展现形式，并拆解这两种形式下，内容发布页各自的设计要点和核心元素。\n\n### 7.4.2 图文内容的展现形式\n\n![image-20250720220845581](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720220845581.png)\n\n在设计发布页之前，我首先要明确，我们平台的内容，最终将以什么样的形式呈现给用户。这通常决定了我们发布器的形态。最常见的两种形式是：\n\n1.  **图文分离展现形式**：图片和文字是分开展示的。通常是上方为图片（或视频），下方为独立的、大段的文字描述。\n2.  **图文混排展现形式**：图片可以自由地插入到文章的任意位置，形成我们常说的“富文本”效果。\n\n### 7.4.3 内容发布页的设计\n\n#### 1. 图文分离形式设计要点\n\n![image-20250720221014583](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221014583.png)\n\n这种形式的发布器，设计上更追求简洁、快速。我通常会关注以下几个设计要点：\n* **核心元素**：必须包含**文本输入区**、**图片/视频上传入口**（通常是一个“+”号按钮），以及**发布/取消按钮**。\n* **字符长度限制**：需要明确告知用户，正文最多可以输入多少字。\n* **图片数量限制**：需要明确告知用户，最多可以上传多少张图片。\n* **发布状态变化**：当用户未输入任何内容时，“发布”按钮应为置灰不可用状态，以避免发布空内容。\n* **草稿箱功能**：当用户意外退出时，我需要设计一个草稿箱功能，自动保存用户未发布的内容，防止心血白费。\n\n#### 2. 图文混排形式设计要点\n\n![image-20250720221124889](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221124889.png)\n\n这种形式的发布器，功能更强大，类似于一个移动端的“Word编辑器”。除了包含图文分离形式的所有要点外，我还会特别关注：\n* **标题输入**：通常会提供一个独立的标题输入框，并有字数限制。\n* **富文本编辑**：支持在正文的任意位置插入图片或视频，并提供基础的排版功能，如加粗、对齐等。\n* **发布与取消**：这两个按钮必须始终清晰可见。\n* **图片/视频上传**：提供清晰的上传入口和进度反馈。\n\n---\n\n## 7.5 内容列表及内容详情\n\n当内容被成功发布后，它就需要被呈现给“普通用户”。这个呈现的过程，主要由两个核心页面来承载：**内容列表页**和**内容详情页**。\n\n### 7.5.1 学习目标\n\n在本节中，我的目标是带大家掌握内容列表页和详情页的设计精髓。我们将学习如何设计一个信息丰富、吸引眼球的列表页，一个沉浸、易读的详情页，以及如何通过巧妙的互动设计，来提升用户的参与感和社区的活跃度。\n\n### 7.5.2 内容列表页设计\n\n![image-20250720221204736](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221204736.png)\n\n内容列表页，是我为用户打造的“内容超市”。它的核心使命，是让用户能在这个超市里，快速地、高效地发现自己可能感兴趣的“商品”（内容）。\n\n#### 1. 列表页元素设计\n\n一个标准的内容卡片，通常包含三类信息：\n* **内容基本信息**：标题、封面图（如果有）、内容摘要。\n* **发布者信息**：作者的头像、昵称。\n* **互动信息**：点赞数、评论数、分享数等。\n\n#### 2. 列表页设计要点\n\n* **图文权重分配**：我需要根据产品定位，来决定图片和文字的权重。左侧的列表形式，更注重文字信息的传递；而右侧的瀑布流形式，则更强调图片的视觉冲击力。\n* **内容排列规则**：列表的排序规则是什么？是按照发布时间倒序？还是按照热度排序？我必须定义清晰的规则。\n\n### 7.5.3 内容详情页设计\n\n当用户在列表页对某项内容产生兴趣并点击后，就进入了**内容详情页**。这是用户进行沉浸式阅读和深度消费的核心场所。\n\n#### 1. 图文混排详情页设计要点\n\n![image-20250720221259547](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221259547.png)\n\n* **核心元素**：必须清晰地展示**导航栏、内容标题、作者信息、发布时间、正文（图文混排）**。\n* **设计要点**：\n    * **支持分段**：长文章必须分段，以提升可读性。\n    * **图片可交互**：图片通常需要支持点击查看大图。\n    * **视觉权重**：正文的视觉权重最高，其他辅助信息（如作者、时间）则相对弱化。\n    * **敏感字过滤**：需要对评论区等UGC内容进行敏感词过滤。\n\n#### 2. 图文分离详情页设计要点\n\n![image-20250720221401530](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221401530.png)\n\n* **核心元素**：与混排类似，但**图片区**和**正文区**是明确分开的。\n* **设计要点**：与混排页的设计要点基本一致，同样需要关注分段、图片交互、视觉权重和敏感字过滤。\n\n### 7.5.4 内容互动设计\n\n![image-20250720221435264](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221435264.png)\n\n#### 1. 互动行为的重要性\n\n在我看来，互动是内容产品的“灵魂”。它不仅仅是一些按钮，而是连接**用户、作者、内容、平台**四方的桥梁。\n* 对于**消费者**，互动是表达情绪的方式。\n* 对于**生产者**，互动是对自己创作的激励。\n* 对于**内容**，互动是区分其质量和热度的标尺。\n* 对于**平台**，互动是口碑营销和用户监督的手段。\n\n#### 2. 常见互动行为设计要点\n\n![image-20250720221636401](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250720221636401.png)\n\n\n\n* **核心互动**：**点赞**和**分享**，是用户成本最低、我们最希望用户去做的行为。因此，这两个功能的按钮，在页面上必须非常突出和便捷。\n* **主要分享渠道**：分享功能，我通常会优先支持微信、朋友圈、QQ和微博这几个主流渠道。\n* **次要互动**：对于一些不常用的功能，比如**删除**（作者可见）、**举报**（用户可见），我通常会将它们收纳在右上角的“更多”按钮中，避免干扰主界面的信息。\n\n### 7.5.5 本节小结\n\n| **页面/模块** | **我的核心设计思考** |\n| :--- | :--- |\n| **内容发布页** | 根据**图文分离/混排**的展现形式，来决定发布器的复杂度和设计要点。 |\n| **内容列表页** | 核心是**信息卡片**的设计，需要平衡好图文权重和信息密度。 |\n| **内容详情页** | 核心是提供**沉浸、易读**的消费体验，并引导用户进行互动。 |\n| **内容互动设计** | **突出核心互动（点赞/分享）**，将次要互动收纳起来，保持界面简洁。 |\n\n\n\n---\n\n## 7.6 内容分发\n\n我们的内容，已经通过发布功能进入了平台的“内容库”，详情页也为用户提供了沉浸式的消费体验。但现在，一个核心问题摆在面前：**在海量的内容库里，我们应该把哪些内容，在什么时候，以什么方式，呈现在用户面前？**\n\n这就是内容分发系统需要解决的问题。它是连接“海量内容”与“个性化用户”的桥梁。\n\n### 7.6.1 学习目标\n\n在本节中，我的目标是带大家深入了解内容产品背后最主流的三种分发模式的设计。我们将重点拆解**算法分发**的核心三要素，学习**用户画像**和**标签**的概念，并了解**热度排序**和**订阅分发**的设计逻辑。\n\n### 7.6.2 算法分发设计\n\n![image-20250721084124586](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084124586.png)\n\n#### 1. 算法分发定义与要素\n\n**算法分发**，是我认为的现代内容产品的“发动机”。我把它定义为：**一套能根据用户数据，自动地、个性化地为用户推荐其可能感兴趣的内容的系统**。\n\n要让这台发动机运转起来，我必须为它准备好三个核心要素：\n* **用户画像 (User Persona)**：深入地理解我的用户，知道“他是谁，他喜欢什么”。\n* **内容画像 (Content Profile)**：深入地理解我的内容，知道“它是什么，它讲了什么”。\n* **算法模型 (Algorithm Model)**：建立一套高效的匹配和排序规则，将最合适的内容，推荐给最合适的用户。\n\n#### 2. 用户画像介绍\n\n算法分发的前提，是了解用户的喜好。**用户画像**，就是我用来“了解”用户的工具。\n\n我把它定义为：**根据用户各维度的真实数据，抽象出的一个标签化的用户模型**。\n我通常会从以下四个维度，来为用户构建画像：\n* **基本属性**：如姓名、性别、年龄、地域等。\n* **社会属性**：如职业、收入、公司、文化等。\n* **行为属性**：这是最重要的，包括用户在我们产品里的登录、活跃、评论、点赞等一切行为。\n* **消费属性**：如果产品有付费点，还包括用户的消费金额、次数等。\n\n#### 3. 标签分类及应用\n\n![image-20250721084235993](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084235993.png)\n\n用户画像和内容画像，都是通过“**标签**”来具体实现的。标签，就是对目标的量化标识和描述。我们的核心工作，就是**为内容和用户，打上同一套标签体系**，从而实现精准匹配。\n\n我把用户标签，分为两大类：\n* **静态标签**：指那些在较长时间内，保持稳定不变的标签，通常具有“先天性”。比如用户的**性别、年龄、星座、地域**等。\n\n![image-20250721084734425](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084734425.png)\n\n\n\n* **动态标签**：指根据用户的**实时操作行为**，动态变化的标签。比如用户刚刚搜索了什么、点赞了什么、购买了什么。这些动态标签，更能反映用户当下的、即时的兴趣。一个完整的用户画像，是静态标签和动态标签的结合体。\n\n![image-20250721084743710](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084743710.png)\n\n#### 4. 算法分发设计逻辑\n\n![image-20250721084704681](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084704681.png)\n\n有了用户画像和内容画像，我的算法推荐逻辑通常遵循四步走：\n1.  **权重设置**：我会为用户的不同行为，赋予不同的权重。比如，“分享”行为的权重，一定高于“点赞”。\n2.  **贴标签**：系统自动为内容和用户打上标签。\n3.  **匹配推荐**：算法模型开始工作，为用户匹配出，与他自身标签相符的内容。\n4.  **排序**：对所有匹配出的内容，根据一个“热度/质量分”公式，进行排序，决定最终呈现给用户的顺序。\n\n我们来看一个具体的**算法分发规则案例**：\n\n![image-20250721084719114](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721084719114.png)\n\n* **计算维度与分值**：我们可以定义一个内容的总分值公式，比如：`内容总分 = 分享数*2 + 评论数*2 + 点赞数*1 + 收藏数*1`。\n* **推送规则**：\n    1.  优先匹配该用户标签权重最高的TOP5的标签内容。\n    2.  根据内容总分值排序，分页推送，每页8条。\n    3.  下拉刷新时，推送新产生的内容。\n    4.  已经推荐过的内容，不再重复推荐。\n\n\n\n\n---\n\n### 7.6.3 热度排序设计\n\n前面我们谈的算法分发，完全是根据“人-内容”的个性化匹配进行推荐的。但这里面可能会存在一个问题：如果推荐出的内容本身质量不高怎么办？\n\n为了过滤掉低质量内容，并让用户感知到“大家都在看什么”，我需要引入一种全局性的排序机制——**热度排序**。\n\n#### 1. 热度排序逻辑与设计要点\n\n![image-20250721085440264](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085440264.png)\n\n我设计热度排序的逻辑，通常遵循四步走：\n1.  **梳理维度**：首先，我会梳理出所有能够反映内容质量和受欢迎程度的用户行为维度，比如**分享、点赞、评论、收藏**等。\n2.  **权重设置**：其次，我会基于这些维度，为不同的行为设置不同的权重。比如，我认为“分享”和“评论”比“点赞”更能代表用户的认可度，因此会给予更高的分值。\n3.  **标准计算**：然后，系统会根据用户产生的实时行为数据，套入我们设定的计分公式，为每一篇内容动态地计算出一个“热度分值”。\n4.  **排序**：最后，系统根据计算出的“热度分值”进行排序。这里需要特别注意，为了**避免热度榜被少数爆款内容长期霸占（固化）**，我通常会在公式中加入“时间衰减”因子，让新发布的内容有更多的曝光机会。\n\n#### 2. 热度排序规则示例\n\n![image-20250721085555753](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085555753.png)\n\n我们来看一个更具体、更复杂的规则案例，它巧妙地融合了**个性化推荐**和**热度排序**。\n\n* **内容总分值公式**：`S = 分享数(A)*2 + 点赞数(B)*1 + 评论数(C)*2 + 收藏数(D)*1`\n* **推送规则**：\n    * **规则1**：筛选出**24小时内**发布的所有内容。\n    * **规则2**：筛选出**24小时至72小时前**发布的内容中，热度分值**S>=30**的全部内容。\n* **排序与分发逻辑**：\n    1.  在信息流中，优先推送满足“规则1”的内容（确保新鲜度），按发布时间由近到远排列；当“规则1”的内容不足时，再推送满足“规则2”的内容（补充高质量老内容），按热度分值由高到低排列。\n    2.  每次下拉刷新时，推送新产生的内容，每次最多推送8条。\n\n### 7.6.4 订阅分发设计\n\n#### 1. 订阅分发的核心逻辑\n\n![image-20250721085717235](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085717235.png)\n\n**订阅分发**，是将内容的选择权，完全交还给用户的一种方式。它的逻辑非常简单：“**我只看我关注的人发布的内容**”。这是一种基于“人”的、强关系的分发模式。\n\n它的核心业务流程是：自媒体发布内容 → 用户在看到后选择关注该自媒体 → 系统此后会自动将该自媒体的新内容，分发到该用户的“关注”信息流中 → 用户随时可以查看。\n\n#### 2. 订阅分发实现的关键功能\n\n![image-20250721085801010](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721085801010.png)\n\n要实现这个逻辑，我的设计通常会围绕以下三个要点展开：\n\n* **连接 (Connection)**：我必须为用户和作者建立“连接”的桥梁。这通常是在作者的个人主页、内容详情页等位置，提供一个清晰的“**关注**”功能按钮。\n* **推荐内容 (Content Delivery)**：我需要为用户提供一个专门的消费场所，也就是一个独立的“**关注**”信息流（Feed）。这个信息流里，只包含用户已关注作者发布的内容。\n* **排序 (Ranking)**：这个信息流的排序规则通常非常简单，就是严格按照**内容发布的时间倒序排列**，确保用户看到的永远是最新的内容。\n\n### 7.6.5 本节小结\n\n我将这三种核心的分发方式总结如下，在我的产品设计中，我通常会将它们组合使用，来满足用户不同的内容发现需求。\n\n| **分发方式** | **核心逻辑** | **我的设计要点** |\n| :--- | :--- | :--- |\n| **算法分发** | **人-内容匹配** | 定义清晰的用户画像、内容标签、推荐与排序规则<br>`内容总分 = 分享数*2 + 评论数*2 + 点赞数*1 + 收藏数*1`。 |\n| **热度排序** | **内容热度值计算** | 定义合理的热度计算公式，并考虑时间衰减，避免榜单固化。<br>S = 分享数(A)*2 + 点赞数(B)*1 + 评论数(C)*2 + 收藏数(D)*1 |\n| **订阅分发** | **用户主动关注** | 设计好关注/取关功能，并提供独立的“关注”信息流。 |\n\n\n\n\n\n---\n\n## 7.7 个人中心\n\n当用户在我们的产品里消费、互动、创作，留下了一系列数字足迹之后，他们需要一个“家”，来安放和管理这些属于自己的信息和资产。这个“家”，就是**个人中心**。\n\n对我来说，个人中心是提升用户归属感和粘性的关键模块，它承载了用户的个人身份，也聚合了产品中与“个人”相关的各种高阶功能。\n\n### 7.7.1 学习目标\n\n在本节，我的目标是带大家掌握个人中心模块的完整设计。我们将学习如何设计用户的“名片”——**个人资料页**，并重点拆解个人中心页在**登录与未登录**两种状态下的差异化设计，以及其中常见的功能模块应该如何组织。\n\n### 7.7.2 个人资料页设计\n\n![image-20250721101941163](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721101941163.png)\n\n个人资料页，是用户在我们的产品里，对外展示自己形象的“个人名片”。它的核心是允许用户自定义和编辑自己的个人信息。\n\n#### 1. 常见个人信息展示\n\n我设计个人资料页时，会根据产品定位，来决定需要提供哪些信息字段。对于一个内容产品，最常见的核心字段包括：\n* **头像**：用户最具识别性的标识，支持用户从相册上传或拍照。\n* **昵称**：用户在社区中行走江湖的“代号”。\n* **简介**：一段个性化的自我介绍。\n* **性别**\n* **生日**\n* **地区**\n\n在设计注册流程时，我有一个重要原则：**渐进式信息收集**。即，在最初的注册环节，我只要求用户提供最核心的信息（比如仅需要手机号验证），而将这些详细的个人资料，引导用户在后续有空时，再来个人中心慢慢完善。这能最大化地降低新用户的注册门槛。\n\n### 7.7.3 个人中心常见功能\n\n个人中心这个页面，它的设计比较特殊，因为我必须同时考虑“游客”和“主人”两种完全不同的状态。\n\n#### 1. 登录与未登录状态区别\n\n* **未登录状态**\n    当用户未登录时，个人中心这个页面的核心设计目标只有一个：**引导用户去登录或注册**。\n    正如案例图所示，此时的页面，我会隐藏掉所有个性化的信息和数据，用一个通用的图标和提示文案（如“点击登录”），来占据视觉中心。大部分功能入口（如“我的收藏”、“历史记录”）也会被隐藏或置灰，用户点击后，会直接跳转到登录页面。\n\n* **登录状态**\n    当用户登录后，页面则会完全“变身”为他专属的个人空间。此时的设计核心，是**清晰的个人信息展示**和**便捷的功能入口聚合**。页面的顶部，会展示用户的头像、昵称和核心数据（如作品数、关注数、粉丝数），下方则会罗列出所有与他相关的功能。\n\n#### 2. 常见功能模块介绍\n\n对于登录后的用户，我会把个人中心的功能入口，按照相关性进行逻辑分组，让用户能快速找到自己想要的功能。\n\n* **核心资产类**：这是用户最关心的，他们在我们平台沉淀下的“数字资产”。通常包括：\n    * **我的收藏**\n    * **浏览历史**\n    * **我的作品**（针对创作者）\n\n* **消息与互动类**：\n    * **消息通知**（包括系统通知、评论、点赞等）\n\n* **账户与安全类**：\n    * **实名认证**\n    * 账号与安全设置\n\n* **App通用类**：\n    * **用户反馈**\n    * **系统设置**（里面通常还包含“关于我们”、“退出登录”等）\n\n### 7.7.4 本节小结\n\n| **模块** | **我的核心设计思考** |\n| :--- | :--- |\n| **个人资料页** | 提供**头像、昵称、简介**等基础字段的编辑功能，遵循**渐进式**信息收集原则。 |\n| **个人中心（未登录）**| 设计核心是**引导登录/注册**，隐藏个性化信息，简化功能入口。 |\n| **个人中心（已登录）**| 设计核心是**个人信息展示**和**功能入口聚合**，将功能按逻辑分组（如资产类、账户类、通用类）。 |\n\n---\n\n## 7.8 本章总结\n\n到这里，我们已经完整地设计出了一个内容产品用户端的所有核心模块。让我们最后回顾一下本章的整个设计旅程：\n\n* **开门三板斧**：我们首先设计了`引导页`、`启动页`和`闪屏页`，为用户打造了完美的“第一印象”。\n* **确立设计思路**：我们通过`背景分析`→`角色提炼`→`用户场景`→`核心功能`的推演，确立了整个产品的设计“骨架”。\n* **设计核心模块**：我们逐一设计了`注册登录`、`内容发布`、`内容列表与详情`、`内容分发`和`个人中心`这几个核心功能模块，为骨架“添上了血肉”。\n\n通过这一章的实战，我们已经将之前学到的所有理论，都转化为了具体、可视的产品设计方案。\n\n\n\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%83%E7%AB%A0%EF%BC%9A%E7%94%A8%E6%88%B7%E7%AB%AF%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.</span> <span class="toc-text">第七章：用户端设计</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#7-1-%E5%BC%95%E5%AF%BC%E9%A1%B5-%E5%90%AF%E5%8A%A8%E9%A1%B5-%E9%97%AA%E5%B1%8F%E9%A1%B5"><span class="toc-number">1.1.</span> <span class="toc-text">7.1 引导页 &amp; 启动页 &amp; 闪屏页</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.1.1.</span> <span class="toc-text">7.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-2-%E5%BC%95%E5%AF%BC%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.1.2.</span> <span class="toc-text">7.1.2 引导页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%BC%95%E5%AF%BC%E9%A1%B5%E6%A6%82%E5%BF%B5"><span class="toc-number">1.1.2.1.</span> <span class="toc-text">1. 引导页概念</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%BC%95%E5%AF%BC%E9%A1%B5%E4%BD%9C%E7%94%A8"><span class="toc-number">1.1.2.2.</span> <span class="toc-text">2. 引导页作用</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-3-%E5%90%AF%E5%8A%A8%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.1.3.</span> <span class="toc-text">7.1.3 启动页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%90%AF%E5%8A%A8%E9%A1%B5%E6%A6%82%E5%BF%B5"><span class="toc-number">1.1.3.1.</span> <span class="toc-text">1. 启动页概念</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%90%AF%E5%8A%A8%E9%A1%B5%E4%BD%9C%E7%94%A8"><span class="toc-number">1.1.3.2.</span> <span class="toc-text">2. 启动页作用</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-4-%E9%97%AA%E5%B1%8F%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.1.4.</span> <span class="toc-text">7.1.4 闪屏页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%97%AA%E5%B1%8F%E9%A1%B5%E6%A6%82%E5%BF%B5"><span class="toc-number">1.1.4.1.</span> <span class="toc-text">1. 闪屏页概念</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E9%97%AA%E5%B1%8F%E9%A1%B5%E4%BD%9C%E7%94%A8"><span class="toc-number">1.1.4.2.</span> <span class="toc-text">2. 闪屏页作用</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-number">1.1.5.</span> <span class="toc-text">7.1.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-2-%E7%94%A8%E6%88%B7%E7%AB%AF%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-number">1.2.</span> <span class="toc-text">7.2 用户端设计思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.2.1.</span> <span class="toc-text">7.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-2-%E9%9C%80%E6%B1%82%E8%83%8C%E6%99%AF%E5%88%86%E6%9E%90"><span class="toc-number">1.2.2.</span> <span class="toc-text">7.2.2 需求背景分析</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-3-%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%EF%BC%88%E8%A7%92%E8%89%B2%E6%8F%90%E7%82%BC%EF%BC%89"><span class="toc-number">1.2.3.</span> <span class="toc-text">7.2.3 需求分析（角色提炼）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-4-%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%EF%BC%88%E7%94%A8%E6%88%B7%E5%9C%BA%E6%99%AF%EF%BC%89"><span class="toc-number">1.2.4.</span> <span class="toc-text">7.2.4 需求分析（用户场景）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-5-%E7%94%A8%E6%88%B7%E7%AB%AF%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.2.5.</span> <span class="toc-text">7.2.5 用户端核心功能设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-6-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-number">1.2.6.</span> <span class="toc-text">7.2.6 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-3-%E6%B3%A8%E5%86%8C%E7%99%BB%E5%BD%95"><span class="toc-number">1.3.</span> <span class="toc-text">7.3 注册登录</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.3.1.</span> <span class="toc-text">7.3.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-2-%E6%B3%A8%E5%86%8C%E7%99%BB%E5%BD%95%E7%9A%84%E7%9B%AE%E7%9A%84"><span class="toc-number">1.3.2.</span> <span class="toc-text">7.3.2 注册登录的目的</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-3-%E5%B8%B8%E8%A7%81%E6%B3%A8%E5%86%8C%E7%99%BB%E5%BD%95%E6%96%B9%E5%BC%8F%E4%BB%8B%E7%BB%8D"><span class="toc-number">1.3.3.</span> <span class="toc-text">7.3.3 常见注册登录方式介绍</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%89%8B%E6%9C%BA%E5%8F%B7-%E9%AA%8C%E8%AF%81%E7%A0%81"><span class="toc-number">1.3.3.1.</span> <span class="toc-text">1. 手机号+验证码</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%89%8B%E6%9C%BA%E5%8F%B7-%E9%AA%8C%E8%AF%81%E7%A0%81-%E5%AF%86%E7%A0%81"><span class="toc-number">1.3.3.2.</span> <span class="toc-text">2. 手机号+验证码+密码</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%AC%AC%E4%B8%89%E6%96%B9%E6%B3%A8%E5%86%8C%E7%99%BB%E5%BD%95"><span class="toc-number">1.3.3.3.</span> <span class="toc-text">3. 第三方注册登录</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-4-%E6%B3%A8%E5%86%8C%E7%99%BB%E5%BD%95%E5%AE%89%E5%85%A8%E9%AA%8C%E8%AF%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.3.4.</span> <span class="toc-text">7.3.4 注册登录安全验证设计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-number">1.3.5.</span> <span class="toc-text">7.3.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-4-%E5%86%85%E5%AE%B9%E5%8F%91%E5%B8%83"><span class="toc-number">1.4.</span> <span class="toc-text">7.4 内容发布</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-4-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.4.1.</span> <span class="toc-text">7.4.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-4-2-%E5%9B%BE%E6%96%87%E5%86%85%E5%AE%B9%E7%9A%84%E5%B1%95%E7%8E%B0%E5%BD%A2%E5%BC%8F"><span class="toc-number">1.4.2.</span> <span class="toc-text">7.4.2 图文内容的展现形式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-4-3-%E5%86%85%E5%AE%B9%E5%8F%91%E5%B8%83%E9%A1%B5%E7%9A%84%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.4.3.</span> <span class="toc-text">7.4.3 内容发布页的设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%9B%BE%E6%96%87%E5%88%86%E7%A6%BB%E5%BD%A2%E5%BC%8F%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-number">1.4.3.1.</span> <span class="toc-text">1. 图文分离形式设计要点</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%9B%BE%E6%96%87%E6%B7%B7%E6%8E%92%E5%BD%A2%E5%BC%8F%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-number">1.4.3.2.</span> <span class="toc-text">2. 图文混排形式设计要点</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-5-%E5%86%85%E5%AE%B9%E5%88%97%E8%A1%A8%E5%8F%8A%E5%86%85%E5%AE%B9%E8%AF%A6%E6%83%85"><span class="toc-number">1.5.</span> <span class="toc-text">7.5 内容列表及内容详情</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-5-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.5.1.</span> <span class="toc-text">7.5.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-5-2-%E5%86%85%E5%AE%B9%E5%88%97%E8%A1%A8%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.5.2.</span> <span class="toc-text">7.5.2 内容列表页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%88%97%E8%A1%A8%E9%A1%B5%E5%85%83%E7%B4%A0%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.5.2.1.</span> <span class="toc-text">1. 列表页元素设计</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%88%97%E8%A1%A8%E9%A1%B5%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-number">1.5.2.2.</span> <span class="toc-text">2. 列表页设计要点</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-5-3-%E5%86%85%E5%AE%B9%E8%AF%A6%E6%83%85%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.5.3.</span> <span class="toc-text">7.5.3 内容详情页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%9B%BE%E6%96%87%E6%B7%B7%E6%8E%92%E8%AF%A6%E6%83%85%E9%A1%B5%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-number">1.5.3.1.</span> <span class="toc-text">1. 图文混排详情页设计要点</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%9B%BE%E6%96%87%E5%88%86%E7%A6%BB%E8%AF%A6%E6%83%85%E9%A1%B5%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-number">1.5.3.2.</span> <span class="toc-text">2. 图文分离详情页设计要点</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-5-4-%E5%86%85%E5%AE%B9%E4%BA%92%E5%8A%A8%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.5.4.</span> <span class="toc-text">7.5.4 内容互动设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%BA%92%E5%8A%A8%E8%A1%8C%E4%B8%BA%E7%9A%84%E9%87%8D%E8%A6%81%E6%80%A7"><span class="toc-number">1.5.4.1.</span> <span class="toc-text">1. 互动行为的重要性</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B8%B8%E8%A7%81%E4%BA%92%E5%8A%A8%E8%A1%8C%E4%B8%BA%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-number">1.5.4.2.</span> <span class="toc-text">2. 常见互动行为设计要点</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-5-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-number">1.5.5.</span> <span class="toc-text">7.5.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-6-%E5%86%85%E5%AE%B9%E5%88%86%E5%8F%91"><span class="toc-number">1.6.</span> <span class="toc-text">7.6 内容分发</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-6-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.6.1.</span> <span class="toc-text">7.6.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-6-2-%E7%AE%97%E6%B3%95%E5%88%86%E5%8F%91%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.6.2.</span> <span class="toc-text">7.6.2 算法分发设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%AE%97%E6%B3%95%E5%88%86%E5%8F%91%E5%AE%9A%E4%B9%89%E4%B8%8E%E8%A6%81%E7%B4%A0"><span class="toc-number">1.6.2.1.</span> <span class="toc-text">1. 算法分发定义与要素</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%94%A8%E6%88%B7%E7%94%BB%E5%83%8F%E4%BB%8B%E7%BB%8D"><span class="toc-number">1.6.2.2.</span> <span class="toc-text">2. 用户画像介绍</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E6%A0%87%E7%AD%BE%E5%88%86%E7%B1%BB%E5%8F%8A%E5%BA%94%E7%94%A8"><span class="toc-number">1.6.2.3.</span> <span class="toc-text">3. 标签分类及应用</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E7%AE%97%E6%B3%95%E5%88%86%E5%8F%91%E8%AE%BE%E8%AE%A1%E9%80%BB%E8%BE%91"><span class="toc-number">1.6.2.4.</span> <span class="toc-text">4. 算法分发设计逻辑</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-6-3-%E7%83%AD%E5%BA%A6%E6%8E%92%E5%BA%8F%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.6.3.</span> <span class="toc-text">7.6.3 热度排序设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%83%AD%E5%BA%A6%E6%8E%92%E5%BA%8F%E9%80%BB%E8%BE%91%E4%B8%8E%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-number">1.6.3.1.</span> <span class="toc-text">1. 热度排序逻辑与设计要点</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%83%AD%E5%BA%A6%E6%8E%92%E5%BA%8F%E8%A7%84%E5%88%99%E7%A4%BA%E4%BE%8B"><span class="toc-number">1.6.3.2.</span> <span class="toc-text">2. 热度排序规则示例</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-6-4-%E8%AE%A2%E9%98%85%E5%88%86%E5%8F%91%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.6.4.</span> <span class="toc-text">7.6.4 订阅分发设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%AE%A2%E9%98%85%E5%88%86%E5%8F%91%E7%9A%84%E6%A0%B8%E5%BF%83%E9%80%BB%E8%BE%91"><span class="toc-number">1.6.4.1.</span> <span class="toc-text">1. 订阅分发的核心逻辑</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%AE%A2%E9%98%85%E5%88%86%E5%8F%91%E5%AE%9E%E7%8E%B0%E7%9A%84%E5%85%B3%E9%94%AE%E5%8A%9F%E8%83%BD"><span class="toc-number">1.6.4.2.</span> <span class="toc-text">2. 订阅分发实现的关键功能</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-6-5-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-number">1.6.5.</span> <span class="toc-text">7.6.5 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-7-%E4%B8%AA%E4%BA%BA%E4%B8%AD%E5%BF%83"><span class="toc-number">1.7.</span> <span class="toc-text">7.7 个人中心</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-7-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.7.1.</span> <span class="toc-text">7.7.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-7-2-%E4%B8%AA%E4%BA%BA%E8%B5%84%E6%96%99%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.7.2.</span> <span class="toc-text">7.7.2 个人资料页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%B8%B8%E8%A7%81%E4%B8%AA%E4%BA%BA%E4%BF%A1%E6%81%AF%E5%B1%95%E7%A4%BA"><span class="toc-number">1.7.2.1.</span> <span class="toc-text">1. 常见个人信息展示</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-7-3-%E4%B8%AA%E4%BA%BA%E4%B8%AD%E5%BF%83%E5%B8%B8%E8%A7%81%E5%8A%9F%E8%83%BD"><span class="toc-number">1.7.3.</span> <span class="toc-text">7.7.3 个人中心常见功能</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%99%BB%E5%BD%95%E4%B8%8E%E6%9C%AA%E7%99%BB%E5%BD%95%E7%8A%B6%E6%80%81%E5%8C%BA%E5%88%AB"><span class="toc-number">1.7.3.1.</span> <span class="toc-text">1. 登录与未登录状态区别</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B8%B8%E8%A7%81%E5%8A%9F%E8%83%BD%E6%A8%A1%E5%9D%97%E4%BB%8B%E7%BB%8D"><span class="toc-number">1.7.3.2.</span> <span class="toc-text">2. 常见功能模块介绍</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-7-4-%E6%9C%AC%E8%8A%82%E5%B0%8F%E7%BB%93"><span class="toc-number">1.7.4.</span> <span class="toc-text">7.7.4 本节小结</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#7-8-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-number">1.8.</span> <span class="toc-text">7.8 本章总结</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>