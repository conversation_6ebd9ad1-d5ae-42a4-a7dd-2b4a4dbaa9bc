#todolist-box
  - let todo_background = page.top_background
  .author-content.author-content-item.todolist.single(style=`${todo_background ? `background: url(${todo_background}) top / cover no-repeat;` : ""}`)
    .card-content
      .author-content-item-tips Todo
      span.author-content-item-title 待办清单
      .content-bottom
      .tips
        i.fa-solid.fa-quote-left.fa-fw
        span 耽误太多时间，事情可就做不完了
        i.fa-solid.fa-quote-right.fa-fw
      .banner-button-group
        a.banner-button(onclick='pjax.loadUrl("/about/")')
          i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.5rem')
          span.banner-button-text 我的更多

  #todolist-filter
    .filter-title
      i.fa-solid.fa-filter
      span 分类筛选
    .filter-buttons
      button.filter-btn.active(data-filter="all") 全部
      - let categories = []
      each i in site.data.todolist
          - if(!categories.includes(i.class_type)) categories.push(i.class_type || '未分类')
      each category in categories
          button.filter-btn(data-filter=category)= category

  #todolist-main.todolist-grid
    each i in site.data.todolist
      - const categoryClass = i.class_type ? `todo-category-${i.class_type}` : 'todo-category-undefined'
      .todolist-item(class=categoryClass, data-category=i.class_type || '未分类')
        h3.todolist-title
          //- 移除了旧的图标
          span= i.class_name
          .task-count
            - let completedCount = 0
            each item in i.todo_list
              - if(item.completed) completedCount++
            span= completedCount + '/' + i.todo_list.length
        ul.todolist-ul
          each item in i.todo_list
            //- 我们只需要设置 class，CSS 会自动创建复选框样式
            - var listItemClass = item.completed ? 'todolist-li-done' : 'todolist-li'
            li(class=listItemClass)
              span= item.content
        //- 移除了进度条

  #todolist-pagination
    .pagination-container
      button#prev-page.page-btn(disabled)
        i.fa-solid.fa-angle-left
      #page-numbers
      button#next-page.page-btn(disabled)
        i.fa-solid.fa-angle-right

//- 下方是实现所有交互功能所需的完整JavaScript代码
script.
  document.addEventListener('DOMContentLoaded', function() {
    // ========================================================================
    // 筛选与分页功能 (您的原始代码)
    // ========================================================================
    const filterBtns = document.querySelectorAll('.filter-btn');
    const todoItems = document.querySelectorAll('.todolist-item');
    const itemsPerPage = 6;
    let currentPage = 1;
    let filteredItems = Array.from(todoItems);

    function applyFilter(filter) {
        filteredItems = Array.from(todoItems);
        if (filter !== 'all') {
            filteredItems = filteredItems.filter(item => item.getAttribute('data-category') === filter);
        }
        todoItems.forEach(item => { item.style.display = 'none'; });
        setupPagination();
        goToPage(1);
    }

    function setupPagination() {
        const pageCount = Math.ceil(filteredItems.length / itemsPerPage);
        const pageNumbersContainer = document.getElementById('page-numbers');
        const paginationContainer = document.getElementById('todolist-pagination');
        pageNumbersContainer.innerHTML = '';

        if (pageCount <= 1) {
            paginationContainer.style.display = 'none';
            showItems(filteredItems);
            return;
        }

        paginationContainer.style.display = 'flex'; // 使用 flex 保证居中
        for (let i = 1; i <= pageCount; i++) {
            const btn = document.createElement('button');
            btn.classList.add('page-number');
            if (i === 1) btn.classList.add('active');
            btn.textContent = i;
            btn.addEventListener('click', () => goToPage(i));
            pageNumbersContainer.appendChild(btn);
        }
        
        document.getElementById('prev-page').disabled = true;
        document.getElementById('next-page').disabled = pageCount <= 1;
    }

    document.getElementById('prev-page').addEventListener('click', () => {
        if (currentPage > 1) goToPage(currentPage - 1);
    });
    
    document.getElementById('next-page').addEventListener('click', () => {
        const pageCount = Math.ceil(filteredItems.length / itemsPerPage);
        if (currentPage < pageCount) goToPage(currentPage + 1);
    });

    function goToPage(page) {
        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const itemsToShow = filteredItems.slice(startIndex, endIndex);
        
        const pageButtons = document.querySelectorAll('.page-number');
        pageButtons.forEach((btn, index) => btn.classList.toggle('active', index + 1 === page));
        
        document.getElementById('prev-page').disabled = page === 1;
        document.getElementById('next-page').disabled = page === Math.ceil(filteredItems.length / itemsPerPage);
        
        todoItems.forEach(item => { item.style.display = 'none'; });
        showItems(itemsToShow);
        currentPage = page;
    }

    function showItems(items) {
        items.forEach(item => { item.style.display = 'block'; });
    }

    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            applyFilter(this.getAttribute('data-filter'));
        });
    });

    // 初始化加载
    setupPagination();
    goToPage(1);

    // ========================================================================
    // --- 新增：清单项目的交互功能 ---
    // ========================================================================
    const allListItems = document.querySelectorAll('.todolist-ul li');

    allListItems.forEach(li => {
        li.addEventListener('click', function() {
            // 1. 切换被点击项目的视觉状态
            this.classList.toggle('todolist-li-done');

            // 2. 找到父级卡片以更新其计数器
            const parentCard = this.closest('.todolist-item');
            if (parentCard) {
                // 3. 在此卡片内重新计算任务数量
                const tasksInCard = parentCard.querySelectorAll('.todolist-ul li');
                const completedTasksInCard = parentCard.querySelectorAll('.todolist-ul li.todolist-li-done');
                const totalCount = tasksInCard.length;
                const completedCount = completedTasksInCard.length;

                // 4. 更新计数器文本
                const counterSpan = parentCard.querySelector('.task-count span');
                if (counterSpan) {
                    counterSpan.textContent = `${completedCount}/${totalCount}`;
                }
            }
        });
    });
  });