#todolist-box
  - let todo_background = page.top_background
  .author-content.author-content-item.todolist.single(style=`${todo_background ? `background: url(${todo_background}) top / cover no-repeat;` : ""}`)
    .card-content
      .author-content-item-tips Todo
      span.author-content-item-title 待办清单
      .content-bottom
      .tips
        i.fa-solid.fa-quote-left.fa-fw
        span 耽误太多时间，事情可就做不完了
        i.fa-solid.fa-quote-right.fa-fw
      .banner-button-group
        a.banner-button(onclick='pjax.loadUrl("/about/")')
          i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.5rem')
          span.banner-button-text 我的更多

  #todolist-filter
    .filter-title
      i.fa-solid.fa-filter
      span 分类筛选
    .filter-buttons
      button.filter-btn.active(data-filter="all") 全部
      - let categories = []
      each i in site.data.todolist
          - if(!categories.includes(i.class_type)) categories.push(i.class_type || '未分类')
      each category in categories
          button.filter-btn(data-filter=category)= category

  #todolist-main.todolist-grid
    each i in site.data.todolist
      - const categoryClass = i.class_type ? `todo-category-${i.class_type}` : 'todo-category-undefined'
      .todolist-item(class=categoryClass, data-category=i.class_type || '未分类')
        h3.todolist-title
          //- 移除了旧的图标
          span= i.class_name
          .task-count
            - let completedCount = 0
            each item in i.todo_list
              - if(item.completed) completedCount++
            span= completedCount + '/' + i.todo_list.length
        ul.todolist-ul
          each item in i.todo_list
            //- 我们只需要设置 class，CSS 会自动创建复选框样式
            - var listItemClass = item.completed ? 'todolist-li-done' : 'todolist-li'
            li(class=listItemClass)
              span= item.content
        //- 移除了进度条

  #todolist-pagination
    .pagination-container
      button#prev-page.page-btn(disabled)
        i.fa-solid.fa-angle-left
      #page-numbers
      button#next-page.page-btn(disabled)
        i.fa-solid.fa-angle-right

//- 下方是实现所有交互功能所需的完整JavaScript代码
script.
  // 防止重复初始化的标志
  let todolistInitialized = false;

  // Todolist 初始化函数
  function initTodolist() {
    // 防止重复初始化
    if (todolistInitialized) return;

    // 检查必要的DOM元素是否存在
    const todolistBox = document.getElementById('todolist-box');
    if (!todolistBox) return;

    // 等待CSS加载完成
    const checkCSSLoaded = () => {
      const testElement = document.querySelector('.filter-btn');
      if (!testElement) return false;
      const computedStyle = window.getComputedStyle(testElement);
      return computedStyle.cursor === 'pointer';
    };

    // 如果CSS未加载完成，延迟初始化
    if (!checkCSSLoaded()) {
      setTimeout(initTodolist, 100);
      return;
    }

    console.log('Todolist 开始初始化...');

    // ========================================================================
    // 筛选与分页功能
    // ========================================================================
    const filterBtns = document.querySelectorAll('.filter-btn');
    const todoItems = document.querySelectorAll('.todolist-item');
    const itemsPerPage = 6;
    let currentPage = 1;
    let filteredItems = Array.from(todoItems);

    // 检查必要元素是否存在
    if (filterBtns.length === 0 || todoItems.length === 0) {
      console.warn('Todolist: 未找到必要的DOM元素');
      return;
    }

    function applyFilter(filter) {
        filteredItems = Array.from(todoItems);
        if (filter !== 'all') {
            filteredItems = filteredItems.filter(item => item.getAttribute('data-category') === filter);
        }
        todoItems.forEach(item => { item.style.display = 'none'; });
        setupPagination();
        goToPage(1);
    }

    function setupPagination() {
        const pageCount = Math.ceil(filteredItems.length / itemsPerPage);
        const pageNumbersContainer = document.getElementById('page-numbers');
        const paginationContainer = document.getElementById('todolist-pagination');

        if (!pageNumbersContainer || !paginationContainer) return;

        pageNumbersContainer.innerHTML = '';

        if (pageCount <= 1) {
            paginationContainer.style.display = 'none';
            showItems(filteredItems);
            return;
        }

        paginationContainer.style.display = 'flex';
        for (let i = 1; i <= pageCount; i++) {
            const btn = document.createElement('button');
            btn.classList.add('page-number');
            if (i === 1) btn.classList.add('active');
            btn.textContent = i;
            btn.addEventListener('click', () => goToPage(i));
            pageNumbersContainer.appendChild(btn);
        }

        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');
        if (prevBtn) prevBtn.disabled = true;
        if (nextBtn) nextBtn.disabled = pageCount <= 1;
    }

    // 分页按钮事件监听
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');

    if (prevBtn) {
      prevBtn.addEventListener('click', () => {
          if (currentPage > 1) goToPage(currentPage - 1);
      });
    }

    if (nextBtn) {
      nextBtn.addEventListener('click', () => {
          const pageCount = Math.ceil(filteredItems.length / itemsPerPage);
          if (currentPage < pageCount) goToPage(currentPage + 1);
      });
    }

    function goToPage(page) {
        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const itemsToShow = filteredItems.slice(startIndex, endIndex);

        const pageButtons = document.querySelectorAll('.page-number');
        pageButtons.forEach((btn, index) => btn.classList.toggle('active', index + 1 === page));

        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');
        if (prevBtn) prevBtn.disabled = page === 1;
        if (nextBtn) nextBtn.disabled = page === Math.ceil(filteredItems.length / itemsPerPage);

        todoItems.forEach(item => { item.style.display = 'none'; });
        showItems(itemsToShow);
        currentPage = page;
    }

    function showItems(items) {
        items.forEach(item => { item.style.display = 'block'; });
    }

    // 筛选按钮事件监听
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            applyFilter(this.getAttribute('data-filter'));
        });
    });

    // ========================================================================
    // 清单项目的交互功能
    // ========================================================================
    const allListItems = document.querySelectorAll('.todolist-ul li');

    allListItems.forEach(li => {
        li.addEventListener('click', function() {
            // 切换被点击项目的视觉状态
            this.classList.toggle('todolist-li-done');

            // 找到父级卡片以更新其计数器
            const parentCard = this.closest('.todolist-item');
            if (parentCard) {
                // 在此卡片内重新计算任务数量
                const tasksInCard = parentCard.querySelectorAll('.todolist-ul li');
                const completedTasksInCard = parentCard.querySelectorAll('.todolist-ul li.todolist-li-done');
                const totalCount = tasksInCard.length;
                const completedCount = completedTasksInCard.length;

                // 更新计数器文本
                const counterSpan = parentCard.querySelector('.task-count span');
                if (counterSpan) {
                    counterSpan.textContent = `${completedCount}/${totalCount}`;
                }
            }
        });
    });

    // 初始化加载
    setupPagination();
    goToPage(1);

    // 标记为已初始化
    todolistInitialized = true;
    console.log('Todolist 初始化完成');
  }

  // 多种初始化触发方式
  // 1. DOM加载完成时
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initTodolist);
  } else {
    // 如果DOM已经加载完成，立即初始化
    setTimeout(initTodolist, 50);
  }

  // 2. PJAX完成时
  document.addEventListener('pjax:complete', () => {
    todolistInitialized = false; // 重置标志
    setTimeout(initTodolist, 100); // 延迟初始化以确保DOM更新完成
  });

  // 3. CSS加载完成时
  document.addEventListener('todolist-css-loaded', () => {
    if (!todolistInitialized) {
      setTimeout(initTodolist, 50);
    }
  });

  // 4. 页面完全加载时（备用）
  window.addEventListener('load', () => {
    if (!todolistInitialized) {
      setTimeout(initTodolist, 200);
    }
  });

  // 5. 兼容性检查 - 如果其他方式都失败了，定期检查
  let retryCount = 0;
  const maxRetries = 10;
  const retryInterval = setInterval(() => {
    if (todolistInitialized || retryCount >= maxRetries) {
      clearInterval(retryInterval);
      return;
    }

    if (document.querySelector('#todolist-box')) {
      initTodolist();
    }
    retryCount++;
  }, 500);