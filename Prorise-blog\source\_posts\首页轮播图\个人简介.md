---
title: 个人简介
categories:
  - 个人介绍
tags:
  - 个人
cover: 'https://bu.dusays.com/2025/07/27/68859fd0d6d39.jpg'
swiper_index: 5
description: 这里是我的个人简介，介绍我的基本信息、技能背景和职业经历
abbrlink: 39655
date: 2025-01-14 10:00:00
---

{% note modern 'anzhiyufont anzhiyu-icon-rocket' %}
你好，我是 **Prorise**！一名以架构为核心，追求深度与广度的全栈开发者。热衷于探索前沿技术、构建优雅应用，并沉淀了这份“全栈笔记为核心的个人博客”。
{% endnote %}



### {% p green, 我是谁？ %}

我是一名对技术充满热忱的全栈工程师。我的技术生涯始于对网页世界的好奇，并逐渐在**大前端领域**构筑了自己深厚的技术壁垒。我不满足于“会用”，而是热衷于探究其**工程化、底层原理与代码规范**，力求在每一个项目中都能写出优雅、可维护的代码。

![grjj1](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/grjj1.jpg)

我的知识体系呈现出清晰的 **T 型结构**。在前端的“横向”广度上，我熟练掌握从 `Vue.js`、`React.js` 等主流框架到 `Electron`、`Uni-app` 等跨端解决方案；在“纵向”深度上，我深入学习了 `Java` 为核心的后端技术栈，并对数据库、Linux 运维、容器化技术 `Docker` 都有着扎实的实践经验。



我享受学习的过程，并将最新的技术思考，如 **AI 提示词工程 (Prompt Engineering)**，融入日常开发流程，以提升效率和创造力。对我而言，代码不仅是工具，更是创造和表达的媒介。
