<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理进阶（三）：第三章：电商用户端产品设计 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理进阶（三）：第三章：电商用户端产品设计"><meta name="application-name" content="产品经理进阶（三）：第三章：电商用户端产品设计"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="产品经理进阶（三）：第三章：电商用户端产品设计"><meta property="og:url" content="https://prorise666.site/posts/17683.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="第三章：电商用户端产品设计欢迎来到第三章。在这一章，我们将真正地以“建筑师”的身份，从地基开始，一砖一瓦地搭建起我们电商产品的“用户端大楼”。 我们将系统性地学习，从用户首次进入产品的“大门”（产品形态），到在“商场”中闲逛（浏览商品）、挑选结账（下单支付）、寻求服务（售后），再到参与“广场”讨论（"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta name="description" content="第三章：电商用户端产品设计欢迎来到第三章。在这一章，我们将真正地以“建筑师”的身份，从地基开始，一砖一瓦地搭建起我们电商产品的“用户端大楼”。 我们将系统性地学习，从用户首次进入产品的“大门”（产品形态），到在“商场”中闲逛（浏览商品）、挑选结账（下单支付）、寻求服务（售后），再到参与“广场”讨论（"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/17683.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"产品经理进阶（三）：第三章：电商用户端产品设计",postAI:"true",pageFillDescription:"第三章：电商用户端产品设计, 3.1 学习目标, 3.2 用户端产品形态选择, 3.2.1 常见产品形态对比 (App x2F 小程序 x2F H5 x2F Web端), 3.2.2 设计规范：以微信小程序为例, 1. 页面结构, 2. 特殊功能与限制, 3.3 用户端产品设计思路, 3.3.1 核心业务与功能模块, 3.3.2 核心设计原则, 1. 接近法则 (Law of Proximity), 2. 相似法则 (Law of Similarity), 3.4 浏览商品, 3.4.1 首页设计, 1. 首页的核心目的, 2. 常见首页模块解析, 3. 我们大P超级电商的首页设计思路, 3.4.2 商品分类设计（无分类、一级、多级）, 3.4.3 商品列表与详情页设计（图文、参数、评价、推荐）, 3.5 下单支付, 3.5.1 下单流程与页面设计（提交页、支付页）, 1. 订单提交页 (Order Submission Page), 2. 支付页x2F收银台 (Payment Page x2F Cashier), 3. 支付成功后的流程, 3.5.2 支付方式（微信、支付宝、银联、聚合支付）, 1. 微信支付 amp 支付宝 amp 银联（独立渠道对接）, 2. 聚合支付（我的推荐方案）, 3.5.3 库存管理与问题应对, 1. 库存扣减方式（拍下减 vs 付款减）, 2. 问题与应对（恶意拍单、超卖）, 应对恶拍, 应对超卖, 3.5.4 拆单逻辑（父子订单、半拆单）, 1. 为什么要拆单？, 2. 拆单的两种主流模式, 父子订单模式 (Parent-Child Order Model), 半拆单模式 (Semi-split Order Model), 3.5.5 购物车功能设计（信息展示、库存监控、结算等）, 1. 购物车的作用与决策, 2. 购物车核心功能设计, 3.6 订单评价及售后, 3.6.1 订单评价维度（商品质量、服务态度等）, 3.6.2 售后流程设计（取消、退款退货、换货等）, 3.7 商品种草（社区化设计）, 3.7.1 种草定义与场景（内容推荐、发布与互动）, 3.7.2 社区功能结构（搜索、发布、瀑布流、话题标签）, 3.8 个人中心, 3.8.1 核心功能版块设计（我的订单、设置、推荐、快捷入口等）, 3.9 本章总结第三章电商用户端产品设计欢迎来到第三章在这一章我们将真正地以建筑师的身份从地基开始一砖一瓦地搭建起我们电商产品的用户端大楼我们将系统性地学习从用户首次进入产品的大门产品形态到在商场中闲逛浏览商品挑选结账下单支付寻求服务售后再到参与广场讨论商品种草和回到私人房间个人中心的全过程设计学习目标在本节中我的核心目标是带大家掌握电商用户端设计的两大基石产品形态选择和核心设计思路我们将学会对比不同产品形态小程序等的优劣并能以微信小程序为例掌握其独特的设计规范用户端产品形态选择在项目正式启动后我面临的第一个重大的技术和战略决策就是我们主要应该为用户打造一个什么样的场是开发一个功能强大的独立还是一个便于在微信里传播的小程序亦或是一个灵活轻便的网页正如我们看到的像淘宝京东这样成熟的平台通常是全都要在每一种形态上都有布局但对于启动期的我们必须做出取舍选择最适合我们当前战略目标的形态常见产品形态对比小程序端为了做出正确的决策我通常会用下面这张对比表格来系统地分析不同形态的优劣产品形态是否需安装开发投入用户体验我的选择考量适用场景是高需安卓分别开发完善当我的产品功能极其复杂需要调用手机底层能力如蓝牙且追求极致性能和体验时我会选择它的推广和获客成本最高微信小程序否中前端开发好当我希望借助微信的社交生态进行裂变传播和获客时小程序是我的不二之选它体验好开发快特别适合电商本地生活等需要社交分享的场景否低前端开发中等当我需要最大化的灵活性和传播范围时我会选择它不受任何平台限制一个链接就可以走天下特别适合制作营销活动页内容文章页端否中前端开发一般当我的核心场景是需要用户在电脑上进行复杂操作时我会选择端比如我们为商家设计的后台管理系统就必须是端对于我们的大超级电商项目结合我们拥有海量端用户的背景优先开发一个微信小程序来承接和转化这部分流量是一个非常明智的启动策略设计规范以微信小程序为例既然我们选择了小程序那我就必须深入理解它的游戏规则虽然小程序的设计在整体上与非常类似但因为它寄生于微信这个超级生态之上所以也带来了一些独特的设计规范和特殊功能页面结构官方小程序胶囊我必须时刻牢记小程序页面的右上角有一个官方的包含关闭更多等功能的胶囊按钮这个区域是不可设计的我的页面布局必须为它留出空间顶部导航栏小程序的顶部导航栏由微信官方统一样式我们只能定义中间的标题区文字和左侧导航区的返回逻辑标签栏小程序底部的标签栏有严格的数量限制最少个最多个尺寸规范在绘制原型时我依然会采用和一致的作为基准画板并遵循状态栏导航栏标签栏的标准高度特殊功能与限制小程序最大的魅力在于它能调用微信生态的独有能力假设我们需要实现获取手机号和推送消息在小程序中的实现方式就与完全不同获取微信手机号在中我需要用户手动输入手机号再通过短信验证流程繁琐而在小程序里我可以直接放置一个微信用户一键授权的按钮用户点击后会拉起微信的官方授权弹窗用户只需点击允许我们就能安全地获取到他绑定在微信上的手机号极大提升了注册登录的转化率订阅消息在中只要用户允许我可以相对自由地向他推送消息但在小程序中则严格得多我不能主动向用户推送营销消息用户必须主动订阅某一个服务通知比如发货通知降价提醒我才能向他发送一条对应的服务消息这是一种对用户打扰更小的一次性授权模式我在设计运营功能时必须充分考虑这个限制用户端产品设计思路在我们选定了产品形态如微信小程序之后我不会立刻开始绘制具体的页面我会先退一步从更高维度建立起整个用户端产品的设计思路和骨架虽然我们之前学习过内容产品的设计但电商用户端有其自身独特的业务核心和功能侧重在这一节我将带大家明确我们电商产品的核心业务并掌握指导我们进行界面布局的两大基本设计原则核心业务与功能模块我的第一步是将用户的完整购物旅程拆解为几个核心的业务模块这能帮助我确保我的产品设计覆盖了用户从认知到购后的每一个关键环节对于一个典型的电商产品我将核心业务拆解为以下六大模块注册登录这是用户获取身份的入口浏览商品这是用户逛商场的核心环节包括有目的的搜索和无目的的闲逛下单支付这是电商的收银台是完成交易的核心闭环订单与售后这是用户购后的服务中心负责履约和处理问题商品种草这是我们产品特色的内容社区负责吸引用户建立信任个人中心这是用户在我们平台的家负责汇总个人数据和产品全局设置这六大业务模块就构成了我们用户端的核心功能架构图中的用户端核心架构就是一个很好的示例它将具体的零散的功能点如搜索栏金刚区清晰地归类到了它所属的业务模块之下如首页这个架构就是我们后续进行详细页面设计的总纲核心设计原则有了功能架构的骨架我接下来需要思考如何为这个骨架填充血肉也就是说在具体的页面上我应该如何组织和排布那些繁多的功能和信息才能让用户觉得界面清晰易于理解在这里我会借助格式塔心理学中两个最基础也最强大的视觉设计原则接近法则法则定义我们的大脑会本能地将物理空间上彼此靠近的元素视为一个整体我的设计应用在界面设计中间距是我最有力的设计工具之一我会把相关的元素比如一张商品图和它的商品标题紧紧地放在一起让它们在视觉上自然地成为一组我会用留白来拉开不相关的元素或组之间的距离形成清晰的视觉区块相似法则法则定义我们的大脑会本能地将那些在视觉特征如形状颜色大小上相似的元素视为同类我的设计应用在界面设计中一致性是建立用户认知的关键我会确保所有功能相同或相近的元素在视觉上保持相似比如所有可点击的按钮都用同一种颜色和形状所有可输入的文本框都用同一种样式正如常用功能案例所示尽管每一个图标的图案都不同但它们统一的尺寸颜色和圆角风格都在强烈地向用户暗示我们都属于一类我们都是可以点击的功能入口总结在后续的页面设计中我将综合运用接近法则来组织页面布局划分区块并用相似法则来统一控件样式建立操作认知这是让我们的设计变得专业和易用的秘诀浏览商品此处放置首页思考的图片我们电商产品的商场已经建好现在当用户走进这扇大门时他们首先看到的就是我们的商场大堂首页首页是用户对我们产品形成第一印象也是我们引导用户走向各个专柜的最核心的枢纽我设计首页时始终围绕着一个核心问题它需要同时满足谁的需求达成什么样的目的首页设计首页的核心目的我设计首页必须同时扮演好服务员满足用户需求和商场经理达成公司目标的双重角色从用户角度满足多样化的逛街心态我需要为进入首页心态各不相同的用户都提供最高效的解决方案用户心态我的设计方案我明确知道要买什么在页面最顶部提供一个高效精准的搜索栏我知道大概要买什么品类提供一套清晰易懂的商品分类入口我就是想随便逛逛看有啥好东西提供一个无限下拉的引人入胜的个性化商品推荐列表我想看看有啥便宜可以占提供突出有吸引力的促销活动专区如秒杀百亿补贴等从公司角度实现平台的商业目标同时我也需要利用首页这个寸土寸金的场地来达成我们的商业目的公司目标我的设计方案帮助商家促销引流在首页的核心位置为付费的商家提供广告位和活动入口帮助自营店铺促销引流若有自营业务为自营的重点商品或活动提供专属的曝光区域展现平台调性整个首页的视觉风格文案推荐内容都必须严格符合我们内容驱动的潮流社区电商的定位常见首页模块解析为了同时满足上述的用户和公司需求经过多年的演化电商首页已经形成了一套相对成熟的模块化布局我会通过分析竞品来借鉴和思考我们自己的设计核心模块我的设计解读与应用搜索栏雷打不动的第一模块必须始终固定在页面最顶部服务于目的性最强的用户金刚区这是指搜索栏下方的一组图标网格导航我把它看作是核心业务的一级入口我会把我们最重要的商品分类如潮流服饰数码新品和特色业务如达人直播好物种草放在这里促销区这是首页最黄金的广告和活动展示位我会用它来推广平台的重大活动或将其作为重要的广告收入来源商品推荐列表这是首页占据面积最大也是留住闲逛用户的核心内容区我会采用瀑布流的形式通过个性化推荐算法为每个用户呈现一个独一无二的无限延伸的商品列表我们大超级电商的首页设计思路最后结合对竞品的分析和我们自身的定位我为我们大超级电商的首页确立了以下设计思路风格界面要简洁留白充分营造出呼吸感整体视觉风格要年轻时尚符合后的审美金刚区设计必须体现我们内容电商的特色除了服装数码等品类入口必须包含直播种草等内容社区的入口我们在设计首页时一定会遇到的经典决策金刚区到底应该放几个图标上面的文字应该怎么写图标示意文字标签我的设计思路手机数码后核心关注的高价值品类属于品类入口潮流服饰贴合我们潮流的平台调性属于品类入口美妆个护年轻用户特别是女性用户的高频消费品类属于品类入口达人直播我们的核心差异化业务必须给予最强的曝光属于功能入口百亿补贴电商平台拉新促活的标配用明确的利益点吸引用户属于活动入口领券中心培养用户先领券再购物的习惯提升转化率属于功能入口我的订单用户最高频使用的查询功能之一提供一个快捷入口属于功能入口全部分类渐进式呈现原则的应用收纳所有其他品类推荐算法商品推荐列表的算法除了考虑用户的浏览和购买行为还必须高度重视用户的社交和内容偏好比如优先推荐用户关注的正在推荐的商品最后我们产出的商品低保真原型原型大致是这样的商品分类设计无分类一级多级在设计好首页之后我们需要为那些有大概购物方向的用户提供一套清晰的货架导引系统这个系统就是商品分类它的核心目的是满足用户高效缩小寻找范围的需求我给商品类目的定义是按照商品的用途特征等维度并且根据一定的管理目的把相似的商品归为一类的行为并且在类别当中又会存在细分的类型我设计分类体系的复杂度完全取决于我们平台商品的数量和丰富度我通常会根据平台的体量考虑三种不同的分类形式这三种形式分别适用于不同的业务阶段和规模我们可以通过上面的案例直观地感受它们的差异对于我们大超级电商这样的综合性平台一个清晰的多级分类体系是必不可少的设计商品列表与详情页设计图文参数评价推荐当用户通过首页搜索或分类最终都会来到两个核心的页面商品列表页和商品详情页我设计这两个页面时脑海里始终装着三类典型用户目的明确的小风犹豫不决的中风以及纯粹闲逛的大风商品列表页商品列表页是我们商场的货架它的核心设计目标是让用户能高效地筛选和对比虽然列表的内容来源可能不同如搜索结果推荐分类但其页面结构和设计要点是共通的一个优秀的商品列表页必须包含清晰的商品卡片展示图文价等核心信息以及强大的筛选与排序功能来帮助用户快速从海量商品中找到自己心仪的目标商品详情页商品详情页是我们商场的金牌销售员它的核心设计目标是打消用户的所有疑虑促成最终的购买我通常会将页面上半部分用来满足理性用户的决策需求页面的首屏要点在于简明扼要地表现出产品的核心信息让用户直接判断出这个产品是什么我会通过商品展示区高清图视频商品属性区规格参数和用户评价区社群证明来分别满足用户对颜值内涵和口碑的确认需求页面的下半部分我则用来服务那些还在犹豫或者纯粹闲逛的用户通过更丰富的内容对他们进行深度种草这部分通常包括图文并茂的详情介绍用来吸引用户以及问答模块用来对评价功能进行进一步强化打消用户的购买疑虑在详情页的底部我一定会设计一个智能推荐模块它的核心目的是在用户对当前商品不满意准备离开时为他提供更多相关的选择形成一个流量的闭环增加用户在我们平台留存和成交的机会下单支付在用户完成了逛和选将心仪的商品加入购物车之后我们就进入了整个电商流程的核心交易环节我把这个环节的设计看作是引导用户走过一条信任与效率的走廊这条走廊上的任何一个障碍一丝疑虑都可能导致用户在最后关头放弃购买因此我的设计目标必须是极致的顺滑清晰与安全下单流程与页面设计提交页支付页我通常会将整个下单支付流程拆解为两个核心的页面来进行设计订单提交页和支付页收银台订单提交页当用户在商品详情页点击立即购买或在购物车点击去结算后并不会直接进入付款环节而是会先来到订单提交页这个页面的核心作用我把它定义为用户的最后一次确认在用户真正掏钱之前我必须为他提供一个清晰的所有交易信息的汇总页面让他进行最后的检查和确认我设计的订单提交页必须让用户能够清晰地完成三件事确认商品清晰地罗列出本次将要购买的所有商品信息名称数量价格确认地址提供默认收货地址并允许用户方便地选择或新增其他地址确认价格清晰地展示商品总额运费优惠券抵扣最终实付金额等所有价格明细支付页收银台当用户在订单提交页点击提交订单后他才真正进入了支付页我常称之为收银台这个页面的设计我追求的是极致的简洁和安全感它的核心作用只有三个确认实付金额醒目地展示最终需要支付的金额选择支付方式提供用户选择支付渠道如微信支付宝的入口完成支付一个清晰唯一的确认支付按钮我的拓展设计支付异常处理设计支付页时我必须考虑一个最常见的异常场景用户进入了收银台但因为种种原因没有完成支付就退出了一个糟糕的设计可能会让用户之前提交的订单直接消失而一个优秀的设计正如案例所示应该将这份订单自动保存为一张待支付的订单用户可以在我的订单中随时找到它并重新发起支付这个小小的设计能为我们挽回大量可能流失的销售额支付成功后的流程当用户成功支付后这笔交易在后台就正式生成了并进入了它的生命周期我会用一组订单状态来清晰地标记它在流程中所处的节点支付成功就是订单状态从待支付流转到待发货的触发器此时用户可以在个人中心的我的订单列表中看到这笔订单并查看到它待发货的状态当商家发货后用户最关心的物流信息就会在这里出现思考物流信息是从哪来的它并不是由我们的商家手动更新的在我设计的后台商家发货时只需要选择快递公司并输入快递单号随后我们的后端服务器就会通过接口定时地向第三方快递查询平台如申通发起查询请求获取最新的物流轨迹然后将这些信息展示在用户端的订单详情页上我在撰写时必须将这个技术方案的逻辑清晰地描述出来支付方式微信支付宝银联聚合支付在设计好收银台页面后我需要做的最重要的决策就是为这个收银台配备哪些收款设备也就是我们常说的支付方式在今天的中国市场只提供一种支付方式是远远不够的为了最大化地提升支付成功率我至少需要为用户提供微信支付和支付宝这两种主流选择微信支付支付宝银联独立渠道对接理论上我们可以分别独立地去对接每一个支付渠道我需要告诉开发的关键点以微信支付为例要接入微信支付不是一个纯粹的技术工作它需要产品运营和技术共同协作我需要了解并推动以下几个步骤商户号申请运营商务负责首先我们需要由公司的运营或商务同事前往微信支付商户平台提交我们公司的营业执照等资质申请一个商户号这个过程支付宝和银联也完全一样都需要我们先拥有一个官方认证的商家身份获取开发凭证研发负责人当我们的商户号被批准后技术负责人需要登录这个商户平台去获取进行技术开发所必需的身份凭证这通常包括证书密钥等我把它们理解为我们公司服务器与微信支付服务器之间进行加密通信的账号和密码技术对接研发负责拿到凭证后研发同学才会真正开始写代码后端开发需要按照微信支付宝的官方开发文档开发服务端接口这些接口主要负责创建预支付订单接收支付成功或失败的异步通知等前端开发需要集成微信支付宝的官方软件开发工具包这个的主要作用是在我们的里能够拉起用户手机上已经安装的微信或支付宝来进行最终的密码指纹输入聚合支付我的推荐方案在我们理解了独立对接的流程后两个核心痛点就浮现出来了接入成本高我要支持微信支付宝银联三种支付我的研发团队就需要把上面的流程重复做三遍这需要耗费巨大的研发资源财务管理难每天我的财务同事需要分别登录三个不同的商户后台去下载三份不同的对账单再进行手动的汇总和核对极其繁琐且容易出错为了解决这两个痛点一个更聪明的也是我强烈推荐的方案就是使用聚合支付聚合支付服务商如就是支付领域的万能转换插头它已经提前帮我们把市面上所有的主流支付渠道微信支付宝银联各类银行卡等都预先集成好了我需要告诉开发的关键点技术视角一次对接全部拥有我们不再需要去对接微信支付宝等多个上游渠道我的研发团队只需要按照聚合支付服务商提供的一份开发文档进行一次技术对接即可统一的和聚合支付会为我们提供一套统一的和当用户在我们的里选择用微信支付时我们的调用的是聚合支付的我们的服务器也只请求聚合支付的后续由聚合支付的服务来和微信的服务器进行通信统一的后台和对账单我的财务同事只需要登录聚合支付这一个后台就可以看到所有渠道的交易流水并下载一份统一的对账单我的决策权衡使用聚合支付的唯一缺点是它会在每个渠道的原有费率基础上再收取一点点的服务费但考虑到它能为我们节省巨大的研发成本和财务管理成本对于绝大多数公司特别是初创和中型公司而言这笔服务费都是一笔极具性价比的投资聚合支付是不是代表不用哪些营业证书之类的直接通过付钱就能接入了答案是不是的聚合支付并不能免除您提供公司资质如营业执照的义务您可以把聚合支付服务商看作是一个超级代办员或技术外包服务商而不是一个资质豁免机构他们的核心价值在于简化技术对接和财务管理而不是绕过金融监管我为您梳理一下实际的流程您与聚合支付签约您首先需要选择一家聚合支付服务商如并在他们的平台上注册账户您向聚合支付提交资质在注册过程中您仍然需要向聚合支付服务商提交您自己公司的全套有效资质包括但不限于营业执照法人身份证信息对公银行账户网站的备案信息如果适用聚合支付为您代办申请聚合支付服务商在收到您的资质后会作为您的代办员拿着您的这些材料去分别向微信支付支付宝银联等官方渠道为您集中申请开通各个支付渠道的商户权限最终结果审批通过后您最终获得的依然是您自己公司名下的在微信和支付宝备案的合法商户号聚合支付只是为您提供了一个统一的技术接口和管理后台来操作它们支付方案我的解读优点缺点逐个渠道对接我们分别与微信支付宝等签约并进行技术开发费率可能略低资金直接到账开发成本极高财务对账繁琐使用聚合支付我们只与一家聚合支付服务商签约和开发开发成本极低只需一次对接财务对账简单费率略高资金需要经过聚合服务商中转库存管理与问题应对我们都可能有过这样的经历在一个平台下单后不急着付款过了一会儿想起来去支付发现订单依然有效而在另一个平台同样的操作回来支付时却被告知商品已售罄这是为什么呢这背后就反映了不同电商平台对于库存扣减这个核心问题采取了两种截然不同的产品策略库存扣减方式拍下减付款减在我设计交易系统时我必须与我的技术和业务负责人共同做出一个关键决策我们的库存到底应该在哪一个节点扣减这个决策没有绝对的好坏只有不同选择下的利弊权衡扣减方式核心逻辑用户体验主要风险我的选择考量拍下减当用户点击提交订单的瞬间无论是否付款系统都会立即为他预留这份库存好用户会感觉只要我下单了这个货就是我的了体验非常安心恶拍我通常会在普通商品的销售中采用此方式因为它能提供最佳的用户体验付款减只有当用户成功完成支付的瞬间系统才会去扣减实际的物理库存一般用户下单后可能会因为犹豫了几分钟回来支付时发现商品已被别人买走导致体验不佳和用户流失超卖我通常只在库存极少瞬时流量极大的秒杀等营销活动中才会谨慎采用此方式作为产品经理我的工作就是选择一种方式并设计一套完整的机制来最大化地规避它的潜在风险问题与应对恶意拍单超卖应对恶拍如果我选择了拍下减库存那我的头号敌人就是恶拍即竞争对手或黄牛恶意地大量下单但不支付以此来锁死我的库存让真实用户无法购买为了应对它我必须建立一套组合防御体系减少库存保留时间这是我最核心的武器我会设计一个订单自动取消的规则比如下单后分钟内未支付系统将自动取消这笔订单并将预留的库存重新释放回公共库存池中供其他用户购买限购对于一些热门或促销商品我会在产品层面增加限购规则比如规定单个限购件这能有效防止单一恶意用户锁死大量库存安全策略我还会和风控团队合作建立监控机制当发现某个用户在短时间内有大量下单后又取消的异常行为时系统可以暂时限制他的下单权限应对超卖如果我选择了付款减库存那我最大的噩梦就是超卖即我们实际卖出的商品数量超过了我们的真实库存这会引发严重的客诉极大地损害平台信誉为了应对它我同样需要一套组合防御体系技术角度解决这主要依赖于研发团队我会要求我的技术负责人必须在技术层面通过数据库锁或分布式队列等技术来处理高并发场景下的库存扣减请求确保对最后一件库存的扣减操作是原子性的即在同一瞬间只能有一个请求能成功提示用户在产品体验层面为了管理用户预期当某个商品的库存数量很少时比如少于件我会在商品详情页和购物车中明确地展示库存紧张或仅剩件的提示文案设置安全库存这是我最常用的一个运营策略如果一个商品的物理库存有件我会在电商后台的可售卖库存中只填写件那剩下的件就成了我的安全库存它就像一个缓冲垫既能消化掉极端情况下因技术原因产生的少量超卖也能用来应对用户退货换货的需求拆单逻辑父子订单半拆单当用户在我们的购物车里同时选中了来自不同商家或者满足某些特殊条件的多个商品然后点击去结算时一个复杂的问题就摆在了我面前后台应该如何处理这张大单是把它当作一个订单还是多个订单如果从购物车多个店铺多个商品进入结算需要考虑什么在这种情况下我的订单提交页必须进行拆单展示我会按照不同的店铺将商品进行分组每个店铺的商品会形成一个独立的包裹分别计算运费和优惠最终汇总成一个总的支付金额这种清晰的结构是解决多商家同时结算场景的最佳实践为什么要拆单我设计拆单逻辑主要是为了应对以下五种常见的业务场景我的设计考量拆单因素这是最常见的拆单原因不同商家的商品其货权发货地财务结算主体都不同因此必须拆分为独立的订单分别进行处理店铺即便用户购买的是同一个自营商家的多件商品这些商品也可能存放在全国不同的仓库为了最高效地完成履约系统需要按仓库将订单拆分给不同的仓储中心进行打包发货仓库不同的快递公司对单个包裹的重量和体积都有上限当用户购买的商品总重量或总体积超过限制时就需要拆分为多个包裹对应生成多个订单物流某些特殊品类的商品需要单独处理比如易碎品需要特殊包装超大件如轮胎无法与普通商品合并打包都需要独立成单品类这主要应用于跨境海淘业务根据国家政策跨境零售进口商品的单次交易限值为元当用户的单笔订单超过这个限值时系统必须将其拆分为多个订单以符合清关和税务要求商品价值拆单的两种主流模式明确了为什么拆我们再来看怎么拆行业内主要有两种主流的拆单模式它们最核心的区别在于拆单发生的时机父子订单模式核心逻辑先支付后拆分我的解读在这种模式下用户从下单到支付完成始终面对的是一个统一的父订单他只需要付一次总的款项当支付成功后我们的后端系统才会根据拆单规则将这个父订单在后台默默地拆分为多个子订单分别推送给不同的仓库或商家去履约用户感知用户在我的订单列表中会看到一个父订单点进去之后才能看到下面包含的多个子订单每个子订单都有独立的物流和状态典型代表京东优点用户支付体验统一流畅能有效避免下面要讲的优惠券漏洞缺点后端系统的处理逻辑相对更复杂半拆单模式核心逻辑先拆分后支付我的解读在这种模式下当用户从购物车点击去结算时系统在进入订单提交页的那一刻就已经完成了拆分页面上会直接按照店铺等维度将商品展示为多个独立的订单用户需要对这些独立的订单进行统一支付或者也可以选择只支付其中一部分用户感知用户在支付前就已经明确知道自己的购物车被分成了几笔不同的订单典型代表淘宝优点业务逻辑相对简单清晰缺点及我的应对这种模式存在一个著名的薅羊毛漏洞比如平台有一个跨店满减的活动用户可以从店选元商品店选元商品凑成一单在订单提交页系统会把优惠按比例分摊到两个独立的订单上此时用户如果只支付店的那个订单就等于用不到元的价格享受到了满减优惠我作为产品经理必须设计规则来规避这个漏洞比如我会定义对于参与跨店满减活动的组合订单若用户在规定时间内未完成所有相关订单的支付则所有订单将被自动取消优惠券也将退回购物车功能设计信息展示库存监控结算等在设计下单流程时我首先要面临一个战略性的选择我们的电商产品到底需不需要购物车这并不是一个理所当然的问题购物车的设计必须服务于我们产品的核心交易模式购物车的作用与决策什么时候我需要购物车对于像我们大超级电商这样的招商模式混合模式平台购物车是必须品因为用户会在不同的店铺之间逛它的核心作用是凑单与比价让用户可以把来自不同店铺的感兴趣的商品先放在一个地方进行统一的比较和筛选跨店促销是实现跨店满减等复杂促销活动的技术基础什么时候我不需要购物车二手交易模式如闲鱼二手商品大多是孤品库存只有件交易前通常需要买卖双方进行沟通议价流程复杂购物车这种先暂存再统一结算的模式会增加无效库存的锁定并打断沟通流程反而降低交易效率拼团模式如拼多多拼多多的核心是低价爆款冲动消费它希望用户看到一个商品立刻就完成下单转化购物车的存在会让用户冷静下来进行反复比价这与它的核心商业模式是相悖的结论对于我们的大超级电商购物车是用户完成多商品跨店铺购买的核心功能我们必须精心设计购物车核心功能设计我设计购物车会围绕进入使用离开这三个场景来规划它的核心功能信息展示这是购物车最基础也是最重要的部分状态区分我需要设计两种状态未登录时购物车应为空并展示商品推荐引导用户去逛登录后则展示用户已添加的商品分组与排序为了让信息清晰我必须将商品按店铺进行分组在店铺内部商品会按照添加时间的倒序排列最新添加的在最上方营销信息我会清晰地展示每个商品适用的优惠信息如满减优惠券以及店铺整体的促销活动刺激用户凑单库存监控我必须在购物车里就向用户提供实时的库存状态避免他到了提交订单的最后一步才发现商品已售罄我会设计三种库存状态的展示有货正常显示库存紧张当库存很少时如件用红字等醒目的方式提示用户仅剩件制造稀缺感促进转化无货当商品售罄时商品必须被置灰数量选择器变为不可用状态并清晰地提示已售罄或无货编辑功能我必须赋予用户对购物车的完全掌控权这包括修改商品数量提供简单易用的加减数量选择器修改商品规格允许用户直接在购物车切换商品的如颜色尺码删除商品提供删除单个商品以及在编辑模式下批量删除多个商品的功能结算功能这是购物车最终的使命召唤按钮我会在页面底部设计一个常驻的结算栏它必须清晰地展示已勾选商品的总计金额优惠减免的金额明细一个色彩鲜明吸引点击的去结算按钮并标明已选商品的数量订单评价及售后用户的购物旅程在支付成功的那一刻其实才刚刚过半从支付成功到满意使用这最后一公里的体验我称之为购后体验它直接决定了用户是否会成为我们的回头客本节我们就来设计购后体验中最重要的两个环节订单评价和售后流程订单评价维度商品质量服务态度等我始终认为订单评价是电商平台信任体系的基石它既是后续用户的购买决策参考也是平台用来管理商家的重要数据来源一个设计良好的评价体系能极大地促进平台的健康循环正如流程图所示查看商品评价是用户在购买决策前的关键一步直接影响着平台的转化率我设计评价体系核心是定义好评价维度即我希望用户从哪些方面来对这次交易进行评价一个专业全面的评价功能至少应该包含以下两部分多维度评分为了得到可量化的能用于商家考核的数据我不会只让用户打一个总分而是会将评分拆解为几个核心的维度评价维度我的设计说明商品质量核心维度直接反映了货的品质发货速度反映商家履约环节的物流效率服务态度反映商家在售前售中售后环节的服务质量我会将这几个维度都设计为星的评分形式这能让我非常直观地计算出商家的综合服务评分图文评价除了量化的评分我还需要提供一个让用户能自由表达分享购物体验的内容创作区这个功能的设计我会包含文字评价一个开放的文本输入框让用户可以详细描述购物心得图片视频评价提供图片视频的上传功能有图有真相带图的评价是所有评价中对其他用户参考价值最高最可信的售后流程设计取消退款退货换货等即便我们尽了最大努力交易过程中也难免会出现各种问题一套清晰合理公正的售后流程是我们在用户遇到问题时挽回他们信任的最后机会我把这个流程也称为逆向流程我设计售后流程最核心的原则是在订单的不同生命周期状态下为用户提供不同的符合当前场景的售后操作订单状态可执行的售后操作我的设计说明待支付取消订单用户未付款可无理由取消待发货仅退款用户已付款但未发货可直接申请退款无需退货待收货申请退款用户可申请退款触发包裹拦截退款成功需待拦截成功或用户拒收建议收货后再发起换货交易成功申请售后在售后保障期内可申请退款退货换货或维修商品种草社区化设计我们已经设计完了电商平台最核心的交易链路现在我们要开始为我们的产品构建真正的护城河商品种草也就是社区化设计这部分的设计将直接体现我们内容驱动的潮流社区电商的核心定位是我们区别于传统货架式电商吸引和留存年轻用户的关键种草定义与场景内容推荐发布与互动首先我来定义一下种草在我看来它是一种基于真实体验和信任关系的内容化商品推荐行为它包含两个方面被种草我通过看别人的分享发现了一款好物并产生了购买的欲望去种草我因为使用了一款好物自发地去分享我的使用心得推荐给别人在我们的平台上用户的种草旅程也分为看帖和发帖这两条核心路径基于这两条路径我提炼出了三大核心用户场景以及支撑这些场景的必备功能发布购物心得这是去种草的场景需要我们提供发布心得的功能查看他人购物心得这是被种草的场景需要我们提供一个种草社区信息流针对购物心得互动这是社区活跃的保障需要我们提供点赞收藏分享评论等功能社区功能结构搜索发布瀑布流话题标签现在我们来具体设计支撑上述场景的核心功能界面种草社区信息流这是用户被种草的核心场所我设计的要点如下瀑布流布局为了最大化地突出图片这种强视觉冲击力的内容我会采用瀑布流的布局形式来呈现种草笔记列表关键词搜索在顶部我必须提供一个强大的搜索功能让用户可以根据关键词精准地查找自己感兴趣的种草内容分类话题查看提供按不同分类或话题来筛选和浏览种草笔记的功能满足用户宽泛的浏览需求发布种草内容发布页这是用户去种草的核心工具我设计的要点如下图片视频上传提供入口让用户可以选择手机里的单张或多张图片视频进行上传编写心得内容提供一个富文本编辑器让用户可以撰写自己的使用心得和推荐理由关联商品这是连接内容与电商最关键的一步我必须提供一个功能让用户可以在发布笔记时方便地关联到我们平台上正在售卖的具体商品这就在种草和拔草之间建立起了最短的转化路径选择话题标签允许用户为自己的笔记选择或创建话题标签这既能表达自己的内容核心也便于被有相同兴趣的用户发现个人中心当用户在我们的商场里完成了浏览购买评价等一系列行为后他们需要一个地方来存放他们的战利品订单会员卡个人信息和购物小票历史记录这个地方就是个人中心在我看来个人中心是用户在我们平台上的数字资产管理中心是提升用户归属感提供深度服务的核心枢纽核心功能版块设计我的订单设置推荐快捷入口等我设计个人中心不会简单地把所有功能堆砌在一起我会像整理房间一样将功能进行逻辑分区让用户能快速找到自己想要的东西根据淘宝这类成熟产品的经验我通常会将个人中心划分为以下四大版块用户数据与资产这是整个页面的门面是用户最核心的个人信息和资产的展示区个人信息最顶部清晰地展示用户的头像和昵称并提供一个入口可以跳转到更详细的个人资料页进行编辑业务数据将用户最关心的几个动态数据进行可视化展示比如商品收藏店铺收藏浏览足迹的数量核心资产入口提供用户最重要的资产的快捷入口对于我们平台最重要的就是我的订单和我们特色功能的我的种草快捷功能入口这是一个灵活的网格布局的区域我用它来聚合一些使用频率相对较高的功能或运营活动入口比如我的优惠券客服中心地址管理每日签到等应用全局设置这个版块我通常会把它放在页面的下半部分或者收纳到一个统一的设置入口里它包含的是一些低频但必要的全局性功能比如账号与安全支付设置关于我们以及最重要的退出登录按钮个性化推荐个人中心是一个高度个性化的页面因此它也是进行精准商品推荐的绝佳场所在页面的底部我会设计一个为你推荐的模块根据用户的历史购买收藏和浏览记录为他推荐可能感兴趣的商品以创造更多的交叉销售机会我们为大超级电商设计的这份个人中心线框图就是上述设计思路的一个具体体现它结构清晰主次分明将用户最关心的我的订单和我的种草放在了最核心的位置确保了用户体验的便捷本章总结至此我们已经完整地设计出了一个电商产品用户端的所有核心模块让我们最后回顾一下本章的整个设计旅程设计模块核心产出与学习要点产品形态选择我们对比了小程序的优劣并深入学习了微信小程序独特的设计规范与特殊功能用户端设计思路我们确立了电商的六大核心业务模块并掌握了指导界面布局的接近法则与相似法则浏览商品我们设计了首页商品分类商品列表页和商品详情页构建了用户逛和选的核心路径下单支付我们设计了订单提交页支付页和购物车并深入探讨了库存管理拆单逻辑等复杂的后端策略订单评价及售后我们设计了购后体验的评价体系和基于订单状态的售后流程以建立用户信任商品种草我们设计了产品的差异化模块社区通过信息流和发布功能打通内容与电商个人中心我们为用户设计了一个清晰有序的家聚合了用户资产快捷入口和全局设置通过这一章的实战我们已经将电商用户端的理论全部转化为了具体可视的产品设计方案我们已经拥有了一份足以交付给和开发团队的完整的建筑蓝图",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-25 11:05:48",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E7%94%A8%E6%88%B7%E7%AB%AF%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-text">第三章：电商用户端产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">3.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-2-%E7%94%A8%E6%88%B7%E7%AB%AF%E4%BA%A7%E5%93%81%E5%BD%A2%E6%80%81%E9%80%89%E6%8B%A9"><span class="toc-text">3.2 用户端产品形态选择</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-1-%E5%B8%B8%E8%A7%81%E4%BA%A7%E5%93%81%E5%BD%A2%E6%80%81%E5%AF%B9%E6%AF%94-App-%E5%B0%8F%E7%A8%8B%E5%BA%8F-H5-Web%E7%AB%AF"><span class="toc-text">3.2.1 常见产品形态对比 (App / 小程序 / H5 / Web端)</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-2-%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83%EF%BC%9A%E4%BB%A5%E5%BE%AE%E4%BF%A1%E5%B0%8F%E7%A8%8B%E5%BA%8F%E4%B8%BA%E4%BE%8B"><span class="toc-text">3.2.2 设计规范：以微信小程序为例</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%A1%B5%E9%9D%A2%E7%BB%93%E6%9E%84"><span class="toc-text">1. 页面结构</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%89%B9%E6%AE%8A%E5%8A%9F%E8%83%BD%E4%B8%8E%E9%99%90%E5%88%B6"><span class="toc-text">2. 特殊功能与限制</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-3-%E7%94%A8%E6%88%B7%E7%AB%AF%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-text">3.3 用户端产品设计思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-1-%E6%A0%B8%E5%BF%83%E4%B8%9A%E5%8A%A1%E4%B8%8E%E5%8A%9F%E8%83%BD%E6%A8%A1%E5%9D%97"><span class="toc-text">3.3.1 核心业务与功能模块</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-2-%E6%A0%B8%E5%BF%83%E8%AE%BE%E8%AE%A1%E5%8E%9F%E5%88%99"><span class="toc-text">3.3.2 核心设计原则</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%8E%A5%E8%BF%91%E6%B3%95%E5%88%99-Law-of-Proximity"><span class="toc-text">1. 接近法则 (Law of Proximity)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%9B%B8%E4%BC%BC%E6%B3%95%E5%88%99-Law-of-Similarity"><span class="toc-text">2. 相似法则 (Law of Similarity)</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-4-%E6%B5%8F%E8%A7%88%E5%95%86%E5%93%81"><span class="toc-text">3.4 浏览商品</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-4-1-%E9%A6%96%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-text">3.4.1 首页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%A6%96%E9%A1%B5%E7%9A%84%E6%A0%B8%E5%BF%83%E7%9B%AE%E7%9A%84"><span class="toc-text">1. 首页的核心目的</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B8%B8%E8%A7%81%E9%A6%96%E9%A1%B5%E6%A8%A1%E5%9D%97%E8%A7%A3%E6%9E%90"><span class="toc-text">2. 常见首页模块解析</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E6%88%91%E4%BB%AC%E2%80%9C%E5%A4%A7P%E8%B6%85%E7%BA%A7%E7%94%B5%E5%95%86%E2%80%9D%E7%9A%84%E9%A6%96%E9%A1%B5%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-text">3. 我们“大P超级电商”的首页设计思路</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-4-2-%E5%95%86%E5%93%81%E5%88%86%E7%B1%BB%E8%AE%BE%E8%AE%A1%EF%BC%88%E6%97%A0%E5%88%86%E7%B1%BB%E3%80%81%E4%B8%80%E7%BA%A7%E3%80%81%E5%A4%9A%E7%BA%A7%EF%BC%89"><span class="toc-text">3.4.2 商品分类设计（无分类、一级、多级）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-4-3-%E5%95%86%E5%93%81%E5%88%97%E8%A1%A8%E4%B8%8E%E8%AF%A6%E6%83%85%E9%A1%B5%E8%AE%BE%E8%AE%A1%EF%BC%88%E5%9B%BE%E6%96%87%E3%80%81%E5%8F%82%E6%95%B0%E3%80%81%E8%AF%84%E4%BB%B7%E3%80%81%E6%8E%A8%E8%8D%90%EF%BC%89"><span class="toc-text">3.4.3 商品列表与详情页设计（图文、参数、评价、推荐）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-5-%E4%B8%8B%E5%8D%95%E6%94%AF%E4%BB%98"><span class="toc-text">3.5 下单支付</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-5-1-%E4%B8%8B%E5%8D%95%E6%B5%81%E7%A8%8B%E4%B8%8E%E9%A1%B5%E9%9D%A2%E8%AE%BE%E8%AE%A1%EF%BC%88%E6%8F%90%E4%BA%A4%E9%A1%B5%E3%80%81%E6%94%AF%E4%BB%98%E9%A1%B5%EF%BC%89"><span class="toc-text">3.5.1 下单流程与页面设计（提交页、支付页）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%AE%A2%E5%8D%95%E6%8F%90%E4%BA%A4%E9%A1%B5-Order-Submission-Page"><span class="toc-text">1. 订单提交页 (Order Submission Page)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%94%AF%E4%BB%98%E9%A1%B5-%E6%94%B6%E9%93%B6%E5%8F%B0-Payment-Page-Cashier"><span class="toc-text">2. 支付页/收银台 (Payment Page / Cashier)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E6%94%AF%E4%BB%98%E6%88%90%E5%8A%9F%E5%90%8E%E7%9A%84%E6%B5%81%E7%A8%8B"><span class="toc-text">3. 支付成功后的流程</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-5-2-%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8F%EF%BC%88%E5%BE%AE%E4%BF%A1%E3%80%81%E6%94%AF%E4%BB%98%E5%AE%9D%E3%80%81%E9%93%B6%E8%81%94%E3%80%81%E8%81%9A%E5%90%88%E6%94%AF%E4%BB%98%EF%BC%89"><span class="toc-text">3.5.2 支付方式（微信、支付宝、银联、聚合支付）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%BE%AE%E4%BF%A1%E6%94%AF%E4%BB%98-%E6%94%AF%E4%BB%98%E5%AE%9D-%E9%93%B6%E8%81%94%EF%BC%88%E7%8B%AC%E7%AB%8B%E6%B8%A0%E9%81%93%E5%AF%B9%E6%8E%A5%EF%BC%89"><span class="toc-text">1. 微信支付 &amp; 支付宝 &amp; 银联（独立渠道对接）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%81%9A%E5%90%88%E6%94%AF%E4%BB%98%EF%BC%88%E6%88%91%E7%9A%84%E6%8E%A8%E8%8D%90%E6%96%B9%E6%A1%88%EF%BC%89"><span class="toc-text">2. 聚合支付（我的推荐方案）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-5-3-%E5%BA%93%E5%AD%98%E7%AE%A1%E7%90%86%E4%B8%8E%E9%97%AE%E9%A2%98%E5%BA%94%E5%AF%B9"><span class="toc-text">3.5.3 库存管理与问题应对</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%BA%93%E5%AD%98%E6%89%A3%E5%87%8F%E6%96%B9%E5%BC%8F%EF%BC%88%E6%8B%8D%E4%B8%8B%E5%87%8F-vs-%E4%BB%98%E6%AC%BE%E5%87%8F%EF%BC%89"><span class="toc-text">1. 库存扣减方式（拍下减 vs 付款减）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E9%97%AE%E9%A2%98%E4%B8%8E%E5%BA%94%E5%AF%B9%EF%BC%88%E6%81%B6%E6%84%8F%E6%8B%8D%E5%8D%95%E3%80%81%E8%B6%85%E5%8D%96%EF%BC%89"><span class="toc-text">2. 问题与应对（恶意拍单、超卖）</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%BA%94%E5%AF%B9%E2%80%9C%E6%81%B6%E6%8B%8D%E2%80%9D"><span class="toc-text">应对“恶拍”</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%BA%94%E5%AF%B9%E2%80%9C%E8%B6%85%E5%8D%96%E2%80%9D"><span class="toc-text">应对“超卖”</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-5-4-%E6%8B%86%E5%8D%95%E9%80%BB%E8%BE%91%EF%BC%88%E7%88%B6%E5%AD%90%E8%AE%A2%E5%8D%95%E3%80%81%E5%8D%8A%E6%8B%86%E5%8D%95%EF%BC%89"><span class="toc-text">3.5.4 拆单逻辑（父子订单、半拆单）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E6%8B%86%E5%8D%95%EF%BC%9F"><span class="toc-text">1. 为什么要拆单？</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%8B%86%E5%8D%95%E7%9A%84%E4%B8%A4%E7%A7%8D%E4%B8%BB%E6%B5%81%E6%A8%A1%E5%BC%8F"><span class="toc-text">2. 拆单的两种主流模式</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%88%B6%E5%AD%90%E8%AE%A2%E5%8D%95%E6%A8%A1%E5%BC%8F-Parent-Child-Order-Model"><span class="toc-text">父子订单模式 (Parent-Child Order Model)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%8D%8A%E6%8B%86%E5%8D%95%E6%A8%A1%E5%BC%8F-Semi-split-Order-Model"><span class="toc-text">半拆单模式 (Semi-split Order Model)</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-5-5-%E8%B4%AD%E7%89%A9%E8%BD%A6%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1%EF%BC%88%E4%BF%A1%E6%81%AF%E5%B1%95%E7%A4%BA%E3%80%81%E5%BA%93%E5%AD%98%E7%9B%91%E6%8E%A7%E3%80%81%E7%BB%93%E7%AE%97%E7%AD%89%EF%BC%89"><span class="toc-text">3.5.5 购物车功能设计（信息展示、库存监控、结算等）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%B4%AD%E7%89%A9%E8%BD%A6%E7%9A%84%E4%BD%9C%E7%94%A8%E4%B8%8E%E5%86%B3%E7%AD%96"><span class="toc-text">1. 购物车的作用与决策</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%B4%AD%E7%89%A9%E8%BD%A6%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-text">2. 购物车核心功能设计</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-6-%E8%AE%A2%E5%8D%95%E8%AF%84%E4%BB%B7%E5%8F%8A%E5%94%AE%E5%90%8E"><span class="toc-text">3.6 订单评价及售后</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-6-1-%E8%AE%A2%E5%8D%95%E8%AF%84%E4%BB%B7%E7%BB%B4%E5%BA%A6%EF%BC%88%E5%95%86%E5%93%81%E8%B4%A8%E9%87%8F%E3%80%81%E6%9C%8D%E5%8A%A1%E6%80%81%E5%BA%A6%E7%AD%89%EF%BC%89"><span class="toc-text">3.6.1 订单评价维度（商品质量、服务态度等）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-6-2-%E5%94%AE%E5%90%8E%E6%B5%81%E7%A8%8B%E8%AE%BE%E8%AE%A1%EF%BC%88%E5%8F%96%E6%B6%88%E3%80%81%E9%80%80%E6%AC%BE%E9%80%80%E8%B4%A7%E3%80%81%E6%8D%A2%E8%B4%A7%E7%AD%89%EF%BC%89"><span class="toc-text">3.6.2 售后流程设计（取消、退款退货、换货等）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-7-%E5%95%86%E5%93%81%E7%A7%8D%E8%8D%89%EF%BC%88%E7%A4%BE%E5%8C%BA%E5%8C%96%E8%AE%BE%E8%AE%A1%EF%BC%89"><span class="toc-text">3.7 商品种草（社区化设计）</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-7-1-%E7%A7%8D%E8%8D%89%E5%AE%9A%E4%B9%89%E4%B8%8E%E5%9C%BA%E6%99%AF%EF%BC%88%E5%86%85%E5%AE%B9%E6%8E%A8%E8%8D%90%E3%80%81%E5%8F%91%E5%B8%83%E4%B8%8E%E4%BA%92%E5%8A%A8%EF%BC%89"><span class="toc-text">3.7.1 种草定义与场景（内容推荐、发布与互动）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-7-2-%E7%A4%BE%E5%8C%BA%E5%8A%9F%E8%83%BD%E7%BB%93%E6%9E%84%EF%BC%88%E6%90%9C%E7%B4%A2%E3%80%81%E5%8F%91%E5%B8%83%E3%80%81%E7%80%91%E5%B8%83%E6%B5%81%E3%80%81%E8%AF%9D%E9%A2%98%E6%A0%87%E7%AD%BE%EF%BC%89"><span class="toc-text">3.7.2 社区功能结构（搜索、发布、瀑布流、话题标签）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-8-%E4%B8%AA%E4%BA%BA%E4%B8%AD%E5%BF%83"><span class="toc-text">3.8 个人中心</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-8-1-%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD%E7%89%88%E5%9D%97%E8%AE%BE%E8%AE%A1%EF%BC%88%E6%88%91%E7%9A%84%E8%AE%A2%E5%8D%95%E3%80%81%E8%AE%BE%E7%BD%AE%E3%80%81%E6%8E%A8%E8%8D%90%E3%80%81%E5%BF%AB%E6%8D%B7%E5%85%A5%E5%8F%A3%E7%AD%89%EF%BC%89"><span class="toc-text">3.8.1 核心功能版块设计（我的订单、设置、推荐、快捷入口等）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-9-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-text">3.9 本章总结</span></a></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理进阶（三）：第三章：电商用户端产品设计</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-24T10:13:45.000Z" title="发表于 2025-07-24 18:13:45">2025-07-24</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.641Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">13.6k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>39分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理进阶（三）：第三章：电商用户端产品设计"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/17683.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/17683.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理进阶（三）：第三章：电商用户端产品设计</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-24T10:13:45.000Z" title="发表于 2025-07-24 18:13:45">2025-07-24</time><time itemprop="dateCreated datePublished" datetime="2025-07-25T03:05:48.641Z" title="更新于 2025-07-25 11:05:48">2025-07-25</time></header><div id="postchat_postcontent"><h1 id="第三章：电商用户端产品设计"><a href="#第三章：电商用户端产品设计" class="headerlink" title="第三章：电商用户端产品设计"></a>第三章：电商用户端产品设计</h1><p>欢迎来到第三章。在这一章，我们将真正地以“建筑师”的身份，从地基开始，一砖一瓦地搭建起我们电商产品的“用户端大楼”。</p><p>我们将系统性地学习，从用户首次进入产品的“大门”（产品形态），到在“商场”中闲逛（浏览商品）、挑选结账（下单支付）、寻求服务（售后），再到参与“广场”讨论（商品种草）和回到“私人房间”（个人中心）的全过程设计。</p><h2 id="3-1-学习目标"><a href="#3-1-学习目标" class="headerlink" title="3.1 学习目标"></a>3.1 学习目标</h2><p>在本节中，我的核心目标是，带大家掌握电商用户端设计的两大基石：<strong>产品形态选择</strong>和<strong>核心设计思路</strong>。</p><p>我们将学会对比不同产品形态（App/小程序等）的优劣，并能以微信小程序为例，掌握其独特的设计规范。</p><h2 id="3-2-用户端产品形态选择"><a href="#3-2-用户端产品形态选择" class="headerlink" title="3.2 用户端产品形态选择"></a>3.2 用户端产品形态选择</h2><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721220851552.png" alt="image-20250721220851552"></p><p>在项目正式启动后，我面临的第一个重大的技术和战略决策就是：<strong>我们主要应该为用户，打造一个什么样的“场”？</strong></p><p>是开发一个功能强大的独立<strong>App</strong>？还是一个便于在微信里传播的<strong>小程序</strong>？亦或是一个灵活轻便的<strong>H5</strong>网页？</p><p>正如我们看到的，像淘宝、京东这样成熟的平台，通常是“全都要”，在每一种形态上都有布局。但对于启动期的我们，必须做出取舍，选择最适合我们当前战略目标的形态。</p><h3 id="3-2-1-常见产品形态对比-App-小程序-H5-Web端"><a href="#3-2-1-常见产品形态对比-App-小程序-H5-Web端" class="headerlink" title="3.2.1 常见产品形态对比 (App / 小程序 / H5 / Web端)"></a>3.2.1 常见产品形态对比 (App / 小程序 / H5 / Web端)</h3><p>为了做出正确的决策，我通常会用下面这张对比表格，来系统地分析不同形态的优劣。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721220811815.png" alt="image-20250721220811815"></p><table><thead><tr><th align="left">产品形态</th><th align="left">是否需安装</th><th align="left">开发投入</th><th align="left">用户体验</th><th align="left">我的选择考量（适用场景）</th></tr></thead><tbody><tr><td align="left"><strong>App</strong></td><td align="left"><strong>是</strong></td><td align="left"><strong>高</strong> (需安卓/iOS分别开发)</td><td align="left"><strong>完善</strong></td><td align="left">当我的产品功能极其复杂、需要调用手机底层能力（如GPS、蓝牙）、且追求极致性能和体验时，我会选择App。它的推广和获客成本最高。</td></tr><tr><td align="left"><strong>微信小程序</strong></td><td align="left"><strong>否</strong></td><td align="left"><strong>中</strong> (前端开发)</td><td align="left"><strong>好</strong></td><td align="left">当我希望<strong>借助微信的社交生态进行裂变传播和获客</strong>时，小程序是我的不二之选。它体验好、开发快，特别适合电商、本地生活等需要社交分享的场景。</td></tr><tr><td align="left"><strong>H5</strong></td><td align="left"><strong>否</strong></td><td align="left"><strong>低</strong> (前端开发)</td><td align="left"><strong>中等</strong></td><td align="left">当我需要最大化的<strong>灵活性和传播范围</strong>时，我会选择H5。它不受任何平台限制，一个链接就可以走天下，特别适合制作营销活动页、内容文章页。</td></tr><tr><td align="left"><strong>Web端</strong></td><td align="left"><strong>否</strong></td><td align="left"><strong>中</strong> (前端开发)</td><td align="left"><strong>一般</strong></td><td align="left">当我的核心场景是<strong>需要用户在电脑上进行复杂操作</strong>时，我会选择Web端。比如，我们为商家设计的后台管理系统，就必须是Web端。</td></tr></tbody></table><p>对于我们的“大P超级电商”项目，结合我们拥有海量C端用户的背景，<strong>优先开发一个微信小程序</strong>来承接和转化这部分流量，是一个非常明智的启动策略。</p><h3 id="3-2-2-设计规范：以微信小程序为例"><a href="#3-2-2-设计规范：以微信小程序为例" class="headerlink" title="3.2.2 设计规范：以微信小程序为例"></a>3.2.2 设计规范：以微信小程序为例</h3><p>既然我们选择了小程序，那我就必须深入理解它的“游戏规则”。虽然小程序的设计在整体上与App非常类似，但因为它“寄生”于微信这个超级生态之上，所以也带来了一些独特的设计规范和特殊功能。</p><h4 id="1-页面结构"><a href="#1-页面结构" class="headerlink" title="1. 页面结构"></a>1. 页面结构</h4><ul><li><strong>官方小程序胶囊</strong>：我必须时刻牢记，小程序页面的右上角，有一个官方的、包含“关闭/更多”等功能的“胶囊”按钮，这个区域是<strong>不可设计的</strong>，我的页面布局必须为它留出空间。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221058059.png" alt="image-20250721221058059"></p><ul><li><strong>顶部导航栏</strong>：小程序的顶部导航栏，由微信官方统一样式，我们只能定义中间的<code>标题区</code>文字和左侧<code>导航区</code>的返回逻辑。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221137650.png" alt="image-20250721221137650"></p><ul><li><strong>标签栏 (Tab Bar)</strong>：小程序底部的标签栏，有严格的数量限制：<strong>最少2个，最多5个</strong>。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221430247.png" alt="image-20250721221430247"></p><ul><li><strong>尺寸规范</strong>：在绘制原型时，我依然会采用和App一致的<strong>375x667px</strong>作为基准画板，并遵循<code>状态栏(22px)</code>、<code>导航栏(44px)</code>、<code>标签栏(49px)</code>的标准高度。</li></ul><h4 id="2-特殊功能与限制"><a href="#2-特殊功能与限制" class="headerlink" title="2. 特殊功能与限制"></a>2. 特殊功能与限制</h4><p>小程序最大的魅力，在于它能调用微信生态的独有能力。假设我们需要实现“获取手机号”和“推送消息”，在小程序中的实现方式就与App完全不同。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221318459.png" alt="image-20250721221318459"></p><ul><li><strong>获取微信手机号</strong>：在App中，我需要用户手动输入手机号，再通过短信验证，流程繁琐。而在小程序里，我可以直接放置一个“<strong>微信用户一键授权</strong>”的按钮。用户点击后，会拉起微信的官方授权弹窗，用户只需点击“允许”，我们就能安全地获取到他绑定在微信上的手机号，<strong>极大提升了注册/登录的转化率</strong>。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221356124.png" alt="image-20250721221356124"></p><ul><li><strong>订阅消息</strong>：在App中，只要用户允许，我可以相对自由地向他推送消息。但在小程序中，则严格得多。我<strong>不能主动向用户推送营销消息</strong>。用户必须**主动“订阅”**某一个服务通知（比如“发货通知”、“降价提醒”），我才能向他发送一条对应的服务消息。这是一种对用户打扰更小的“<strong>一次性授权</strong>”模式，我在设计运营功能时必须充分考虑这个限制。</li></ul><hr><h2 id="3-3-用户端产品设计思路"><a href="#3-3-用户端产品设计思路" class="headerlink" title="3.3 用户端产品设计思路"></a>3.3 用户端产品设计思路</h2><p>在我们选定了产品形态（如：微信小程序）之后，我不会立刻开始绘制具体的页面。我会先退一步，从更高维度，建立起整个用户端产品的“<strong>设计思路和骨架</strong>”。</p><p>虽然我们之前学习过内容产品的设计，但电商用户端，有其自身独特的业务核心和功能侧重。在这一节，我将带大家明确我们电商产品的核心业务，并掌握指导我们进行界面布局的两大基本设计原则。</p><h3 id="3-3-1-核心业务与功能模块"><a href="#3-3-1-核心业务与功能模块" class="headerlink" title="3.3.1 核心业务与功能模块"></a>3.3.1 核心业务与功能模块</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722091703261.png" alt="image-20250722091703261"></p><p>我的第一步，是<strong>将用户的完整购物旅程，拆解为几个核心的业务模块</strong>。这能帮助我确保，我的产品设计，覆盖了用户从“认知”到“购后”的每一个关键环节。</p><p>对于一个典型的电商产品，我将核心业务拆解为以下六大模块：</p><ol><li><strong>注册登录</strong>：这是用户“获取身份”的入口。</li><li><strong>浏览商品</strong>：这是用户“逛商场”的核心环节，包括有目的的搜索和无目的的闲逛。</li><li><strong>下单支付</strong>：这是电商的“收银台”，是完成交易的核心闭环。</li><li><strong>订单与售后</strong>：这是用户购后的“服务中心”，负责履约和处理问题。</li><li><strong>商品种草</strong>：这是我们产品特色的“内容社区”，负责吸引用户、建立信任。</li><li><strong>个人中心</strong>：这是用户在我们平台的“家”，负责汇总个人数据和产品全局设置。</li></ol><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722091746923.png" alt="image-20250722091746923"></p><p>这六大业务模块，就构成了我们用户端的“<strong>核心功能架构</strong>”。图中的“用户端核心架构”就是一个很好的示例，它将具体的、零散的功能点（如：搜索栏、金刚区），清晰地归类到了它所属的业务模块之下（如：首页）。这个架构，就是我们后续进行详细页面设计的“总纲”。</p><h3 id="3-3-2-核心设计原则"><a href="#3-3-2-核心设计原则" class="headerlink" title="3.3.2 核心设计原则"></a>3.3.2 核心设计原则</h3><p>有了功能架构的“骨架”，我接下来需要思考，如何为这个骨架“填充血肉”？也就是说，在具体的页面上，我应该如何组织和排布那些繁多的功能和信息，才能让用户觉得界面清晰、易于理解？</p><p>在这里，我会借助格式塔心理学（Gestalt Psychology）中，两个最基础、也最强大的视觉设计原则。</p><h4 id="1-接近法则-Law-of-Proximity"><a href="#1-接近法则-Law-of-Proximity" class="headerlink" title="1. 接近法则 (Law of Proximity)"></a>1. 接近法则 (Law of Proximity)</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722091947975.png" alt="image-20250722091947975"></p><ul><li><strong>法则定义</strong>：我们的大脑，会本能地，将物理空间上<strong>彼此靠近</strong>的元素，视为一个<strong>整体</strong>。</li><li><strong>我的设计应用</strong>：在界面设计中，“<strong>间距</strong>”是我最有力的设计工具之一。<ul><li>我会把<strong>相关</strong>的元素（比如，一张商品图和它的商品标题）紧紧地放在一起，让它们在视觉上自然地成为一组。</li><li>我会用<strong>留白</strong>，来拉开<strong>不相关</strong>的元素或组之间的距离，形成清晰的视觉区块。</li></ul></li></ul><h4 id="2-相似法则-Law-of-Similarity"><a href="#2-相似法则-Law-of-Similarity" class="headerlink" title="2. 相似法则 (Law of Similarity)"></a>2. 相似法则 (Law of Similarity)</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092013201.png" alt="image-20250722092013020"></p><ul><li><p><strong>法则定义</strong>：我们的大脑，会本能地，将那些在<strong>视觉特征（如形状、颜色、大小）上相似</strong>的元素，视为<strong>同类</strong>。</p></li><li><p><strong>我的设计应用</strong>：在界面设计中，“<strong>一致性</strong>”是建立用户认知的关键。</p><ul><li>我会确保所有<strong>功能相同或相近</strong>的元素，在视觉上保持<strong>相似</strong>。比如，所有“可点击”的按钮，都用同一种颜色和形状；所有“可输入”的文本框，都用同一种样式。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092053425.png" alt="image-20250722092053425"></p><ul><li>正如“常用功能”案例所示，尽管每一个图标的图案都不同，但它们统一的尺寸、颜色和圆角风格，都在强烈地向用户暗示：“我们都属于一类，我们都是可以点击的功能入口”。</li></ul></li></ul><p><strong>总结</strong>：在后续的页面设计中，我将综合运用“<strong>接近法则</strong>”来<strong>组织页面布局、划分区块</strong>，并用“<strong>相似法则</strong>”来<strong>统一控件样式、建立操作认知</strong>。这是让我们的设计变得“专业”和“易用”的秘诀。</p><hr><h2 id="3-4-浏览商品"><a href="#3-4-浏览商品" class="headerlink" title="3.4 浏览商品"></a>3.4 浏览商品</h2><p>[此处放置“首页思考”的图片 (<code>image_2d6800.png</code>)]</p><p>我们电商产品的“商场”已经建好，现在，当用户走进这扇“大门”时，他们首先看到的，就是我们的“<strong>商场大堂</strong>”——<strong>首页</strong>。</p><p>首页，是用户对我们产品形成第一印象、也是我们引导用户走向各个“专柜”的最核心的枢纽。我设计首页时，始终围绕着一个核心问题：<strong>它需要同时满足谁的需求？达成什么样的目的？</strong></p><h3 id="3-4-1-首页设计"><a href="#3-4-1-首页设计" class="headerlink" title="3.4.1 首页设计"></a>3.4.1 首页设计</h3><h4 id="1-首页的核心目的"><a href="#1-首页的核心目的" class="headerlink" title="1. 首页的核心目的"></a>1. 首页的核心目的</h4><p>我设计首页，必须同时扮演好“<strong>服务员</strong>”（满足用户需求）和“<strong>商场经理</strong>”（达成公司目标）的双重角色。</p><ul><li><p><strong>从用户角度：满足多样化的“逛街”心态</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092825580.png" alt="image-20250722092825580"></p><p>我需要为进入首页、心态各不相同的用户，都提供最高效的解决方案。</p></li></ul><table><thead><tr><th align="left">用户心态</th><th align="left">我的设计方案</th></tr></thead><tbody><tr><td align="left"><strong>“我明确知道要买什么”</strong></td><td align="left">在页面最顶部，提供一个<strong>高效、精准的搜索栏</strong>。</td></tr><tr><td align="left"><strong>“我知道大概要买什么品类”</strong></td><td align="left">提供一套<strong>清晰、易懂的商品分类入口</strong>。</td></tr><tr><td align="left"><strong>“我就是想随便逛逛，看有啥好东西”</strong></td><td align="left">提供一个无限下拉的、引人入胜的<strong>个性化商品推荐列表</strong>。</td></tr><tr><td align="left"><strong>“我想看看有啥便宜可以占”</strong></td><td align="left">提供突出、有吸引力的<strong>促销/活动专区</strong>，如秒杀、百亿补贴等。</td></tr></tbody></table><ul><li><p><strong>从公司角度：实现平台的商业目标</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092907876.png" alt="image-20250722092907876"></p><p>同时，我也需要利用首页这个“寸土寸金”的场地，来达成我们的商业目的。</p></li></ul><table><thead><tr><th align="left">公司目标</th><th align="left">我的设计方案</th></tr></thead><tbody><tr><td align="left"><strong>帮助商家促销引流</strong></td><td align="left">在首页的核心位置，为付费的商家提供<strong>Banner广告位</strong>和<strong>活动入口</strong>。</td></tr><tr><td align="left"><strong>帮助自营店铺促销引流</strong></td><td align="left">（若有自营业务）为自营的重点商品或活动，提供专属的曝光区域。</td></tr><tr><td align="left"><strong>展现平台调性</strong></td><td align="left">整个首页的<strong>视觉风格（UI）、文案、推荐内容</strong>，都必须严格符合我们“内容驱动的潮流社区电商”的定位。</td></tr></tbody></table><h4 id="2-常见首页模块解析"><a href="#2-常见首页模块解析" class="headerlink" title="2. 常见首页模块解析"></a>2. 常见首页模块解析</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092959787.png" alt="image-20250722092959787"></p><p>为了同时满足上述的用户和公司需求，经过多年的演化，电商首页已经形成了一套相对成熟的模块化布局。我会通过分析竞品，来借鉴和思考我们自己的设计。</p><table><thead><tr><th align="left"><strong>核心模块</strong></th><th align="left"><strong>我的设计解读与应用</strong></th></tr></thead><tbody><tr><td align="left"><strong>搜索栏</strong></td><td align="left"><strong>雷打不动的第一模块</strong>，必须始终固定在页面最顶部，服务于目的性最强的用户。</td></tr><tr><td align="left"><strong>金刚区</strong></td><td align="left"><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722093051105.png" alt="image-20250722093051105"><br>这是指搜索栏下方的一组<strong>图标网格导航</strong>。我把它看作是<strong>核心业务的“一级入口”</strong>。我会把我们最重要的商品分类（如“潮流服饰”、“数码新品”）和特色业务（如“达人直播”、“好物种草”）放在这里。</td></tr><tr><td align="left"><strong>Banner / 促销区</strong></td><td align="left">这是首页最<strong>黄金的广告和活动展示位</strong>。我会用它来推广平台的重大活动，或将其作为重要的广告收入来源。</td></tr><tr><td align="left"><strong>商品推荐列表</strong></td><td align="left">这是首页占据面积最大、也是留住“闲逛”用户的<strong>核心内容区</strong>。我会采用“<strong>瀑布流</strong>”的形式，通过个性化推荐算法，为每个用户呈现一个独一无二的、无限延伸的商品列表。</td></tr></tbody></table><h4 id="3-我们“大P超级电商”的首页设计思路"><a href="#3-我们“大P超级电商”的首页设计思路" class="headerlink" title="3. 我们“大P超级电商”的首页设计思路"></a>3. 我们“大P超级电商”的首页设计思路</h4><p>最后，结合对竞品的分析和我们自身的定位，我为我们“大P超级电商”的首页，确立了以下设计思路：</p><ol><li><strong>UI风格</strong>：界面要简洁、留白充分，营造出“呼吸感”，整体视觉风格要年轻、时尚，符合“90后”的审美。</li><li><strong>金刚区设计</strong>：必须体现我们“内容+电商”的特色。除了<code>服装</code>、<code>数码</code>等品类入口，必须包含<code>直播</code>、<code>种草</code>等内容社区的入口。</li></ol><p>我们在设计首页时一定会遇到的经典决策：“金刚区”到底应该放几个图标？上面的文字应该怎么写？</p><table><thead><tr><th>图标 (示意)</th><th>文字标签</th><th>我的设计思路</th></tr></thead><tbody><tr><td>📱</td><td><strong>手机数码</strong></td><td>“90后”核心关注的高价值品类，属于<strong>品类入口</strong>。</td></tr><tr><td>👚</td><td><strong>潮流服饰</strong></td><td>贴合我们“潮流”的平台调性，属于<strong>品类入口</strong>。</td></tr><tr><td>💄</td><td><strong>美妆个护</strong></td><td>年轻用户，特别是女性用户的高频消费品类，属于<strong>品类入口</strong>。</td></tr><tr><td>📺</td><td><strong>达人直播</strong></td><td>我们的<strong>核心差异化</strong>业务，必须给予最强的曝光，属于<strong>功能入口</strong>。</td></tr><tr><td>💸</td><td><strong>百亿补贴</strong></td><td>电商平台“拉新促活”的标配，用明确的利益点吸引用户，属于<strong>活动入口</strong>。</td></tr><tr><td>🧾</td><td><strong>领券中心</strong></td><td>培养用户“先领券再购物”的习惯，提升转化率，属于<strong>功能入口</strong>。</td></tr><tr><td>📦</td><td><strong>我的订单</strong></td><td>用户最高频使用的查询功能之一，提供一个快捷入口，属于<strong>功能入口</strong>。</td></tr><tr><td>➡️</td><td><strong>全部分类</strong></td><td>“渐进式呈现”原则的应用，收纳所有其他品类。</td></tr></tbody></table><p>3.<strong>推荐算法</strong>：商品推荐列表的算法，除了考虑用户的浏览和购买行为，还必须<strong>高度重视用户的社交和内容偏好</strong>。比如，优先推荐“用户关注的KOL正在推荐的商品”</p><p>最后我们产出的商品低保真原型原型，大致是这样的：</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/c2f3be4f-3f73-4c54-a40b-d41760dc006b.png" alt="img"></p><hr><h3 id="3-4-2-商品分类设计（无分类、一级、多级）"><a href="#3-4-2-商品分类设计（无分类、一级、多级）" class="headerlink" title="3.4.2 商品分类设计（无分类、一级、多级）"></a>3.4.2 商品分类设计（无分类、一级、多级）</h3><p>在设计好首页之后，我们需要为那些有大概购物方向的用户，提供一套清晰的“货架导引”系统，这个系统就是<strong>商品分类</strong>。它的核心目的，是满足用户高效缩小寻找范围的需求。</p><p>我给商品类目的定义是：按照商品的用途、特征等维度，并且根据一定的管理目的，把相似的商品归为一类的行为。并且在类别当中又会存在细分的类型。</p><p>我设计分类体系的复杂度，完全取决于我们平台商品的数量和丰富度。我通常会根据平台的体量，考虑三种不同的分类形式。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101139313.png" alt="image-20250722101139313"></p><p>这三种形式，分别适用于不同的业务阶段和规模，我们可以通过上面的案例直观地感受它们的差异。对于我们“大P超级电商”这样的综合性平台，一个清晰的“多级分类”体系是必不可少的设计。</p><h3 id="3-4-3-商品列表与详情页设计（图文、参数、评价、推荐）"><a href="#3-4-3-商品列表与详情页设计（图文、参数、评价、推荐）" class="headerlink" title="3.4.3 商品列表与详情页设计（图文、参数、评价、推荐）"></a>3.4.3 商品列表与详情页设计（图文、参数、评价、推荐）</h3><p>当用户通过首页、搜索或分类，最终都会来到两个核心的页面：<strong>商品列表页（PLP）<strong>和</strong>商品详情页（PDP）</strong>。</p><p>我设计这两个页面时，脑海里始终装着三类典型用户：目的明确的“小风”、犹豫不决的“中风”、以及纯粹闲逛的“大风”。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101836348.png" alt="image-20250722101836348"></p><p><strong>1. 商品列表页 (Product List Page - PLP)</strong></p><p>商品列表页是我们商场的“<strong>货架</strong>”。它的核心设计目标，是让用户能<strong>高效地筛选和对比</strong>。虽然列表的内容来源可能不同（如搜索结果、推荐、分类），但其页面结构和设计要点是共通的。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101931329.png" alt="image-20250722101931329"></p><p>一个优秀的商品列表页，必须包含清晰的<strong>商品卡片</strong>（展示图、文、价等核心信息），以及强大的<strong>筛选与排序</strong>功能，来帮助用户快速从海量商品中，找到自己心仪的目标。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722105209159.png" alt="image-20250722105209159"></p><p><strong>2. 商品详情页 (Product Detail Page - PDP)</strong></p><p>商品详情页是我们商场的“<strong>金牌销售员</strong>”，它的核心设计目标是<strong>打消用户的所有疑虑，促成最终的购买</strong>。我通常会将页面上半部分，用来满足理性用户的决策需求。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101959967.png" alt="image-20250722101959967"></p><p>页面的首屏要点，在于简明扼要地表现出产品的核心信息，让用户直接判断出“这个产品是什么”。我会通过<strong>商品展示区</strong>（高清图/视频）、<strong>商品属性区</strong>（规格参数）和<strong>用户评价区</strong>（社群证明），来分别满足用户对“颜值”、“内涵”和“口碑”的确认需求。</p><p>页面的下半部分，我则用来服务那些还在犹豫、或者纯粹闲逛的用户，通过更丰富的内容，对他们进行深度“种草”。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722102009141.png" alt="image-20250722102009141"></p><p>这部分通常包括图文并茂的<strong>详情介绍</strong>，用来吸引用户；以及<strong>问答模块</strong>，用来对评价功能进行进一步强化，打消用户的购买疑虑。</p><p>在详情页的底部，我一定会设计一个“<strong>智能推荐</strong>”模块。它的核心目的，是在用户对当前商品不满意、准备离开时，为他提供更多相关的选择，<strong>形成一个流量的闭环</strong>，增加用户在我们平台留存和成交的机会。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722102020748.png" alt="image-20250722102020748"></p><hr><h2 id="3-5-下单支付"><a href="#3-5-下单支付" class="headerlink" title="3.5 下单支付"></a>3.5 下单支付</h2><p>在用户完成了“逛”和“选”，将心仪的商品加入购物车之后，我们就进入了整个电商流程的“<strong>核心交易环节</strong>”。</p><p>我把这个环节的设计，看作是引导用户走过一条“<strong>信任与效率的走廊</strong>”。这条走廊上的任何一个障碍、一丝疑虑，都可能导致用户在最后关头放弃购买。因此，我的设计目标，必须是<strong>极致的顺滑、清晰与安全</strong>。</p><h3 id="3-5-1-下单流程与页面设计（提交页、支付页）"><a href="#3-5-1-下单流程与页面设计（提交页、支付页）" class="headerlink" title="3.5.1 下单流程与页面设计（提交页、支付页）"></a>3.5.1 下单流程与页面设计（提交页、支付页）</h3><p>我通常会将整个下单支付流程，拆解为两个核心的页面来进行设计：<strong>订单提交页</strong>和<strong>支付页（收银台）</strong>。</p><h4 id="1-订单提交页-Order-Submission-Page"><a href="#1-订单提交页-Order-Submission-Page" class="headerlink" title="1. 订单提交页 (Order Submission Page)"></a>1. 订单提交页 (Order Submission Page)</h4><p>当用户在商品详情页点击“立即购买”，或在购物车点击“去结算”后，并不会直接进入付款环节，而是会先来到<strong>订单提交页</strong>。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110142381.png" alt="image-20250722110142381"></p><p>这个页面的核心作用，我把它定义为用户的“<strong>最后一次确认</strong>”。在用户真正掏钱之前，我必须为他提供一个清晰的、所有交易信息的汇总页面，让他进行最后的检查和确认。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110204985.png" alt="image-20250722110204985"></p><p>我设计的订单提交页，必须让用户能够清晰地完成三件事：</p><ul><li><strong>确认商品</strong>：清晰地罗列出本次将要购买的所有商品信息（名称、SKU、数量、价格）。</li><li><strong>确认地址</strong>：提供默认收货地址，并允许用户方便地选择或新增其他地址。</li><li><strong>确认价格</strong>：清晰地展示商品总额、运费、优惠券抵扣、最终实付金额等所有价格明细。</li></ul><h4 id="2-支付页-收银台-Payment-Page-Cashier"><a href="#2-支付页-收银台-Payment-Page-Cashier" class="headerlink" title="2. 支付页/收银台 (Payment Page / Cashier)"></a>2. 支付页/收银台 (Payment Page / Cashier)</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110039262.png" alt="image-20250722110039262"></p><p>当用户在订单提交页，点击“提交订单”后，他才真正进入了“<strong>支付页</strong>”，我常称之为“<strong>收银台</strong>”。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722105741343.png" alt="image-20250722105741343"></p><p>这个页面的设计，我追求的是<strong>极致的简洁和安全感</strong>。它的核心作用只有三个：</p><ol><li><strong>确认实付金额</strong>：醒目地展示最终需要支付的金额。</li><li><strong>选择支付方式</strong>：提供用户选择支付渠道（如微信、支付宝）的入口。</li><li><strong>完成支付</strong>：一个清晰、唯一的“确认支付”按钮。</li></ol><p><strong>我的拓展设计（支付异常处理）</strong>：<br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110255711.png" alt="image-20250722110255711"></p><p>设计支付页时，我必须考虑一个最常见的异常场景：<strong>用户进入了收银台，但因为种种原因，没有完成支付就退出了</strong>。<br>一个糟糕的设计，可能会让用户之前提交的订单直接消失。而一个优秀的设计，正如案例所示，应该<strong>将这份订单，自动保存为一张“待支付”的订单</strong>。用户可以在“我的订单”中随时找到它，并重新发起支付。这个小小的设计，能为我们挽回大量可能流失的销售额。</p><h4 id="3-支付成功后的流程"><a href="#3-支付成功后的流程" class="headerlink" title="3. 支付成功后的流程"></a>3. 支付成功后的流程</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110528929.png" alt="image-20250722110528929"></p><p>当用户成功支付后，这笔“交易”在后台就正式生成了，并进入了它的生命周期。我会用一组“<strong>订单状态</strong>”，来清晰地标记它在流程中所处的节点。支付成功，就是订单状态从“<strong>待支付</strong>”流转到“<strong>待发货</strong>”的触发器。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110510755.png" alt="image-20250722110510755"></p><p>此时，用户可以在“个人中心”的“<strong>我的订单</strong>”列表中，看到这笔订单，并查看到它“待发货”的状态。当商家发货后，用户最关心的“<strong>物流信息</strong>”就会在这里出现。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110805038.png" alt="image-20250722110805038"></p><blockquote><p><code>思考：“物流信息是从哪来的🤔”</code></p></blockquote><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110821135.png" alt="image-20250722110821135"></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110923521.png" alt="image-20250722110923521"></p><p>它并不是由我们的商家手动更新的。在我设计的后台，商家发货时，只需要选择<strong>快递公司</strong>并输入<strong>快递单号</strong>。随后，我们的<strong>后端服务器</strong>，就会通过API接口，<strong>定时地向第三方快递查询平台（如“申通”）发起查询请求</strong>，获取最新的物流轨迹，然后将这些信息，展示在用户端的订单详情页上。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111007040.png" alt="image-20250722111007040"></p><p>我在撰写PRD时，必须将这个技术方案的逻辑，清晰地描述出来。</p><hr><h3 id="3-5-2-支付方式（微信、支付宝、银联、聚合支付）"><a href="#3-5-2-支付方式（微信、支付宝、银联、聚合支付）" class="headerlink" title="3.5.2 支付方式（微信、支付宝、银联、聚合支付）"></a>3.5.2 支付方式（微信、支付宝、银联、聚合支付）</h3><p>在设计好“收银台”页面后，我需要做的最重要的决策，就是为这个收银台，配备哪些“<strong>收款设备</strong>”，也就是我们常说的<strong>支付方式</strong>。</p><p>在今天的中国市场，只提供一种支付方式是远远不够的。为了最大化地提升支付成功率，我至少需要为用户提供微信支付和支付宝这两种主流选择。</p><h4 id="1-微信支付-支付宝-银联（独立渠道对接）"><a href="#1-微信支付-支付宝-银联（独立渠道对接）" class="headerlink" title="1. 微信支付 &amp; 支付宝 &amp; 银联（独立渠道对接）"></a>1. 微信支付 &amp; 支付宝 &amp; 银联（独立渠道对接）</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111349078.png" alt="image-20250722111349078"></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111359741.png" alt="image-20250722111359741"></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111408567.png" alt="image-20250722111408567"></p><p>理论上，我们可以分别独立地去对接每一个支付渠道。</p><p><strong>我需要告诉开发的关键点（以微信支付为例）：</strong><br>要接入微信支付，不是一个纯粹的技术工作，它需要产品、运营和技术共同协作。我需要了解并推动以下几个步骤：</p><ol><li><strong>商户号申请（运营/商务负责）</strong>：首先，我们需要由公司的运营或商务同事，前往“<strong>微信支付商户平台</strong>”，提交我们公司的营业执照等资质，申请一个“商户号（mch_id）”。这个过程，支付宝和银联也完全一样，都需要我们先拥有一个官方认证的“商家身份”。</li><li><strong>获取开发凭证（研发负责人）</strong>：当我们的商户号被批准后，技术负责人需要登录这个商户平台，去获取进行技术开发所必需的“<strong>身份凭证</strong>”。这通常包括<code>AppID</code>、<code>API证书</code>、<code>API密钥</code>等。我把它们理解为，我们公司服务器与微信支付服务器之间，进行加密通信的“账号和密码”。</li><li><strong>技术对接（研发负责）</strong>：拿到凭证后，研发同学才会真正开始写代码。<ul><li><strong>后端开发</strong>：需要按照微信/支付宝的官方开发文档，开发服务端接口。这些接口主要负责创建“预支付订单”、接收支付成功或失败的“异步通知”等。</li><li><strong>前端开发（App）</strong>：需要集成微信/支付宝的官方SDK（软件开发工具包）。这个SDK的主要作用，是在我们的App里，能够“拉起”用户手机上已经安装的微信或支付宝App，来进行最终的密码/指纹输入。</li></ul></li></ol><h4 id="2-聚合支付（我的推荐方案）"><a href="#2-聚合支付（我的推荐方案）" class="headerlink" title="2. 聚合支付（我的推荐方案）"></a>2. 聚合支付（我的推荐方案）</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111626234.png" alt="image-20250722111626234"></p><p>在我们理解了独立对接的流程后，两个核心痛点就浮现出来了：</p><ul><li><strong>接入成本高</strong>：我要支持微信、支付宝、银联三种支付，我的研发团队就需要把上面的流程，<strong>重复做三遍</strong>。这需要耗费巨大的研发资源。</li><li><strong>财务管理难</strong>：每天，我的财务同事，需要分别登录三个不同的商户后台，去下载三份不同的对账单，再进行手动的汇总和核对，极其繁琐且容易出错。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111642616.png" alt="image-20250722111642616"></p><p>为了解决这两个痛点，一个更聪明的、也是我强烈推荐的方案，就是使用“<strong>聚合支付</strong>”。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111748908.png" alt="image-20250722111748908"></p><p><strong>聚合支付</strong>服务商（如 <strong>Ping++、Adapay</strong>），就是支付领域的“<strong>万能转换插头</strong>”。它已经提前帮我们，把市面上所有的主流支付渠道（微信、支付宝、银联、各类银行卡等），都预先集成好了。</p><p><strong>我需要告诉开发的关键点（技术视角）：</strong></p><ul><li><strong>一次对接，全部拥有</strong>：我们不再需要去对接微信、支付宝等多个上游渠道。我的研发团队，只需要按照聚合支付服务商提供的一份开发文档，<strong>进行一次技术对接</strong>即可。</li><li><strong>统一的API和SDK</strong>：聚合支付会为我们提供<strong>一套统一的API和SDK</strong>。当用户在我们的App里选择用微信支付时，我们的App调用的，是聚合支付的SDK；我们的服务器，也只请求聚合支付的API。后续由聚合支付的服务，来和微信的服务器进行通信。</li><li><strong>统一的后台和对账单</strong>：我的财务同事，只需要登录聚合支付这<strong>一个后台</strong>，就可以看到所有渠道的交易流水，并下载一份统一的对-账单。</li></ul><p><strong>我的决策权衡：</strong><br>使用聚合支付的唯一“缺点”，是它会在每个渠道的原有费率基础上，再收取一点点的服务费。但考虑到它能为我们<strong>节省巨大的研发成本和财务管理成本</strong>，对于绝大多数公司（特别是初创和中型公司）而言，这笔服务费，都是一笔<strong>极具性价比</strong>的投资。</p><blockquote><p><code>聚合支付是不是代表不用哪些营业证书之类的，直接通过付钱就能接入了🤔</code></p><p>答案是：<strong>不是的，聚合支付并不能免除您提供公司资质（如营业执照）的义务。</strong></p><p>您可以把聚合支付服务商，看作是一个“<strong>超级代办员</strong>”或“<strong>技术外包服务商</strong>”，而不是一个“<strong>资质豁免机构</strong>”。他们的核心价值在于<strong>简化技术对接和财务管理</strong>，而不是绕过金融监管。</p><p>我为您梳理一下实际的流程：</p><ol><li><strong>您与聚合支付签约</strong>：您首先需要选择一家聚合支付服务商（如Ping++），并在他们的平台上注册账户。</li><li><strong>您向聚合支付提交资质</strong>：在注册过程中，您<strong>仍然需要</strong>向聚合支付服务商，提交您自己公司的全套有效资质，包括但不限于：</li></ol><ul><li><strong>营业执照</strong></li><li><strong>法人身份证信息</strong></li><li><strong>对公银行账户</strong></li><li><strong>网站/App的ICP备案信息</strong>（如果适用）</li></ul><ol start="3"><li><strong>聚合支付为您代办申请</strong>：聚合支付服务商在收到您的资质后，会作为您的“代办员”，拿着您的这些材料，去分别向微信支付、支付宝、银联等官方渠道，为您<strong>集中申请</strong>开通各个支付渠道的商户权限。</li><li><strong>最终结果</strong>：审批通过后，您最终获得的，依然是<strong>您自己公司名下的、在微信和支付宝备案的合法商户号</strong>。聚合支付只是为您提供了一个统一的技术接口和管理后台来操作它们。</li></ol></blockquote><table><thead><tr><th align="left"><strong>支付方案</strong></th><th align="left"><strong>我的解读</strong></th><th align="left"><strong>优点</strong></th><th align="left"><strong>缺点</strong></th></tr></thead><tbody><tr><td align="left"><strong>逐个渠道对接</strong></td><td align="left">我们分别与微信、支付宝等签约并进行技术开发。</td><td align="left">费率可能略低，资金直接到账。</td><td align="left">开发成本极高，财务对账繁琐。</td></tr><tr><td align="left"><strong>使用聚合支付</strong></td><td align="left">我们只与一家聚合支付服务商签约和开发。</td><td align="left"><strong>开发成本极低（只需一次对接）</strong>，财务对账简单。</td><td align="left">费率略高，资金需要经过聚合服务商中转。</td></tr></tbody></table><hr><h3 id="3-5-3-库存管理与问题应对"><a href="#3-5-3-库存管理与问题应对" class="headerlink" title="3.5.3 库存管理与问题应对"></a>3.5.3 库存管理与问题应对</h3><p>我们都可能有过这样的经历：在一个平台下单后，不急着付款，过了一会儿想起来去支付，发现订单依然有效；</p><p>而在另一个平台，同样的操作，回来支付时却被告知“商品已售罄”。</p><p><strong>这是为什么呢？</strong><br>这背后，就反映了不同电商平台，对于“<strong>库存扣减</strong>”这个核心问题，采取了两种截然不同的产品策略。</p><h4 id="1-库存扣减方式（拍下减-vs-付款减）"><a href="#1-库存扣减方式（拍下减-vs-付款减）" class="headerlink" title="1. 库存扣减方式（拍下减 vs 付款减）"></a>1. 库存扣减方式（拍下减 vs 付款减）</h4><p>在我设计交易系统时，我必须与我的技术和业务负责人，共同做出一个关键决策：<strong>我们的库存，到底应该在哪一个节点扣减？</strong> 这个决策，没有绝对的好坏，只有不同选择下的利弊权衡。</p><table><thead><tr><th align="left">扣减方式</th><th align="left">核心逻辑</th><th align="left">用户体验</th><th align="left">主要风险</th><th align="left">我的选择考量</th></tr></thead><tbody><tr><td align="left"><strong>拍下减</strong></td><td align="left">当用户点击“<strong>提交订单</strong>”的瞬间，无论是否付款，系统都会立即为他预留这份库存。</td><td align="left"><strong>好</strong>。用户会感觉“只要我下单了，这个货就是我的了”，体验非常安心。</td><td align="left"><strong>恶拍</strong></td><td align="left">我通常会在普通商品的销售中，采用此方式，因为它能提供最佳的用户体验。</td></tr><tr><td align="left"><strong>付款减</strong></td><td align="left">只有当用户<strong>成功完成支付</strong>的瞬间，系统才会去扣减实际的物理库存。</td><td align="left"><strong>一般</strong>。用户下单后，可能会因为犹豫了几分钟，回来支付时发现商品已被别人买走，导致体验不佳和用户流失。</td><td align="left"><strong>超卖</strong></td><td align="left">我通常只在库存极少、瞬时流量极大的“秒杀”等营销活动中，才会谨慎采用此方式。</td></tr></tbody></table><p>作为产品经理，我的工作，就是<strong>选择一种方式，并设计一套完整的机制，来最大化地规避它的潜在风险</strong>。</p><h4 id="2-问题与应对（恶意拍单、超卖）"><a href="#2-问题与应对（恶意拍单、超卖）" class="headerlink" title="2. 问题与应对（恶意拍单、超卖）"></a>2. 问题与应对（恶意拍单、超卖）</h4><h5 id="应对“恶拍”"><a href="#应对“恶拍”" class="headerlink" title="应对“恶拍”"></a><strong>应对“恶拍”</strong></h5><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722112931696.png" alt="image-20250722112931696"></p><p>如果我选择了“<strong>拍下减库存</strong>”，那我的头号敌人，就是“<strong>恶拍</strong>”——即，竞争对手或黄牛，恶意地大量下单但不支付，以此来锁死我的库存，让真实用户无法购买。</p><p>为了应对它，我必须建立一套“<strong>组合防御</strong>”体系：</p><ol><li><strong>减少库存保留时间</strong>：这是我最核心的武器。我会设计一个“<strong>订单自动取消</strong>”的规则。比如，<strong>下单后15分钟内未支付</strong>，系统将自动取消这笔订单，并将预留的库存，<strong>重新释放</strong>回公共库存池中，供其他用户购买。</li><li><strong>限购</strong>：对于一些热门或促销商品，我会在产品层面，增加“<strong>限购</strong>”规则。比如，规定“<strong>单个ID限购1件</strong>”，这能有效防止单一恶意用户锁死大量库存。</li><li><strong>安全策略</strong>：我还会和风控团队合作，建立监控机制。当发现某个用户ID，在短时间内，有大量“下单后又取消”的异常行为时，系统可以暂时限制他的下单权限。</li></ol><h5 id="应对“超卖”"><a href="#应对“超卖”" class="headerlink" title="应对“超卖”"></a><strong>应对“超卖”</strong></h5><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113011849.png" alt="image-20250722113011849"></p><p>如果我选择了“<strong>付款减库存</strong>”，那我最大的噩梦，就是“<strong>超卖</strong>”——即，我们实际卖出的商品数量，超过了我们的真实库存。这会引发严重的客诉，极大地损害平台信誉。</p><p>为了应对它，我同样需要一套“<strong>组合防御</strong>”体系：</p><ol><li><strong>技术角度解决</strong>：这主要依赖于研发团队。我会要求我的技术负责人，必须在技术层面，通过<strong>数据库锁</strong>或<strong>分布式队列</strong>等技术，来处理高并发场景下的库存扣减请求，确保对最后一件库存的扣减操作，是“<strong>原子性</strong>”的（即，在同一瞬间，只能有一个请求能成功）。</li><li><strong>提示用户</strong>：在产品体验层面，为了管理用户预期，当某个商品的库存数量很少时（比如，少于10件），我会在商品详情页和购物车中，明确地展示“<strong>库存紧张</strong>”或“<strong>仅剩X件</strong>”的提示文案。</li><li><strong>设置安全库存</strong>：这是我最常用的一个运营策略。如果一个商品的物理库存有1000件，我会在电商后台的“可售卖库存”中，只填写<strong>950</strong>件。那剩下的50件，就成了我的“<strong>安全库存</strong>”。它就像一个缓冲垫，既能消化掉极端情况下，因技术原因产生的少量超卖，也能用来应对用户“退货换货”的需求。</li></ol><hr><h3 id="3-5-4-拆单逻辑（父子订单、半拆单）"><a href="#3-5-4-拆单逻辑（父子订单、半拆单）" class="headerlink" title="3.5.4 拆单逻辑（父子订单、半拆单）"></a>3.5.4 拆单逻辑（父子订单、半拆单）</h3><p>当用户在我们的购物车里，同时选中了来自不同商家、或者满足某些特殊条件的多个商品，然后点击“去结算”时，一个复杂的问题就摆在了我面前：</p><p><strong>后台应该如何处理这张“大单”？是把它当作一个订单，还是多个订单？</strong></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110016603.png" alt="image-20250722110016603"></p><p>“如果从购物车（多个店铺多个商品）进入结算，需要考虑什么？” 在这种情况下，我的订单提交页，必须进行“<strong>拆单</strong>”展示。我会<strong>按照不同的店铺，将商品进行分组</strong>。每个店铺的商品，会形成一个独立的“包裹”，分别计算运费和优惠，最终汇总成一个总的支付金额。这种清晰的结构，是解决多商家同时结算场景的最佳实践。</p><h4 id="1-为什么要拆单？"><a href="#1-为什么要拆单？" class="headerlink" title="1. 为什么要拆单？"></a>1. 为什么要拆单？</h4><p>我设计拆单逻辑，主要是为了应对以下五种常见的业务场景：</p><table><thead><tr><th align="left"><strong>我的设计考量</strong></th><th align="left"><strong>拆单因素</strong></th></tr></thead><tbody><tr><td align="left"><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113550199.png" alt="image-20250722113550199"><br>这是最常见的拆单原因。不同商家的商品，其<strong>货权、发货地、财务结算主体</strong>都不同，因此必须拆分为独立的订单，分别进行处理。</td><td align="left"><strong>店铺 (Store)</strong></td></tr><tr><td align="left"><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113814579.png" alt="image-20250722113814579"><br>即便用户购买的是同一个自营商家的多件商品，这些商品也可能存放在<strong>全国不同的仓库</strong>。为了最高效地完成履约，系统需要按仓库，将订单拆分给不同的仓储中心进行打包发货。</td><td align="left"><strong>仓库 (Warehouse)</strong></td></tr><tr><td align="left"><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113838115.png" alt="image-20250722113838115"><br>不同的快递公司，对单个包裹的<strong>重量和体积</strong>都有上限。当用户购买的商品总重量或总体积超过限制时，就需要拆分为多个包裹，对应生成多个订单。</td><td align="left"><strong>物流 (Logistics)</strong></td></tr><tr><td align="left"><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113859980.png" alt="image-20250722113859980"><br>某些<strong>特殊品类</strong>的商品需要单独处理。比如，<code>易碎品</code>需要特殊包装，<code>超大件</code>（如轮胎）无法与普通商品合并打包，都需要独立成单。</td><td align="left"><strong>品类 (Category)</strong></td></tr><tr><td align="left"><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113920499.png" alt="image-20250722113920499"><br>这主要应用于<strong>跨境海淘</strong>业务。根据国家政策，跨境零售进口商品的单次交易限值为5000元。当用户的单笔订单超过这个限值时，系统必须将其拆分为多个订单，以符合清关和税务要求。</td><td align="left"><strong>商品价值 (Value)</strong></td></tr></tbody></table><h4 id="2-拆单的两种主流模式"><a href="#2-拆单的两种主流模式" class="headerlink" title="2. 拆单的两种主流模式"></a>2. 拆单的两种主流模式</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113943020.png" alt="image-20250722113943020"></p><p>明确了“为什么拆”，我们再来看“怎么拆”。行业内，主要有两种主流的拆单模式，它们最核心的区别，在于<strong>拆单发生的时机</strong>。</p><h5 id="父子订单模式-Parent-Child-Order-Model"><a href="#父子订单模式-Parent-Child-Order-Model" class="headerlink" title="父子订单模式 (Parent-Child Order Model)"></a><strong>父子订单模式 (Parent-Child Order Model)</strong></h5><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722114113042.png" alt="image-20250722114113042"></p><ul><li><strong>核心逻辑</strong>：<strong>先支付，后拆分</strong>。</li><li><strong>我的解读</strong>：在这种模式下，用户从下单到支付完成，始终面对的是一个统一的“<strong>父订单</strong>”。他只需要付一次总的款项。当支付成功后，我们的后端系统，才会根据拆单规则，将这个父订单，在后台默默地拆分为多个“<strong>子订单</strong>”，分别推送给不同的仓库或商家去履约。</li><li><strong>用户感知</strong>：用户在“我的订单”列表中，会看到一个父订单，点进去之后，才能看到下面包含的多个子订单，每个子订单都有独立的物流和状态。</li><li><strong>典型代表</strong>：<strong>京东</strong>。</li><li><strong>优点</strong>：用户支付体验统一、流畅。能有效避免下面要讲的“优惠券漏洞”。</li><li><strong>缺点</strong>：后端系统的处理逻辑相对更复杂。</li></ul><h5 id="半拆单模式-Semi-split-Order-Model"><a href="#半拆单模式-Semi-split-Order-Model" class="headerlink" title="半拆单模式 (Semi-split Order Model)"></a><strong>半拆单模式 (Semi-split Order Model)</strong></h5><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722132926059.png" alt="image-20250722132926059"></p><ul><li><p><strong>核心逻辑</strong>：<strong>先拆分，后支付</strong>。</p></li><li><p><strong>我的解读</strong>：在这种模式下，当用户从购物车点击“去结算”时，系统在进入“<strong>订单提交页</strong>”的那一刻，就已经<strong>完成了拆分</strong>。页面上会直接按照店铺等维度，将商品展示为<strong>多个独立的订单</strong>。用户需要对这些独立的订单，进行统一支付（或者也可以选择只支付其中一部分）。</p></li><li><p><strong>用户感知</strong>：用户在支付前，就已经明确知道自己的购物车，被分成了几笔不同的订单。</p></li><li><p><strong>典型代表</strong>：<strong>淘宝</strong>。</p></li><li><p><strong>优点</strong>：业务逻辑相对简单清晰。</p></li><li><p><strong>缺点（及我的应对）</strong>：<br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133014820.png" alt="image-20250722133014820"></p><p>这种模式存在一个著名的“<strong>薅羊毛</strong>”漏洞。比如，平台有一个“跨店满300减50”的活动。用户可以从A店选200元商品，B店选100元商品，凑成一单。在订单提交页，系统会把优惠按比例分摊到两个独立的订单上。此时，用户如果只支付A店的那个订单，就等于用不到200元的价格，享受到了满减优惠。</p><p>我作为产品经理，<strong>必须设计规则来规避这个漏洞</strong>。比如，我会定义：</p><p>“<strong>对于参与跨店满减活动的组合订单，若用户在规定时间内未完成所有相关订单的支付，则所有订单将被自动取消，优惠券也将退回。</strong>”</p></li></ul><hr><h3 id="3-5-5-购物车功能设计（信息展示、库存监控、结算等）"><a href="#3-5-5-购物车功能设计（信息展示、库存监控、结算等）" class="headerlink" title="3.5.5 购物车功能设计（信息展示、库存监控、结算等）"></a>3.5.5 购物车功能设计（信息展示、库存监控、结算等）</h3><p>在设计下单流程时，我首先要面临一个战略性的选择：<strong>我们的电商产品，到底需不需要购物车？</strong></p><p>这并不是一个理所当然的问题。购物车的设计，必须服务于我们产品的核心交易模式。</p><h4 id="1-购物车的作用与决策"><a href="#1-购物车的作用与决策" class="headerlink" title="1. 购物车的作用与决策"></a>1. 购物车的作用与决策</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133533377.png" alt="image-20250722133533377"></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133601731.png" alt="image-20250722133601731"></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133608676.png" alt="image-20250722133608676"></p><ul><li><p><strong>什么时候我“需要”购物车？</strong><br>对于像我们“大P超级电商”这样的<strong>招商模式/混合模式</strong>平台，购物车是<strong>必须品</strong>。因为用户会在不同的店铺之间“逛”，它的核心作用是：</p><ol><li><strong>凑单与比价</strong>：让用户可以把来自不同店铺的、感兴趣的商品，先放在一个地方，进行统一的比较和筛选。</li><li><strong>跨店促销</strong>：是实现“跨店满减”等复杂促销活动的<strong>技术基础</strong>。</li></ol></li><li><p><strong>什么时候我“不需要”购物车？</strong></p><ol><li><strong>C2C二手交易模式（如：闲鱼）</strong>：二手商品大多是“孤品”（库存只有1件），交易前通常需要买卖双方进行沟通议价，流程复杂。购物车这种“先暂存再统一结算”的模式，会增加无效库存的锁定，并打断沟通流程，反而降低交易效率。</li><li><strong>拼团模式（如：拼多多）</strong>：拼多多的核心是“低价爆款、冲动消费”。它希望用户看到一个商品，立刻就完成下单转化。购物车的存在，会让用户“冷静下来”、进行“反复比价”，这与它的核心商业模式是相悖的。</li></ol></li></ul><p><strong>结论</strong>：对于我们的“大P超级电商”，购物车是用户完成多商品、跨店铺购买的核心功能，我们必须精心设计。</p><h4 id="2-购物车核心功能设计"><a href="#2-购物车核心功能设计" class="headerlink" title="2. 购物车核心功能设计"></a>2. 购物车核心功能设计</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133729846.png" alt="image-20250722133729846"></p><p>我设计购物车，会围绕“<strong>进入</strong>”、“<strong>使用</strong>”、“<strong>离开</strong>”这三个场景，来规划它的核心功能。</p><ul><li><p><strong>信息展示 (Information Display)</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133750028.png" alt="image-20250722133750028"><br>这是购物车最基础，也是最重要的部分。</p><ul><li><strong>状态区分</strong>：我需要设计两种状态。<strong>未登录</strong>时，购物车应为空，并展示商品推荐，引导用户去逛；<strong>登录</strong>后，则展示用户已添加的商品。</li><li><strong>分组与排序</strong>：为了让信息清晰，我必须将商品按“<strong>店铺</strong>”进行分组。在店铺内部，商品会按照“<strong>添加时间</strong>”的倒序排列，最新添加的在最上方。</li><li><strong>营销信息</strong>：我会清晰地展示每个商品适用的优惠信息（如“满减”、“优惠券”），以及店铺整体的促销活动，刺激用户凑单。</li></ul></li><li><p><strong>库存监控 (Inventory Monitoring)</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133856218.png" alt="image-20250722133856218"><br>我必须在购物车里，就向用户提供实时的库存状态，避免他到了提交订单的最后一步，才发现商品已售罄。我会设计三种库存状态的展示：</p><ol><li><strong>有货</strong>：正常显示。</li><li><strong>库存紧张</strong>：当库存很少时（如＜5件），用红字等醒目的方式，提示用户“仅剩X件”，制造稀缺感，促进转化。</li><li><strong>无货</strong>：当商品售罄时，商品必须被置灰，数量选择器变为不可用状态，并清晰地提示“已售罄”或“无货”。</li></ol></li><li><p><strong>编辑功能 (Editing)</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133941901.png" alt="image-20250722133941901"><br>我必须赋予用户对购物车的完全掌控权。这包括：</p><ul><li><strong>修改商品数量</strong>：提供简单易用的加、减数量选择器。</li><li><strong>修改商品规格</strong>：允许用户直接在购物车，切换商品的SKU（如颜色、尺码）。</li><li><strong>删除商品</strong>：提供删除单个商品，以及在“编辑”模式下，批量删除多个商品的功能。</li></ul></li><li><p><strong>结算功能 (Checkout)</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134009275.png" alt="image-20250722134009275"><br>这是购物车最终的“使命召唤”按钮。我会在页面底部，设计一个常驻的结算栏，它必须清晰地展示：</p><ul><li><strong>已勾选商品的总计金额</strong></li><li><strong>优惠减免的金额明细</strong></li><li><strong>一个色彩鲜明、吸引点击的“去结算”按钮</strong>，并标明已选商品的数量。</li></ul></li></ul><hr><h2 id="3-6-订单评价及售后"><a href="#3-6-订单评价及售后" class="headerlink" title="3.6 订单评价及售后"></a>3.6 订单评价及售后</h2><p>用户的购物旅程，在支付成功的那一刻，其实才刚刚过半。</p><p><strong>从“支付成功”到“满意使用”</strong>，这“最后一公里”的体验，我称之为<strong>购后体验</strong>，它直接决定了用户是否会成为我们的回头客。</p><p>本节，我们就来设计购后体验中，最重要的两个环节：<strong>订单评价</strong>和<strong>售后流程</strong>。</p><h3 id="3-6-1-订单评价维度（商品质量、服务态度等）"><a href="#3-6-1-订单评价维度（商品质量、服务态度等）" class="headerlink" title="3.6.1 订单评价维度（商品质量、服务态度等）"></a>3.6.1 订单评价维度（商品质量、服务态度等）</h3><p>我始终认为，<strong>订单评价</strong>，是电商平台<strong>信任体系的基石</strong>。它既是后续用户的“购买决策参考”，也是平台用来“管理商家”的重要数据来源。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134457500.png" alt="image-20250722134457500"></p><p>一个设计良好的评价体系，能极大地促进平台的健康循环。正如流程图所示，<strong>查看商品评价</strong>，是用户在“购买决策”前的关键一步，直接影响着平台的转化率。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134535203.png" alt="image-20250722134535203"></p><p>我设计评价体系，核心是定义好“<strong>评价维度</strong>”，即，我希望用户从哪些方面，来对这次交易进行评价。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134626963.png" alt="image-20250722134626963"></p><p>一个专业、全面的评价功能，至少应该包含以下两部分：</p><p><strong>1. 多维度评分</strong><br>为了得到可量化的、能用于商家考核的数据，我不会只让用户打一个“总分”，而是会将评分，拆解为几个核心的维度。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134658540.png" alt="image-20250722134658540"></p><table><thead><tr><th align="left"><strong>评价维度</strong></th><th align="left"><strong>我的设计说明</strong></th></tr></thead><tbody><tr><td align="left"><strong>商品质量</strong></td><td align="left">核心维度，直接反映了“货”的品质。</td></tr><tr><td align="left"><strong>发货速度</strong></td><td align="left">反映商家履约环节的“物流”效率。</td></tr><tr><td align="left"><strong>服务态度</strong></td><td align="left">反映商家在售前、售中、售后环节的“服务”质量。</td></tr></tbody></table><p>我会将这几个维度，都设计为“<strong>1-5星</strong>”的评分形式，这能让我非常直观地，计算出商家的综合服务评分。</p><p><strong>2. 图文评价</strong><br>除了量化的评分，我还需要提供一个让用户能自由表达、分享购物体验的“内容创作区”。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134741503.png" alt="image-20250722134741503"></p><p>这个功能的设计，我会包含：</p><ul><li><strong>文字评价</strong>：一个开放的文本输入框，让用户可以详细描述购物心得。</li><li><strong>图片/视频评价</strong>：提供图片/视频的上传功能。“有图有真相”，带图的评价，是所有评价中，对其他用户参考价值最高、最可信的。</li></ul><h3 id="3-6-2-售后流程设计（取消、退款退货、换货等）"><a href="#3-6-2-售后流程设计（取消、退款退货、换货等）" class="headerlink" title="3.6.2 售后流程设计（取消、退款退货、换货等）"></a>3.6.2 售后流程设计（取消、退款退货、换货等）</h3><p>即便我们尽了最大努力，交易过程中也难免会出现各种问题。一套<strong>清晰、合理、公正</strong>的售后流程，是我们在用户遇到问题时，挽回他们信任的最后机会。我把这个流程，也称为“<strong>逆向流程</strong>”。</p><p>我设计售后流程，最核心的原则是：<strong>在订单的不同生命周期（状态）下，为用户提供不同的、符合当前场景的售后操作</strong>。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134857703.png" alt="image-20250722134857703"></p><table><thead><tr><th align="left"><strong>订单状态</strong></th><th align="left"><strong>可执行的售后操作</strong></th><th align="left"><strong>我的设计说明</strong></th></tr></thead><tbody><tr><td align="left"><code>待支付</code></td><td align="left"><strong>取消订单</strong></td><td align="left">用户未付款，可无理由取消。</td></tr><tr><td align="left"><code>待发货</code></td><td align="left"><strong>仅退款</strong></td><td align="left">用户已付款但未发货，可直接申请退款（无需退货）。</td></tr><tr><td align="left"><code>待收货</code></td><td align="left"><strong>申请退款</strong></td><td align="left">用户可申请退款，触发包裹拦截。<strong>退款成功需待拦截成功或用户拒收。</strong> 建议收货后再发起换货。</td></tr><tr><td align="left"><code>交易成功</code></td><td align="left"><strong>申请售后</strong></td><td align="left">在售后保障期内，可申请退款退货、换货或维修。</td></tr></tbody></table><hr><h2 id="3-7-商品种草（社区化设计）"><a href="#3-7-商品种草（社区化设计）" class="headerlink" title="3.7 商品种草（社区化设计）"></a>3.7 商品种草（社区化设计）</h2><p>我们已经设计完了电商平台最核心的“<strong>交易链路</strong>”。现在，我们要开始为我们的产品，构建真正的“<strong>护城河</strong>”——<strong>商品种草</strong>，也就是<strong>社区化设计</strong>。</p><p>这部分的设计，将直接体现我们“<strong>内容驱动的潮流社区电商</strong>”的核心定位，是我们区别于传统货架式电商、吸引和留存年轻用户的关键。</p><h3 id="3-7-1-种草定义与场景（内容推荐、发布与互动）"><a href="#3-7-1-种草定义与场景（内容推荐、发布与互动）" class="headerlink" title="3.7.1 种草定义与场景（内容推荐、发布与互动）"></a>3.7.1 种草定义与场景（内容推荐、发布与互动）</h3><p>首先，我来定义一下“种草”。在我看来，它是一种<strong>基于真实体验和信任关系的内容化商品推荐行为</strong>。它包含两个方面：</p><ul><li><strong>被种草</strong>：我通过看别人的分享，发现了一款好物，并产生了购买的欲望。</li><li><strong>去种草</strong>：我因为使用了一款好物，自发地去分享我的使用心得，推荐给别人。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722154101263.png" alt="image-20250722154101263"></p><p>在我们的平台上，用户的“种草”旅程，也分为“<strong>看帖</strong>”和“<strong>发帖</strong>”这两条核心路径。基于这两条路径，我提炼出了三大核心用户场景，以及支撑这些场景的必备功能。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722154203318.png" alt="image-20250722154203318"></p><ol><li><strong>发布购物心得</strong>：这是“去种草”的场景，需要我们提供<code>发布心得</code>的功能。</li><li><strong>查看他人购物心得</strong>：这是“被种草”的场景，需要我们提供一个<code>种草社区</code>（信息流）。</li><li><strong>针对购物心得互动</strong>：这是社区活跃的保障，需要我们提供<code>点赞</code>、<code>收藏</code>、<code>分享</code>、<code>评论</code>等功能。</li></ol><h3 id="3-7-2-社区功能结构（搜索、发布、瀑布流、话题标签）"><a href="#3-7-2-社区功能结构（搜索、发布、瀑布流、话题标签）" class="headerlink" title="3.7.2 社区功能结构（搜索、发布、瀑布流、话题标签）"></a>3.7.2 社区功能结构（搜索、发布、瀑布流、话题标签）</h3><p>现在，我们来具体设计支撑上述场景的核心功能界面。</p><p><strong>1. 种草社区 (信息流)</strong></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722154335398.png" alt="image-20250722154335398"></p><p>这是用户“被种草”的核心场所，我设计的要点如下：</p><ul><li><strong>瀑布流布局</strong>：为了最大化地突出图片这种强视觉冲击力的内容，我会采用<strong>瀑布流</strong>的布局形式来呈现“种草”笔记列表。</li><li><strong>关键词搜索</strong>：在顶部，我必须提供一个强大的<strong>搜索</strong>功能，让用户可以根据关键词，精准地查找自己感兴趣的“种草”内容。</li><li><strong>分类/话题查看</strong>：提供按不同<strong>分类</strong>或<strong>话题</strong>，来筛选和浏览“种草”笔记的功能，满足用户宽泛的浏览需求。</li></ul><p><strong>2. 发布种草 (内容发布页)</strong></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722155905759.png" alt="image-20250722155905759"></p><p>这是用户“去种草”的核心工具，我设计的要点如下：</p><ul><li><strong>图片/视频上传</strong>：提供入口，让用户可以选择手机里的<strong>单张或多张图片/视频</strong>进行上传。</li><li><strong>编写心得内容</strong>：提供一个富文本编辑器，让用户可以<strong>撰写</strong>自己的使用心得和推荐理由。</li><li><strong>关联商品</strong>：<strong>这是连接“内容”与“电商”最关键的一步</strong>。我必须提供一个功能，让用户可以在发布笔记时，方便地<strong>关联</strong>到我们平台上正在售卖的<strong>具体商品</strong>。这就在“种草”和“拔草”之间，建立起了最短的转化路径。</li><li><strong>选择话题标签</strong>：允许用户为自己的笔记，选择或创建<strong>话题标签</strong>，这既能表达自己的内容核心，也便于被有相同兴趣的用户发现。</li></ul><hr><h2 id="3-8-个人中心"><a href="#3-8-个人中心" class="headerlink" title="3.8 个人中心"></a>3.8 个人中心</h2><p>当用户在我们的“商场”里完成了浏览、购买、评价等一系列行为后，他们需要一个地方，来存放他们的“战利品”（订单）、“会员卡”（个人信息）和“购物小票”（历史记录）。这个地方，就是<strong>个人中心</strong>。</p><p>在我看来，个人中心是<strong>用户在我们平台上的“数字资产”管理中心</strong>，是提升用户归属感、提供深度服务的核心枢纽。</p><h3 id="3-8-1-核心功能版块设计（我的订单、设置、推荐、快捷入口等）"><a href="#3-8-1-核心功能版块设计（我的订单、设置、推荐、快捷入口等）" class="headerlink" title="3.8.1 核心功能版块设计（我的订单、设置、推荐、快捷入口等）"></a>3.8.1 核心功能版块设计（我的订单、设置、推荐、快捷入口等）</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722160633138.png" alt="image-20250722160633138"></p><p>我设计个人中心，不会简单地把所有功能堆砌在一起。我会像整理房间一样，将功能进行<strong>逻辑分区</strong>，让用户能快速找到自己想要的东西。根据淘宝这类成熟产品的经验，我通常会将个人中心，划分为以下四大版块：</p><p><strong>1. 用户数据与资产</strong><br>这是整个页面的“门面”，是用户最核心的个人信息和资产的展示区。</p><ul><li><strong>个人信息</strong>：最顶部，清晰地展示用户的<code>头像</code>和<code>昵称</code>，并提供一个入口，可以跳转到更详细的“个人资料页”进行编辑。</li><li><strong>业务数据</strong>：将用户最关心的几个动态数据，进行可视化展示，比如<code>商品收藏</code>、<code>店铺收藏</code>、<code>浏览足迹</code>的数量。</li><li><strong>核心资产入口</strong>：提供用户最重要的“资产”的快捷入口。对于我们平台，最重要的就是<code>我的订单</code>和我们特色功能的<code>我的种草</code>。</li></ul><p><strong>2. 快捷功能入口</strong><br>这是一个灵活的、网格布局的区域，我用它来聚合一些<strong>使用频率相对较高</strong>的功能或运营活动入口。比如<code>我的优惠券</code>、<code>客服中心</code>、<code>地址管理</code>、<code>每日签到</code>等。</p><p><strong>3. 应用全局设置</strong><br>这个版块，我通常会把它放在页面的下半部分，或者收纳到一个统一的“<strong>设置</strong>”入口里。它包含的是一些低频、但必要的全局性功能，比如<code>账号与安全</code>、<code>支付设置</code>、<code>关于我们</code>，以及最重要的<code>退出登录</code>按钮。</p><p><strong>4. 个性化推荐</strong><br>个人中心是一个高度个性化的页面，因此，它也是进行<strong>精准商品推荐</strong>的绝佳场所。在页面的底部，我会设计一个“<strong>为你推荐</strong>”的模块，根据用户的历史购买、收藏和浏览记录，为他推荐可能感兴趣的商品，以创造更多的交叉销售机会。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722160650480.png" alt="image-20250722160650480"></p><p>我们为“大P超级电商”设计的这份个人中心线框图，就是上述设计思路的一个具体体现。它结构清晰、主次分明，将用户最关心的“我的订单”和“我的种草”放在了最核心的位置，确保了用户体验的便捷。</p><h2 id="3-9-本章总结"><a href="#3-9-本章总结" class="headerlink" title="3.9 本章总结"></a>3.9 本章总结</h2><p>至此，我们已经完整地设计出了一个电商产品用户端的所有核心模块。让我们最后回顾一下本章的整个设计旅程：</p><table><thead><tr><th align="left"><strong>设计模块</strong></th><th align="left"><strong>核心产出与学习要点</strong></th></tr></thead><tbody><tr><td align="left"><strong>产品形态选择</strong></td><td align="left">我们对比了<strong>App/小程序/H5/Web</strong>的优劣，并深入学习了<strong>微信小程序</strong>独特的设计规范与特殊功能。</td></tr><tr><td align="left"><strong>用户端设计思路</strong></td><td align="left">我们确立了电商的<strong>六大核心业务模块</strong>，并掌握了指导界面布局的<strong>接近法则</strong>与<strong>相似法则</strong>。</td></tr><tr><td align="left"><strong>浏览商品</strong></td><td align="left">我们设计了<strong>首页、商品分类、商品列表页</strong>和<strong>商品详情页</strong>，构建了用户“逛”和“选”的核心路径。</td></tr><tr><td align="left"><strong>下单支付</strong></td><td align="left">我们设计了<strong>订单提交页、支付页</strong>和<strong>购物车</strong>，并深入探讨了<strong>库存管理、拆单逻辑</strong>等复杂的后端策略。</td></tr><tr><td align="left"><strong>订单评价及售后</strong></td><td align="left">我们设计了购后体验的<strong>评价体系</strong>和基于订单状态的<strong>售后流程</strong>，以建立用户信任。</td></tr><tr><td align="left"><strong>商品种草</strong></td><td align="left">我们设计了产品的差异化模块——<strong>社区</strong>，通过<strong>信息流</strong>和<strong>发布</strong>功能，打通内容与电商。</td></tr><tr><td align="left"><strong>个人中心</strong></td><td align="left">我们为用户设计了一个清晰、有序的“家”，聚合了<strong>用户资产、快捷入口</strong>和<strong>全局设置</strong>。</td></tr></tbody></table><p>通过这一章的实战，我们已经将电商用户端的理论，全部转化为了具体、可视的产品设计方案。我们已经拥有了一份足以交付给UI和开发团队的、完整的“建筑蓝图”。</p><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/17683.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/17683.html&quot;)">产品经理进阶（三）：第三章：电商用户端产品设计</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/17683.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/17683.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/8272.html"><img class="prev-cover" src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理进阶（二）：第二章：电商项目立项</div></div></a></div><div class="next-post pull-right"><a href="/posts/26490.html"><img class="next-cover" src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理进阶（四）：第四章：电商后台产品设计</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理进阶（三）：第三章：电商用户端产品设计",date:"2025-07-24 18:13:45",updated:"2025-07-25 11:05:48",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第三章：电商用户端产品设计\n\n欢迎来到第三章。在这一章，我们将真正地以“建筑师”的身份，从地基开始，一砖一瓦地搭建起我们电商产品的“用户端大楼”。\n\n我们将系统性地学习，从用户首次进入产品的“大门”（产品形态），到在“商场”中闲逛（浏览商品）、挑选结账（下单支付）、寻求服务（售后），再到参与“广场”讨论（商品种草）和回到“私人房间”（个人中心）的全过程设计。\n\n## 3.1 学习目标\n\n在本节中，我的核心目标是，带大家掌握电商用户端设计的两大基石：**产品形态选择**和**核心设计思路**。\n\n我们将学会对比不同产品形态（App/小程序等）的优劣，并能以微信小程序为例，掌握其独特的设计规范。\n\n\n\n## 3.2 用户端产品形态选择\n\n![image-20250721220851552](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721220851552.png)\n\n在项目正式启动后，我面临的第一个重大的技术和战略决策就是：**我们主要应该为用户，打造一个什么样的“场”？**\n\n是开发一个功能强大的独立**App**？还是一个便于在微信里传播的**小程序**？亦或是一个灵活轻便的**H5**网页？\n\n正如我们看到的，像淘宝、京东这样成熟的平台，通常是“全都要”，在每一种形态上都有布局。但对于启动期的我们，必须做出取舍，选择最适合我们当前战略目标的形态。\n\n### 3.2.1 常见产品形态对比 (App / 小程序 / H5 / Web端)\n\n为了做出正确的决策，我通常会用下面这张对比表格，来系统地分析不同形态的优劣。\n\n![image-20250721220811815](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721220811815.png)\n\n| 产品形态 | 是否需安装 | 开发投入 | 用户体验 | 我的选择考量（适用场景） |\n| :--- | :--- | :--- | :--- | :--- |\n| **App** | **是** | **高** (需安卓/iOS分别开发) | **完善** | 当我的产品功能极其复杂、需要调用手机底层能力（如GPS、蓝牙）、且追求极致性能和体验时，我会选择App。它的推广和获客成本最高。 |\n| **微信小程序**| **否** | **中** (前端开发) | **好** | 当我希望**借助微信的社交生态进行裂变传播和获客**时，小程序是我的不二之选。它体验好、开发快，特别适合电商、本地生活等需要社交分享的场景。 |\n| **H5** | **否** | **低** (前端开发) | **中等** | 当我需要最大化的**灵活性和传播范围**时，我会选择H5。它不受任何平台限制，一个链接就可以走天下，特别适合制作营销活动页、内容文章页。 |\n| **Web端**| **否** | **中** (前端开发) | **一般** | 当我的核心场景是**需要用户在电脑上进行复杂操作**时，我会选择Web端。比如，我们为商家设计的后台管理系统，就必须是Web端。 |\n\n对于我们的“大P超级电商”项目，结合我们拥有海量C端用户的背景，**优先开发一个微信小程序**来承接和转化这部分流量，是一个非常明智的启动策略。\n\n### 3.2.2 设计规范：以微信小程序为例\n\n既然我们选择了小程序，那我就必须深入理解它的“游戏规则”。虽然小程序的设计在整体上与App非常类似，但因为它“寄生”于微信这个超级生态之上，所以也带来了一些独特的设计规范和特殊功能。\n\n#### 1. 页面结构\n\n* **官方小程序胶囊**：我必须时刻牢记，小程序页面的右上角，有一个官方的、包含“关闭/更多”等功能的“胶囊”按钮，这个区域是**不可设计的**，我的页面布局必须为它留出空间。\n\n![image-20250721221058059](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221058059.png)\n\n* **顶部导航栏**：小程序的顶部导航栏，由微信官方统一样式，我们只能定义中间的`标题区`文字和左侧`导航区`的返回逻辑。\n\n![image-20250721221137650](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221137650.png)\n\n* **标签栏 (Tab Bar)**：小程序底部的标签栏，有严格的数量限制：**最少2个，最多5个**。\n\n![image-20250721221430247](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221430247.png)\n\n* **尺寸规范**：在绘制原型时，我依然会采用和App一致的**375x667px**作为基准画板，并遵循`状态栏(22px)`、`导航栏(44px)`、`标签栏(49px)`的标准高度。\n\n#### 2. 特殊功能与限制\n\n小程序最大的魅力，在于它能调用微信生态的独有能力。假设我们需要实现“获取手机号”和“推送消息”，在小程序中的实现方式就与App完全不同。\n\n\n\n![image-20250721221318459](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221318459.png)\n\n* **获取微信手机号**：在App中，我需要用户手动输入手机号，再通过短信验证，流程繁琐。而在小程序里，我可以直接放置一个“**微信用户一键授权**”的按钮。用户点击后，会拉起微信的官方授权弹窗，用户只需点击“允许”，我们就能安全地获取到他绑定在微信上的手机号，**极大提升了注册/登录的转化率**。\n\n![image-20250721221356124](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721221356124.png)\n\n* **订阅消息**：在App中，只要用户允许，我可以相对自由地向他推送消息。但在小程序中，则严格得多。我**不能主动向用户推送营销消息**。用户必须**主动“订阅”**某一个服务通知（比如“发货通知”、“降价提醒”），我才能向他发送一条对应的服务消息。这是一种对用户打扰更小的“**一次性授权**”模式，我在设计运营功能时必须充分考虑这个限制。\n\n\n---\n## 3.3 用户端产品设计思路\n\n在我们选定了产品形态（如：微信小程序）之后，我不会立刻开始绘制具体的页面。我会先退一步，从更高维度，建立起整个用户端产品的“**设计思路和骨架**”。\n\n虽然我们之前学习过内容产品的设计，但电商用户端，有其自身独特的业务核心和功能侧重。在这一节，我将带大家明确我们电商产品的核心业务，并掌握指导我们进行界面布局的两大基本设计原则。\n\n### 3.3.1 核心业务与功能模块\n\n![image-20250722091703261](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722091703261.png)\n\n我的第一步，是**将用户的完整购物旅程，拆解为几个核心的业务模块**。这能帮助我确保，我的产品设计，覆盖了用户从“认知”到“购后”的每一个关键环节。\n\n对于一个典型的电商产品，我将核心业务拆解为以下六大模块：\n1.  **注册登录**：这是用户“获取身份”的入口。\n2.  **浏览商品**：这是用户“逛商场”的核心环节，包括有目的的搜索和无目的的闲逛。\n3.  **下单支付**：这是电商的“收银台”，是完成交易的核心闭环。\n4.  **订单与售后**：这是用户购后的“服务中心”，负责履约和处理问题。\n5.  **商品种草**：这是我们产品特色的“内容社区”，负责吸引用户、建立信任。\n6.  **个人中心**：这是用户在我们平台的“家”，负责汇总个人数据和产品全局设置。\n\n![image-20250722091746923](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722091746923.png)\n\n这六大业务模块，就构成了我们用户端的“**核心功能架构**”。图中的“用户端核心架构”就是一个很好的示例，它将具体的、零散的功能点（如：搜索栏、金刚区），清晰地归类到了它所属的业务模块之下（如：首页）。这个架构，就是我们后续进行详细页面设计的“总纲”。\n\n### 3.3.2 核心设计原则\n\n有了功能架构的“骨架”，我接下来需要思考，如何为这个骨架“填充血肉”？也就是说，在具体的页面上，我应该如何组织和排布那些繁多的功能和信息，才能让用户觉得界面清晰、易于理解？\n\n在这里，我会借助格式塔心理学（Gestalt Psychology）中，两个最基础、也最强大的视觉设计原则。\n\n#### 1. 接近法则 (Law of Proximity)\n\n![image-20250722091947975](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722091947975.png)\n\n* **法则定义**：我们的大脑，会本能地，将物理空间上**彼此靠近**的元素，视为一个**整体**。\n* **我的设计应用**：在界面设计中，“**间距**”是我最有力的设计工具之一。\n    * 我会把**相关**的元素（比如，一张商品图和它的商品标题）紧紧地放在一起，让它们在视觉上自然地成为一组。\n    * 我会用**留白**，来拉开**不相关**的元素或组之间的距离，形成清晰的视觉区块。\n\n#### 2. 相似法则 (Law of Similarity)\n\n![image-20250722092013020](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092013201.png)\n\n* **法则定义**：我们的大脑，会本能地，将那些在**视觉特征（如形状、颜色、大小）上相似**的元素，视为**同类**。\n\n* **我的设计应用**：在界面设计中，“**一致性**”是建立用户认知的关键。\n  \n    * 我会确保所有**功能相同或相近**的元素，在视觉上保持**相似**。比如，所有“可点击”的按钮，都用同一种颜色和形状；所有“可输入”的文本框，都用同一种样式。\n    \n    ![image-20250722092053425](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092053425.png)\n    \n    * 正如“常用功能”案例所示，尽管每一个图标的图案都不同，但它们统一的尺寸、颜色和圆角风格，都在强烈地向用户暗示：“我们都属于一类，我们都是可以点击的功能入口”。\n\n**总结**：在后续的页面设计中，我将综合运用“**接近法则**”来**组织页面布局、划分区块**，并用“**相似法则**”来**统一控件样式、建立操作认知**。这是让我们的设计变得“专业”和“易用”的秘诀。\n\n\n\n-----\n\n## 3.4 浏览商品\n\n[此处放置“首页思考”的图片 (`image_2d6800.png`)]\n\n我们电商产品的“商场”已经建好，现在，当用户走进这扇“大门”时，他们首先看到的，就是我们的“**商场大堂**”——**首页**。\n\n首页，是用户对我们产品形成第一印象、也是我们引导用户走向各个“专柜”的最核心的枢纽。我设计首页时，始终围绕着一个核心问题：**它需要同时满足谁的需求？达成什么样的目的？**\n\n### 3.4.1 首页设计\n\n#### 1\\. 首页的核心目的\n\n我设计首页，必须同时扮演好“**服务员**”（满足用户需求）和“**商场经理**”（达成公司目标）的双重角色。\n\n  * **从用户角度：满足多样化的“逛街”心态**\n    ![image-20250722092825580](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092825580.png)\n\n    我需要为进入首页、心态各不相同的用户，都提供最高效的解决方案。\n\n| 用户心态 | 我的设计方案 |\n| :--- | :--- |\n| **“我明确知道要买什么”** | 在页面最顶部，提供一个**高效、精准的搜索栏**。 |\n| **“我知道大概要买什么品类”** | 提供一套**清晰、易懂的商品分类入口**。 |\n| **“我就是想随便逛逛，看有啥好东西”** | 提供一个无限下拉的、引人入胜的**个性化商品推荐列表**。 |\n| **“我想看看有啥便宜可以占”** | 提供突出、有吸引力的**促销/活动专区**，如秒杀、百亿补贴等。 |\n\n  * **从公司角度：实现平台的商业目标**\n    ![image-20250722092907876](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092907876.png)\n\n    同时，我也需要利用首页这个“寸土寸金”的场地，来达成我们的商业目的。\n\n| 公司目标 | 我的设计方案 |\n| :--- | :--- |\n| **帮助商家促销引流** | 在首页的核心位置，为付费的商家提供**Banner广告位**和**活动入口**。 |\n| **帮助自营店铺促销引流** | （若有自营业务）为自营的重点商品或活动，提供专属的曝光区域。 |\n| **展现平台调性** | 整个首页的**视觉风格（UI）、文案、推荐内容**，都必须严格符合我们“内容驱动的潮流社区电商”的定位。 |\n\n#### 2\\. 常见首页模块解析\n\n![image-20250722092959787](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722092959787.png)\n\n为了同时满足上述的用户和公司需求，经过多年的演化，电商首页已经形成了一套相对成熟的模块化布局。我会通过分析竞品，来借鉴和思考我们自己的设计。\n\n| **核心模块** | **我的设计解读与应用** |\n| :--- | :--- |\n| **搜索栏** | **雷打不动的第一模块**，必须始终固定在页面最顶部，服务于目的性最强的用户。 |\n| **金刚区** | ![image-20250722093051105](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722093051105.png)<br> 这是指搜索栏下方的一组**图标网格导航**。我把它看作是**核心业务的“一级入口”**。我会把我们最重要的商品分类（如“潮流服饰”、“数码新品”）和特色业务（如“达人直播”、“好物种草”）放在这里。 |\n| **Banner / 促销区** | 这是首页最**黄金的广告和活动展示位**。我会用它来推广平台的重大活动，或将其作为重要的广告收入来源。 |\n| **商品推荐列表** | 这是首页占据面积最大、也是留住“闲逛”用户的**核心内容区**。我会采用“**瀑布流**”的形式，通过个性化推荐算法，为每个用户呈现一个独一无二的、无限延伸的商品列表。 |\n\n#### 3\\. 我们“大P超级电商”的首页设计思路\n\n最后，结合对竞品的分析和我们自身的定位，我为我们“大P超级电商”的首页，确立了以下设计思路：\n\n1.  **UI风格**：界面要简洁、留白充分，营造出“呼吸感”，整体视觉风格要年轻、时尚，符合“90后”的审美。\n2.  **金刚区设计**：必须体现我们“内容+电商”的特色。除了`服装`、`数码`等品类入口，必须包含`直播`、`种草`等内容社区的入口。\n\n我们在设计首页时一定会遇到的经典决策：“金刚区”到底应该放几个图标？上面的文字应该怎么写？\n\n| 图标 (示意) | 文字标签     | 我的设计思路                                                 |\n| ----------- | ------------ | ------------------------------------------------------------ |\n| 📱           | **手机数码** | “90后”核心关注的高价值品类，属于**品类入口**。               |\n| 👚           | **潮流服饰** | 贴合我们“潮流”的平台调性，属于**品类入口**。                 |\n| 💄           | **美妆个护** | 年轻用户，特别是女性用户的高频消费品类，属于**品类入口**。   |\n| 📺           | **达人直播** | 我们的**核心差异化**业务，必须给予最强的曝光，属于**功能入口**。 |\n| 💸           | **百亿补贴** | 电商平台“拉新促活”的标配，用明确的利益点吸引用户，属于**活动入口**。 |\n| 🧾           | **领券中心** | 培养用户“先领券再购物”的习惯，提升转化率，属于**功能入口**。 |\n| 📦           | **我的订单** | 用户最高频使用的查询功能之一，提供一个快捷入口，属于**功能入口**。 |\n| ➡️           | **全部分类** | “渐进式呈现”原则的应用，收纳所有其他品类。                   |\n\n3.**推荐算法**：商品推荐列表的算法，除了考虑用户的浏览和购买行为，还必须**高度重视用户的社交和内容偏好**。比如，优先推荐“用户关注的KOL正在推荐的商品”\n\n最后我们产出的商品低保真原型原型，大致是这样的：\n\n![img](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/c2f3be4f-3f73-4c54-a40b-d41760dc006b.png)\n\n\n---\n### 3.4.2 商品分类设计（无分类、一级、多级）\n\n在设计好首页之后，我们需要为那些有大概购物方向的用户，提供一套清晰的“货架导引”系统，这个系统就是**商品分类**。它的核心目的，是满足用户高效缩小寻找范围的需求。\n\n我给商品类目的定义是：按照商品的用途、特征等维度，并且根据一定的管理目的，把相似的商品归为一类的行为。并且在类别当中又会存在细分的类型。\n\n我设计分类体系的复杂度，完全取决于我们平台商品的数量和丰富度。我通常会根据平台的体量，考虑三种不同的分类形式。\n\n![image-20250722101139313](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101139313.png)\n\n这三种形式，分别适用于不同的业务阶段和规模，我们可以通过上面的案例直观地感受它们的差异。对于我们“大P超级电商”这样的综合性平台，一个清晰的“多级分类”体系是必不可少的设计。\n\n### 3.4.3 商品列表与详情页设计（图文、参数、评价、推荐）\n\n当用户通过首页、搜索或分类，最终都会来到两个核心的页面：**商品列表页（PLP）**和**商品详情页（PDP）**。\n\n我设计这两个页面时，脑海里始终装着三类典型用户：目的明确的“小风”、犹豫不决的“中风”、以及纯粹闲逛的“大风”。\n\n![image-20250722101836348](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101836348.png)\n\n**1. 商品列表页 (Product List Page - PLP)**\n\n商品列表页是我们商场的“**货架**”。它的核心设计目标，是让用户能**高效地筛选和对比**。虽然列表的内容来源可能不同（如搜索结果、推荐、分类），但其页面结构和设计要点是共通的。\n\n![image-20250722101931329](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101931329.png)\n\n一个优秀的商品列表页，必须包含清晰的**商品卡片**（展示图、文、价等核心信息），以及强大的**筛选与排序**功能，来帮助用户快速从海量商品中，找到自己心仪的目标。\n\n![image-20250722105209159](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722105209159.png)\n\n\n\n**2. 商品详情页 (Product Detail Page - PDP)**\n\n商品详情页是我们商场的“**金牌销售员**”，它的核心设计目标是**打消用户的所有疑虑，促成最终的购买**。我通常会将页面上半部分，用来满足理性用户的决策需求。\n\n![image-20250722101959967](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722101959967.png)\n\n页面的首屏要点，在于简明扼要地表现出产品的核心信息，让用户直接判断出“这个产品是什么”。我会通过**商品展示区**（高清图/视频）、**商品属性区**（规格参数）和**用户评价区**（社群证明），来分别满足用户对“颜值”、“内涵”和“口碑”的确认需求。\n\n页面的下半部分，我则用来服务那些还在犹豫、或者纯粹闲逛的用户，通过更丰富的内容，对他们进行深度“种草”。\n\n![image-20250722102009141](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722102009141.png)\n\n这部分通常包括图文并茂的**详情介绍**，用来吸引用户；以及**问答模块**，用来对评价功能进行进一步强化，打消用户的购买疑虑。\n\n在详情页的底部，我一定会设计一个“**智能推荐**”模块。它的核心目的，是在用户对当前商品不满意、准备离开时，为他提供更多相关的选择，**形成一个流量的闭环**，增加用户在我们平台留存和成交的机会。\n\n![image-20250722102020748](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722102020748.png)\n\n\n\n\n---\n## 3.5 下单支付\n\n在用户完成了“逛”和“选”，将心仪的商品加入购物车之后，我们就进入了整个电商流程的“**核心交易环节**”。\n\n我把这个环节的设计，看作是引导用户走过一条“**信任与效率的走廊**”。这条走廊上的任何一个障碍、一丝疑虑，都可能导致用户在最后关头放弃购买。因此，我的设计目标，必须是**极致的顺滑、清晰与安全**。\n\n### 3.5.1 下单流程与页面设计（提交页、支付页）\n\n我通常会将整个下单支付流程，拆解为两个核心的页面来进行设计：**订单提交页**和**支付页（收银台）**。\n\n#### 1. 订单提交页 (Order Submission Page)\n\n当用户在商品详情页点击“立即购买”，或在购物车点击“去结算”后，并不会直接进入付款环节，而是会先来到**订单提交页**。\n\n![image-20250722110142381](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110142381.png)\n\n这个页面的核心作用，我把它定义为用户的“**最后一次确认**”。在用户真正掏钱之前，我必须为他提供一个清晰的、所有交易信息的汇总页面，让他进行最后的检查和确认。\n\n![image-20250722110204985](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110204985.png)\n\n我设计的订单提交页，必须让用户能够清晰地完成三件事：\n* **确认商品**：清晰地罗列出本次将要购买的所有商品信息（名称、SKU、数量、价格）。\n* **确认地址**：提供默认收货地址，并允许用户方便地选择或新增其他地址。\n* **确认价格**：清晰地展示商品总额、运费、优惠券抵扣、最终实付金额等所有价格明细。\n\n\n\n#### 2. 支付页/收银台 (Payment Page / Cashier)\n\n![image-20250722110039262](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110039262.png)\n\n当用户在订单提交页，点击“提交订单”后，他才真正进入了“**支付页**”，我常称之为“**收银台**”。\n\n![image-20250722105741343](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722105741343.png)\n\n这个页面的设计，我追求的是**极致的简洁和安全感**。它的核心作用只有三个：\n1.  **确认实付金额**：醒目地展示最终需要支付的金额。\n2.  **选择支付方式**：提供用户选择支付渠道（如微信、支付宝）的入口。\n3.  **完成支付**：一个清晰、唯一的“确认支付”按钮。\n\n**我的拓展设计（支付异常处理）**：\n![image-20250722110255711](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110255711.png)\n\n设计支付页时，我必须考虑一个最常见的异常场景：**用户进入了收银台，但因为种种原因，没有完成支付就退出了**。\n一个糟糕的设计，可能会让用户之前提交的订单直接消失。而一个优秀的设计，正如案例所示，应该**将这份订单，自动保存为一张“待支付”的订单**。用户可以在“我的订单”中随时找到它，并重新发起支付。这个小小的设计，能为我们挽回大量可能流失的销售额。\n\n#### 3. 支付成功后的流程\n\n![image-20250722110528929](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110528929.png)\n\n当用户成功支付后，这笔“交易”在后台就正式生成了，并进入了它的生命周期。我会用一组“**订单状态**”，来清晰地标记它在流程中所处的节点。支付成功，就是订单状态从“**待支付**”流转到“**待发货**”的触发器。\n\n![image-20250722110510755](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110510755.png)\n\n此时，用户可以在“个人中心”的“**我的订单**”列表中，看到这笔订单，并查看到它“待发货”的状态。当商家发货后，用户最关心的“**物流信息**”就会在这里出现。\n\n![image-20250722110805038](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110805038.png)\n\n>`思考：“物流信息是从哪来的🤔”`\n\n![image-20250722110821135](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110821135.png)\n\n![image-20250722110923521](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110923521.png)\n\n它并不是由我们的商家手动更新的。在我设计的后台，商家发货时，只需要选择**快递公司**并输入**快递单号**。随后，我们的**后端服务器**，就会通过API接口，**定时地向第三方快递查询平台（如“申通”）发起查询请求**，获取最新的物流轨迹，然后将这些信息，展示在用户端的订单详情页上。\n\n![image-20250722111007040](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111007040.png)\n\n我在撰写PRD时，必须将这个技术方案的逻辑，清晰地描述出来。\n\n\n\n\n---\n### 3.5.2 支付方式（微信、支付宝、银联、聚合支付）\n\n在设计好“收银台”页面后，我需要做的最重要的决策，就是为这个收银台，配备哪些“**收款设备**”，也就是我们常说的**支付方式**。\n\n在今天的中国市场，只提供一种支付方式是远远不够的。为了最大化地提升支付成功率，我至少需要为用户提供微信支付和支付宝这两种主流选择。\n\n#### 1. 微信支付 & 支付宝 & 银联（独立渠道对接）\n\n![image-20250722111349078](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111349078.png)\n\n\n\n![image-20250722111359741](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111359741.png)\n\n![image-20250722111408567](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111408567.png)\n\n理论上，我们可以分别独立地去对接每一个支付渠道。\n\n**我需要告诉开发的关键点（以微信支付为例）：**\n要接入微信支付，不是一个纯粹的技术工作，它需要产品、运营和技术共同协作。我需要了解并推动以下几个步骤：\n\n1.  **商户号申请（运营/商务负责）**：首先，我们需要由公司的运营或商务同事，前往“**微信支付商户平台**”，提交我们公司的营业执照等资质，申请一个“商户号（mch_id）”。这个过程，支付宝和银联也完全一样，都需要我们先拥有一个官方认证的“商家身份”。\n2.  **获取开发凭证（研发负责人）**：当我们的商户号被批准后，技术负责人需要登录这个商户平台，去获取进行技术开发所必需的“**身份凭证**”。这通常包括`AppID`、`API证书`、`API密钥`等。我把它们理解为，我们公司服务器与微信支付服务器之间，进行加密通信的“账号和密码”。\n3.  **技术对接（研发负责）**：拿到凭证后，研发同学才会真正开始写代码。\n    * **后端开发**：需要按照微信/支付宝的官方开发文档，开发服务端接口。这些接口主要负责创建“预支付订单”、接收支付成功或失败的“异步通知”等。\n    * **前端开发（App）**：需要集成微信/支付宝的官方SDK（软件开发工具包）。这个SDK的主要作用，是在我们的App里，能够“拉起”用户手机上已经安装的微信或支付宝App，来进行最终的密码/指纹输入。\n\n#### 2. 聚合支付（我的推荐方案）\n\n![image-20250722111626234](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111626234.png)\n\n在我们理解了独立对接的流程后，两个核心痛点就浮现出来了：\n* **接入成本高**：我要支持微信、支付宝、银联三种支付，我的研发团队就需要把上面的流程，**重复做三遍**。这需要耗费巨大的研发资源。\n* **财务管理难**：每天，我的财务同事，需要分别登录三个不同的商户后台，去下载三份不同的对账单，再进行手动的汇总和核对，极其繁琐且容易出错。\n\n![image-20250722111642616](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111642616.png)\n\n为了解决这两个痛点，一个更聪明的、也是我强烈推荐的方案，就是使用“**聚合支付**”。\n\n![image-20250722111748908](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722111748908.png)\n\n**聚合支付**服务商（如 **Ping++、Adapay**），就是支付领域的“**万能转换插头**”。它已经提前帮我们，把市面上所有的主流支付渠道（微信、支付宝、银联、各类银行卡等），都预先集成好了。\n\n**我需要告诉开发的关键点（技术视角）：**\n* **一次对接，全部拥有**：我们不再需要去对接微信、支付宝等多个上游渠道。我的研发团队，只需要按照聚合支付服务商提供的一份开发文档，**进行一次技术对接**即可。\n* **统一的API和SDK**：聚合支付会为我们提供**一套统一的API和SDK**。当用户在我们的App里选择用微信支付时，我们的App调用的，是聚合支付的SDK；我们的服务器，也只请求聚合支付的API。后续由聚合支付的服务，来和微信的服务器进行通信。\n* **统一的后台和对账单**：我的财务同事，只需要登录聚合支付这**一个后台**，就可以看到所有渠道的交易流水，并下载一份统一的对-账单。\n\n**我的决策权衡：**\n使用聚合支付的唯一“缺点”，是它会在每个渠道的原有费率基础上，再收取一点点的服务费。但考虑到它能为我们**节省巨大的研发成本和财务管理成本**，对于绝大多数公司（特别是初创和中型公司）而言，这笔服务费，都是一笔**极具性价比**的投资。\n\n>`聚合支付是不是代表不用哪些营业证书之类的，直接通过付钱就能接入了🤔`\n>\n>答案是：**不是的，聚合支付并不能免除您提供公司资质（如营业执照）的义务。**\n>\n>您可以把聚合支付服务商，看作是一个“**超级代办员**”或“**技术外包服务商**”，而不是一个“**资质豁免机构**”。他们的核心价值在于**简化技术对接和财务管理**，而不是绕过金融监管。\n>\n>我为您梳理一下实际的流程：\n>\n>1. **您与聚合支付签约**：您首先需要选择一家聚合支付服务商（如Ping++），并在他们的平台上注册账户。\n>2. **您向聚合支付提交资质**：在注册过程中，您**仍然需要**向聚合支付服务商，提交您自己公司的全套有效资质，包括但不限于：\n>\t- **营业执照**\n>\t- **法人身份证信息**\n>\t- **对公银行账户**\n>\t- **网站/App的ICP备案信息**（如果适用）\n>3. **聚合支付为您代办申请**：聚合支付服务商在收到您的资质后，会作为您的“代办员”，拿着您的这些材料，去分别向微信支付、支付宝、银联等官方渠道，为您**集中申请**开通各个支付渠道的商户权限。\n>4. **最终结果**：审批通过后，您最终获得的，依然是**您自己公司名下的、在微信和支付宝备案的合法商户号**。聚合支付只是为您提供了一个统一的技术接口和管理后台来操作它们。\n\n| **支付方案** | **我的解读** | **优点** | **缺点** |\n| :--- | :--- | :--- | :--- |\n| **逐个渠道对接** | 我们分别与微信、支付宝等签约并进行技术开发。 | 费率可能略低，资金直接到账。 | 开发成本极高，财务对账繁琐。 |\n| **使用聚合支付** | 我们只与一家聚合支付服务商签约和开发。 | **开发成本极低（只需一次对接）**，财务对账简单。 | 费率略高，资金需要经过聚合服务商中转。 |\n\n\n\n\n\n---\n### 3.5.3 库存管理与问题应对\n\n我们都可能有过这样的经历：在一个平台下单后，不急着付款，过了一会儿想起来去支付，发现订单依然有效；\n\n而在另一个平台，同样的操作，回来支付时却被告知“商品已售罄”。\n\n**这是为什么呢？**\n这背后，就反映了不同电商平台，对于“**库存扣减**”这个核心问题，采取了两种截然不同的产品策略。\n\n#### 1. 库存扣减方式（拍下减 vs 付款减）\n\n在我设计交易系统时，我必须与我的技术和业务负责人，共同做出一个关键决策：**我们的库存，到底应该在哪一个节点扣减？** 这个决策，没有绝对的好坏，只有不同选择下的利弊权衡。\n\n| 扣减方式 | 核心逻辑 | 用户体验 | 主要风险 | 我的选择考量 |\n| :--- | :--- | :--- | :--- | :--- |\n| **拍下减** | 当用户点击“**提交订单**”的瞬间，无论是否付款，系统都会立即为他预留这份库存。 | **好**。用户会感觉“只要我下单了，这个货就是我的了”，体验非常安心。 | **恶拍** | 我通常会在普通商品的销售中，采用此方式，因为它能提供最佳的用户体验。 |\n| **付款减** | 只有当用户**成功完成支付**的瞬间，系统才会去扣减实际的物理库存。 | **一般**。用户下单后，可能会因为犹豫了几分钟，回来支付时发现商品已被别人买走，导致体验不佳和用户流失。 | **超卖** | 我通常只在库存极少、瞬时流量极大的“秒杀”等营销活动中，才会谨慎采用此方式。 |\n\n作为产品经理，我的工作，就是**选择一种方式，并设计一套完整的机制，来最大化地规避它的潜在风险**。\n\n#### 2. 问题与应对（恶意拍单、超卖）\n\n##### **应对“恶拍”**\n\n![image-20250722112931696](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722112931696.png)\n\n如果我选择了“**拍下减库存**”，那我的头号敌人，就是“**恶拍**”——即，竞争对手或黄牛，恶意地大量下单但不支付，以此来锁死我的库存，让真实用户无法购买。\n\n为了应对它，我必须建立一套“**组合防御**”体系：\n1.  **减少库存保留时间**：这是我最核心的武器。我会设计一个“**订单自动取消**”的规则。比如，**下单后15分钟内未支付**，系统将自动取消这笔订单，并将预留的库存，**重新释放**回公共库存池中，供其他用户购买。\n2.  **限购**：对于一些热门或促销商品，我会在产品层面，增加“**限购**”规则。比如，规定“**单个ID限购1件**”，这能有效防止单一恶意用户锁死大量库存。\n3.  **安全策略**：我还会和风控团队合作，建立监控机制。当发现某个用户ID，在短时间内，有大量“下单后又取消”的异常行为时，系统可以暂时限制他的下单权限。\n\n##### **应对“超卖”**\n\n![image-20250722113011849](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113011849.png)\n\n如果我选择了“**付款减库存**”，那我最大的噩梦，就是“**超卖**”——即，我们实际卖出的商品数量，超过了我们的真实库存。这会引发严重的客诉，极大地损害平台信誉。\n\n为了应对它，我同样需要一套“**组合防御**”体系：\n1.  **技术角度解决**：这主要依赖于研发团队。我会要求我的技术负责人，必须在技术层面，通过**数据库锁**或**分布式队列**等技术，来处理高并发场景下的库存扣减请求，确保对最后一件库存的扣减操作，是“**原子性**”的（即，在同一瞬间，只能有一个请求能成功）。\n2.  **提示用户**：在产品体验层面，为了管理用户预期，当某个商品的库存数量很少时（比如，少于10件），我会在商品详情页和购物车中，明确地展示“**库存紧张**”或“**仅剩X件**”的提示文案。\n3.  **设置安全库存**：这是我最常用的一个运营策略。如果一个商品的物理库存有1000件，我会在电商后台的“可售卖库存”中，只填写**950**件。那剩下的50件，就成了我的“**安全库存**”。它就像一个缓冲垫，既能消化掉极端情况下，因技术原因产生的少量超卖，也能用来应对用户“退货换货”的需求。\n\n\n\n\n\n-----\n\n### 3.5.4 拆单逻辑（父子订单、半拆单）\n\n当用户在我们的购物车里，同时选中了来自不同商家、或者满足某些特殊条件的多个商品，然后点击“去结算”时，一个复杂的问题就摆在了我面前：\n\n**后台应该如何处理这张“大单”？是把它当作一个订单，还是多个订单？**\n\n![image-20250722110016603](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722110016603.png)\n\n“如果从购物车（多个店铺多个商品）进入结算，需要考虑什么？” 在这种情况下，我的订单提交页，必须进行“**拆单**”展示。我会**按照不同的店铺，将商品进行分组**。每个店铺的商品，会形成一个独立的“包裹”，分别计算运费和优惠，最终汇总成一个总的支付金额。这种清晰的结构，是解决多商家同时结算场景的最佳实践。\n\n#### 1\\. 为什么要拆单？\n\n我设计拆单逻辑，主要是为了应对以下五种常见的业务场景：\n\n| **我的设计考量** | **拆单因素** |\n| :--- | :--- |\n| ![image-20250722113550199](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113550199.png) <br> 这是最常见的拆单原因。不同商家的商品，其**货权、发货地、财务结算主体**都不同，因此必须拆分为独立的订单，分别进行处理。 | **店铺 (Store)** |\n| ![image-20250722113814579](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113814579.png)<br> 即便用户购买的是同一个自营商家的多件商品，这些商品也可能存放在**全国不同的仓库**。为了最高效地完成履约，系统需要按仓库，将订单拆分给不同的仓储中心进行打包发货。 | **仓库 (Warehouse)** |\n| ![image-20250722113838115](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113838115.png) <br> 不同的快递公司，对单个包裹的**重量和体积**都有上限。当用户购买的商品总重量或总体积超过限制时，就需要拆分为多个包裹，对应生成多个订单。 | **物流 (Logistics)** |\n| ![image-20250722113859980](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113859980.png)<br> 某些**特殊品类**的商品需要单独处理。比如，`易碎品`需要特殊包装，`超大件`（如轮胎）无法与普通商品合并打包，都需要独立成单。 | **品类 (Category)** |\n| ![image-20250722113920499](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113920499.png) <br> 这主要应用于**跨境海淘**业务。根据国家政策，跨境零售进口商品的单次交易限值为5000元。当用户的单笔订单超过这个限值时，系统必须将其拆分为多个订单，以符合清关和税务要求。 | **商品价值 (Value)** |\n\n#### 2\\. 拆单的两种主流模式\n\n![image-20250722113943020](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722113943020.png)\n\n明确了“为什么拆”，我们再来看“怎么拆”。行业内，主要有两种主流的拆单模式，它们最核心的区别，在于**拆单发生的时机**。\n\n##### **父子订单模式 (Parent-Child Order Model)**\n\n![image-20250722114113042](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722114113042.png)\n\n  * **核心逻辑**：**先支付，后拆分**。\n  * **我的解读**：在这种模式下，用户从下单到支付完成，始终面对的是一个统一的“**父订单**”。他只需要付一次总的款项。当支付成功后，我们的后端系统，才会根据拆单规则，将这个父订单，在后台默默地拆分为多个“**子订单**”，分别推送给不同的仓库或商家去履约。\n  * **用户感知**：用户在“我的订单”列表中，会看到一个父订单，点进去之后，才能看到下面包含的多个子订单，每个子订单都有独立的物流和状态。\n  * **典型代表**：**京东**。\n  * **优点**：用户支付体验统一、流畅。能有效避免下面要讲的“优惠券漏洞”。\n  * **缺点**：后端系统的处理逻辑相对更复杂。\n\n##### **半拆单模式 (Semi-split Order Model)**\n\n![image-20250722132926059](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722132926059.png)\n\n  * **核心逻辑**：**先拆分，后支付**。\n\n  * **我的解读**：在这种模式下，当用户从购物车点击“去结算”时，系统在进入“**订单提交页**”的那一刻，就已经**完成了拆分**。页面上会直接按照店铺等维度，将商品展示为**多个独立的订单**。用户需要对这些独立的订单，进行统一支付（或者也可以选择只支付其中一部分）。\n\n  * **用户感知**：用户在支付前，就已经明确知道自己的购物车，被分成了几笔不同的订单。\n\n  * **典型代表**：**淘宝**。\n\n  * **优点**：业务逻辑相对简单清晰。\n\n  * **缺点（及我的应对）**：\n    ![image-20250722133014820](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133014820.png)\n\n    这种模式存在一个著名的“**薅羊毛**”漏洞。比如，平台有一个“跨店满300减50”的活动。用户可以从A店选200元商品，B店选100元商品，凑成一单。在订单提交页，系统会把优惠按比例分摊到两个独立的订单上。此时，用户如果只支付A店的那个订单，就等于用不到200元的价格，享受到了满减优惠。\n\n    我作为产品经理，**必须设计规则来规避这个漏洞**。比如，我会定义：\n    \n    “**对于参与跨店满减活动的组合订单，若用户在规定时间内未完成所有相关订单的支付，则所有订单将被自动取消，优惠券也将退回。**”\n\n---\n### 3.5.5 购物车功能设计（信息展示、库存监控、结算等）\n\n在设计下单流程时，我首先要面临一个战略性的选择：**我们的电商产品，到底需不需要购物车？**\n\n这并不是一个理所当然的问题。购物车的设计，必须服务于我们产品的核心交易模式。\n\n#### 1. 购物车的作用与决策\n\n![image-20250722133533377](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133533377.png)\n\n![image-20250722133601731](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133601731.png)\n\n![image-20250722133608676](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133608676.png)\n\n\n* **什么时候我“需要”购物车？**\n    对于像我们“大P超级电商”这样的**招商模式/混合模式**平台，购物车是**必须品**。因为用户会在不同的店铺之间“逛”，它的核心作用是：\n    1.  **凑单与比价**：让用户可以把来自不同店铺的、感兴趣的商品，先放在一个地方，进行统一的比较和筛选。\n    2.  **跨店促销**：是实现“跨店满减”等复杂促销活动的**技术基础**。\n\n* **什么时候我“不需要”购物车？**\n    1.  **C2C二手交易模式（如：闲鱼）**：二手商品大多是“孤品”（库存只有1件），交易前通常需要买卖双方进行沟通议价，流程复杂。购物车这种“先暂存再统一结算”的模式，会增加无效库存的锁定，并打断沟通流程，反而降低交易效率。\n    2.  **拼团模式（如：拼多多）**：拼多多的核心是“低价爆款、冲动消费”。它希望用户看到一个商品，立刻就完成下单转化。购物车的存在，会让用户“冷静下来”、进行“反复比价”，这与它的核心商业模式是相悖的。\n\n**结论**：对于我们的“大P超级电商”，购物车是用户完成多商品、跨店铺购买的核心功能，我们必须精心设计。\n\n#### 2. 购物车核心功能设计\n\n![image-20250722133729846](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133729846.png)\n\n我设计购物车，会围绕“**进入**”、“**使用**”、“**离开**”这三个场景，来规划它的核心功能。\n\n* **信息展示 (Information Display)**\n    ![image-20250722133750028](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133750028.png)\n    这是购物车最基础，也是最重要的部分。\n    * **状态区分**：我需要设计两种状态。**未登录**时，购物车应为空，并展示商品推荐，引导用户去逛；**登录**后，则展示用户已添加的商品。\n    * **分组与排序**：为了让信息清晰，我必须将商品按“**店铺**”进行分组。在店铺内部，商品会按照“**添加时间**”的倒序排列，最新添加的在最上方。\n    * **营销信息**：我会清晰地展示每个商品适用的优惠信息（如“满减”、“优惠券”），以及店铺整体的促销活动，刺激用户凑单。\n\n* **库存监控 (Inventory Monitoring)**\n    ![image-20250722133856218](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133856218.png)\n    我必须在购物车里，就向用户提供实时的库存状态，避免他到了提交订单的最后一步，才发现商品已售罄。我会设计三种库存状态的展示：\n    1.  **有货**：正常显示。\n    2.  **库存紧张**：当库存很少时（如＜5件），用红字等醒目的方式，提示用户“仅剩X件”，制造稀缺感，促进转化。\n    3.  **无货**：当商品售罄时，商品必须被置灰，数量选择器变为不可用状态，并清晰地提示“已售罄”或“无货”。\n\n* **编辑功能 (Editing)**\n    ![image-20250722133941901](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722133941901.png)\n    我必须赋予用户对购物车的完全掌控权。这包括：\n    * **修改商品数量**：提供简单易用的加、减数量选择器。\n    * **修改商品规格**：允许用户直接在购物车，切换商品的SKU（如颜色、尺码）。\n    * **删除商品**：提供删除单个商品，以及在“编辑”模式下，批量删除多个商品的功能。\n\n* **结算功能 (Checkout)**\n    ![image-20250722134009275](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134009275.png)\n    这是购物车最终的“使命召唤”按钮。我会在页面底部，设计一个常驻的结算栏，它必须清晰地展示：\n    * **已勾选商品的总计金额**\n    * **优惠减免的金额明细**\n    * **一个色彩鲜明、吸引点击的“去结算”按钮**，并标明已选商品的数量。\n\n\n\n\n---\n## 3.6 订单评价及售后\n\n用户的购物旅程，在支付成功的那一刻，其实才刚刚过半。\n\n**从“支付成功”到“满意使用”**，这“最后一公里”的体验，我称之为**购后体验**，它直接决定了用户是否会成为我们的回头客。\n\n本节，我们就来设计购后体验中，最重要的两个环节：**订单评价**和**售后流程**。\n\n### 3.6.1 订单评价维度（商品质量、服务态度等）\n\n我始终认为，**订单评价**，是电商平台**信任体系的基石**。它既是后续用户的“购买决策参考”，也是平台用来“管理商家”的重要数据来源。\n\n![image-20250722134457500](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134457500.png)\n\n一个设计良好的评价体系，能极大地促进平台的健康循环。正如流程图所示，**查看商品评价**，是用户在“购买决策”前的关键一步，直接影响着平台的转化率。\n\n![image-20250722134535203](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134535203.png)\n\n我设计评价体系，核心是定义好“**评价维度**”，即，我希望用户从哪些方面，来对这次交易进行评价。\n\n![image-20250722134626963](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134626963.png)\n\n一个专业、全面的评价功能，至少应该包含以下两部分：\n\n**1. 多维度评分**\n为了得到可量化的、能用于商家考核的数据，我不会只让用户打一个“总分”，而是会将评分，拆解为几个核心的维度。\n\n![image-20250722134658540](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134658540.png)\n\n| **评价维度** | **我的设计说明** |\n| :--- | :--- |\n| **商品质量** | 核心维度，直接反映了“货”的品质。 |\n| **发货速度**| 反映商家履约环节的“物流”效率。 |\n| **服务态度**| 反映商家在售前、售中、售后环节的“服务”质量。 |\n\n我会将这几个维度，都设计为“**1-5星**”的评分形式，这能让我非常直观地，计算出商家的综合服务评分。\n\n**2. 图文评价**\n除了量化的评分，我还需要提供一个让用户能自由表达、分享购物体验的“内容创作区”。\n\n![image-20250722134741503](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134741503.png)\n\n这个功能的设计，我会包含：\n* **文字评价**：一个开放的文本输入框，让用户可以详细描述购物心得。\n* **图片/视频评价**：提供图片/视频的上传功能。“有图有真相”，带图的评价，是所有评价中，对其他用户参考价值最高、最可信的。\n\n### 3.6.2 售后流程设计（取消、退款退货、换货等）\n\n即便我们尽了最大努力，交易过程中也难免会出现各种问题。一套**清晰、合理、公正**的售后流程，是我们在用户遇到问题时，挽回他们信任的最后机会。我把这个流程，也称为“**逆向流程**”。\n\n我设计售后流程，最核心的原则是：**在订单的不同生命周期（状态）下，为用户提供不同的、符合当前场景的售后操作**。\n\n![image-20250722134857703](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722134857703.png)\n\n\n| **订单状态** | **可执行的售后操作** | **我的设计说明** |\n| :--- | :--- | :--- |\n| `待支付` | **取消订单** | 用户未付款，可无理由取消。 |\n| `待发货` | **仅退款** | 用户已付款但未发货，可直接申请退款（无需退货）。 |\n| `待收货` | **申请退款** | 用户可申请退款，触发包裹拦截。**退款成功需待拦截成功或用户拒收。** 建议收货后再发起换货。 |\n| `交易成功`| **申请售后** | 在售后保障期内，可申请退款退货、换货或维修。 |\n\n\n\n\n---\n## 3.7 商品种草（社区化设计）\n\n我们已经设计完了电商平台最核心的“**交易链路**”。现在，我们要开始为我们的产品，构建真正的“**护城河**”——**商品种草**，也就是**社区化设计**。\n\n这部分的设计，将直接体现我们“**内容驱动的潮流社区电商**”的核心定位，是我们区别于传统货架式电商、吸引和留存年轻用户的关键。\n\n### 3.7.1 种草定义与场景（内容推荐、发布与互动）\n\n首先，我来定义一下“种草”。在我看来，它是一种**基于真实体验和信任关系的内容化商品推荐行为**。它包含两个方面：\n* **被种草**：我通过看别人的分享，发现了一款好物，并产生了购买的欲望。\n* **去种草**：我因为使用了一款好物，自发地去分享我的使用心得，推荐给别人。\n\n![image-20250722154101263](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722154101263.png)\n\n在我们的平台上，用户的“种草”旅程，也分为“**看帖**”和“**发帖**”这两条核心路径。基于这两条路径，我提炼出了三大核心用户场景，以及支撑这些场景的必备功能。\n\n![image-20250722154203318](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722154203318.png)\n\n1.  **发布购物心得**：这是“去种草”的场景，需要我们提供`发布心得`的功能。\n2.  **查看他人购物心得**：这是“被种草”的场景，需要我们提供一个`种草社区`（信息流）。\n3.  **针对购物心得互动**：这是社区活跃的保障，需要我们提供`点赞`、`收藏`、`分享`、`评论`等功能。\n\n### 3.7.2 社区功能结构（搜索、发布、瀑布流、话题标签）\n\n现在，我们来具体设计支撑上述场景的核心功能界面。\n\n**1. 种草社区 (信息流)**\n\n![image-20250722154335398](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722154335398.png)\n\n这是用户“被种草”的核心场所，我设计的要点如下：\n* **瀑布流布局**：为了最大化地突出图片这种强视觉冲击力的内容，我会采用**瀑布流**的布局形式来呈现“种草”笔记列表。\n* **关键词搜索**：在顶部，我必须提供一个强大的**搜索**功能，让用户可以根据关键词，精准地查找自己感兴趣的“种草”内容。\n* **分类/话题查看**：提供按不同**分类**或**话题**，来筛选和浏览“种草”笔记的功能，满足用户宽泛的浏览需求。\n\n**2. 发布种草 (内容发布页)**\n\n![image-20250722155905759](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722155905759.png)\n\n这是用户“去种草”的核心工具，我设计的要点如下：\n* **图片/视频上传**：提供入口，让用户可以选择手机里的**单张或多张图片/视频**进行上传。\n* **编写心得内容**：提供一个富文本编辑器，让用户可以**撰写**自己的使用心得和推荐理由。\n* **关联商品**：**这是连接“内容”与“电商”最关键的一步**。我必须提供一个功能，让用户可以在发布笔记时，方便地**关联**到我们平台上正在售卖的**具体商品**。这就在“种草”和“拔草”之间，建立起了最短的转化路径。\n* **选择话题标签**：允许用户为自己的笔记，选择或创建**话题标签**，这既能表达自己的内容核心，也便于被有相同兴趣的用户发现。\n\n\n\n\n\n---\n## 3.8 个人中心\n\n当用户在我们的“商场”里完成了浏览、购买、评价等一系列行为后，他们需要一个地方，来存放他们的“战利品”（订单）、“会员卡”（个人信息）和“购物小票”（历史记录）。这个地方，就是**个人中心**。\n\n在我看来，个人中心是**用户在我们平台上的“数字资产”管理中心**，是提升用户归属感、提供深度服务的核心枢纽。\n\n### 3.8.1 核心功能版块设计（我的订单、设置、推荐、快捷入口等）\n\n![image-20250722160633138](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722160633138.png)\n\n我设计个人中心，不会简单地把所有功能堆砌在一起。我会像整理房间一样，将功能进行**逻辑分区**，让用户能快速找到自己想要的东西。根据淘宝这类成熟产品的经验，我通常会将个人中心，划分为以下四大版块：\n\n**1. 用户数据与资产**\n这是整个页面的“门面”，是用户最核心的个人信息和资产的展示区。\n* **个人信息**：最顶部，清晰地展示用户的`头像`和`昵称`，并提供一个入口，可以跳转到更详细的“个人资料页”进行编辑。\n* **业务数据**：将用户最关心的几个动态数据，进行可视化展示，比如`商品收藏`、`店铺收藏`、`浏览足迹`的数量。\n* **核心资产入口**：提供用户最重要的“资产”的快捷入口。对于我们平台，最重要的就是`我的订单`和我们特色功能的`我的种草`。\n\n**2. 快捷功能入口**\n这是一个灵活的、网格布局的区域，我用它来聚合一些**使用频率相对较高**的功能或运营活动入口。比如`我的优惠券`、`客服中心`、`地址管理`、`每日签到`等。\n\n**3. 应用全局设置**\n这个版块，我通常会把它放在页面的下半部分，或者收纳到一个统一的“**设置**”入口里。它包含的是一些低频、但必要的全局性功能，比如`账号与安全`、`支付设置`、`关于我们`，以及最重要的`退出登录`按钮。\n\n**4. 个性化推荐**\n个人中心是一个高度个性化的页面，因此，它也是进行**精准商品推荐**的绝佳场所。在页面的底部，我会设计一个“**为你推荐**”的模块，根据用户的历史购买、收藏和浏览记录，为他推荐可能感兴趣的商品，以创造更多的交叉销售机会。\n\n![image-20250722160650480](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250722160650480.png)\n\n我们为“大P超级电商”设计的这份个人中心线框图，就是上述设计思路的一个具体体现。它结构清晰、主次分明，将用户最关心的“我的订单”和“我的种草”放在了最核心的位置，确保了用户体验的便捷。\n\n## 3.9 本章总结\n\n至此，我们已经完整地设计出了一个电商产品用户端的所有核心模块。让我们最后回顾一下本章的整个设计旅程：\n\n| **设计模块** | **核心产出与学习要点** |\n| :--- | :--- |\n| **产品形态选择**| 我们对比了**App/小程序/H5/Web**的优劣，并深入学习了**微信小程序**独特的设计规范与特殊功能。 |\n| **用户端设计思路**| 我们确立了电商的**六大核心业务模块**，并掌握了指导界面布局的**接近法则**与**相似法则**。 |\n| **浏览商品**| 我们设计了**首页、商品分类、商品列表页**和**商品详情页**，构建了用户“逛”和“选”的核心路径。 |\n| **下单支付**| 我们设计了**订单提交页、支付页**和**购物车**，并深入探讨了**库存管理、拆单逻辑**等复杂的后端策略。 |\n| **订单评价及售后**| 我们设计了购后体验的**评价体系**和基于订单状态的**售后流程**，以建立用户信任。 |\n| **商品种草**| 我们设计了产品的差异化模块——**社区**，通过**信息流**和**发布**功能，打通内容与电商。 |\n| **个人中心**| 我们为用户设计了一个清晰、有序的“家”，聚合了**用户资产、快捷入口**和**全局设置**。 |\n\n通过这一章的实战，我们已经将电商用户端的理论，全部转化为了具体、可视的产品设计方案。我们已经拥有了一份足以交付给UI和开发团队的、完整的“建筑蓝图”。\n\n\n\n\n\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E7%94%A8%E6%88%B7%E7%AB%AF%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.</span> <span class="toc-text">第三章：电商用户端产品设计</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.1.</span> <span class="toc-text">3.1 学习目标</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-2-%E7%94%A8%E6%88%B7%E7%AB%AF%E4%BA%A7%E5%93%81%E5%BD%A2%E6%80%81%E9%80%89%E6%8B%A9"><span class="toc-number">1.2.</span> <span class="toc-text">3.2 用户端产品形态选择</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-1-%E5%B8%B8%E8%A7%81%E4%BA%A7%E5%93%81%E5%BD%A2%E6%80%81%E5%AF%B9%E6%AF%94-App-%E5%B0%8F%E7%A8%8B%E5%BA%8F-H5-Web%E7%AB%AF"><span class="toc-number">1.2.1.</span> <span class="toc-text">3.2.1 常见产品形态对比 (App / 小程序 / H5 / Web端)</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-2-%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83%EF%BC%9A%E4%BB%A5%E5%BE%AE%E4%BF%A1%E5%B0%8F%E7%A8%8B%E5%BA%8F%E4%B8%BA%E4%BE%8B"><span class="toc-number">1.2.2.</span> <span class="toc-text">3.2.2 设计规范：以微信小程序为例</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%A1%B5%E9%9D%A2%E7%BB%93%E6%9E%84"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">1. 页面结构</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%89%B9%E6%AE%8A%E5%8A%9F%E8%83%BD%E4%B8%8E%E9%99%90%E5%88%B6"><span class="toc-number">1.2.2.2.</span> <span class="toc-text">2. 特殊功能与限制</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-3-%E7%94%A8%E6%88%B7%E7%AB%AF%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-number">1.3.</span> <span class="toc-text">3.3 用户端产品设计思路</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-1-%E6%A0%B8%E5%BF%83%E4%B8%9A%E5%8A%A1%E4%B8%8E%E5%8A%9F%E8%83%BD%E6%A8%A1%E5%9D%97"><span class="toc-number">1.3.1.</span> <span class="toc-text">3.3.1 核心业务与功能模块</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-2-%E6%A0%B8%E5%BF%83%E8%AE%BE%E8%AE%A1%E5%8E%9F%E5%88%99"><span class="toc-number">1.3.2.</span> <span class="toc-text">3.3.2 核心设计原则</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%8E%A5%E8%BF%91%E6%B3%95%E5%88%99-Law-of-Proximity"><span class="toc-number">1.3.2.1.</span> <span class="toc-text">1. 接近法则 (Law of Proximity)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%9B%B8%E4%BC%BC%E6%B3%95%E5%88%99-Law-of-Similarity"><span class="toc-number">1.3.2.2.</span> <span class="toc-text">2. 相似法则 (Law of Similarity)</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-4-%E6%B5%8F%E8%A7%88%E5%95%86%E5%93%81"><span class="toc-number">1.4.</span> <span class="toc-text">3.4 浏览商品</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-4-1-%E9%A6%96%E9%A1%B5%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.4.1.</span> <span class="toc-text">3.4.1 首页设计</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%A6%96%E9%A1%B5%E7%9A%84%E6%A0%B8%E5%BF%83%E7%9B%AE%E7%9A%84"><span class="toc-number">1.4.1.1.</span> <span class="toc-text">1. 首页的核心目的</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%B8%B8%E8%A7%81%E9%A6%96%E9%A1%B5%E6%A8%A1%E5%9D%97%E8%A7%A3%E6%9E%90"><span class="toc-number">1.4.1.2.</span> <span class="toc-text">2. 常见首页模块解析</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E6%88%91%E4%BB%AC%E2%80%9C%E5%A4%A7P%E8%B6%85%E7%BA%A7%E7%94%B5%E5%95%86%E2%80%9D%E7%9A%84%E9%A6%96%E9%A1%B5%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF"><span class="toc-number">1.4.1.3.</span> <span class="toc-text">3. 我们“大P超级电商”的首页设计思路</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-4-2-%E5%95%86%E5%93%81%E5%88%86%E7%B1%BB%E8%AE%BE%E8%AE%A1%EF%BC%88%E6%97%A0%E5%88%86%E7%B1%BB%E3%80%81%E4%B8%80%E7%BA%A7%E3%80%81%E5%A4%9A%E7%BA%A7%EF%BC%89"><span class="toc-number">1.4.2.</span> <span class="toc-text">3.4.2 商品分类设计（无分类、一级、多级）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-4-3-%E5%95%86%E5%93%81%E5%88%97%E8%A1%A8%E4%B8%8E%E8%AF%A6%E6%83%85%E9%A1%B5%E8%AE%BE%E8%AE%A1%EF%BC%88%E5%9B%BE%E6%96%87%E3%80%81%E5%8F%82%E6%95%B0%E3%80%81%E8%AF%84%E4%BB%B7%E3%80%81%E6%8E%A8%E8%8D%90%EF%BC%89"><span class="toc-number">1.4.3.</span> <span class="toc-text">3.4.3 商品列表与详情页设计（图文、参数、评价、推荐）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-5-%E4%B8%8B%E5%8D%95%E6%94%AF%E4%BB%98"><span class="toc-number">1.5.</span> <span class="toc-text">3.5 下单支付</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-5-1-%E4%B8%8B%E5%8D%95%E6%B5%81%E7%A8%8B%E4%B8%8E%E9%A1%B5%E9%9D%A2%E8%AE%BE%E8%AE%A1%EF%BC%88%E6%8F%90%E4%BA%A4%E9%A1%B5%E3%80%81%E6%94%AF%E4%BB%98%E9%A1%B5%EF%BC%89"><span class="toc-number">1.5.1.</span> <span class="toc-text">3.5.1 下单流程与页面设计（提交页、支付页）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%AE%A2%E5%8D%95%E6%8F%90%E4%BA%A4%E9%A1%B5-Order-Submission-Page"><span class="toc-number">1.5.1.1.</span> <span class="toc-text">1. 订单提交页 (Order Submission Page)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%94%AF%E4%BB%98%E9%A1%B5-%E6%94%B6%E9%93%B6%E5%8F%B0-Payment-Page-Cashier"><span class="toc-number">1.5.1.2.</span> <span class="toc-text">2. 支付页/收银台 (Payment Page / Cashier)</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E6%94%AF%E4%BB%98%E6%88%90%E5%8A%9F%E5%90%8E%E7%9A%84%E6%B5%81%E7%A8%8B"><span class="toc-number">1.5.1.3.</span> <span class="toc-text">3. 支付成功后的流程</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-5-2-%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8F%EF%BC%88%E5%BE%AE%E4%BF%A1%E3%80%81%E6%94%AF%E4%BB%98%E5%AE%9D%E3%80%81%E9%93%B6%E8%81%94%E3%80%81%E8%81%9A%E5%90%88%E6%94%AF%E4%BB%98%EF%BC%89"><span class="toc-number">1.5.2.</span> <span class="toc-text">3.5.2 支付方式（微信、支付宝、银联、聚合支付）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%BE%AE%E4%BF%A1%E6%94%AF%E4%BB%98-%E6%94%AF%E4%BB%98%E5%AE%9D-%E9%93%B6%E8%81%94%EF%BC%88%E7%8B%AC%E7%AB%8B%E6%B8%A0%E9%81%93%E5%AF%B9%E6%8E%A5%EF%BC%89"><span class="toc-number">1.5.2.1.</span> <span class="toc-text">1. 微信支付 &amp; 支付宝 &amp; 银联（独立渠道对接）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%81%9A%E5%90%88%E6%94%AF%E4%BB%98%EF%BC%88%E6%88%91%E7%9A%84%E6%8E%A8%E8%8D%90%E6%96%B9%E6%A1%88%EF%BC%89"><span class="toc-number">1.5.2.2.</span> <span class="toc-text">2. 聚合支付（我的推荐方案）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-5-3-%E5%BA%93%E5%AD%98%E7%AE%A1%E7%90%86%E4%B8%8E%E9%97%AE%E9%A2%98%E5%BA%94%E5%AF%B9"><span class="toc-number">1.5.3.</span> <span class="toc-text">3.5.3 库存管理与问题应对</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%BA%93%E5%AD%98%E6%89%A3%E5%87%8F%E6%96%B9%E5%BC%8F%EF%BC%88%E6%8B%8D%E4%B8%8B%E5%87%8F-vs-%E4%BB%98%E6%AC%BE%E5%87%8F%EF%BC%89"><span class="toc-number">1.5.3.1.</span> <span class="toc-text">1. 库存扣减方式（拍下减 vs 付款减）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E9%97%AE%E9%A2%98%E4%B8%8E%E5%BA%94%E5%AF%B9%EF%BC%88%E6%81%B6%E6%84%8F%E6%8B%8D%E5%8D%95%E3%80%81%E8%B6%85%E5%8D%96%EF%BC%89"><span class="toc-number">1.5.3.2.</span> <span class="toc-text">2. 问题与应对（恶意拍单、超卖）</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%BA%94%E5%AF%B9%E2%80%9C%E6%81%B6%E6%8B%8D%E2%80%9D"><span class="toc-number">1.5.3.2.1.</span> <span class="toc-text">应对“恶拍”</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%BA%94%E5%AF%B9%E2%80%9C%E8%B6%85%E5%8D%96%E2%80%9D"><span class="toc-number">1.5.3.2.2.</span> <span class="toc-text">应对“超卖”</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-5-4-%E6%8B%86%E5%8D%95%E9%80%BB%E8%BE%91%EF%BC%88%E7%88%B6%E5%AD%90%E8%AE%A2%E5%8D%95%E3%80%81%E5%8D%8A%E6%8B%86%E5%8D%95%EF%BC%89"><span class="toc-number">1.5.4.</span> <span class="toc-text">3.5.4 拆单逻辑（父子订单、半拆单）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E6%8B%86%E5%8D%95%EF%BC%9F"><span class="toc-number">1.5.4.1.</span> <span class="toc-text">1. 为什么要拆单？</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%8B%86%E5%8D%95%E7%9A%84%E4%B8%A4%E7%A7%8D%E4%B8%BB%E6%B5%81%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.5.4.2.</span> <span class="toc-text">2. 拆单的两种主流模式</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E7%88%B6%E5%AD%90%E8%AE%A2%E5%8D%95%E6%A8%A1%E5%BC%8F-Parent-Child-Order-Model"><span class="toc-number">1.5.4.2.1.</span> <span class="toc-text">父子订单模式 (Parent-Child Order Model)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E5%8D%8A%E6%8B%86%E5%8D%95%E6%A8%A1%E5%BC%8F-Semi-split-Order-Model"><span class="toc-number">1.5.4.2.2.</span> <span class="toc-text">半拆单模式 (Semi-split Order Model)</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-5-5-%E8%B4%AD%E7%89%A9%E8%BD%A6%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1%EF%BC%88%E4%BF%A1%E6%81%AF%E5%B1%95%E7%A4%BA%E3%80%81%E5%BA%93%E5%AD%98%E7%9B%91%E6%8E%A7%E3%80%81%E7%BB%93%E7%AE%97%E7%AD%89%EF%BC%89"><span class="toc-number">1.5.5.</span> <span class="toc-text">3.5.5 购物车功能设计（信息展示、库存监控、结算等）</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%B4%AD%E7%89%A9%E8%BD%A6%E7%9A%84%E4%BD%9C%E7%94%A8%E4%B8%8E%E5%86%B3%E7%AD%96"><span class="toc-number">1.5.5.1.</span> <span class="toc-text">1. 购物车的作用与决策</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%B4%AD%E7%89%A9%E8%BD%A6%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD%E8%AE%BE%E8%AE%A1"><span class="toc-number">1.5.5.2.</span> <span class="toc-text">2. 购物车核心功能设计</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-6-%E8%AE%A2%E5%8D%95%E8%AF%84%E4%BB%B7%E5%8F%8A%E5%94%AE%E5%90%8E"><span class="toc-number">1.6.</span> <span class="toc-text">3.6 订单评价及售后</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-6-1-%E8%AE%A2%E5%8D%95%E8%AF%84%E4%BB%B7%E7%BB%B4%E5%BA%A6%EF%BC%88%E5%95%86%E5%93%81%E8%B4%A8%E9%87%8F%E3%80%81%E6%9C%8D%E5%8A%A1%E6%80%81%E5%BA%A6%E7%AD%89%EF%BC%89"><span class="toc-number">1.6.1.</span> <span class="toc-text">3.6.1 订单评价维度（商品质量、服务态度等）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-6-2-%E5%94%AE%E5%90%8E%E6%B5%81%E7%A8%8B%E8%AE%BE%E8%AE%A1%EF%BC%88%E5%8F%96%E6%B6%88%E3%80%81%E9%80%80%E6%AC%BE%E9%80%80%E8%B4%A7%E3%80%81%E6%8D%A2%E8%B4%A7%E7%AD%89%EF%BC%89"><span class="toc-number">1.6.2.</span> <span class="toc-text">3.6.2 售后流程设计（取消、退款退货、换货等）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-7-%E5%95%86%E5%93%81%E7%A7%8D%E8%8D%89%EF%BC%88%E7%A4%BE%E5%8C%BA%E5%8C%96%E8%AE%BE%E8%AE%A1%EF%BC%89"><span class="toc-number">1.7.</span> <span class="toc-text">3.7 商品种草（社区化设计）</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-7-1-%E7%A7%8D%E8%8D%89%E5%AE%9A%E4%B9%89%E4%B8%8E%E5%9C%BA%E6%99%AF%EF%BC%88%E5%86%85%E5%AE%B9%E6%8E%A8%E8%8D%90%E3%80%81%E5%8F%91%E5%B8%83%E4%B8%8E%E4%BA%92%E5%8A%A8%EF%BC%89"><span class="toc-number">1.7.1.</span> <span class="toc-text">3.7.1 种草定义与场景（内容推荐、发布与互动）</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-7-2-%E7%A4%BE%E5%8C%BA%E5%8A%9F%E8%83%BD%E7%BB%93%E6%9E%84%EF%BC%88%E6%90%9C%E7%B4%A2%E3%80%81%E5%8F%91%E5%B8%83%E3%80%81%E7%80%91%E5%B8%83%E6%B5%81%E3%80%81%E8%AF%9D%E9%A2%98%E6%A0%87%E7%AD%BE%EF%BC%89"><span class="toc-number">1.7.2.</span> <span class="toc-text">3.7.2 社区功能结构（搜索、发布、瀑布流、话题标签）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-8-%E4%B8%AA%E4%BA%BA%E4%B8%AD%E5%BF%83"><span class="toc-number">1.8.</span> <span class="toc-text">3.8 个人中心</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-8-1-%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD%E7%89%88%E5%9D%97%E8%AE%BE%E8%AE%A1%EF%BC%88%E6%88%91%E7%9A%84%E8%AE%A2%E5%8D%95%E3%80%81%E8%AE%BE%E7%BD%AE%E3%80%81%E6%8E%A8%E8%8D%90%E3%80%81%E5%BF%AB%E6%8D%B7%E5%85%A5%E5%8F%A3%E7%AD%89%EF%BC%89"><span class="toc-number">1.8.1.</span> <span class="toc-text">3.8.1 核心功能版块设计（我的订单、设置、推荐、快捷入口等）</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-9-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-number">1.9.</span> <span class="toc-text">3.9 本章总结</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>