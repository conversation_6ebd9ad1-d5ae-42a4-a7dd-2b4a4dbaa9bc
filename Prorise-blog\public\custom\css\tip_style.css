/* build time:Sun Jul 27 2025 21:07:30 GMT+0800 (中国标准时间)*/
.el-input.el-input--small.el-input-group.el-input-group--prepend:nth-child(1):before{content:'输入QQ号会自动获取昵称和头像🐧'}.el-input.el-input--small.el-input-group.el-input-group--prepend:nth-child(2):before{content:'收到回复将会发送到您的邮箱📧'}.el-input.el-input--small.el-input-group.el-input-group--prepend:nth-child(3):before{content:'可以通过昵称访问您的网站🔗'}.el-input.el-input--small.el-input-group.el-input-group--prepend:focus-within::after,.el-input.el-input--small.el-input-group.el-input-group--prepend:focus-within::before{display:block}.el-input.el-input--small.el-input-group.el-input-group--prepend::before{display:none;position:absolute;top:-60px;white-space:nowrap;border-radius:10px;left:50%;transform:translate(-50%);padding:14px 18px;background:#444;color:#fff;z-index:10}.el-input.el-input--small.el-input-group.el-input-group--prepend::after{display:none;content:'';position:absolute;border:12px solid transparent;border-top-color:#444;left:50%;transform:translate(-50%,-48px);z-index:10}#owo-big{position:fixed;display:none;align-items:center;background-color:#fff;border:1px #aaa solid;border-radius:10px;z-index:9999;transform:translate(0,-105%);overflow:hidden;animation:owoIn .3s cubic-bezier(.42,0,.3,1.11)}[data-theme=dark] #owo-big{background-color:#4a4a4a}#owo-big img{width:100%}@keyframes owoIn{0%{transform:translate(0,-95%);opacity:0}100%{transform:translate(0,-105%);opacity:1}}
/* rebuild by neat */