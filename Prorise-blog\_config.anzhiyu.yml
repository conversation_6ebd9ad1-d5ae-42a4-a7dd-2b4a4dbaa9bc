# 菜单配置
menu:
  # 文章分类菜单项
  文章: # 菜单项名称
    归档: /archives/ || anzhiyu-icon-box-archive # 子菜单项名称: 链接 || 图标类名
    分类: /categories/ || anzhiyu-icon-shapes
    标签: /tags/ || anzhiyu-icon-tags
  # 个人相关菜单项
  个人:
    友人帐: /link/ || anzhiyu-icon-link
    # 朋友圈: /fcircle/ || anzhiyu-icon-artstation
    # 留言板: /comments/ || anzhiyu-icon-envelope
    留言板: /todolist/ || fas fa-check-double
    实用网站: /awesome-links/ || fas fa-link

  # 预览相关菜单项
  预览:
    音乐馆: /music/?server=netease&id=3117791189 || anzhiyu-icon-music
    相册集: /album/ || anzhiyu-icon-images
    评论总览: /messages/ || fas fa-comments

  # 关于相关菜单项
  关于:
    关于本人: /about/ || anzhiyu-icon-paper-plane
    即刻短文: /essay/ || anzhiyu-icon-lightbulb
    随便逛逛: javascript:toRandomPost() || anzhiyu-icon-shoe-prints1 # 使用js函数跳转到随机文章

# 导航栏相关配置 (顶部导航栏)
nav:
  enable: true # 是否启用顶部导航栏
  travelling: false # 是否显示前往"开往其他项目"按钮
  clock: false # 是否显示时钟
  menu: # 自定义导航菜单项
    - title: 网页 # 分组标题
      item: # 分组下的链接项
        - name: 博客 # 链接名称
          link: / # 链接地址
          icon: https://bu.dusays.com/2025/07/19/687b394cb439b.ico # 链接图标 (图片url)
    - title: 项目
      item:
        - name: 后续项目...
          link: /
          icon: https://image.anheyu.com/favicon.ico

# mourn （哀悼日，指定日期网站简单变灰，不包括滚动条）
# 注意： 仅网站首页变灰，其他页面正常显示
mourn:
  enable: false # 是否启用哀悼日效果
  days: [4-5, 5-12, 7-7, 9-18, 12-13] # 指定哀悼日期，格式为 月-日

# Code Blocks (代码相关)
# --------------------------------------

highlight_theme: mac # 代码块主题，可选值：darker / pale night / light / ocean / mac / mac light / false (关闭高亮)
highlight_copy: true # 是否显示代码块复制按钮
highlight_lang: true # 是否显示代码块语言名称
highlight_shrink: false # 是否启用代码块收缩功能。true: 收缩 / false: 展开 | none: 展开并隐藏收缩按钮
highlight_height_limit: 330 # 代码块收缩后的高度限制，单位：px
code_word_wrap: false # 是否启用代码块自动换行

# copy settings (复制设置)
# copyright: Add the copyright information after copied content (复制的内容后面加上版权信息)
# copy: enable 复制后弹窗提示版权信息
copy:
  enable: true # 复制内容后是否弹窗提示
  copyright:
    enable: false # 是否在复制的内容后面添加版权信息
    limit_count: 50 # 当复制字符数超过此限制时才添加版权信息

# social settings (社交图标设置)
# formal: # 格式说明
#   name: link || icon # 社交平台名称: 链接 || 图标类名
social:
  Github: https://github.com/Prorise-cool || fa-github # Github图标
  BiliBili: https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0 || fa-bilibili # Bilibili图标

# 作者卡片 状态 (侧边栏作者卡片上的个性签名/状态)
author_status:
  enable: true # 是否启用作者状态显示
  # 可以是任何图片，建议放表情包或者emoji图片，效果都很好，[表情包速查](https://emotion.xiaokang.me/)
  statusImg: https://bu.dusays.com/2025/07/19/687b396f89934.png # 状态图片url
  skills: # 技能/标签列表 (使用示例)
    - 🤣 全栈开发工程师
    - 🙂 前端架构设计师
    - 🙃 后端服务构建者
    - 🤩 移动端应用开发
    - 🤯 数据库设计专家
    - 🥳 DevOps运维实践者
    - 🤭 技术栈多面手
    - 😎 性能优化达人

  # 智慧话语列表（Solitude风格）
  witty_words:
    - 你可以的
    - 你一定可以的
    - 祝你好运，陌生人
    - 保持热爱，奔赴山海
    - 愿你历尽千帆，归来仍是少年
    - 纵然世事无常，也要保持内心的光亮
    - 时间会证明一切，也会治愈一切
    - 做自己的太阳，无需凭借谁的光

  # 时间状态配置
  states:
    morning: ✨ 早上好，新的一天开始了
    noon: 🍲 午餐时间
    afternoon: 🌞 下午好
    night: 早点休息
    goodnight: 晚安 😴

# search (搜索)
# see https://blog.anheyu.com/posts/c27d.html#搜索系统
# --------------------------------------

# Algolia search (Algolia 搜索)
algolia_search:
  enable: true # 确保这里是 true 来启用 Algolia 搜索

  # --- 关键API信息 ---
  # (请将下面三项替换为您自己的信息)
  appId: "K8Y7M0RMXQ" # 粘贴您的 Application ID
  apiKey: "********************************" # 注意！这里必须是只读的 Search Key
  indexName: "prorise_blog" # 例如 'prorise_blog'

  # --- 搜索设置 ---
  hits:
    per_page: 6 # 每页显示搜索结果数，您可以根据喜好修改

  # --- 按标签过滤 (可选功能) ---
  # 如果您想让用户可以筛选特定标签下的搜索结果，可以在这里配置
  # 初期可以保持注释或留空
  tags:
# - 前端
# - Hexo

# Docsearch (Docsearch 搜索)
# Apply and Option Docs: see https://docsearch.algolia.com/
# Crawler Admin Console: see https://crawler.algolia.com/
# Settings: https://www.algolia.com/
docsearch:
  enable: false # 是否启用 Docsearch 搜索
  appId: # Algolia 应用 ID (参阅邮件获取)
  apiKey: # Algolia API Key (参阅邮件获取)
  indexName: # Algolia 索引名称 (参阅邮件获取)
  option: # Docsearch 其他配置项

# Local search (本地搜索)
local_search:
  enable: false # 是否启用本地搜索
  preload: true # 是否预加载搜索索引
  CDN: # 本地搜索所需的js文件CDN地址 (可选)

# Math (数学公式渲染)
# --------------------------------------
# About the per_page (关于 per_page 参数)
# if you set it to true, it will load mathjax/katex script in each page (true 表示每一页都加载js)
# if you set it to false, it will load mathjax/katex script according to your setting (add the 'mathjax: true' in page's front-matter)
# (false 需要时加载，须在使用的 Markdown Front-matter 加上 mathjax: true)

# MathJax
mathjax:
  enable: false # 是否启用 MathJax
  per_page: false # 是否在每一页都加载 MathJax 脚本

# KaTeX
katex:
  enable: true # 是否启用 KaTeX
  per_page: false # 是否在每一页都加载 KaTeX 脚本
  hide_scrollbar: true # 是否隐藏 KaTeX 渲染块的滚动条

# Image (图片设置)
# --------------------------------------

# Favicon（网站图标）
favicon: https://bu.dusays.com/2025/07/19/687b394cb439b.ico

# Avatar (头像)
avatar:
  img: https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png # 作者头像图片url
  effect: false # 头像是否启用悬停特效

# Disable all banner image (禁用所有顶部图片/横幅)
disable_top_img: true # 是否禁用所有页面的顶部图片

# 首页媒体配置
# 注意：index_img 和 index_video 的 enable 只能有一个为 true
index_img:
  enable: false # 如果想用图片背景，请将此项设为 true，并将下面的 index_video 的 enable 设为 false
  path: ""
  vpath: "[您的竖屏图片链接]" # vpath 为竖屏设备显示的图片

index_video:
  enable: false # ✅ 启用视频背景
  path: "https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/background.mp4"
  poster: "https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/static_background.png" # 视频加载出来前的占位图
  vpath: "https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/phone.mp4"
  vposter: "https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/static_phone.png"
# If the banner of page not setting, it will show the top_img (如果页面未设置顶部图片，则显示此默认图片)
default_top_img: "" # 默认顶部图片的url，设置为 false 则不显示默认图片

cover:
  # 是否显示文章封面
  index_enable: true # 是否在首页文章列表中显示封面
  aside_enable: true # 是否在侧边栏显示封面 (如最新文章卡片)
  archives_enable: true # 是否在归档页显示封面
  # 首页文章封面显示的位置
  # left/right/both
  position: left # 首页封面位置，可选 left/right/both (左/右/左右交替)
  # 当没有设置cover时，默认的封面显示
  default_cover: https://bu.dusays.com/2025/06/15/684e5ba422dc2.png # 默认封面图片url示例

# Replace Broken Images (替换无法显示的图片)
error_img:
  flink: https://bu.dusays.com/2024/06/20/6673caa5bca18.gif # 友链中图片加载失败时的替换图片
  post_page: https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg # 文章内图片加载失败时的替换图片

# A simple 404 page (简单的404页面)
error_404:
  enable: true # 是否启用自定义404页面
  subtitle: "请尝试站内搜索寻找文章" # 404页面的副标题
  background: https://bu.dusays.com/2023/05/08/645907596997d.gif # 404页面的背景图片url

post_meta:
  page: # Home Page (主页文章列表的元信息显示设置)
    date_type: created # 日期类型，可选 created (创建日期) or updated (更新日期) or both (都显示)
    date_format: relative # 日期格式，可选 date (完整日期) / relative (相对日期，如"3天前") / simple (简单日期，如 MM-DD)
    categories: true # 是否显示分类
    tags: true # 是否显示标签
    label: true # 是否显示元信息前面的描述性文字 (如"发布于"、"分类于")
  post: # 文章详情页的元信息显示设置
    date_type: both # 日期类型，可选 created (创建日期) or updated (更新日期) or both (都显示)
    date_format: relative # 日期格式，可选 date (完整日期) / relative (相对日期)
    categories: true # 是否显示分类
    tags: true # 是否显示标签
    label: true # 是否显示元信息前面的描述性文字
    unread: true # 是否启用文章未读功能 (显示阅读进度条)

# 主色调相关配置 (从图片中提取主色调并应用于页面元素)
mainTone:
  enable: true # 是否启用获取图片主色调功能
  mode: both # 获取主色调的模式，可选 cdn/api/both。cdn模式为图片url+imageAve参数获取主色调，api模式为请求API获取主色调，both模式会先请求cdn参数，无法获取的情况下将请求API获取。可以在文章内配置 main_color: '#3e5658'，使用十六进制颜色，则不会请求获取，而是直接使用配置的颜色。
  # 项目地址：https://github.com/anzhiyu-c/img2color-go
  api: https://img2color-go.vercel.app/api?img= # 当 mode 为 api 或 both 时，填写图片颜色提取API地址
  cover_change: true # 是否整篇文章跟随封面图片修改主色调

# wordcount (字数统计)
# see https://blog.anheyu.com/posts/c27d.html#字数统计
wordcount:
  enable: true # 是否启用字数统计和阅读时长功能
  post_wordcount: true # 是否在文章详情页显示字数统计
  min2read: true # 是否在文章详情页显示预计阅读时长
  total_wordcount: true # 是否在站点整体显示总字数统计

# Display the article introduction on homepage (在首页显示文章摘要)
# 1: description (优先显示文章 frontmatter 中的 description)
# 2: both (如果存在 description 则显示 description，否则显示自动截取的 auto_excerpt)
# 3: auto_excerpt (默认值，总是显示自动截取的摘要)
# false: do not show the article introduction (不在首页显示文章摘要)
index_post_content:
  method: 3 # 摘要显示方法
  length: 100 # 如果 method 设置为 2 或 3，需要配置自动截取的摘要长度 (单位：字符)

# anchor (锚点链接)
# when you scroll in post, the URL will update according to header id. (当你在文章中滚动时，URL 会根据标题的 ID 进行更新)
anchor: true # 是否启用滚动时更新URL的锚点功能

# Post (文章设置)
# --------------------------------------

# toc (目录)
toc:
  post: true # 是否在文章页显示目录
  page: false # 是否在普通页面显示目录
  number: true # 目录中是否显示标题编号
  expand: true # 是否默认展开所有目录项
  style_simple: false # 文章页目录是否使用简洁样式

post_copyright:
  enable: true # 是否启用文章版权信息
  decode: false # 是否解码博主姓名 (此处通常用于加密)
  author_href: # 博主名称链接 (留空则默认为站点首页)
  location: 广东 # 文章发布地点
  license: CC BY-NC-SA 4.0 # 版权许可类型
  license_url: https://creativecommons.org/licenses/by-nc-sa/4.0/ # 版权许可链接
  avatarSinks: true # 悬停时作者头像是否下沉
  copyright_author_img_back: https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png # 版权信息区域作者头像背景图片
  copyright_author_img_front: https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片
  copyright_author_link: / # 版权信息区域作者名称的链接

# Sponsor/reward (赞赏/打赏)
reward:
  enable: true # 是否启用赞赏功能
  QR_code: # 赞赏二维码列表
    - img: https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png # 二维码图片url
      link: # 二维码链接 (可选)
      text: 微信 # 二维码描述文本
    - img: https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png
      link:
      text: 支付宝

# Post edit (文章编辑链接)
# Easily browse and edit blog source code online. (方便在线浏览和编辑博客源代码)
post_edit: # 目前仅可选择一个平台在线编辑
  enable: false # 是否启用文章编辑链接
  # github: https://github.com/user-name/repo-name/edit/branch-name/subdirectory-name/ # Github 编辑链接格式
  # For example: https://github.com/jerryc127/butterfly.js.org/edit/main/source/ # Github 示例
  github: false # Github 编辑链接前缀

  # yuque: https://www.yuque.com/user-name/repo-name/ # 语雀编辑链接格式
  # 示例: https://www.yuque.com/yuque/yuque/
  # 你需要在语雀文章 Front Matter 添加参数 id 并确保其唯一性（例如 "id: yuque", "id: 01"）
  yuque: false # 语雀编辑链接前缀

# Related Articles (相关文章)
related_post:
  enable: true # 是否启用相关文章功能
  limit: 6 # 显示相关文章的数量
  date_type: created # 相关文章的日期类型，可选 created (创建日期) or updated (更新日期)

# figcaption (图片描述文字)
photofigcaption: true # 是否显示图片的 figcaption 描述文字

# post_pagination (文章分页导航)
# value: 1 || 2 || 3 || 4 || false
# 1: 下一篇文章链接到旧文章
# 2: 文下一篇文章链接到新章
# 3: 只有下一篇，并且只在文章滚动到评论区时显示下一篇文章(旧文章)
# 4: 只有下一篇，并且只在文章滚动到评论区时显示下一篇文章(旧文章)，显示图片cover
# false: disable pagination (禁用文章分页导航)
post_pagination: 2 # 文章分页导航样式

# Displays outdated notice for a post (文章过期提醒)
noticeOutdate:
  enable: true # 是否启用文章过期提醒
  style: flat # 样式，可选 simple/flat
  limit_day: 360 # 文章发布或更新超过多少天后显示提醒
  position: top # 提醒显示位置，可选 top/bottom (顶部/底部)
  message_prev: 距离上次更新已经过了 # 提醒信息前缀文本
  message_next: 天，文章内容可能已经过时。 # 提醒信息后缀文本

# Share System (分享功能)
# --------------------------------------

# Share.js (分享库)
# https://github.com/overtrue/share.js
sharejs:
  enable: true # 是否启用 Share.js 分享功能
  sites: facebook,twitter,wechat,weibo,qq # 要显示的分享平台列表 (用逗号分隔)

# AddToAny (分享库)
# https://www.addtoany.com/
addtoany:
  enable: false # 是否启用 AddToAny 分享功能
  item: facebook,twitter,wechat,sina_weibo,email,copy_link # 要显示的分享平台列表 (用逗号分隔)

# Comments System (评论系统)
# --------------------------------------

comments:
  # 最多可配置两个评论系统，第一个将作为默认显示
  # Choose: Valine/Waline/Twikoo/Artalk (可选择的评论系统)
  use: [
    "Twikoo",
    "Waline",
  ] # Twikoo/Waline # 配置使用的评论系统名称 (例如 ['Twikoo', 'Waline'])
  text: true # 是否在评论按钮旁边显示评论系统的名称
  # 如果启用懒加载，评论计数可能不准确
  lazyload: false # 是否启用评论系统懒加载
  count: true # 是否在文章顶部图片中显示评论计数
  card_post_count: true # 是否在首页文章列表卡片中显示评论计数

# valine (Valine 评论系统)
# https://valine.js.org
valine:
  appId: xxxxx # LeanCloud 应用 App ID
  appKey: xxxxx # LeanCloud 应用 App Key
  pageSize: 10 # 评论列表每页显示数量
  avatar: mp # Gravatar 头像样式，可选值参考 https://valine.js.org/#/avatar
  lang: zh-CN # 语言，可选 zh-CN/zh-TW/en/ja 等
  placeholder: 填写QQ邮箱就会使用QQ头像喔~. # 评论输入框的占位文本
  guest_info: nick,mail,link # 评论者信息填写项，可选 nick/mail/link (昵称/邮箱/网址)
  recordIP: false # 是否记录评论者 IP 地址
  serverURLs: # LeanCloud 国内自定义域名，海外版无需填写
  bg: https://bu.dusays.com/2023/05/27/6471589647982.png # Valine 评论框背景图片
  emojiCDN: //i0.hdslb.com/bfs/emote/ # Emoji CDN 地址
  enableQQ: true # 是否启用 QQ 邮箱自动获取昵称和头像
  requiredFields: nick,mail # 必填字段，可选 nick/mail
  visitor: false # 是否启用文章阅读量统计 (基于 Valine 的 LeanCloud)
  master: # 博主邮箱 MD5 值列表 (用于标识博主身份)
    - ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a
  friends: # 朋友邮箱 MD5 值列表 (用于标识朋友身份)
    - tagMeta: "博主,小伙伴,访客" # 评论身份标签文本
      option: # 其他可选配置项

# waline - A simple comment system with backend support fork from Valine (Waline 评论系统)
# https://waline.js.org/
waline:
  serverURL: https://waline.prorise666.site # Waline 后端服务地址 URL
  bg: https://bu.dusays.com/2023/05/27/6471589647982.png # Waline 评论框背景图片
  pageview: true # 是否启用文章阅读量统计 (基于 Waline)
  meta_css: true # 是否引入 waline-meta.css, 以便显示 meta 图标
  imageUploader: true # 是否启用图片上传功能。配置为 > 换行后可自定义图片上传逻辑，示例: https://waline.js.org/cookbook/customize/upload-image.html#案例
  # 以下为可选配置，后续若有新增/修改配置参数可在此自行添加/修改
  option: # 其他可选配置项

# Twikoo (Twikoo 评论系统)
# https://github.com/imaegoo/twikoo
twikoo:
  envId: https://twikoo.prorise666.site/ # Twikoo 环境 ID
  region: ap-shanghai # Twikoo 环境地域 (例如 ap-shanghai, 可选)
  visitor: true # 是否启用文章阅读量统计 (基于 Twikoo)
  option: # 其他可选配置项

# Artalk (Artalk 评论系统)
# https://artalk.js.org/guide/frontend/config.html
artalk:
  server: # Artalk 后端服务地址
  site: # Artalk 站点名称
  visitor: false # 是否启用文章阅读量统计 (基于 Artalk)
  option: # 其他可选配置项

# giscus (giscus 评论系统)
# https://giscus.app/
giscus:
  repo: # GitHub 仓库名称 (格式: owner/repo)
  repo_id: # GitHub 仓库 ID
  category_id: # GitHub Discussions 分类 ID
  theme: # 主题样式
    light: light # Light mode 主题
    dark: dark # Dark mode 主题
  option: # 其他可选配置项
    data-lang: zh-CN # 语言
    data-mapping: # Discussions 和页面的映射关系
    data-category: # 分类名称 (如果设置了 data-category-id 则优先使用 ID)
    data-input-position: # 输入框位置

# Chat Services (聊天服务)
# --------------------------------------

# Chat Button [recommend] (聊天按钮 [推荐])
# It will create a button in the bottom right corner of website, and hide the origin button (这会在网站右下角创建一个聊天按钮，并隐藏原始的聊天窗口按钮)
chat_btn: true # 是否启用聊天按钮 (启用后原始聊天窗口会被隐藏)

# The origin chat button is displayed when scrolling up, and the button is hidden when scrolling down (原始聊天按钮在向上滚动时显示，向下滚动时隐藏)
chat_hide_show: true # 是否启用聊天按钮的滚动隐藏/显示效果

# chatra (Chatra 聊天服务)
# https://chatra.io/
chatra:
  enable: false # 是否启用 Chatra
  id: # Chatra 项目 ID

# tidio (Tidio 聊天服务)
# https://www.tidio.com/
tidio:
  # 启用 Tidio
  enable: true
  public_key: "tk571vck0ua2wjoalfbp8wyrlsdaunmz"

# daovoice (DaoVoice 聊天服务)
# http://daovoice.io/
daovoice:
  enable: false # 是否启用 DaoVoice
  app_id: # DaoVoice 应用 ID

# crisp (Crisp 聊天服务)
# https://crisp.chat/en/
crisp:
  enable: false # 是否启用 Crisp
  website_id: # Crisp 网站 ID

# Footer Settings (页脚设置)
# --------------------------------------
footer:
  # 站点拥有者信息
  owner:
    enable: true # ✅ 已启用
    since: 2025 # [请替换为您的站点起始年份]

  # 自定义页脚文本
  custom_text: 这是我的个人博客，分享技术与生活点滴。

  # 网站运行时间
  runtime:
    enable: true # ✅ 已启用
    launch_time: 06/10/2025 00:00:00 # [请替换为您的网站实际上线时间]
    # 上班状态图片
    work_img: # [请替换为您的"上班"状态图片链接,若有需要]
    work_description: 努力搬砖中，为了诗和远方~
    # 下班状态图片
    offduty_img: # [请替换为您的"下班"状态图片链接,若有需要]
    offduty_description: 下班了就该开开心心的玩耍，嘿嘿~

  # 徽标部分配置
  bdageitem:
    enable: true # ✅ 已启用
    list:
      - link: https://hexo.io/
        shields: https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg
        message: 博客框架为Hexo7.0
      - link: https://www.dogecloud.com/
        shields: https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg
        message: 本站使用多吉云为静态资源提供CDN加速
      - link: https://github.com/
        shields: https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg
        message: 本站项目由Github托管
      - link: http://creativecommons.org/licenses/by-nc-sa/4.0/
        shields: https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg
        message: 本站采用CC BY-NC-SA 4.0协议

  # 社交链接栏 (在页脚显示社交图标)
  socialBar:
    enable: true # ✅ 已启用
    centerImg: https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png # [请替换为您的中心图片链接，例如头像]
    # 左侧社交链接列表
    left:
      - title: email
        link: mailto:<EMAIL> # [请替换为您的邮箱地址]
        icon: anzhiyu-icon-envelope
      - title: 微博
        link: https://weibo.com/u/**********?tabtype=home # [请替换为您的微博链接]
        icon: anzhiyu-icon-weibo
      - title: RSS
        link: /atom.xml # [您的RSS地址，通常无需修改]
        icon: anzhiyu-icon-rss
    # 右侧社交链接列表
    right:
      - title: Github
        link: https://github.com/Prorise-cool # [请替换为您的Github链接]
        icon: anzhiyu-icon-github
      - title: Bilibili
        link: https://space.bilibili.com/361040115?spm_id_from=333.788.0.0 # [请替换为您的B站链接]
        icon: anzhiyu-icon-bilibili
      - title: CC
        link: /copyright # [您的版权页面链接]
        icon: anzhiyu-icon-copyright-line

  # 页脚自定义链接列表 (多列显示)
  list:
    enable: true # ✅ 已启用
    randomFriends: 3
    project:
      - title: 服务
        links:
          - title: 51la统计
            link: https://v6.51.la/
          - title: 十年之约
            link: https://www.foreverblog.cn/
      - title: 主题
        links:
          - title: 文档
            link: /docs/ # [链接到您的文档页面]
          - title: 源码
            link: https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main
      - title: 导航
        links:
          - title: 即刻短文
            link: /essay/ # [链接到您的即刻短文页面]
          - title: 留言板
            link: /comments/ # [链接到您的留言板页面]
      - title: 协议
        links:
          - title: 隐私协议
            link: /privacy/ # [链接到您的隐私协议页面]
          - title: 版权协议
            link: /copyright/ # [链接到您的版权协议页面]

  # 页脚底部横栏
  footerBar:
    enable: true # ✅ 已启用
    authorLink: /about/ # [作者名称的链接，通常是"关于"页面]
    # CC 许可信息
    cc:
      enable: true # ✅ 已启用
      link: /copyright # [CC许可页面链接]
    # 底部横栏自定义链接列表
    linkList:
      - link: https://github.com/Prorise-cool?tab=repositories # [请替换为您的博客仓库链接]
        text: 网站源码
      - link: https://beian.miit.gov.cn/ # [请替换为您的ICP备案链接]
        text: 湘ICP备23041-302号 # [请替换为您的备案号]
    # 底部横栏副标题
    subTitle:
      enable: true # ✅ 已启用
      effect: true # 启用打字效果
      startDelay: 300
      typeSpeed: 150
      backSpeed: 50
      loop: true
      source: 1 # 调用一言网
      # 自定义副标题文本列表
      sub:
        - 生活明朗, 万物可爱, 人间值得, 未来可期.
        - 愿你眼里的星星，永远亮晶晶。
        - 愿我们每个人都能被世界温柔以待。
# Analysis (统计分析)
# --------------------------------------

# Baidu Analytics (百度统计)
# https://tongji.baidu.com/web/welcome/login
baidu_analytics: # 百度统计代码 ID (hm.baidu.com/hm.js? 后面的那串字符)

# Google Analytics (谷歌统计)
# https://analytics.google.com/analytics/web/
google_analytics:
  enable: true
  tracking_id: "G-M18Z104LF2" # <--- 粘贴到这里
# CNZZ Analytics (CNZZ 统计, 现友盟统计)
# https://www.umeng.com/
cnzz_analytics: # CNZZ 统计代码 ID

# Cloudflare Analytics (Cloudflare 统计)
# https://www.cloudflare.com/zh-tw/web-analytics/
cloudflare_analytics: # Cloudflare 统计 Token

# Microsoft Clarity (微软 Clarity)
# https://clarity.microsoft.com/
microsoft_clarity: # Microsoft Clarity 项目 ID

# Advertisement (广告)
# --------------------------------------

# Google Adsense (谷歌广告)
google_adsense:
  enable: false # 是否启用 Google Adsense
  auto_ads: true # 是否启用自动广告
  js: https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js # Adsense js 脚本地址 (通常无需修改)
  client: # Adsense Publisher ID (pub-XXXXXXXXXXXXXXX)
  enable_page_level_ads: true # 是否启用页面级广告 (已废弃，由 auto_ads 替代)

# Insert ads manually (手动插入广告)
# ad: # 广告插入位置配置
#   index: # 首页文章列表中的广告代码
#   aside: # 侧边栏广告代码
#   post: # 文章详情页中的广告代码

# Verification (站长验证)
# --------------------------------------

site_verification: # 站长验证列表
  - name: google-site-verification # 验证名称
    content: xxx # 验证代码
  - name: baidu-site-verification
    content: code-xxx
  - name: msvalidate.01
    content: xxx

# Beautify/Effect (美化/效果)
# --------------------------------------

# Theme color for customize (自定义主题配色)
theme_color:
  enable: true # 是否启用自定义主题颜色
  main: "#1e2022" # 主题主色调 (light mode)
  dark_main: "#52616b" # 主题主色调 (dark mode)
  paginator: "#1e2022" # 分页器颜色
  #   button_hover: "#FF7242" # 按钮悬停颜色 (示例)
  text_selection: "#c9d6df" # 文本选中颜色
  link_color: "var(--anzhiyu-fontcolor)" # 链接颜色 (使用CSS变量)
  meta_color: "var(--anzhiyu-fontcolor)" # 元信息颜色 (使用CSS变量)
  hr_color: "#c9d6df33" # 水平分隔线颜色
  code_foreground: "#FFFFFF" # 代码块前景色
  code_background: "var(--anzhiyu-code-stress)" # 代码块背景色 (使用CSS变量)
  toc_color: "#52616b" # 目录颜色
  #   blockquote_padding_color: "#4d648d" # 引用块 padding 颜色 (示例)
  #   blockquote_background_color: "#4d648d" # 引用块背景颜色 (示例)
  scrollbar_color: "var(--anzhiyu-scrollbar)" # 滚动条颜色 (使用CSS变量)
  meta_theme_color_light: "#f0f5f9" # 移动端浏览器顶部主题色 (light mode)
  meta_theme_color_dark: "#1e2022" # 移动端浏览器顶部主题色 (dark mode)

# 移动端侧栏 (Mobile sidebar)
sidebar:
  site_data: # 站点数据卡片 (归档、标签、分类计数)
    archive: true # 是否显示归档计数
    tag: true # 是否显示标签计数
    category: true # 是否显示分类计数
  menus_items: true # 是否显示菜单项卡片
  tags_cloud: true # 是否显示标签云卡片
  display_mode: true # 是否显示显示模式切换 (亮色/暗色)
  nav_menu_project: true # 是否显示导航菜单项目卡片 (对应nav.menu配置)

# 文章h2添加分隔线
h2Divider: true # 是否在文章的 H2 标题下方添加分隔线

# 表格隔行变色
table_interlaced_discoloration: true # 是否启用表格隔行变色效果

# 首页双栏显示 (文章列表双列布局)
article_double_row: true # 是否在首页启用文章列表双列布局 (仅在大屏幕下生效)

# The top_img settings of home page (首页顶部图片/横幅设置)
# default: top img - full screen, site info - middle (默认：顶部图片全屏，站点信息居中)
# The position of site info, eg: 300px/300em/300rem/10% (主页标题距离顶部距离)
index_site_info_top: # 首页站点信息距离顶部的距离
# The height of top_img, eg: 300px/300em/300rem (主页top_img高度)
index_top_img_height: # 首页顶部图片的高度

# The user interface setting of category and tag page (category和tag页的UI设置)
# index - same as Homepage UI (index 值代表 UI将与首页的UI一样)
# default - same as archives UI 默认跟archives页面UI一样
category_ui: # 分类页面的 UI 样式，留空或 index
tag_ui: # 标签页面的 UI 样式，留空或 index

# Footer Background (页脚背景)
footer_bg: false # 页脚是否使用背景图片

# the position of bottom right button/default unit: px (右下角按钮距离底部的距离/默认单位为px)
rightside-bottom: 100px # 右下角功能按钮组距离浏览器底部的距离

# Background effects (背景特效)
# --------------------------------------

# canvas_ribbon (静止彩带背景)
# See: https://github.com/hustcc/ribbon.js
canvas_ribbon:
  enable: false # 是否启用静止彩带背景
  size: 150 # 彩带大小
  alpha: 0.6 # 彩带透明度 (0~1)
  zIndex: -1 # 彩带元素的 z-index 值 (通常设置为 -1 使其在背景层)
  click_to_change: false # 是否点击页面时改变彩带颜色
  mobile: false # 是否在移动端启用

# Fluttering Ribbon (动态彩带)
canvas_fluttering_ribbon:
  enable: false # 是否启用动态彩带背景
  mobile: false # 是否在移动端启用

# canvas_nest (动态线条背景)
# https://github.com/hustcc/canvas-nest.js
canvas_nest:
  enable: false # 是否启用动态线条背景
  color: "0,0,255" # 线条颜色，格式为 RGB 值 (R,G,B)。注意：使用逗号分隔
  opacity: 0.7 # 线条透明度 (0~1)
  zIndex: -1 # 背景元素的 z-index 值
  count: 99 # 线条数量
  mobile: false # 是否在移动端启用

# Typewriter Effect (打字效果)
# https://github.com/disjukr/activate-power-mode
activate_power_mode:
  enable: false # 是否启用打字时的 Power Mode 效果
  colorful: false # 是否启用粒子动画 (冒光特效)
  shake: false # 是否启用抖动特效
  mobile: false # 是否在移动端启用

# Mouse click effects: fireworks (鼠标点击效果: 烟火特效)
fireworks:
  enable: false # 是否启用鼠标点击烟火特效
  zIndex: 9999 # 烟火元素的 z-index 值 (-1 或 9999)
  mobile: false # 是否在移动端启用

# Mouse click effects: Heart symbol (鼠标点击效果: 爱心)
click_heart:
  enable: false # 是否启用鼠标点击爱心特效
  mobile: false # 是否在移动端启用

# Mouse click effects: words (鼠标点击效果: 文字)
ClickShowText:
  enable: false # 是否启用鼠标点击文字特效
  text: # 点击后显示的文本列表
  # - I # 文本内容示例
  # - LOVE
  # - YOU
  fontSize: 15px # 文字大小
  random: false # 是否随机显示文本列表中的内容
  mobile: false # 是否在移动端启用

# Default display mode (网站默认的显示模式)
# light (default) / dark (默认亮色模式 / 暗色模式)
display_mode: light # 网站默认显示模式

# Beautify (美化页面显示)
beautify:
  enable: true # 是否启用页面美化功能
  field: post # 应用范围，可选 site (全站) / post (仅文章)
  title-prefix-icon: '\f0c1' # 标题前缀图标的 Unicode 值 (Font Awesome 图标)
  title-prefix-icon-color: "#F47466" # 标题前缀图标颜色

# 全局字体设置
# 除非你了解这些设置如何工作，否则不要修改它们
font:
  global-font-size: 16px
  code-font-size:
  font-family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"'
  code-font-family: consolas, Menlo, "PingFang SC", "Microsoft JhengHei", "Microsoft YaHei", sans-serif

# 网站标题和副标题字体设置
# 左上角网站名字 主页居中网站名字
blog_title_font:
  font_link:
  font-family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"'
# The setting of divider icon (水平分隔线图标设置)
hr_icon:
  enable: true # 是否启用水平分隔线图标
  icon: \f0c4 # 图标的 Unicode 值 (Font Awesome 图标，如 '\f0c4' 表示链接图标)
  icon-top: # 图标距离顶部距离 (可选)

# the subtitle on homepage (主页subtitle)
subtitle:
  enable: false # 是否启用主页副标题
  # Typewriter Effect (打字效果)
  effect: true # 是否启用打字效果
  # Effect Speed Options (打字效果速度参数)
  startDelay: 300 # 打字开始前的延迟时间 (毫秒)
  typeSpeed: 150 # 打字速度 (毫秒/字符)
  backSpeed: 50 # 回退速度 (毫秒/字符)
  # loop (循环打字)
  loop: true # 是否循环打字
  # source 调用第三方服务 (副标题内容来源)
  # source: false 关闭调用
  # source: 1  调用一言网的一句话（简体） https://hitokoto.cn/
  # source: 2  调用一句网（简体） http://yijuzhan.com/
  # source: 3  调用今日诗词（简体） https://www.jinrishici.com/
  # subtitle 会先显示 source , 再显示 sub 的内容 (如果同时启用 source 和 sub，会先显示 source 的内容，再显示 sub 的内容)
  source: false # 副标题内容来源
  # 如果关闭打字效果，subtitle 只会显示 sub 的第一行文字 (如果禁用打字效果，只会显示 sub 列表中的第一项)
  sub: # 自定义副标题文本列表
    - 山川湖海, 万物可爱, 人间值得, 未来可期. # 文本内容
    - 愿你历尽千帆, 归来仍是少年.
    - 纵然世事无常, 也要保持内心的光亮.
    - 时间会证明一切, 也会治愈一切.
    - 做自己的太阳, 无需凭借谁的光.

# Loading Animation (加载动画)
preloader:
  enable: true # 是否启用页面加载动画
  # source (加载动画类型)
  # 1. fullpage-loading (全屏加载动画)
  # 2. pace (进度条)
  # else all (默认显示头像加载动画)
  source: 3 # 加载动画类型
  # pace theme (pace 进度条主题，参见 https://codebyzach.github.io/pace/)
  pace_css_url: # pace 主题 CSS 文件地址
  avatar: https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png # 自定义加载动画头像图片url (当 source 非 1 或 2 时生效)

# aside (侧边栏)
# --------------------------------------

aside:
  enable: true # 是否启用侧边栏
  hide: false # 是否默认隐藏侧边栏 (需要配合 button: true 使用)
  button: true # 是否显示侧边栏显示/隐藏按钮
  mobile: true # 是否在移动端显示侧边栏
  position: right # 侧边栏位置，可选 left (左) or right (右)
  display: # 控制对应详情页面是否显示侧边栏
    archive: true # 归档页是否显示侧边栏
    tag: true # 标签页是否显示侧边栏
    category: true # 分类页是否显示侧边栏
  card_author: # 作者信息卡片
    enable: true # 是否启用作者信息卡片
    description: '<div style="line-height:1.4;color:rgba(255,255,255,0.9);text-align:center;">🚀 全栈开发者，专注于构建优雅高效的数字解决方案<br><span style="color:#fff;font-weight:bold;">前端 · 后端 · 架构 · 优化</span><br>📚 分享实战经验，一起探索技术边界</div>'
    name_link: / # 作者姓名的链接
    content: "这是我的博客，分享技术与生活的点点滴滴" # 悬停时显示的内容
  card_announcement: # 公告卡片
    enable: false # 是否启用公告卡片
    content: "" # 公告内容
  card_weixin: # 微信二维码卡片
    enable: false # 是否启用微信二维码卡片
    face: https://bu.dusays.com/2023/01/13/63c02edf44033.png # 微信前置二维码图片url (如个人微信)
    backFace: https://bu.dusays.com/2025/07/13/687310ff570ac.png # 微信背景二维码图片url (如公众号)
  card_recent_post: # 最新文章卡片
    enable: true # 是否启用最新文章卡片
    limit: 5 # 显示最新文章数量 (设置为 0 显示所有)
    sort: date # 排序方式，可选 date (按创建日期) or updated (按更新日期)
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
  card_categories: # 分类卡片
    enable: false # 是否启用分类卡片
    limit: 8 # 显示分类数量 (设置为 0 显示所有)
    expand: none # 默认展开子分类，可选 none (不展开) / true (展开所有) / false (不展开)
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
  card_tags: # 标签卡片 (标签云)
    enable: true # 是否启用标签卡片
    limit: 40 # 显示标签数量 (设置为 0 显示所有)
    color: false # 标签是否使用随机颜色
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
    highlightTags: # 高亮显示的标签列表 (使用示例)
      - Java
      - Python
  card_archives: # 归档卡片
    enable: true # 是否启用归档卡片
    type: monthly # 归档类型，可选 yearly (按年) or monthly (按月)
    format: MMMM YYYY # 归档格式，例如 MMMM YYYY (七月 2023)
    order: 1 # 排序顺序，1 为升序，-1 为降序
    limit: 8 # 显示归档数量 (设置为 0 显示所有)
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
  card_webinfo: # 站点信息卡片
    enable: true # 是否启用站点信息卡片
    post_count: true # 是否显示文章总数
    last_push_date: false # 是否显示站点最后更新日期
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)

# busuanzi count for PV / UV in site (不蒜子统计，用于站点访客数/访问量)
# 访问人数
busuanzi:
  site_uv: true # 是否显示站点总访客数 (UV)
  site_pv: true # 是否显示站点总访问量 (PV)
  page_pv: true # 是否显示文章页面访问量 (PV)

# Time difference between publish date and now (网页运行时间)
# Formal: Month/Day/Year Time or Year/Month/Day Time (格式：月/日/年 时间 或 年/月/日 时间)
runtimeshow:
  enable: true # 是否显示网站运行时间
  publish_date: 6/1/2025 00:00:00 # 网站上线时间，格式必须正确以便计算时长

# Console - Newest Comments (控制台 - 最新评论)
newest_comments:
  enable: true # 是否标签页面 显示最新评论
  sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
  limit: 6 # 显示最新评论数量
  storage: 10 # 数据存储到 localStorage 的时间 (单位：分钟)，避免频繁请求 API

# 右下角按钮
# --------------------------------------

# 简繁转换
translate:
  enable: true # 是否启用简繁转换按钮
  # 按钮上显示的文本
  default: 繁 # 默认按钮文本 (在简体模式下显示)
  # 右键菜单默认文本
  rightMenuMsgDefault: "轉為繁體" # 右键菜单转换为繁体的文本
  # 网站语言，1 - 繁体中文 / 2 - 简体中文
  defaultEncoding: 2 # 网站默认编码 (简体)
  # 延迟时间
  translateDelay: 0 # 翻译延迟时间 (毫秒)
  # 在简体模式下按钮显示的文本
  msgToTraditionalChinese: "繁" # 按钮文本：转换为繁体
  # 在繁体模式下按钮显示的文本
  msgToSimplifiedChinese: "简" # 按钮文本：转换为简体
  # 右键菜单转换为繁体
  rightMenuMsgToTraditionalChinese: "转为繁体" # 右键菜单文本：转为繁体
  # 右键菜单转换为简体
  rightMenuMsgToSimplifiedChinese: "转为简体" # 右键菜单文本：转为简体

# Read Mode (阅读模式)
readmode: false # 是否启用阅读模式按钮

# 中控台 (Center Console)
centerConsole:
  enable: true # 是否启用中控台按钮 (通常包含快捷功能或信息)
  card_tags: # 中控台中的标签卡片
    enable: true # 是否启用中控台标签卡片
    limit: 40 # 显示标签数量 (0 为所有)
    color: true # 标签是否使用随机颜色
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)
    highlightTags: # 高亮显示的标签列表 (使用示例)
  # - Hexo
  # - 前端
  card_archives: # 中控台中的归档卡片
    enable: true # 是否启用中控台归档卡片
    type: monthly # 归档类型，可选 yearly (按年) or monthly (按月)
    format: MMMM YYYY # 归档格式，例如 MMMM YYYY (七月 2023)
    order: -1 # 排序顺序，1 为升序，-1 为降序
    limit: 8 # 显示归档数量 (0 为所有)
    sort_order: # 排序顺序 (不要修改此设置，除非你知道其作用)

# dark mode (暗色模式)
darkmode:
  enable: true # 是否启用暗色模式功能
  # 切换亮色/暗色模式的按钮
  button: true # 是否显示暗色模式切换按钮
  # 自动切换 dark mode 和 light mode
  # 跟随系统设置，如果系统不支持暗色模式，则在晚上 6 点到早上 6 点之间切换到暗色模式
  # autoChangeMode: 1
  # 在晚上 6 点到早上 6 点之间切换到暗色模式
  # autoChangeMode: 2
  # 关闭自动切换
  # autoChangeMode: false
  autoChangeMode: 1 # 自动切换模式
  start: # 自动切换到暗色模式的开始时间 (小时，例如 18)
  end: # 自动切换到暗色模式的结束时间 (小时，例如 6)

# 代码运行器配置
code_runner:
  enable: true # 是否启用代码运行器功能
  title: "代码运行器" # 面板标题
  button_title: "代码运行器" # 按钮提示文字
  panel_width: "600px" # 面板宽度
  auto_load_first: true # 是否自动加载第一个实例
  close_on_escape: true # 是否支持ESC键关闭
  remember_selection: true # 是否记住用户选择

  # 服务商分类配置
  categories:
    # 第一个分类：Trinket
    - name: "Trinket"
      icon: "fas fa-leaf"
      description: "适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"
      instances:
        - name: "Python 3"
          url: "https://trinket.io/embed/python3/f417f7026885"
          description: "Python 3 在线编程环境"
        - name: "HTML/CSS/JS"
          url: "https://trinket.io/embed/html/1aac0e8640a7"
          description: "前端三件套在线编辑器"
        - name: "Java"
          url: "https://trinket.io/embed/java/33cfa8ec292c"
          description: "Java 在线编程环境"

    # 第二个分类：JDoodle
    - name: "JDoodle"
      icon: "fas fa-terminal"
      description: "支持70+种编程语言，功能强大的在线编译器"
      instances:
        - name: "C++ Compiler"
          url: "https://www.jdoodle.com/online-compiler-c++/"
          description: "C++ 在线编译器"
        - name: "Java Compiler"
          url: "https://www.jdoodle.com/online-java-compiler/"
          description: "Java 在线编译器"
        - name: "Python 3"
          url: "https://www.jdoodle.com/python3-programming-online/"
          description: "Python 3 在线编程"
        - name: "TypeScript 在线编译器"
          url: "https://www.jdoodle.com/embed/v1/f40f7676c55c09b"
          description: "TypeScript"
        - name: "SQL 在线编译器"
          url: "https://www.jdoodle.com/embed/v1/510d607e22ee9e7c"
          description: "SQL 在线编译器"
        - name: "PHP 在线编译器"
          url: "https://www.jdoodle.com/embed/v1/ca85d333af57a5fc"
          description: "PHP 在线编译器"
        - name: "C语言 在线编译器"
          url: "https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248"
          description: "C语言 在线编译器"

# 非必要请不要修改
# 可选的右下角按钮项
# 不要重复
rightside_item_order: # 右下角按钮顺序和显示控制
  enable: true # 是否启用自定义右下角按钮顺序
  hide: readmode,translate,darkmode,hideAside # 要隐藏的按钮列表
  show: toc,chat,comment,downloadMd,docToc,codeRunner # 要显示的按钮列表 (如果启用自定义顺序，只列出的会显示)

# 文档式左侧目录 (Document-style left sidebar TOC)
doc_toc:
  enable: true # 是否启用文档式左侧目录
  title: "文档目录" # 侧边栏标题
  subtitle: "快速导航" # 侧边栏副标题

# Lightbox (图片大图查看模式)
# --------------------------------------
# 只能选择一个 或者 两个都不选

# medium-zoom (Medium Zoom 灯箱效果)
# https://github.com/francoischalifour/medium-zoom
medium_zoom: false # 是否启用 medium-zoom

# fancybox (Fancybox 3 灯箱效果)
# http://fancyapps.com/fancybox/3/
fancybox: true # 是否启用 fancybox

# 标签外挂设置
# --------------------------------------

# mermaid (Mermaid 图形渲染)
# see https://github.com/mermaid-js/mermaid
mermaid:
  enable: true # 是否启用 Mermaid 图形渲染
  # 内置主题
  theme: # 主题
    light: default # Light mode 主题
    dark: dark # Dark mode 主题

# Note (Bootstrap Callout / 提示框)
note:
  # Note tag style values: (提示框样式值)
  #  - simple    bs-callout old alert style. Default. (简洁风格)
  #  - modern    bs-callout new (v2-v3) alert style. (现代风格)
  #  - flat      flat callout style with background, like on Mozilla or StackOverflow. (扁平风格带背景)
  #  - disabled  disable all CSS styles import of note tag. (禁用所有样式导入)
  style: flat # 提示框样式
  icons: true # 是否显示图标
  border_radius: 3 # 边框圆角半径
  # Offset lighter of background in % for modern and flat styles (modern: -12 | 12; flat: -18 | 6). (现代和扁平风格背景颜色偏移百分比)
  # Offset also applied to label tag variables. This option can work with disabled note tag. (偏移也应用于标签变量。此选项在禁用 note 标签时也有效)
  light_bg_offset: 0 # 背景亮度偏移

icons:
  ali_iconfont_js: # 阿里图标 symbol 引用链接，主题会进行加载 symbol 引用
  fontawesome: true # 是否启用 fontawesome6 图标库
  fontawesome_animation_css: # fontawesome_animation 如果有就会加载，示例值：https://npm.elemecdn.com/hexo-butterfly-tag-plugins-plus@1.0.17/lib/assets/font-awesome-animation.min.css (fontawesome 动画 CSS 链接)

# other (其他设置)
# --------------------------------------

# Pjax (页面无刷新加载)
# It may contain bugs and unstable, give feedback when you find the bugs. (可能包含 bug 不稳定，发现 bug 时请反馈)
# https://github.com/MoOx/pjax
pjax:
  enable: true # 是否启用 Pjax 无刷新加载
  exclude: # 排除使用 Pjax 的页面路径列表 (使用示例)
# - xxxx
# - xxxx

# Inject the css and script (aplayer/meting) (注入 CSS 和脚本，如 Aplayer/Meting)
aplayerInject:
  enable: true # 是否启用 Aplayer/Meting 脚本注入
  per_page: true # 是否只在文章 frontmatter 中设置 aplayer: true / meting: true 的页面注入脚本

# Snackbar (Toast Notification 弹窗提示)
# https://github.com/polonel/SnackBar
# position 弹窗位置
# 可选 top-left / top-center / top-right / bottom-left / bottom-center / bottom-right
snackbar:
  enable: true # 是否启用 Snackbar 弹窗提示
  position: top-center # 弹窗位置
  bg_light: "#425AEF" # 亮色模式下弹窗背景颜色
  bg_dark: "#1f1f1f" # 暗色模式下弹窗背景颜色

# https://instant.page/ (链接预加载)
# prefetch (预加载)
instantpage: true # 是否启用 InstantClick/InstantPage 链接预加载

# https://github.com/vinta/pangu.js (盘古计划，中英文之间添加空格)
# Insert a space between Chinese character and English character (中英文之间添加空格)
pangu:
  enable: true # 是否启用盘古计划
  field: site # 应用范围，可选 site (全站) / post (仅文章)

# Lazyload (图片懒加载)
# https://github.com/verlok/vanilla-lazyload
lazyload:
  enable: true # 是否启用图片懒加载
  field: post # 应用范围，可选 site (全站) / post (仅文章)
  placeholder: "https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" # 图片加载前的占位符图片url (可选)
  blur: false # 图片加载前是否显示模糊效果
  progressive: true # 是否启用渐进式加载 (先加载模糊低质量图，再加载清晰图)

# PWA (渐进式 Web 应用)
# See https://github.com/JLHwung/hexo-offline
# ---------------
pwa:
  enable: false # 是否启用 PWA
  startup_image_enable: true # 是否启用启动画面
  manifest: /manifest.json # manifest 文件路径
  theme_color: var(--anzhiyu-main) # 主题颜色 (用于浏览器界面元素)
  mask_icon: https://bu.dusays.com/2025/07/19/687b394cb439b.ico # Mask Icon 路径 (用于 Safari 固定标签页)
  apple_touch_icon: https://bu.dusays.com/2025/07/19/687b394cb439b.ico # Apple Touch Icon 路径
  bookmark_icon: https://bu.dusays.com/2025/07/19/687b394cb439b.ico # 书签图标路径
  favicon_32_32: https://bu.dusays.com/2025/07/19/687b394cb439b.ico # 32x32 favicon
  favicon_16_16: https://bu.dusays.com/2025/07/19/687b394cb439b.ico # 16x16 favicon

# Open graph meta tags (Open Graph 元标签)
# https://developers.facebook.com/docs/sharing/webmasters/
Open_Graph_meta: true # 是否启用 Open Graph meta 标签 (用于社交分享预览)

# Add the vendor prefixes to ensure compatibility (添加厂商前缀以确保兼容性)
css_prefix: true # 是否自动为 CSS 添加厂商前缀

# 首页顶部相关配置 (Homepage Top Section Settings)
home_top:
  enable: true # 开关，是否启用自定义首页顶部区域
  timemode: date # 日期显示模式，可选 date (创建日期) / updated (更新日期)
  title: 情似溪水入梦来 # 顶部区域主标题
  subTitle: 心随君意共飞扬 # 顶部区域副标题
  siteText: 技术栈系列 # 顶部区域网站文本
  category: # 自定义分类链接列表
    - name: 前端 # 分类名称
      path: /categories/前端开发/ # 分类页面路径
      shadow: var(--anzhiyu-shadow-blue) # 阴影颜色 (使用CSS变量)
      class: blue # CSS 类名 (用于自定义样式)
      icon: anzhiyu-icon-dove # 图标类名
    - name: 后端
      path: /categories/大学生涯/
      shadow: var(--anzhiyu-shadow-red)
      class: red
      icon: anzhiyu-icon-fire
    - name: 运维
      path: /categories/生活日常/
      shadow: var(--anzhiyu-shadow-green)
      class: green
      icon: anzhiyu-icon-book
  default_descr: 再怎么看我也不知道怎么描述它的啦！ # 默认描述文本 (当文章没有 description 时显示)
  swiper: # Swiper 轮播图配置
    enable: true # 是否启用 Swiper 轮播图
    swiper_css: https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css # swiper css 依赖
    swiper_js: https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.js # swiper js 依赖
  banner: # 顶部静态 banner 配置
    enable: true # 是否启用静态 banner
    tips: # 提示文本
    title: # 主标题
    image: https://bu.dusays.com/2025/07/27/68858bbe17047.png # 背景图片 url
    link: / # 点击链接地址

# 朋友圈配置 (友链朋友圈)
friends_vue:
  enable: false # 是否启用友链朋友圈功能
  vue_js: https://npm.elemecdn.com/anzhiyu-theme-static@1.1.1/friends/index.4f887d95.js # 友链朋友圈所需的 Vue JS 文件
  apiurl: # 友链朋友圈后端 API 地址
  top_tips: 使用 友链朋友圈 订阅友链最新文章 # 顶部提示文本
  top_background: # 顶部背景图片

# 深色模式粒子效果 canvas (Universe effect in dark mode)
universe:
  enable: true # 是否在暗色模式下启用宇宙粒子背景效果

# 页面卡片顶部气泡升起效果 (Bubble effect on card tops)
bubble:
  enable: true # 是否启用页面卡片顶部的气泡升起效果

#  控制台打印信息 (Console log messages)
console:
  enable: true # 是否在浏览器控制台打印主题相关信息

# 51a统计配置 (51.la Analytics)
LA:
  enable: false # 是否启用 51.la 统计
  ck: # 统计代码中的 ck 参数
  LingQueMonitorID: # 凌鹊监控 ID (可选)

# 标签标题动态变化 (Browser tab title change on visibility change)
diytitle:
  enable: false # 是否启用浏览器标签页标题变化效果
  leaveTitle: 点击返回Prorise的博客小站 # 离开当前标签页时显示的标题文本
  backTitle: 欢迎回到网站 # 返回当前标签页时显示的标题文本

# 留言弹幕配置
comment_barrage_config:
  enable: true # 是否启用评论弹幕功能
  # 同时最多显示弹幕数
  maxBarrage: 1 # 屏幕上同时显示的弹幕数量上限
  # 弹幕显示间隔时间ms
  barrageTime: 10000 # 两条弹幕之间的显示间隔时间 (毫秒)
  # token，在控制台中获取 (获取方式需参考主题或相关插件文档)
  accessToken: "b39753735ed0ed5ffeb771bc108e3158" # 评论弹幕的访问 token
  # 博主邮箱md5值 (用于标识博主弹幕)
  mailMd5: "ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a" # 博主邮箱的 MD5 值

# 左下角音乐配置项 (Bottom Left Music Player Configuration)
# https://github.com/metowolf/MetingJS
nav_music:
  enable: true # 是否启用左下角音乐播放器 (MetingJS)
  console_widescreen_music: false # 在宽屏状态下是否将音乐播放器显示在控制台内，而非左下角 (enable 为 true 时，控制台依然会显示)
  id: 3117791189 # 歌单或歌曲 ID (取决于 server 类型)
  server: netease # 音乐服务提供商，可选 netease (网易云音乐), tencent (QQ 音乐), kugou (酷狗音乐) 等
  volume: 0.7 # 默认音量 (0.0 ~ 1.0)
  all_playlist: https://music.163.com/#/playlist?id=3117791189 # 全部歌单链接 (点击播放器时跳转的链接)

# 路径为 /music 的音乐页面默认加载的歌单 1. nav_music 2. custom ( /music 页面的默认歌单来源)
music_page_default: nav_music # 可选 nav_music (使用 nav_music 配置的歌单) or custom (自定义歌单)

# 评论匿名邮箱 (Anonymous Email for Comments)
visitorMail:
  enable: false # 是否启用评论匿名邮箱功能 (评论时可选择使用匿名邮箱)
  mail: "" # 匿名邮箱地址 (可选)

# ptool 文章底部工具 (Post Bottom Tools)
ptool:
  enable: true # 是否启用文章底部工具栏
  share_mobile: true # 在移动端是否显示分享按钮
  share_weibo: true # 是否显示微博分享按钮
  share_copyurl: true # 是否显示复制链接按钮
  categories: true # 是否在底部工具栏显示分类信息
  mode: # 运营模式与责任说明 (不配置则不显示)

# 欢迎语配置 (Greeting Box Configuration)
greetingBox:
  enable: true # 开启后必须配置下面的list对应的时间段，不然会出现小白条 (开启后必须配置下面的 list 列表，否则可能显示异常)
  default: 晚上好👋 # 没有匹配到时间段时的默认欢迎语
  list: # 时间段欢迎语列表
    - greeting: 晚安😴 # 欢迎语文本
      startTime: 0 # 开始时间 (小时，24小时制)
      endTime: 5 # 结束时间 (小时，24小时制)
    - greeting: 早上好鸭👋, 祝你一天好心情！
      startTime: 6
      endTime: 9
    - greeting: 上午好👋, 状态很好，鼓励一下～
      startTime: 10
      endTime: 10
    - greeting: 11点多啦, 在坚持一下就吃饭啦～
      startTime: 11
      endTime: 11
    - greeting: 午安👋, 宝贝
      startTime: 12
      endTime: 14
    - greeting: 🌈充实的一天辛苦啦！
      startTime: 14
      endTime: 18
    - greeting: 19点喽, 奖励一顿丰盛的大餐吧🍔。
      startTime: 19
      endTime: 19
    - greeting: 晚上好👋, 在属于自己的时间好好放松😌~
      startTime: 20
      endTime: 24

# 文章顶部ai摘要 (Post Head AI Description)
post_head_ai_description:
  enable: true # 是否启用文章顶部 AI 摘要功能
  gptName: Prorise # AI 的名称 (显示在摘要前)
  mode: tianli # 摘要模式，可选值: tianli (调用 Tianli API) / local (本地处理，需要配置 key)
  switchBtn: false # 是否显示切换模式按钮 (在 tianli 和 local 之间切换)
  btnLink: https://afdian.net/item/886a79d4db6711eda42a52540025c377 # 切换模式按钮的链接 (可选)
  randomNum: 3 # 按钮最大的随机次数，也就是一篇文章最大随机出来几种 (切换模式按钮随机显示的文本种类数量)
  basicWordCount: 1000 # 最低获取字符数, 最小1000, 最大1999 (用于生成摘要的文章内容最低字数要求)
  key: S-DNA1JM95BX2F9L0N # API Key (当 mode 为 local 或 tianli 时可能需要，具体取决于实现)
  Referer: https://xx.xx/ # Referer 请求头 (可选)

# 快捷键配置 (Shortcut Key Configuration)
shortcutKey:
  enable: false # 是否启用快捷键功能
  delay: 100 # 所有键位延时触发而不是立即触发（包括shift，以解决和浏览器键位冲突问题）(所有快捷键触发的延迟时间，毫秒)
  shiftDelay: 200 # shift 按下延时多久开启 (shift 键按下后，等待多久开始检测其他快捷键组合)

# 无障碍优化（在首页按下「shift + ?」以查看效果）(Accessibility Optimization)
accesskey:
  enable: false # 是否启用无障碍优化功能 (按下 shift + ? 可查看说明)

# 友情链接顶部相关配置 (Friend Link Page Top Section Settings)
linkPageTop:
  enable: true # 是否启用友链页面顶部区域自定义内容
  title: 与数百名博主无限进步 # 友链页面顶部主标题
  # 添加博主友链的评论自定义格式 (在友链页面评论区显示的占位文本，引导评论者填写友链信息)
  addFriendPlaceholder: "昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"

# 缩略图后缀 archive/tag/category 页面单独开启后缀 (Thumbnail Suffix)
pageThumbnailSuffix: "" # 归档、标签、分类页面文章缩略图 URL 的后缀 (可选，用于 CDN 图片处理等)

# 隐私协议弹窗 (Privacy Agreement Popup)
agreementPopup:
  enable: false # 是否启用隐私协议弹窗
  url: /privacy # 隐私协议页面链接

# 右键菜单 (Custom Right Click Menu)
rightClickMenu:
  enable: true # 是否启用自定义右键菜单

# 首页随便逛逛people模式 而非技能点模式，关闭后为技能点模式需要配置creativity.yml (Homepage "People" Mode Background)
peoplecanvas:
  enable: false # 是否在首页启用人物背景模式 (取代技能点模式，需要配合 people.yml 配置)
  img: https://upload-bbs.miyoushe.com/upload/2024/07/27/125766904/ba62475f396df9de3316a08ed9e65d86_5680958632268053399..png # 人物背景图片url

# 动效 (Dynamic Effects)
dynamicEffect:
  postTopWave: true # 文章顶部是否启用波浪效果
  postTopRollZoomInfo: true # 文章顶部区域在滚动时是否缩放信息 (标题、日期等)
  pageCommentsRollZoom: true # 非文章页面 (如独立页) 的评论区域在滚动时是否缩放显示 (目前仅 Twikoo 生效)

inject:
  head:
    # 只保留全站必需的基础资源
    - '<link rel="stylesheet" href="/css/font.css">'

  bottom:
    # 轻量级按需加载资源管理器（全站必需）- 负责所有其他资源的按需加载
    - '<script src="/js/load-on-demand.js"></script>'

# CDN (CDN Settings)
# Don't modify the following settings unless you know how they work (非必要请不要修改)
# 非必要请不要修改
CDN:
  # The CDN provider of internal scripts (主题内部 JS 文件的 CDN 配置)
  # option: local/elemecdn/jsdelivr/unpkg/cdnjs/onmicrosoft/cbd/anheyu/custom (可选 CDN 提供商)
  # Dev version can only choose. ( dev版的主题只能设置为 local )
  internal_provider: local # 主题内部脚本的 CDN 提供商

  # The CDN provider of third party scripts (第三方 JS 库的 CDN 配置)
  # option: elemecdn/jsdelivr/unpkg/cdnjs/onmicrosoft/cbd/anheyu/custom (可选 CDN 提供商)
  third_party_provider: cbd # 第三方库的 CDN 提供商

  # Add version number to CDN, true or false (是否在 CDN 链接中包含版本号)
  version: true # 是否在 CDN 链接中包含版本号

  # Custom format (自定义 CDN 格式)
  # For example: https://cdn.staticfile.org/${cdnjs_name}/${version}/${min_cdnjs_file} # 示例格式
  custom_format: # https://npm.elemecdn.com/${name}@latest/${file} # 自定义 CDN 格式字符串

  option:
    # --- 核心与基础库 ---
    # 主题内部核心JS/CSS文件，如 main.js, utils.js 等，建议保持 local，由 Hexo 直接生成，以确保主题功能稳定。
    # main_css:
    # main: ""
    # utils: ""
    # jquery: ""
    pjax: https://lib.baomitu.com/pjax/0.2.8/pjax.min.js

    # --- 页面功能与特效 ---
    lazyload: https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js
    instantpage: https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js
    typed: https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js
    pangu: https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js
    # fancybox: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/fancybox/3.5.7/jquery.fancybox.min.js
    # fancybox_css: https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/fancybox/3.5.7/jquery.fancybox.min.css
    medium_zoom: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/medium-zoom/1.0.6/medium-zoom.min.js
    snackbar: https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js
    snackbar_css: https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css
    fontawesome: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css

    # --- 评论系统 ---
    # valine: # 如有需要，可自行查找Valine的CDN链接
    # twikoo: ""
    # waline_js: ""
    #waline_css: ""
    # artalk_js: # 如有需要，可自行查找Artalk的CDN链接
    # artalk_css:

    # --- 搜索系统 ---
    # local_search: # 本地搜索通常与主题内部JS关联，建议保持local
    #algolia_search: ""

    # --- 音乐播放器 ---
    aplayer_css: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css
    aplayer_js: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js
    meting_js: https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js

    sharejs: https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js

    # --- 其他不常用或建议保持默认的库 ---
    # mathjax:
    # katex:
    # katex_copytex:
    # mermaid:
    # busuanzi: # 不蒜子官方脚本通常不建议替换
