<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>22.内容扩展：创建“实用网站”导航页 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="博客搭建教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="22.内容扩展：创建“实用网站”导航页"><meta name="application-name" content="22.内容扩展：创建“实用网站”导航页"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="22.内容扩展：创建“实用网站”导航页"><meta property="og:url" content="https://prorise666.site/posts/49291.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="22.内容扩展：创建“实用网站”导航页前言：功能介绍本指南将引导您创建一个高度结构化、美观且易于维护的“实用网站”导航页面。我们将实现以下效果：  Tab 切换分类：将您不同类别的网站链接，放入可以点击切换的Tab选项卡中，让页面保持整洁。 数据集中管理：将所有的链接数据，统一存放在一个独立的 ym"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp"><meta name="description" content="22.内容扩展：创建“实用网站”导航页前言：功能介绍本指南将引导您创建一个高度结构化、美观且易于维护的“实用网站”导航页面。我们将实现以下效果：  Tab 切换分类：将您不同类别的网站链接，放入可以点击切换的Tab选项卡中，让页面保持整洁。 数据集中管理：将所有的链接数据，统一存放在一个独立的 ym"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/49291.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"22.内容扩展：创建“实用网站”导航页",postAI:"true",pageFillDescription:"22.内容扩展：创建实用网站导航页, 前言：功能介绍, 第一步：创建并编辑您的链接数据文件, 第二步：创建页面并使用魔法代码, 第三步：在菜单中添加入口内容扩展创建实用网站导航页前言功能介绍本指南将引导您创建一个高度结构化美观且易于维护的实用网站导航页面我们将实现以下效果切换分类将您不同类别的网站链接放入可以点击切换的选项卡中让页面保持整洁数据集中管理将所有的链接数据统一存放在一个独立的文件中方便您未来随时增删和修改而无需触碰页面代码样式自动化完全利用主题内置的和外挂标签自动生成美观的卡片布局无需您编写任何额外的第一步创建并编辑您的链接数据文件这是本方案的核心我们将所有链接数据都存放在这里创建数据文件在您博客的文件夹内新建一个名为的文件编辑文件将您所有的网站链接按照下面的格式进行分类和整理您可以创建任意多个分类前端开发前端开发者的权威参考手册关于的一切技巧教程和灵感设计与灵感全球顶尖设计师的作品展示平台发现和收藏创意灵感的视觉探索工具云服务与部署前端项目的自动化部署与托管平台格式说明最外层是一个列表每一项以开头都包含一个分类名和一个该分类下的链接列表第二步创建页面并使用魔法代码创建页面文件在终端运行命令或您喜欢的任何名字修改页面打开新生成的文件设置好它的实用网站导航创建模板文件在将下面这段代码完整地复制粘贴进去添加搜索功能区域搜索实用网站全部荐暂无实用网站数据在新增页面块在这里新增创建搜索功能的文件实用网站导航搜索功能初始化实用网站导航搜索功能从页面中提取网站数据加载了个网站数据搜索输入框事件回车键搜索分类筛选事件如果不是全部则滚动到对应分类根据当前分类过滤高亮显示匹配的关键词记录该分类下有可见项目显示隐藏分类标题显示所有分类标题使用主题自带的平滑滚动函数预留搜索框高度备用滚动方案找到个包含的网站分类下共个网站共个实用网站防抖函数页面加载完成后初始化搜索功能确保在实用网站导航页面才初始化支持刷新创建对应的样式文件实用网站导航搜索功能样式适配主题搜索容器搜索头部搜索框样式搜索图标分类过滤器搜索统计信息搜索结果高亮隐藏不匹配的项目响应式设计深色模式适配动画效果搜索框聚焦动画加载状态无结果状态滚动到顶部按钮增强搜索时显示分类标题动画搜索结果项目动画高亮动画可以使用直接注入和但是我们这里就用自己写的按需载入脚本了按需加载资源管理器用于优化网站性能只在需要时加载特定资源动态加载文件文件路径可选的元素动态加载文件文件路径可选的元素检测页面内容并按需加载相关资源检测是否为首页修复现在由头部优先加载只需加载检测是否为文章页检测站视频内容检测代码块检测评论区检测即刻短文页面检测待办清单页面检测实用网站导航页面检测侧边栏相关功能侧边栏脚本加载失败创建全局实例页面加载完成后自动检测为提供支持第三步在菜单中添加入口打开您主题的配置文件在部分添加一个新链接指向我们刚刚创建的页面百宝箱或者您喜欢的任何名字实用网站",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-19 19:25:44",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-3"><a class="toc-link" href="#22-%E5%86%85%E5%AE%B9%E6%89%A9%E5%B1%95%EF%BC%9A%E5%88%9B%E5%BB%BA%E2%80%9C%E5%AE%9E%E7%94%A8%E7%BD%91%E7%AB%99%E2%80%9D%E5%AF%BC%E8%88%AA%E9%A1%B5"><span class="toc-text">22.内容扩展：创建“实用网站”导航页</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%89%8D%E8%A8%80%EF%BC%9A%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D"><span class="toc-text">前言：功能介绍</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E5%88%9B%E5%BB%BA%E5%B9%B6%E7%BC%96%E8%BE%91%E6%82%A8%E7%9A%84%E9%93%BE%E6%8E%A5%E6%95%B0%E6%8D%AE%E6%96%87%E4%BB%B6"><span class="toc-text">第一步：创建并编辑您的链接数据文件</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E5%88%9B%E5%BB%BA%E9%A1%B5%E9%9D%A2%E5%B9%B6%E4%BD%BF%E7%94%A8%E2%80%9C%E9%AD%94%E6%B3%95%E4%BB%A3%E7%A0%81%E2%80%9D"><span class="toc-text">第二步：创建页面并使用“魔法代码”</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E6%AD%A5%EF%BC%9A%E5%9C%A8%E8%8F%9C%E5%8D%95%E4%B8%AD%E6%B7%BB%E5%8A%A0%E5%85%A5%E5%8F%A3"><span class="toc-text">第三步：在菜单中添加入口</span></a></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/%E9%AD%94%E6%94%B9/" itemprop="url">魔改</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>博客搭建教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">22.内容扩展：创建“实用网站”导航页</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-11T06:13:45.000Z" title="发表于 2025-07-11 14:13:45">2025-07-11</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-19T11:25:44.289Z" title="更新于 2025-07-19 19:25:44">2025-07-19</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">4.2k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>22分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="22.内容扩展：创建“实用网站”导航页"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/49291.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/49291.html"><header><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/%E9%AD%94%E6%94%B9/" itemprop="url">魔改</a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">博客搭建教程</a><h1 id="CrawlerTitle" itemprop="name headline">22.内容扩展：创建“实用网站”导航页</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-11T06:13:45.000Z" title="发表于 2025-07-11 14:13:45">2025-07-11</time><time itemprop="dateCreated datePublished" datetime="2025-07-19T11:25:44.289Z" title="更新于 2025-07-19 19:25:44">2025-07-19</time></header><div id="postchat_postcontent"><h3 id="22-内容扩展：创建“实用网站”导航页"><a href="#22-内容扩展：创建“实用网站”导航页" class="headerlink" title="22.内容扩展：创建“实用网站”导航页"></a><strong>22.内容扩展：创建“实用网站”导航页</strong></h3><h6 id="前言：功能介绍"><a href="#前言：功能介绍" class="headerlink" title="前言：功能介绍"></a><strong>前言：功能介绍</strong></h6><p>本指南将引导您创建一个高度结构化、美观且易于维护的“实用网站”导航页面。我们将实现以下效果：</p><ol><li><strong>Tab 切换分类</strong>：将您不同类别的网站链接，放入可以点击切换的Tab选项卡中，让页面保持整洁。</li><li><strong>数据集中管理</strong>：将所有的链接数据，统一存放在一个独立的 <code>yml</code> 文件中，方便您未来随时增删和修改，而无需触碰页面代码。</li><li><strong>样式自动化</strong>：完全利用主题内置的 <code>{% tabs %}</code> 和 <code>{% link %}</code> 外挂标签，自动生成美观的卡片布局，无需您编写任何额外的CSS。</li></ol><hr><h6 id="第一步：创建并编辑您的链接数据文件"><a href="#第一步：创建并编辑您的链接数据文件" class="headerlink" title="第一步：创建并编辑您的链接数据文件"></a><strong>第一步：创建并编辑您的链接数据文件</strong></h6><p>这是本方案的核心。我们将所有链接数据都存放在这里。</p><ol><li><strong>创建数据文件 (<code>useful_links.yml</code>)</strong></li></ol><ul><li>在您博客的 <code>source/_data/</code> 文件夹内，新建一个名为 <code>useful_links.yml</code> 的文件。</li></ul><ol start="2"><li><p><strong>编辑 <code>useful_links.yml</code> 文件</strong></p><ul><li>将您所有的网站链接，按照下面的格式进行分类和整理。您可以创建任意多个分类。</li></ul><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># source/_data/useful_links.yml</span></span><br><span class="line"></span><br><span class="line"><span class="bullet">-</span> <span class="attr">category:</span> <span class="string">"前端开发"</span></span><br><span class="line">  <span class="attr">links:</span></span><br><span class="line">    <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"MDN Web Docs"</span></span><br><span class="line">      <span class="attr">descr:</span> <span class="string">"前端开发者的权威参考手册"</span></span><br><span class="line">      <span class="attr">link:</span> <span class="string">"https://developer.mozilla.org/zh-CN/"</span></span><br><span class="line">      <span class="attr">avatar:</span> <span class="string">"https://developer.mozilla.org/favicon.ico"</span></span><br><span class="line">    <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"CSS-Tricks"</span></span><br><span class="line">      <span class="attr">descr:</span> <span class="string">"关于CSS的一切，技巧、教程和灵感"</span></span><br><span class="line">      <span class="attr">link:</span> <span class="string">"https://css-tricks.com/"</span></span><br><span class="line">      <span class="attr">avatar:</span> <span class="string">"https://css-tricks.com/favicon.ico"</span></span><br><span class="line"></span><br><span class="line"><span class="bullet">-</span> <span class="attr">category:</span> <span class="string">"设计与灵感"</span></span><br><span class="line">  <span class="attr">links:</span></span><br><span class="line">    <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"Dribbble"</span></span><br><span class="line">      <span class="attr">descr:</span> <span class="string">"全球顶尖设计师的作品展示平台"</span></span><br><span class="line">      <span class="attr">link:</span> <span class="string">"https://dribbble.com/"</span></span><br><span class="line">      <span class="attr">avatar:</span> <span class="string">"https://cdn.dribbble.com/assets/favicon-63b2909422b16815e47346142d4d2b56.ico"</span></span><br><span class="line">    <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"Pinterest"</span></span><br><span class="line">      <span class="attr">descr:</span> <span class="string">"发现和收藏创意灵感的视觉探索工具"</span></span><br><span class="line">      <span class="attr">link:</span> <span class="string">"https://www.pinterest.com/"</span></span><br><span class="line">      <span class="attr">avatar:</span> <span class="string">"https://www.pinterest.com/favicon.ico"</span></span><br><span class="line"></span><br><span class="line"><span class="bullet">-</span> <span class="attr">category:</span> <span class="string">"云服务与部署"</span></span><br><span class="line">  <span class="attr">links:</span></span><br><span class="line">    <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"Vercel"</span></span><br><span class="line">      <span class="attr">descr:</span> <span class="string">"前端项目的自动化部署与托管平台"</span></span><br><span class="line">      <span class="attr">link:</span> <span class="string">"https://vercel.com/"</span></span><br><span class="line">      <span class="attr">avatar:</span> <span class="string">"https://assets.vercel.com/image/upload/front/favicon/vercel/favicon.ico"</span></span><br></pre></td></tr></tbody></table></figure><ul><li><strong>格式说明</strong>：最外层是一个列表，每一项（以 <code>-</code> 开头）都包含一个 <code>category</code>（分类名）和一个 <code>links</code>（该分类下的链接列表）。</li></ul></li></ol><hr><h6 id="第二步：创建页面并使用“魔法代码”"><a href="#第二步：创建页面并使用“魔法代码”" class="headerlink" title="第二步：创建页面并使用“魔法代码”"></a><strong>第二步：创建页面并使用“魔法代码”</strong></h6><ol><li><strong>创建页面文件</strong></li></ol><ul><li>在终端运行命令： <code>hexo new page awesome-links</code> (或您喜欢的任何名字)。</li></ul><ol start="2"><li><p><strong>修改页面 Front-matter</strong></p><ul><li>打开新生成的 <code>source/awesome-links/index.md</code> 文件，设置好它的Front-matter：</li></ul><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line"><span class="meta">---</span></span><br><span class="line"><span class="attr">title:</span> <span class="string">实用网站导航</span></span><br><span class="line"><span class="attr">date:</span> <span class="number">2025-06-17 18:00:00</span></span><br><span class="line"><span class="attr">comments:</span> <span class="literal">true</span></span><br><span class="line"><span class="attr">aside:</span> <span class="literal">false</span></span><br><span class="line"><span class="attr">type:</span> <span class="string">useful_links</span></span><br><span class="line"><span class="meta">---</span></span><br><span class="line"><span class="meta"></span></span><br><span class="line"><span class="meta"></span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>创建 Pug 模板文件</strong></p><ul><li>在 <code>themes/anzhiyu/layout/includes/page/useful_links.pug</code>，<strong>将下面这段代码完整地复制粘贴进去</strong>。</li></ul><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br></pre></td><td class="code"><pre><span class="line">#article-container</span><br><span class="line">  // 添加搜索功能区域</span><br><span class="line">  #useful-links-search-container</span><br><span class="line">    .search-header</span><br><span class="line">      .search-box</span><br><span class="line">        input#useful-links-search(type="text" placeholder="搜索实用网站..." autocomplete="off")</span><br><span class="line">        i.anzhiyufont.anzhiyu-icon-search.search-icon</span><br><span class="line">      .category-filters</span><br><span class="line">        .category-filter.active(data-category="all") 全部</span><br><span class="line">        if site.data.useful_links</span><br><span class="line">          each i in site.data.useful_links</span><br><span class="line">            if i.class_name</span><br><span class="line">              .category-filter(data-category=`${i.class_name}`)= i.class_name</span><br><span class="line">    .search-stats</span><br><span class="line">      span#search-results-count </span><br><span class="line"></span><br><span class="line">  .flink</span><br><span class="line">    if site.data.useful_links</span><br><span class="line">      each i in site.data.useful_links</span><br><span class="line">        if i.class_name</span><br><span class="line">          h2(data-category=`${i.class_name}`)!= i.class_name + "(" + i.link_list.length + ")"</span><br><span class="line">        if i.class_desc</span><br><span class="line">          .flink-desc!=i.class_desc</span><br><span class="line">        if i.flink_style === 'anzhiyu'</span><br><span class="line">          div(class=i.lost_contact ? 'anzhiyu-flink-list cf-friends-lost-contact' : 'anzhiyu-flink-list')</span><br><span class="line">            if i.link_list</span><br><span class="line">              each item in i.link_list</span><br><span class="line">                - let color = item.color || ""</span><br><span class="line">                - let tag = item.tag || ""</span><br><span class="line">                </span><br><span class="line">                .flink-list-item</span><br><span class="line">                  if color == "vip" &amp;&amp; tag</span><br><span class="line">                    span.site-card-tag.vip #[=tag]</span><br><span class="line">                      i.light</span><br><span class="line">                  else if color == "speed" &amp;&amp; tag</span><br><span class="line">                    span.site-card-tag.speed #[=ta3g]</span><br><span class="line">                  else if tag</span><br><span class="line">                    span.site-card-tag(style=`background-color: ${color}`) #[=tag]</span><br><span class="line">                  else if item.recommend</span><br><span class="line">                    span.site-card-tag 荐</span><br><span class="line">                  if i.lost_contact</span><br><span class="line">                    a.cf-friends-link(href=url_for(item.link) title=item.name target="_blank")</span><br><span class="line">                      if theme.lazyload.enable</span><br><span class="line">                        img.no-lightbox(data-lazy-src=url_for(item.avatar) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=item.name )</span><br><span class="line">                      else</span><br><span class="line">                        img.cf-friends-avatar.no-lightbox(src=url_for(item.avatar) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=item.name )</span><br><span class="line">                      .flink-item-info</span><br><span class="line">                        .flink-item-name.cf-friends-name-lost-contact= item.name</span><br><span class="line">                  else</span><br><span class="line">                    a.cf-friends-link(href=url_for(item.link) cf-href=url_for(item.link) title=item.name target="_blank")</span><br><span class="line">                      if theme.lazyload.enable</span><br><span class="line">                        img.cf-friends-avatar.no-lightbox(data-lazy-src=url_for(item.avatar), cf-src=url_for(item.avatar), onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=item.name )</span><br><span class="line">                      else</span><br><span class="line">                        img.cf-friends-avatar.no-lightbox(src=url_for(item.avatar) cf-src=url_for(item.avatar) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=item.name )</span><br><span class="line">                      .flink-item-info</span><br><span class="line">                        .flink-item-name.cf-friends-name= item.name</span><br><span class="line">                        .flink-item-desc(title=item.descr)= item.descr</span><br><span class="line">    else</span><br><span class="line">      .flink-null 暂无实用网站数据</span><br><span class="line">    != page.content </span><br></pre></td></tr></tbody></table></figure></li><li><p>在<code>themes/anzhiyu/layout/page.pug</code>新增页面块</p><figure class="highlight js"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line">when <span class="string">'todolist'</span></span><br><span class="line">  include includes/page/todolist.<span class="property">pug</span></span><br><span class="line">when <span class="string">'useful_links'</span> <span class="comment">// 在这里新增</span></span><br><span class="line">  include includes/page/useful_links.<span class="property">pug</span></span><br></pre></td></tr></tbody></table></figure></li><li><p>创建搜索功能的JavaScript文件<code>themes/anzhiyu/source/js/useful-links-search.js</code></p><figure class="highlight js"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br><span class="line">142</span><br><span class="line">143</span><br><span class="line">144</span><br><span class="line">145</span><br><span class="line">146</span><br><span class="line">147</span><br><span class="line">148</span><br><span class="line">149</span><br><span class="line">150</span><br><span class="line">151</span><br><span class="line">152</span><br><span class="line">153</span><br><span class="line">154</span><br><span class="line">155</span><br><span class="line">156</span><br><span class="line">157</span><br><span class="line">158</span><br><span class="line">159</span><br><span class="line">160</span><br><span class="line">161</span><br><span class="line">162</span><br><span class="line">163</span><br><span class="line">164</span><br><span class="line">165</span><br><span class="line">166</span><br><span class="line">167</span><br><span class="line">168</span><br><span class="line">169</span><br><span class="line">170</span><br><span class="line">171</span><br><span class="line">172</span><br><span class="line">173</span><br><span class="line">174</span><br><span class="line">175</span><br><span class="line">176</span><br><span class="line">177</span><br><span class="line">178</span><br><span class="line">179</span><br><span class="line">180</span><br><span class="line">181</span><br><span class="line">182</span><br><span class="line">183</span><br><span class="line">184</span><br><span class="line">185</span><br><span class="line">186</span><br><span class="line">187</span><br><span class="line">188</span><br><span class="line">189</span><br><span class="line">190</span><br><span class="line">191</span><br><span class="line">192</span><br><span class="line">193</span><br><span class="line">194</span><br><span class="line">195</span><br><span class="line">196</span><br><span class="line">197</span><br><span class="line">198</span><br><span class="line">199</span><br><span class="line">200</span><br><span class="line">201</span><br><span class="line">202</span><br><span class="line">203</span><br><span class="line">204</span><br><span class="line">205</span><br><span class="line">206</span><br><span class="line">207</span><br><span class="line">208</span><br><span class="line">209</span><br><span class="line">210</span><br><span class="line">211</span><br><span class="line">212</span><br><span class="line">213</span><br><span class="line">214</span><br><span class="line">215</span><br><span class="line">216</span><br><span class="line">217</span><br><span class="line">218</span><br><span class="line">219</span><br><span class="line">220</span><br><span class="line">221</span><br><span class="line">222</span><br><span class="line">223</span><br><span class="line">224</span><br><span class="line">225</span><br><span class="line">226</span><br><span class="line">227</span><br><span class="line">228</span><br><span class="line">229</span><br><span class="line">230</span><br><span class="line">231</span><br><span class="line">232</span><br><span class="line">233</span><br><span class="line">234</span><br><span class="line">235</span><br><span class="line">236</span><br><span class="line">237</span><br><span class="line">238</span><br><span class="line">239</span><br><span class="line">240</span><br><span class="line">241</span><br><span class="line">242</span><br><span class="line">243</span><br><span class="line">244</span><br><span class="line">245</span><br><span class="line">246</span><br><span class="line">247</span><br><span class="line">248</span><br><span class="line">249</span><br><span class="line">250</span><br><span class="line">251</span><br><span class="line">252</span><br><span class="line">253</span><br><span class="line">254</span><br><span class="line">255</span><br><span class="line">256</span><br><span class="line">257</span><br><span class="line">258</span><br><span class="line">259</span><br><span class="line">260</span><br><span class="line">261</span><br><span class="line">262</span><br><span class="line">263</span><br><span class="line">264</span><br><span class="line">265</span><br><span class="line">266</span><br><span class="line">267</span><br><span class="line">268</span><br><span class="line">269</span><br><span class="line">270</span><br><span class="line">271</span><br><span class="line">272</span><br><span class="line">273</span><br><span class="line">274</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 实用网站导航搜索功能</span></span><br><span class="line"><span class="comment"> * Author: Prorise</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">const</span> <span class="title class_">UsefulLinksSearch</span> = {</span><br><span class="line">    <span class="attr">data</span>: [],</span><br><span class="line">    <span class="attr">currentCategory</span>: <span class="string">'all'</span>,</span><br><span class="line"></span><br><span class="line">    <span class="title function_">init</span>(<span class="params"></span>) {</span><br><span class="line">        <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'初始化实用网站导航搜索功能...'</span>);</span><br><span class="line">        <span class="variable language_">this</span>.<span class="title function_">loadData</span>();</span><br><span class="line">        <span class="variable language_">this</span>.<span class="title function_">bindEvents</span>();</span><br><span class="line">        <span class="variable language_">this</span>.<span class="title function_">updateResultsCount</span>();</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="title function_">loadData</span>(<span class="params"></span>) {</span><br><span class="line">        <span class="comment">// 从页面中提取网站数据</span></span><br><span class="line">        <span class="keyword">const</span> linkItems = <span class="variable language_">document</span>.<span class="title function_">querySelectorAll</span>(<span class="string">'.anzhiyu-flink-list .flink-list-item'</span>);</span><br><span class="line">        <span class="variable language_">this</span>.<span class="property">data</span> = [];</span><br><span class="line"></span><br><span class="line">        linkItems.<span class="title function_">forEach</span>(<span class="function">(<span class="params">item, index</span>) =&gt;</span> {</span><br><span class="line">            <span class="keyword">const</span> nameEl = item.<span class="title function_">querySelector</span>(<span class="string">'.flink-item-name'</span>);</span><br><span class="line">            <span class="keyword">const</span> descEl = item.<span class="title function_">querySelector</span>(<span class="string">'.flink-item-desc'</span>);</span><br><span class="line">            <span class="keyword">const</span> linkEl = item.<span class="title function_">querySelector</span>(<span class="string">'.cf-friends-link'</span>);</span><br><span class="line">            <span class="keyword">const</span> categoryEl = item.<span class="title function_">closest</span>(<span class="string">'.flink'</span>).<span class="title function_">querySelector</span>(<span class="string">'h2'</span>);</span><br><span class="line"></span><br><span class="line">            <span class="keyword">if</span> (nameEl &amp;&amp; linkEl) {</span><br><span class="line">                <span class="variable language_">this</span>.<span class="property">data</span>.<span class="title function_">push</span>({</span><br><span class="line">                    <span class="attr">index</span>: index,</span><br><span class="line">                    <span class="attr">name</span>: nameEl.<span class="property">textContent</span>.<span class="title function_">trim</span>(),</span><br><span class="line">                    <span class="attr">description</span>: descEl ? descEl.<span class="property">textContent</span>.<span class="title function_">trim</span>() : <span class="string">''</span>,</span><br><span class="line">                    <span class="attr">link</span>: linkEl.<span class="property">href</span>,</span><br><span class="line">                    <span class="attr">category</span>: categoryEl ? categoryEl.<span class="property">dataset</span>.<span class="property">category</span> : <span class="string">''</span>,</span><br><span class="line">                    <span class="attr">element</span>: item,</span><br><span class="line">                    <span class="attr">categoryElement</span>: categoryEl</span><br><span class="line">                });</span><br><span class="line">            }</span><br><span class="line">        });</span><br><span class="line"></span><br><span class="line">        <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">`加载了 <span class="subst">${<span class="variable language_">this</span>.data.length}</span> 个网站数据`</span>);</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="title function_">bindEvents</span>(<span class="params"></span>) {</span><br><span class="line">        <span class="keyword">const</span> searchInput = <span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'useful-links-search'</span>);</span><br><span class="line">        <span class="keyword">const</span> categoryFilters = <span class="variable language_">document</span>.<span class="title function_">querySelectorAll</span>(<span class="string">'.category-filter'</span>);</span><br><span class="line"></span><br><span class="line">        <span class="keyword">if</span> (searchInput) {</span><br><span class="line">            <span class="comment">// 搜索输入框事件</span></span><br><span class="line">            searchInput.<span class="title function_">addEventListener</span>(<span class="string">'input'</span>, <span class="variable language_">this</span>.<span class="title function_">debounce</span>(<span class="function">(<span class="params">e</span>) =&gt;</span> {</span><br><span class="line">                <span class="variable language_">this</span>.<span class="title function_">performSearch</span>(e.<span class="property">target</span>.<span class="property">value</span>);</span><br><span class="line">            }, <span class="number">300</span>));</span><br><span class="line"></span><br><span class="line">            <span class="comment">// 回车键搜索</span></span><br><span class="line">            searchInput.<span class="title function_">addEventListener</span>(<span class="string">'keydown'</span>, <span class="function">(<span class="params">e</span>) =&gt;</span> {</span><br><span class="line">                <span class="keyword">if</span> (e.<span class="property">key</span> === <span class="string">'Enter'</span>) {</span><br><span class="line">                    e.<span class="title function_">preventDefault</span>();</span><br><span class="line">                    <span class="variable language_">this</span>.<span class="title function_">performSearch</span>(e.<span class="property">target</span>.<span class="property">value</span>);</span><br><span class="line">                }</span><br><span class="line">            });</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 分类筛选事件</span></span><br><span class="line">        categoryFilters.<span class="title function_">forEach</span>(<span class="function"><span class="params">filter</span> =&gt;</span> {</span><br><span class="line">            filter.<span class="title function_">addEventListener</span>(<span class="string">'click'</span>, <span class="function">(<span class="params">e</span>) =&gt;</span> {</span><br><span class="line">                <span class="keyword">const</span> category = e.<span class="property">target</span>.<span class="property">dataset</span>.<span class="property">category</span>;</span><br><span class="line">                <span class="variable language_">this</span>.<span class="title function_">filterByCategory</span>(category);</span><br><span class="line">                <span class="variable language_">this</span>.<span class="title function_">updateActiveFilter</span>(e.<span class="property">target</span>);</span><br><span class="line"></span><br><span class="line">                <span class="comment">// 如果不是"全部"，则滚动到对应分类</span></span><br><span class="line">                <span class="keyword">if</span> (category !== <span class="string">'all'</span>) {</span><br><span class="line">                    <span class="variable language_">this</span>.<span class="title function_">scrollToCategory</span>(category);</span><br><span class="line">                }</span><br><span class="line">            });</span><br><span class="line">        });</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="title function_">performSearch</span>(<span class="params">keyword</span>) {</span><br><span class="line">        keyword = keyword.<span class="title function_">toLowerCase</span>().<span class="title function_">trim</span>();</span><br><span class="line"></span><br><span class="line">        <span class="keyword">if</span> (keyword === <span class="string">''</span>) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">showAllItems</span>();</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">updateResultsCount</span>();</span><br><span class="line">            <span class="keyword">return</span>;</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="keyword">let</span> visibleCount = <span class="number">0</span>;</span><br><span class="line">        <span class="keyword">const</span> categoryVisibility = {};</span><br><span class="line"></span><br><span class="line">        <span class="variable language_">this</span>.<span class="property">data</span>.<span class="title function_">forEach</span>(<span class="function"><span class="params">item</span> =&gt;</span> {</span><br><span class="line">            <span class="keyword">const</span> matchName = item.<span class="property">name</span>.<span class="title function_">toLowerCase</span>().<span class="title function_">includes</span>(keyword);</span><br><span class="line">            <span class="keyword">const</span> matchDesc = item.<span class="property">description</span>.<span class="title function_">toLowerCase</span>().<span class="title function_">includes</span>(keyword);</span><br><span class="line">            <span class="keyword">const</span> isVisible = matchName || matchDesc;</span><br><span class="line"></span><br><span class="line">            <span class="comment">// 根据当前分类过滤</span></span><br><span class="line">            <span class="keyword">const</span> categoryMatch = <span class="variable language_">this</span>.<span class="property">currentCategory</span> === <span class="string">'all'</span> || item.<span class="property">category</span> === <span class="variable language_">this</span>.<span class="property">currentCategory</span>;</span><br><span class="line">            <span class="keyword">const</span> shouldShow = isVisible &amp;&amp; categoryMatch;</span><br><span class="line"></span><br><span class="line">            <span class="keyword">if</span> (shouldShow) {</span><br><span class="line">                visibleCount++;</span><br><span class="line">                <span class="comment">// 高亮显示匹配的关键词</span></span><br><span class="line">                <span class="variable language_">this</span>.<span class="title function_">highlightKeyword</span>(item, keyword);</span><br><span class="line">                item.<span class="property">element</span>.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'hidden'</span>);</span><br><span class="line"></span><br><span class="line">                <span class="comment">// 记录该分类下有可见项目</span></span><br><span class="line">                categoryVisibility[item.<span class="property">category</span>] = <span class="literal">true</span>;</span><br><span class="line">            } <span class="keyword">else</span> {</span><br><span class="line">                item.<span class="property">element</span>.<span class="property">classList</span>.<span class="title function_">add</span>(<span class="string">'hidden'</span>);</span><br><span class="line">            }</span><br><span class="line">        });</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 显示/隐藏分类标题</span></span><br><span class="line">        <span class="variable language_">this</span>.<span class="title function_">toggleCategoryTitles</span>(categoryVisibility);</span><br><span class="line">        <span class="variable language_">this</span>.<span class="title function_">updateResultsCount</span>(visibleCount, keyword);</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="title function_">filterByCategory</span>(<span class="params">category</span>) {</span><br><span class="line">        <span class="variable language_">this</span>.<span class="property">currentCategory</span> = category;</span><br><span class="line">        <span class="keyword">const</span> searchInput = <span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'useful-links-search'</span>);</span><br><span class="line">        <span class="keyword">const</span> keyword = searchInput ? searchInput.<span class="property">value</span> : <span class="string">''</span>;</span><br><span class="line"></span><br><span class="line">        <span class="keyword">if</span> (keyword) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">performSearch</span>(keyword);</span><br><span class="line">        } <span class="keyword">else</span> {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">showCategoryItems</span>(category);</span><br><span class="line">        }</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="title function_">showCategoryItems</span>(<span class="params">category</span>) {</span><br><span class="line">        <span class="keyword">let</span> visibleCount = <span class="number">0</span>;</span><br><span class="line">        <span class="keyword">const</span> categoryVisibility = {};</span><br><span class="line"></span><br><span class="line">        <span class="variable language_">this</span>.<span class="property">data</span>.<span class="title function_">forEach</span>(<span class="function"><span class="params">item</span> =&gt;</span> {</span><br><span class="line">            <span class="keyword">const</span> shouldShow = category === <span class="string">'all'</span> || item.<span class="property">category</span> === category;</span><br><span class="line"></span><br><span class="line">            <span class="keyword">if</span> (shouldShow) {</span><br><span class="line">                visibleCount++;</span><br><span class="line">                item.<span class="property">element</span>.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'hidden'</span>);</span><br><span class="line">                categoryVisibility[item.<span class="property">category</span>] = <span class="literal">true</span>;</span><br><span class="line">            } <span class="keyword">else</span> {</span><br><span class="line">                item.<span class="property">element</span>.<span class="property">classList</span>.<span class="title function_">add</span>(<span class="string">'hidden'</span>);</span><br><span class="line">            }</span><br><span class="line">        });</span><br><span class="line"></span><br><span class="line">        <span class="variable language_">this</span>.<span class="title function_">toggleCategoryTitles</span>(categoryVisibility);</span><br><span class="line">        <span class="variable language_">this</span>.<span class="title function_">updateResultsCount</span>(visibleCount);</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="title function_">showAllItems</span>(<span class="params"></span>) {</span><br><span class="line">        <span class="variable language_">this</span>.<span class="property">data</span>.<span class="title function_">forEach</span>(<span class="function"><span class="params">item</span> =&gt;</span> {</span><br><span class="line">            item.<span class="property">element</span>.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'hidden'</span>);</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">removeHighlight</span>(item);</span><br><span class="line">        });</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 显示所有分类标题</span></span><br><span class="line">        <span class="variable language_">document</span>.<span class="title function_">querySelectorAll</span>(<span class="string">'.flink h2'</span>).<span class="title function_">forEach</span>(<span class="function"><span class="params">title</span> =&gt;</span> {</span><br><span class="line">            title.<span class="property">style</span>.<span class="property">display</span> = <span class="string">''</span>;</span><br><span class="line">        });</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="title function_">highlightKeyword</span>(<span class="params">item, keyword</span>) {</span><br><span class="line">        <span class="keyword">const</span> nameEl = item.<span class="property">element</span>.<span class="title function_">querySelector</span>(<span class="string">'.flink-item-name'</span>);</span><br><span class="line">        <span class="keyword">const</span> descEl = item.<span class="property">element</span>.<span class="title function_">querySelector</span>(<span class="string">'.flink-item-desc'</span>);</span><br><span class="line"></span><br><span class="line">        <span class="keyword">if</span> (nameEl) {</span><br><span class="line">            nameEl.<span class="property">innerHTML</span> = <span class="variable language_">this</span>.<span class="title function_">highlightText</span>(item.<span class="property">name</span>, keyword);</span><br><span class="line">        }</span><br><span class="line">        <span class="keyword">if</span> (descEl) {</span><br><span class="line">            descEl.<span class="property">innerHTML</span> = <span class="variable language_">this</span>.<span class="title function_">highlightText</span>(item.<span class="property">description</span>, keyword);</span><br><span class="line">        }</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="title function_">removeHighlight</span>(<span class="params">item</span>) {</span><br><span class="line">        <span class="keyword">const</span> nameEl = item.<span class="property">element</span>.<span class="title function_">querySelector</span>(<span class="string">'.flink-item-name'</span>);</span><br><span class="line">        <span class="keyword">const</span> descEl = item.<span class="property">element</span>.<span class="title function_">querySelector</span>(<span class="string">'.flink-item-desc'</span>);</span><br><span class="line"></span><br><span class="line">        <span class="keyword">if</span> (nameEl) {</span><br><span class="line">            nameEl.<span class="property">textContent</span> = item.<span class="property">name</span>;</span><br><span class="line">        }</span><br><span class="line">        <span class="keyword">if</span> (descEl) {</span><br><span class="line">            descEl.<span class="property">textContent</span> = item.<span class="property">description</span>;</span><br><span class="line">        }</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="title function_">highlightText</span>(<span class="params">text, keyword</span>) {</span><br><span class="line">        <span class="keyword">if</span> (!text || !keyword) <span class="keyword">return</span> text;</span><br><span class="line"></span><br><span class="line">        <span class="keyword">const</span> regex = <span class="keyword">new</span> <span class="title class_">RegExp</span>(<span class="string">`(<span class="subst">${keyword}</span>)`</span>, <span class="string">'gi'</span>);</span><br><span class="line">        <span class="keyword">return</span> text.<span class="title function_">replace</span>(regex, <span class="string">'&lt;span class="search-highlight"&gt;$1&lt;/span&gt;'</span>);</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="title function_">toggleCategoryTitles</span>(<span class="params">categoryVisibility</span>) {</span><br><span class="line">        <span class="variable language_">document</span>.<span class="title function_">querySelectorAll</span>(<span class="string">'.flink h2'</span>).<span class="title function_">forEach</span>(<span class="function"><span class="params">title</span> =&gt;</span> {</span><br><span class="line">            <span class="keyword">const</span> category = title.<span class="property">dataset</span>.<span class="property">category</span>;</span><br><span class="line">            <span class="keyword">if</span> (<span class="variable language_">this</span>.<span class="property">currentCategory</span> === <span class="string">'all'</span>) {</span><br><span class="line">                title.<span class="property">style</span>.<span class="property">display</span> = categoryVisibility[category] ? <span class="string">''</span> : <span class="string">'none'</span>;</span><br><span class="line">            } <span class="keyword">else</span> {</span><br><span class="line">                title.<span class="property">style</span>.<span class="property">display</span> = category === <span class="variable language_">this</span>.<span class="property">currentCategory</span> ? <span class="string">''</span> : <span class="string">'none'</span>;</span><br><span class="line">            }</span><br><span class="line">        });</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="title function_">updateActiveFilter</span>(<span class="params">activeFilter</span>) {</span><br><span class="line">        <span class="variable language_">document</span>.<span class="title function_">querySelectorAll</span>(<span class="string">'.category-filter'</span>).<span class="title function_">forEach</span>(<span class="function"><span class="params">filter</span> =&gt;</span> {</span><br><span class="line">            filter.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'active'</span>);</span><br><span class="line">        });</span><br><span class="line">        activeFilter.<span class="property">classList</span>.<span class="title function_">add</span>(<span class="string">'active'</span>);</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="title function_">scrollToCategory</span>(<span class="params">categoryName</span>) {</span><br><span class="line">        <span class="keyword">const</span> targetTitle = <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">`h2[data-category="<span class="subst">${categoryName}</span>"]`</span>);</span><br><span class="line">        <span class="keyword">if</span> (targetTitle &amp;&amp; <span class="keyword">typeof</span> anzhiyu !== <span class="string">'undefined'</span> &amp;&amp; anzhiyu.<span class="property">scrollToDest</span>) {</span><br><span class="line">            <span class="comment">// 使用主题自带的平滑滚动函数</span></span><br><span class="line">            <span class="keyword">const</span> targetTop = anzhiyu.<span class="title function_">getEleTop</span>(targetTitle) - <span class="number">120</span>; <span class="comment">// 预留搜索框高度</span></span><br><span class="line">            anzhiyu.<span class="title function_">scrollToDest</span>(targetTop, <span class="number">500</span>);</span><br><span class="line">        } <span class="keyword">else</span> <span class="keyword">if</span> (targetTitle) {</span><br><span class="line">            <span class="comment">// 备用滚动方案</span></span><br><span class="line">            targetTitle.<span class="title function_">scrollIntoView</span>({</span><br><span class="line">                <span class="attr">behavior</span>: <span class="string">'smooth'</span>,</span><br><span class="line">                <span class="attr">block</span>: <span class="string">'start'</span>,</span><br><span class="line">                <span class="attr">inline</span>: <span class="string">'nearest'</span></span><br><span class="line">            });</span><br><span class="line">        }</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="title function_">updateResultsCount</span>(<span class="params">count = <span class="literal">null</span>, keyword = <span class="string">''</span></span>) {</span><br><span class="line">        <span class="keyword">const</span> statsEl = <span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'search-results-count'</span>);</span><br><span class="line">        <span class="keyword">if</span> (!statsEl) <span class="keyword">return</span>;</span><br><span class="line"></span><br><span class="line">        <span class="keyword">if</span> (count === <span class="literal">null</span>) {</span><br><span class="line">            count = <span class="variable language_">this</span>.<span class="property">data</span>.<span class="title function_">filter</span>(<span class="function"><span class="params">item</span> =&gt;</span> !item.<span class="property">element</span>.<span class="property">classList</span>.<span class="title function_">contains</span>(<span class="string">'hidden'</span>)).<span class="property">length</span>;</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="keyword">if</span> (keyword) {</span><br><span class="line">            statsEl.<span class="property">textContent</span> = <span class="string">`找到 <span class="subst">${count}</span> 个包含 "<span class="subst">${keyword}</span>" 的网站`</span>;</span><br><span class="line">        } <span class="keyword">else</span> <span class="keyword">if</span> (<span class="variable language_">this</span>.<span class="property">currentCategory</span> !== <span class="string">'all'</span>) {</span><br><span class="line">            statsEl.<span class="property">textContent</span> = <span class="string">`<span class="subst">${<span class="variable language_">this</span>.currentCategory}</span> 分类下共 <span class="subst">${count}</span> 个网站`</span>;</span><br><span class="line">        } <span class="keyword">else</span> {</span><br><span class="line">            statsEl.<span class="property">textContent</span> = <span class="string">`共 <span class="subst">${count}</span> 个实用网站`</span>;</span><br><span class="line">        }</span><br><span class="line">    },</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 防抖函数</span></span><br><span class="line">    <span class="title function_">debounce</span>(<span class="params">func, wait</span>) {</span><br><span class="line">        <span class="keyword">let</span> timeout;</span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">function</span> <span class="title function_">executedFunction</span>(<span class="params">...args</span>) {</span><br><span class="line">            <span class="keyword">const</span> <span class="title function_">later</span> = (<span class="params"></span>) =&gt; {</span><br><span class="line">                <span class="built_in">clearTimeout</span>(timeout);</span><br><span class="line">                <span class="title function_">func</span>(...args);</span><br><span class="line">            };</span><br><span class="line">            <span class="built_in">clearTimeout</span>(timeout);</span><br><span class="line">            timeout = <span class="built_in">setTimeout</span>(later, wait);</span><br><span class="line">        };</span><br><span class="line">    }</span><br><span class="line">};</span><br><span class="line"></span><br><span class="line"><span class="comment">// 页面加载完成后初始化搜索功能</span></span><br><span class="line"><span class="variable language_">document</span>.<span class="title function_">addEventListener</span>(<span class="string">'DOMContentLoaded'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">    <span class="comment">// 确保在实用网站导航页面才初始化</span></span><br><span class="line">    <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'useful-links-search-container'</span>)) {</span><br><span class="line">        <span class="title class_">UsefulLinksSearch</span>.<span class="title function_">init</span>();</span><br><span class="line">    }</span><br><span class="line">});</span><br><span class="line"></span><br><span class="line"><span class="comment">// 支持 PJAX 刷新</span></span><br><span class="line"><span class="keyword">if</span> (<span class="keyword">typeof</span> anzhiyu !== <span class="string">'undefined'</span>) {</span><br><span class="line">    anzhiyu.<span class="title function_">addGlobalFn</span>(<span class="string">'pjax'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'useful-links-search-container'</span>)) {</span><br><span class="line">            <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> {</span><br><span class="line">                <span class="title class_">UsefulLinksSearch</span>.<span class="title function_">init</span>();</span><br><span class="line">            }, <span class="number">100</span>);</span><br><span class="line">        }</span><br><span class="line">    });</span><br><span class="line">} </span><br></pre></td></tr></tbody></table></figure><p>6.创建对应的样式文件：<code>themes/anzhiyu/source/css/useful-links-search.css</code></p><figure class="highlight css"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br><span class="line">142</span><br><span class="line">143</span><br><span class="line">144</span><br><span class="line">145</span><br><span class="line">146</span><br><span class="line">147</span><br><span class="line">148</span><br><span class="line">149</span><br><span class="line">150</span><br><span class="line">151</span><br><span class="line">152</span><br><span class="line">153</span><br><span class="line">154</span><br><span class="line">155</span><br><span class="line">156</span><br><span class="line">157</span><br><span class="line">158</span><br><span class="line">159</span><br><span class="line">160</span><br><span class="line">161</span><br><span class="line">162</span><br><span class="line">163</span><br><span class="line">164</span><br><span class="line">165</span><br><span class="line">166</span><br><span class="line">167</span><br><span class="line">168</span><br><span class="line">169</span><br><span class="line">170</span><br><span class="line">171</span><br><span class="line">172</span><br><span class="line">173</span><br><span class="line">174</span><br><span class="line">175</span><br><span class="line">176</span><br><span class="line">177</span><br><span class="line">178</span><br><span class="line">179</span><br><span class="line">180</span><br><span class="line">181</span><br><span class="line">182</span><br><span class="line">183</span><br><span class="line">184</span><br><span class="line">185</span><br><span class="line">186</span><br><span class="line">187</span><br><span class="line">188</span><br><span class="line">189</span><br><span class="line">190</span><br><span class="line">191</span><br><span class="line">192</span><br><span class="line">193</span><br><span class="line">194</span><br><span class="line">195</span><br><span class="line">196</span><br><span class="line">197</span><br><span class="line">198</span><br><span class="line">199</span><br><span class="line">200</span><br><span class="line">201</span><br><span class="line">202</span><br><span class="line">203</span><br><span class="line">204</span><br><span class="line">205</span><br><span class="line">206</span><br><span class="line">207</span><br><span class="line">208</span><br><span class="line">209</span><br><span class="line">210</span><br><span class="line">211</span><br><span class="line">212</span><br><span class="line">213</span><br><span class="line">214</span><br><span class="line">215</span><br><span class="line">216</span><br><span class="line">217</span><br><span class="line">218</span><br><span class="line">219</span><br><span class="line">220</span><br><span class="line">221</span><br><span class="line">222</span><br><span class="line">223</span><br><span class="line">224</span><br><span class="line">225</span><br><span class="line">226</span><br><span class="line">227</span><br><span class="line">228</span><br><span class="line">229</span><br><span class="line">230</span><br><span class="line">231</span><br><span class="line">232</span><br><span class="line">233</span><br><span class="line">234</span><br><span class="line">235</span><br><span class="line">236</span><br><span class="line">237</span><br><span class="line">238</span><br><span class="line">239</span><br><span class="line">240</span><br><span class="line">241</span><br><span class="line">242</span><br><span class="line">243</span><br><span class="line">244</span><br><span class="line">245</span><br><span class="line">246</span><br><span class="line">247</span><br><span class="line">248</span><br><span class="line">249</span><br><span class="line">250</span><br><span class="line">251</span><br><span class="line">252</span><br><span class="line">253</span><br><span class="line">254</span><br><span class="line">255</span><br><span class="line">256</span><br><span class="line">257</span><br><span class="line">258</span><br><span class="line">259</span><br><span class="line">260</span><br><span class="line">261</span><br><span class="line">262</span><br><span class="line">263</span><br><span class="line">264</span><br><span class="line">265</span><br><span class="line">266</span><br><span class="line">267</span><br><span class="line">268</span><br><span class="line">269</span><br><span class="line">270</span><br><span class="line">271</span><br><span class="line">272</span><br><span class="line">273</span><br><span class="line">274</span><br><span class="line">275</span><br><span class="line">276</span><br><span class="line">277</span><br><span class="line">278</span><br><span class="line">279</span><br><span class="line">280</span><br><span class="line">281</span><br><span class="line">282</span><br><span class="line">283</span><br><span class="line">284</span><br><span class="line">285</span><br><span class="line">286</span><br><span class="line">287</span><br><span class="line">288</span><br><span class="line">289</span><br><span class="line">290</span><br><span class="line">291</span><br><span class="line">292</span><br><span class="line">293</span><br><span class="line">294</span><br><span class="line">295</span><br><span class="line">296</span><br><span class="line">297</span><br><span class="line">298</span><br><span class="line">299</span><br><span class="line">300</span><br><span class="line">301</span><br><span class="line">302</span><br><span class="line">303</span><br><span class="line">304</span><br><span class="line">305</span><br><span class="line">306</span><br><span class="line">307</span><br><span class="line">308</span><br><span class="line">309</span><br><span class="line">310</span><br><span class="line">311</span><br><span class="line">312</span><br><span class="line">313</span><br><span class="line">314</span><br><span class="line">315</span><br><span class="line">316</span><br><span class="line">317</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 实用网站导航搜索功能样式</span></span><br><span class="line"><span class="comment"> * 适配 AnZhiYu 主题</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"></span><br><span class="line"><span class="comment">/* 搜索容器 */</span></span><br><span class="line"><span class="selector-id">#useful-links-search-container</span> {</span><br><span class="line">  <span class="attribute">margin-bottom</span>: <span class="number">2rem</span>;</span><br><span class="line">  <span class="attribute">padding</span>: <span class="number">1.5rem</span>;</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-card-bg);</span><br><span class="line">  <span class="attribute">border-radius</span>: <span class="number">12px</span>;</span><br><span class="line">  <span class="attribute">border</span>: <span class="built_in">var</span>(--style-border);</span><br><span class="line">  <span class="attribute">box-shadow</span>: <span class="built_in">var</span>(--anzhiyu-shadow-border);</span><br><span class="line">  <span class="attribute">transition</span>: all <span class="number">0.3s</span> ease;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-id">#useful-links-search-container</span><span class="selector-pseudo">:hover</span> {</span><br><span class="line">  <span class="attribute">border</span>: <span class="built_in">var</span>(--style-border-hover);</span><br><span class="line">  <span class="attribute">box-shadow</span>: <span class="built_in">var</span>(--anzhiyu-shadow-theme);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 搜索头部 */</span></span><br><span class="line"><span class="selector-class">.search-header</span> {</span><br><span class="line">  <span class="attribute">margin-bottom</span>: <span class="number">1rem</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 搜索框样式 */</span></span><br><span class="line"><span class="selector-class">.search-box</span> {</span><br><span class="line">  <span class="attribute">position</span>: relative;</span><br><span class="line">  <span class="attribute">margin-bottom</span>: <span class="number">1rem</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.search-box</span> <span class="selector-tag">input</span> {</span><br><span class="line">  <span class="attribute">width</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">padding</span>: <span class="number">0.75rem</span> <span class="number">3rem</span> <span class="number">0.75rem</span> <span class="number">1rem</span>;</span><br><span class="line">  <span class="attribute">border</span>: <span class="built_in">var</span>(--style-border);</span><br><span class="line">  <span class="attribute">border-radius</span>: <span class="number">8px</span>;</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-secondbg);</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-fontcolor);</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">1rem</span>;</span><br><span class="line">  <span class="attribute">transition</span>: all <span class="number">0.3s</span> ease;</span><br><span class="line">  <span class="attribute">outline</span>: none;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.search-box</span> <span class="selector-tag">input</span><span class="selector-pseudo">:focus</span> {</span><br><span class="line">  <span class="attribute">border</span>: <span class="built_in">var</span>(--style-border-hover);</span><br><span class="line">  <span class="attribute">box-shadow</span>: <span class="number">0</span> <span class="number">0</span> <span class="number">0</span> <span class="number">3px</span> <span class="built_in">rgba</span>(<span class="built_in">var</span>(--anzhiyu-main-rgb), <span class="number">0.1</span>);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.search-box</span> <span class="selector-tag">input</span><span class="selector-pseudo">::placeholder</span> {</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-secondtext);</span><br><span class="line">  <span class="attribute">opacity</span>: <span class="number">0.8</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 搜索图标 */</span></span><br><span class="line"><span class="selector-class">.search-icon</span> {</span><br><span class="line">  <span class="attribute">position</span>: absolute;</span><br><span class="line">  <span class="attribute">right</span>: <span class="number">1rem</span>;</span><br><span class="line">  <span class="attribute">top</span>: <span class="number">50%</span>;</span><br><span class="line">  <span class="attribute">transform</span>: <span class="built_in">translateY</span>(-<span class="number">50%</span>);</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-secondtext);</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">1.1rem</span>;</span><br><span class="line">  <span class="attribute">pointer-events</span>: none;</span><br><span class="line">  <span class="attribute">transition</span>: color <span class="number">0.3s</span> ease;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.search-box</span> <span class="selector-tag">input</span><span class="selector-pseudo">:focus</span> + <span class="selector-class">.search-icon</span> {</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 分类过滤器 */</span></span><br><span class="line"><span class="selector-class">.category-filters</span> {</span><br><span class="line">  <span class="attribute">display</span>: flex;</span><br><span class="line">  <span class="attribute">flex-wrap</span>: wrap;</span><br><span class="line">  <span class="attribute">gap</span>: <span class="number">0.5rem</span>;</span><br><span class="line">  <span class="attribute">margin-bottom</span>: <span class="number">0.5rem</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.category-filter</span> {</span><br><span class="line">  <span class="attribute">padding</span>: <span class="number">0.5rem</span> <span class="number">1rem</span>;</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-secondbg);</span><br><span class="line">  <span class="attribute">border</span>: <span class="built_in">var</span>(--style-border);</span><br><span class="line">  <span class="attribute">border-radius</span>: <span class="number">20px</span>;</span><br><span class="line">  <span class="attribute">cursor</span>: pointer;</span><br><span class="line">  <span class="attribute">transition</span>: all <span class="number">0.3s</span> ease;</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">0.9rem</span>;</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-fontcolor);</span><br><span class="line">  <span class="attribute">user-select</span>: none;</span><br><span class="line">  <span class="attribute">white-space</span>: nowrap;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.category-filter</span><span class="selector-pseudo">:hover</span> {</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-white);</span><br><span class="line">  <span class="attribute">border-color</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">  <span class="attribute">transform</span>: <span class="built_in">translateY</span>(-<span class="number">1px</span>);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.category-filter</span><span class="selector-class">.active</span> {</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-white);</span><br><span class="line">  <span class="attribute">border-color</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">  <span class="attribute">box-shadow</span>: <span class="number">0</span> <span class="number">2px</span> <span class="number">8px</span> <span class="built_in">rgba</span>(<span class="built_in">var</span>(--anzhiyu-main-rgb), <span class="number">0.3</span>);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 搜索统计信息 */</span></span><br><span class="line"><span class="selector-class">.search-stats</span> {</span><br><span class="line">  <span class="attribute">margin-top</span>: <span class="number">0.5rem</span>;</span><br><span class="line">  <span class="attribute">padding-top</span>: <span class="number">0.5rem</span>;</span><br><span class="line">  <span class="attribute">border-top</span>: <span class="built_in">var</span>(--style-border);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-id">#search-results-count</span> {</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-secondtext);</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">0.9rem</span>;</span><br><span class="line">  <span class="attribute">font-weight</span>: <span class="number">500</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 搜索结果高亮 */</span></span><br><span class="line"><span class="selector-class">.search-highlight</span> {</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-white);</span><br><span class="line">  <span class="attribute">padding</span>: <span class="number">2px</span> <span class="number">4px</span>;</span><br><span class="line">  <span class="attribute">border-radius</span>: <span class="number">3px</span>;</span><br><span class="line">  <span class="attribute">font-weight</span>: bold;</span><br><span class="line">  <span class="attribute">box-decoration-break</span>: clone;</span><br><span class="line">  -webkit-<span class="attribute">box-decoration-break</span>: clone;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 隐藏不匹配的项目 */</span></span><br><span class="line"><span class="selector-class">.flink-list-item</span><span class="selector-class">.hidden</span> {</span><br><span class="line">  <span class="attribute">display</span>: none <span class="meta">!important</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.flink</span> <span class="selector-tag">h2</span><span class="selector-class">.hidden</span> {</span><br><span class="line">  <span class="attribute">display</span>: none <span class="meta">!important</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 响应式设计 */</span></span><br><span class="line"><span class="keyword">@media</span> (<span class="attribute">max-width</span>: <span class="number">768px</span>) {</span><br><span class="line">  <span class="selector-id">#useful-links-search-container</span> {</span><br><span class="line">    <span class="attribute">padding</span>: <span class="number">1rem</span>;</span><br><span class="line">    <span class="attribute">margin-bottom</span>: <span class="number">1.5rem</span>;</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-class">.search-box</span> <span class="selector-tag">input</span> {</span><br><span class="line">    <span class="attribute">padding</span>: <span class="number">0.6rem</span> <span class="number">2.5rem</span> <span class="number">0.6rem</span> <span class="number">0.8rem</span>;</span><br><span class="line">    <span class="attribute">font-size</span>: <span class="number">0.9rem</span>;</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-class">.search-icon</span> {</span><br><span class="line">    <span class="attribute">right</span>: <span class="number">0.8rem</span>;</span><br><span class="line">    <span class="attribute">font-size</span>: <span class="number">1rem</span>;</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-class">.category-filter</span> {</span><br><span class="line">    <span class="attribute">padding</span>: <span class="number">0.4rem</span> <span class="number">0.8rem</span>;</span><br><span class="line">    <span class="attribute">font-size</span>: <span class="number">0.85rem</span>;</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-class">.category-filters</span> {</span><br><span class="line">    <span class="attribute">gap</span>: <span class="number">0.4rem</span>;</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-id">#search-results-count</span> {</span><br><span class="line">    <span class="attribute">font-size</span>: <span class="number">0.85rem</span>;</span><br><span class="line">  }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">@media</span> (<span class="attribute">max-width</span>: <span class="number">480px</span>) {</span><br><span class="line">  <span class="selector-id">#useful-links-search-container</span> {</span><br><span class="line">    <span class="attribute">padding</span>: <span class="number">0.8rem</span>;</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-class">.search-box</span> <span class="selector-tag">input</span> {</span><br><span class="line">    <span class="attribute">padding</span>: <span class="number">0.5rem</span> <span class="number">2rem</span> <span class="number">0.5rem</span> <span class="number">0.6rem</span>;</span><br><span class="line">    <span class="attribute">font-size</span>: <span class="number">0.85rem</span>;</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-class">.category-filter</span> {</span><br><span class="line">    <span class="attribute">padding</span>: <span class="number">0.3rem</span> <span class="number">0.6rem</span>;</span><br><span class="line">    <span class="attribute">font-size</span>: <span class="number">0.8rem</span>;</span><br><span class="line">  }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 深色模式适配 */</span></span><br><span class="line"><span class="selector-attr">[data-theme=<span class="string">'dark'</span>]</span> <span class="selector-id">#useful-links-search-container</span> {</span><br><span class="line">  <span class="attribute">border-color</span>: <span class="built_in">var</span>(--style-border);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-attr">[data-theme=<span class="string">'dark'</span>]</span> <span class="selector-class">.search-box</span> <span class="selector-tag">input</span> {</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-secondbg);</span><br><span class="line">  <span class="attribute">border-color</span>: <span class="built_in">var</span>(--style-border);</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-fontcolor);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-attr">[data-theme=<span class="string">'dark'</span>]</span> <span class="selector-class">.category-filter</span> {</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-secondbg);</span><br><span class="line">  <span class="attribute">border-color</span>: <span class="built_in">var</span>(--style-border);</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-fontcolor);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 动画效果 */</span></span><br><span class="line"><span class="selector-class">.category-filter</span> {</span><br><span class="line">  <span class="attribute">position</span>: relative;</span><br><span class="line">  <span class="attribute">overflow</span>: hidden;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.category-filter</span><span class="selector-pseudo">::before</span> {</span><br><span class="line">  <span class="attribute">content</span>: <span class="string">''</span>;</span><br><span class="line">  <span class="attribute">position</span>: absolute;</span><br><span class="line">  <span class="attribute">top</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">left</span>: -<span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">width</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">height</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">linear-gradient</span>(<span class="number">90deg</span>, transparent, <span class="built_in">rgba</span>(<span class="number">255</span>, <span class="number">255</span>, <span class="number">255</span>, <span class="number">0.2</span>), transparent);</span><br><span class="line">  <span class="attribute">transition</span>: left <span class="number">0.5s</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.category-filter</span><span class="selector-pseudo">:hover</span><span class="selector-pseudo">::before</span> {</span><br><span class="line">  <span class="attribute">left</span>: <span class="number">100%</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 搜索框聚焦动画 */</span></span><br><span class="line"><span class="selector-class">.search-box</span> {</span><br><span class="line">  <span class="attribute">position</span>: relative;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.search-box</span><span class="selector-pseudo">::after</span> {</span><br><span class="line">  <span class="attribute">content</span>: <span class="string">''</span>;</span><br><span class="line">  <span class="attribute">position</span>: absolute;</span><br><span class="line">  <span class="attribute">bottom</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">left</span>: <span class="number">50%</span>;</span><br><span class="line">  <span class="attribute">width</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">height</span>: <span class="number">2px</span>;</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">  <span class="attribute">transition</span>: all <span class="number">0.3s</span> ease;</span><br><span class="line">  <span class="attribute">transform</span>: <span class="built_in">translateX</span>(-<span class="number">50%</span>);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.search-box</span> <span class="selector-tag">input</span><span class="selector-pseudo">:focus</span> ~ <span class="selector-pseudo">::after</span> {</span><br><span class="line">  <span class="attribute">width</span>: <span class="number">100%</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 加载状态 */</span></span><br><span class="line"><span class="selector-class">.search-loading</span> {</span><br><span class="line">  <span class="attribute">opacity</span>: <span class="number">0.6</span>;</span><br><span class="line">  <span class="attribute">pointer-events</span>: none;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.search-loading</span> <span class="selector-class">.search-icon</span><span class="selector-pseudo">::before</span> {</span><br><span class="line">  <span class="attribute">content</span>: <span class="string">'\e6cd'</span>; <span class="comment">/* loading icon */</span></span><br><span class="line">  <span class="attribute">animation</span>: spin <span class="number">1s</span> linear infinite;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">@keyframes</span> spin {</span><br><span class="line">  <span class="selector-tag">from</span> { <span class="attribute">transform</span>: <span class="built_in">translateY</span>(-<span class="number">50%</span>) <span class="built_in">rotate</span>(<span class="number">0deg</span>); }</span><br><span class="line">  <span class="selector-tag">to</span> { <span class="attribute">transform</span>: <span class="built_in">translateY</span>(-<span class="number">50%</span>) <span class="built_in">rotate</span>(<span class="number">360deg</span>); }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 无结果状态 */</span></span><br><span class="line"><span class="selector-class">.no-results-message</span> {</span><br><span class="line">  <span class="attribute">text-align</span>: center;</span><br><span class="line">  <span class="attribute">padding</span>: <span class="number">2rem</span>;</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-secondtext);</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">1rem</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.no-results-message</span> <span class="selector-tag">i</span> {</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">3rem</span>;</span><br><span class="line">  <span class="attribute">margin-bottom</span>: <span class="number">1rem</span>;</span><br><span class="line">  <span class="attribute">opacity</span>: <span class="number">0.5</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 滚动到顶部按钮增强（搜索时显示） */</span></span><br><span class="line"><span class="selector-class">.search-active</span> <span class="selector-id">#nav-totop</span> {</span><br><span class="line">  <span class="attribute">opacity</span>: <span class="number">1</span> <span class="meta">!important</span>;</span><br><span class="line">  <span class="attribute">transform</span>: <span class="built_in">translateX</span>(-<span class="number">58px</span>) <span class="meta">!important</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 分类标题动画 */</span></span><br><span class="line"><span class="selector-class">.flink</span> <span class="selector-tag">h2</span> {</span><br><span class="line">  <span class="attribute">transition</span>: all <span class="number">0.3s</span> ease;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.flink</span> <span class="selector-tag">h2</span><span class="selector-attr">[style*=<span class="string">"display: none"</span>]</span> {</span><br><span class="line">  <span class="attribute">opacity</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">transform</span>: <span class="built_in">translateY</span>(-<span class="number">10px</span>);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 搜索结果项目动画 */</span></span><br><span class="line"><span class="selector-class">.flink-list-item</span> {</span><br><span class="line">  <span class="attribute">transition</span>: all <span class="number">0.3s</span> ease;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.flink-list-item</span><span class="selector-class">.hidden</span> {</span><br><span class="line">  <span class="attribute">opacity</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">transform</span>: <span class="built_in">scale</span>(<span class="number">0.9</span>) <span class="built_in">translateY</span>(-<span class="number">10px</span>);</span><br><span class="line">  <span class="attribute">pointer-events</span>: none;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 高亮动画 */</span></span><br><span class="line"><span class="selector-class">.search-highlight</span> {</span><br><span class="line">  <span class="attribute">animation</span>: highlight-pulse <span class="number">1s</span> ease-in-out;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">@keyframes</span> highlight-pulse {</span><br><span class="line">  <span class="number">0%</span> {</span><br><span class="line">    <span class="attribute">box-shadow</span>: <span class="number">0</span> <span class="number">0</span> <span class="number">0</span> <span class="number">0</span> <span class="built_in">rgba</span>(<span class="built_in">var</span>(--anzhiyu-main-rgb), <span class="number">0.7</span>);</span><br><span class="line">  }</span><br><span class="line">  <span class="number">70%</span> {</span><br><span class="line">    <span class="attribute">box-shadow</span>: <span class="number">0</span> <span class="number">0</span> <span class="number">0</span> <span class="number">6px</span> <span class="built_in">rgba</span>(<span class="built_in">var</span>(--anzhiyu-main-rgb), <span class="number">0</span>);</span><br><span class="line">  }</span><br><span class="line">  <span class="number">100%</span> {</span><br><span class="line">    <span class="attribute">box-shadow</span>: <span class="number">0</span> <span class="number">0</span> <span class="number">0</span> <span class="number">0</span> <span class="built_in">rgba</span>(<span class="built_in">var</span>(--anzhiyu-main-rgb), <span class="number">0</span>);</span><br><span class="line">  }</span><br><span class="line">} </span><br></pre></td></tr></tbody></table></figure><p>7.可以使用<code>inject</code>直接注入js和css，但是我们这里就用自己写的按需载入js脚本了</p><figure class="highlight js"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 按需加载资源管理器</span></span><br><span class="line"><span class="comment"> * 用于优化网站性能，只在需要时加载特定资源</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">ResourceLoader</span> {</span><br><span class="line">    <span class="title function_">constructor</span>(<span class="params"></span>) {</span><br><span class="line">        <span class="variable language_">this</span>.<span class="property">loadedCSS</span> = <span class="keyword">new</span> <span class="title class_">Set</span>();</span><br><span class="line">        <span class="variable language_">this</span>.<span class="property">loadedJS</span> = <span class="keyword">new</span> <span class="title class_">Set</span>();</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 动态加载CSS文件</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> {<span class="type">string</span>} <span class="variable">href</span> - CSS文件路径</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> {<span class="type">string</span>} <span class="variable">id</span> - 可选的link元素ID</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="title function_">loadCSS</span>(<span class="params">href, id = <span class="literal">null</span></span>) {</span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">this</span>.<span class="property">loadedCSS</span>.<span class="title function_">has</span>(href) || <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">`link[href="<span class="subst">${href}</span>"]`</span>)) {</span><br><span class="line">            <span class="keyword">return</span> <span class="title class_">Promise</span>.<span class="title function_">resolve</span>();</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">Promise</span>(<span class="function">(<span class="params">resolve, reject</span>) =&gt;</span> {</span><br><span class="line">            <span class="keyword">const</span> link = <span class="variable language_">document</span>.<span class="title function_">createElement</span>(<span class="string">'link'</span>);</span><br><span class="line">            link.<span class="property">rel</span> = <span class="string">'stylesheet'</span>;</span><br><span class="line">            link.<span class="property">href</span> = href;</span><br><span class="line">            <span class="keyword">if</span> (id) link.<span class="property">id</span> = id;</span><br><span class="line"></span><br><span class="line">            link.<span class="property">onload</span> = <span class="function">() =&gt;</span> {</span><br><span class="line">                <span class="variable language_">this</span>.<span class="property">loadedCSS</span>.<span class="title function_">add</span>(href);</span><br><span class="line">                <span class="title function_">resolve</span>();</span><br><span class="line">            };</span><br><span class="line">            link.<span class="property">onerror</span> = reject;</span><br><span class="line"></span><br><span class="line">            <span class="variable language_">document</span>.<span class="property">head</span>.<span class="title function_">appendChild</span>(link);</span><br><span class="line">        });</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 动态加载JS文件</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> {<span class="type">string</span>} <span class="variable">src</span> - JS文件路径</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> {<span class="type">string</span>} <span class="variable">id</span> - 可选的script元素ID</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="title function_">loadJS</span>(<span class="params">src, id = <span class="literal">null</span></span>) {</span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">this</span>.<span class="property">loadedJS</span>.<span class="title function_">has</span>(src) || <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">`script[src="<span class="subst">${src}</span>"]`</span>)) {</span><br><span class="line">            <span class="keyword">return</span> <span class="title class_">Promise</span>.<span class="title function_">resolve</span>();</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">Promise</span>(<span class="function">(<span class="params">resolve, reject</span>) =&gt;</span> {</span><br><span class="line">            <span class="keyword">const</span> script = <span class="variable language_">document</span>.<span class="title function_">createElement</span>(<span class="string">'script'</span>);</span><br><span class="line">            script.<span class="property">src</span> = src;</span><br><span class="line">            <span class="keyword">if</span> (id) script.<span class="property">id</span> = id;</span><br><span class="line"></span><br><span class="line">            script.<span class="property">onload</span> = <span class="function">() =&gt;</span> {</span><br><span class="line">                <span class="variable language_">this</span>.<span class="property">loadedJS</span>.<span class="title function_">add</span>(src);</span><br><span class="line">                <span class="title function_">resolve</span>();</span><br><span class="line">            };</span><br><span class="line">            script.<span class="property">onerror</span> = reject;</span><br><span class="line"></span><br><span class="line">            <span class="variable language_">document</span>.<span class="property">body</span>.<span class="title function_">appendChild</span>(script);</span><br><span class="line">        });</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 检测页面内容并按需加载相关资源</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line">    <span class="title function_">autoDetectAndLoad</span>(<span class="params"></span>) {</span><br><span class="line">        <span class="comment">// 检测是否为首页</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">window</span>.<span class="property">location</span>.<span class="property">pathname</span> === <span class="string">'/'</span> || <span class="variable language_">window</span>.<span class="property">location</span>.<span class="property">pathname</span> === <span class="string">'/index.html'</span>) {</span><br><span class="line">            <span class="comment">// 修复：index_media.css 现在由头部优先加载，只需加载JS</span></span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/js/index_media.js'</span>, <span class="string">'index-media-script'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测是否为文章页</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#post'</span>) || <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'.post-content'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/css/custom-comment.css'</span>, <span class="string">'custom-comment-style'</span>);</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/custom/css/tip_style.css'</span>, <span class="string">'tip-style'</span>);</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/js/fixed_comment.js'</span>, <span class="string">'fixed-comment-script'</span>);</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/custom/js/tip_main.js'</span>, <span class="string">'tip-main-script'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测B站视频内容</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'iframe[src*="bilibili.com"]'</span>) ||</span><br><span class="line">            <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'iframe[src*="player.bilibili.com"]'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/css/bilibili.css'</span>, <span class="string">'bilibili-style'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测代码块</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'pre code'</span>) || <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'.highlight'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/custom/css/sandbox_style.css'</span>, <span class="string">'sandbox-style'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测评论区</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#twikoo'</span>) ||</span><br><span class="line">            <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#waline'</span>) ||</span><br><span class="line">            <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#valine'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/js/comments.js'</span>, <span class="string">'comments-script'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测即刻短文页面</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">window</span>.<span class="property">location</span>.<span class="property">pathname</span>.<span class="title function_">includes</span>(<span class="string">'/essay/'</span>) || <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#essay_page'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/css/essay-style.css'</span>, <span class="string">'essay-style'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测待办清单页面</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">window</span>.<span class="property">location</span>.<span class="property">pathname</span>.<span class="title function_">includes</span>(<span class="string">'/todolist/'</span>) || <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#todolist-box'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/custom/css/todolist.css'</span>, <span class="string">'todolist-style'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测实用网站导航页面</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">window</span>.<span class="property">location</span>.<span class="property">pathname</span>.<span class="title function_">includes</span>(<span class="string">'/awesome-links/'</span>) ||</span><br><span class="line">            <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#useful-links-container'</span>) ||</span><br><span class="line">            <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'.useful-links-page'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/css/useful-links-search.css'</span>, <span class="string">'useful-links-search-style'</span>);</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/js/useful-links-search.js'</span>, <span class="string">'useful-links-search-script'</span>);</span><br><span class="line">        }</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 检测侧边栏相关功能</span></span><br><span class="line">        <span class="keyword">if</span> (<span class="variable language_">document</span>.<span class="title function_">querySelector</span>(<span class="string">'#sidebar'</span>)) {</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/custom/css/schedule.css'</span>, <span class="string">'schedule-style'</span>);</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadCSS</span>(<span class="string">'/custom/css/background-box.css'</span>, <span class="string">'background-style'</span>);</span><br><span class="line">            <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'https://cdn.jsdelivr.net/npm/winbox@0.2.82/dist/winbox.bundle.min.js'</span>, <span class="string">'winbox-lib'</span>)</span><br><span class="line">                .<span class="title function_">then</span>(<span class="function">() =&gt;</span> <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/custom/js/chineselunar.js'</span>, <span class="string">'chineselunar-script'</span>))</span><br><span class="line">                .<span class="title function_">then</span>(<span class="function">() =&gt;</span> <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/custom/js/schedule.js'</span>, <span class="string">'schedule-script'</span>))</span><br><span class="line">                .<span class="title function_">then</span>(<span class="function">() =&gt;</span> <span class="variable language_">this</span>.<span class="title function_">loadJS</span>(<span class="string">'/custom/js/background-box.js'</span>, <span class="string">'background-script'</span>))</span><br><span class="line">                .<span class="title function_">catch</span>(<span class="function"><span class="params">err</span> =&gt;</span> <span class="variable language_">console</span>.<span class="title function_">warn</span>(<span class="string">'侧边栏脚本加载失败:'</span>, err));</span><br><span class="line">        }</span><br><span class="line">    }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// 创建全局实例</span></span><br><span class="line"><span class="variable language_">window</span>.<span class="property">resourceLoader</span> = <span class="keyword">new</span> <span class="title class_">ResourceLoader</span>();</span><br><span class="line"></span><br><span class="line"><span class="comment">// 页面加载完成后自动检测</span></span><br><span class="line"><span class="variable language_">document</span>.<span class="title function_">addEventListener</span>(<span class="string">'DOMContentLoaded'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">    <span class="variable language_">window</span>.<span class="property">resourceLoader</span>.<span class="title function_">autoDetectAndLoad</span>();</span><br><span class="line">});</span><br><span class="line"></span><br><span class="line"><span class="comment">// 为PJAX提供支持</span></span><br><span class="line"><span class="variable language_">document</span>.<span class="title function_">addEventListener</span>(<span class="string">'pjax:complete'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">    <span class="variable language_">window</span>.<span class="property">resourceLoader</span>.<span class="title function_">autoDetectAndLoad</span>();</span><br><span class="line">}); </span><br></pre></td></tr></tbody></table></figure></li></ol><hr><h6 id="第三步：在菜单中添加入口"><a href="#第三步：在菜单中添加入口" class="headerlink" title="第三步：在菜单中添加入口"></a><strong>第三步：在菜单中添加入口</strong></h6><ol><li>打开您<strong>主题的配置文件</strong> (<code>_config.yml</code>)。</li><li>在 <code>menu:</code> 部分添加一个新链接，指向我们刚刚创建的页面。<figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">menu:</span></span><br><span class="line">  <span class="comment"># ...</span></span><br><span class="line">  <span class="string">百宝箱:</span> <span class="comment"># 或者您喜欢的任何名字</span></span><br><span class="line">    <span class="string">实用网站:</span> <span class="string">/awesome-links/</span> <span class="string">||</span> <span class="string">anzhiyu-icon-compass</span></span><br><span class="line">  <span class="comment"># ...</span></span><br></pre></td></tr></tbody></table></figure></li></ol><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/49291.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/49291.html&quot;)">22.内容扩展：创建“实用网站”导航页</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/49291.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/49291.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>框架技术<span class="categoryesPageCount">31</span></a><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Hexo<span class="categoryesPageCount">31</span></a><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/%E9%AD%94%E6%94%B9/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>魔改<span class="categoryesPageCount">23</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>博客搭建教程<span class="tagsPageCount">31</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/63007.html"><img class="prev-cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">21.内容拓展：通过前端JS动态生成并下载.md文件</div></div></a></div><div class="next-post pull-right"><a href="/posts/56426.html"><img class="next-cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">23.内容拓展：代码运行器功能实现指南</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/24286.html" title="10.内容扩展：添加“安全跳转”中间页"><img class="cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">10.内容扩展：添加“安全跳转”中间页</div></div></a></div><div><a href="/posts/65188.html" title="11.Twikoo 美化：添加自定义表情包"><img class="cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">11.Twikoo 美化：添加自定义表情包</div></div></a></div><div><a href="/posts/57565.html" title="12.Twikoo 美化：自定义评论回复邮件模板"><img class="cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">12.Twikoo 美化：自定义评论回复邮件模板</div></div></a></div><div><a href="/posts/20246.html" title="13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）"><img class="cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）</div></div></a></div><div><a href="/posts/43263.html" title="14.主题魔改：添加“背景切换”弹窗面板"><img class="cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">14.主题魔改：添加“背景切换”弹窗面板</div></div></a></div><div><a href="/posts/34091.html" title="15.主题魔改：自定义全站字体"><img class="cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">15.主题魔改：自定义全站字体</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"22.内容扩展：创建“实用网站”导航页",date:"2025-07-11 14:13:45",updated:"2025-07-19 19:25:44",tags:["博客搭建教程"],categories:["框架技术","Hexo","魔改"],content:"\n### **22.内容扩展：创建“实用网站”导航页**\n\n###### **前言：功能介绍**\n\n本指南将引导您创建一个高度结构化、美观且易于维护的“实用网站”导航页面。我们将实现以下效果：\n1.  **Tab 切换分类**：将您不同类别的网站链接，放入可以点击切换的Tab选项卡中，让页面保持整洁。\n2.  **数据集中管理**：将所有的链接数据，统一存放在一个独立的 `yml` 文件中，方便您未来随时增删和修改，而无需触碰页面代码。\n3.  **样式自动化**：完全利用主题内置的 `{% tabs %}` 和 `{% link %}` 外挂标签，自动生成美观的卡片布局，无需您编写任何额外的CSS。\n\n---\n###### **第一步：创建并编辑您的链接数据文件**\n\n这是本方案的核心。我们将所有链接数据都存放在这里。\n\n1.  **创建数据文件 (`useful_links.yml`)**\n  \n* 在您博客的 `source/_data/` 文件夹内，新建一个名为 `useful_links.yml` 的文件。\n  \n2.  **编辑 `useful_links.yml` 文件**\n    * 将您所有的网站链接，按照下面的格式进行分类和整理。您可以创建任意多个分类。\n\n    ```yaml\n    # source/_data/useful_links.yml\n\n    - category: \"前端开发\"\n      links:\n        - name: \"MDN Web Docs\"\n          descr: \"前端开发者的权威参考手册\"\n          link: \"https://developer.mozilla.org/zh-CN/\"\n          avatar: \"https://developer.mozilla.org/favicon.ico\"\n        - name: \"CSS-Tricks\"\n          descr: \"关于CSS的一切，技巧、教程和灵感\"\n          link: \"https://css-tricks.com/\"\n          avatar: \"https://css-tricks.com/favicon.ico\"\n\n    - category: \"设计与灵感\"\n      links:\n        - name: \"Dribbble\"\n          descr: \"全球顶尖设计师的作品展示平台\"\n          link: \"https://dribbble.com/\"\n          avatar: \"https://cdn.dribbble.com/assets/favicon-63b2909422b16815e47346142d4d2b56.ico\"\n        - name: \"Pinterest\"\n          descr: \"发现和收藏创意灵感的视觉探索工具\"\n          link: \"https://www.pinterest.com/\"\n          avatar: \"https://www.pinterest.com/favicon.ico\"\n\n    - category: \"云服务与部署\"\n      links:\n        - name: \"Vercel\"\n          descr: \"前端项目的自动化部署与托管平台\"\n          link: \"https://vercel.com/\"\n          avatar: \"https://assets.vercel.com/image/upload/front/favicon/vercel/favicon.ico\"\n    ```\n    * **格式说明**：最外层是一个列表，每一项（以 `-` 开头）都包含一个 `category`（分类名）和一个 `links`（该分类下的链接列表）。\n\n---\n###### **第二步：创建页面并使用“魔法代码”**\n\n1.  **创建页面文件**\n  \n* 在终端运行命令： `hexo new page awesome-links` (或您喜欢的任何名字)。\n  \n2.  **修改页面 Front-matter**\n    * 打开新生成的 `source/awesome-links/index.md` 文件，设置好它的Front-matter：\n    ```yaml\n    ---\n    title: 实用网站导航\n    date: 2025-06-17 18:00:00\n    comments: true\n    aside: false\n    type: useful_links\n    ---\n\n    \n    ```\n    \n3.  **创建 Pug 模板文件**\n\n    * 在 `themes/anzhiyu/layout/includes/page/useful_links.pug`，**将下面这段代码完整地复制粘贴进去**。\n    ```nunjucks\n    #article-container\n      // 添加搜索功能区域\n      #useful-links-search-container\n        .search-header\n          .search-box\n            input#useful-links-search(type=\"text\" placeholder=\"搜索实用网站...\" autocomplete=\"off\")\n            i.anzhiyufont.anzhiyu-icon-search.search-icon\n          .category-filters\n            .category-filter.active(data-category=\"all\") 全部\n            if site.data.useful_links\n              each i in site.data.useful_links\n                if i.class_name\n                  .category-filter(data-category=`${i.class_name}`)= i.class_name\n        .search-stats\n          span#search-results-count \n    \n      .flink\n        if site.data.useful_links\n          each i in site.data.useful_links\n            if i.class_name\n              h2(data-category=`${i.class_name}`)!= i.class_name + \"(\" + i.link_list.length + \")\"\n            if i.class_desc\n              .flink-desc!=i.class_desc\n            if i.flink_style === 'anzhiyu'\n              div(class=i.lost_contact ? 'anzhiyu-flink-list cf-friends-lost-contact' : 'anzhiyu-flink-list')\n                if i.link_list\n                  each item in i.link_list\n                    - let color = item.color || \"\"\n                    - let tag = item.tag || \"\"\n                    \n                    .flink-list-item\n                      if color == \"vip\" && tag\n                        span.site-card-tag.vip #[=tag]\n                          i.light\n                      else if color == \"speed\" && tag\n                        span.site-card-tag.speed #[=ta3g]\n                      else if tag\n                        span.site-card-tag(style=`background-color: ${color}`) #[=tag]\n                      else if item.recommend\n                        span.site-card-tag 荐\n                      if i.lost_contact\n                        a.cf-friends-link(href=url_for(item.link) title=item.name target=\"_blank\")\n                          if theme.lazyload.enable\n                            img.no-lightbox(data-lazy-src=url_for(item.avatar) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=item.name )\n                          else\n                            img.cf-friends-avatar.no-lightbox(src=url_for(item.avatar) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=item.name )\n                          .flink-item-info\n                            .flink-item-name.cf-friends-name-lost-contact= item.name\n                      else\n                        a.cf-friends-link(href=url_for(item.link) cf-href=url_for(item.link) title=item.name target=\"_blank\")\n                          if theme.lazyload.enable\n                            img.cf-friends-avatar.no-lightbox(data-lazy-src=url_for(item.avatar), cf-src=url_for(item.avatar), onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=item.name )\n                          else\n                            img.cf-friends-avatar.no-lightbox(src=url_for(item.avatar) cf-src=url_for(item.avatar) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=item.name )\n                          .flink-item-info\n                            .flink-item-name.cf-friends-name= item.name\n                            .flink-item-desc(title=item.descr)= item.descr\n        else\n          .flink-null 暂无实用网站数据\n        != page.content \n    ```\n\n4.  在`themes/anzhiyu/layout/page.pug`新增页面块\n\n    ```js\n          when 'todolist'\n            include includes/page/todolist.pug\n          when 'useful_links' // 在这里新增\n            include includes/page/useful_links.pug\n    ```\n\n5.  创建搜索功能的JavaScript文件`themes/anzhiyu/source/js/useful-links-search.js`\n\n    ```js\n    /**\n     * 实用网站导航搜索功能\n     * Author: Prorise\n     */\n    \n    const UsefulLinksSearch = {\n        data: [],\n        currentCategory: 'all',\n    \n        init() {\n            console.log('初始化实用网站导航搜索功能...');\n            this.loadData();\n            this.bindEvents();\n            this.updateResultsCount();\n        },\n    \n        loadData() {\n            // 从页面中提取网站数据\n            const linkItems = document.querySelectorAll('.anzhiyu-flink-list .flink-list-item');\n            this.data = [];\n    \n            linkItems.forEach((item, index) => {\n                const nameEl = item.querySelector('.flink-item-name');\n                const descEl = item.querySelector('.flink-item-desc');\n                const linkEl = item.querySelector('.cf-friends-link');\n                const categoryEl = item.closest('.flink').querySelector('h2');\n    \n                if (nameEl && linkEl) {\n                    this.data.push({\n                        index: index,\n                        name: nameEl.textContent.trim(),\n                        description: descEl ? descEl.textContent.trim() : '',\n                        link: linkEl.href,\n                        category: categoryEl ? categoryEl.dataset.category : '',\n                        element: item,\n                        categoryElement: categoryEl\n                    });\n                }\n            });\n    \n            console.log(`加载了 ${this.data.length} 个网站数据`);\n        },\n    \n        bindEvents() {\n            const searchInput = document.getElementById('useful-links-search');\n            const categoryFilters = document.querySelectorAll('.category-filter');\n    \n            if (searchInput) {\n                // 搜索输入框事件\n                searchInput.addEventListener('input', this.debounce((e) => {\n                    this.performSearch(e.target.value);\n                }, 300));\n    \n                // 回车键搜索\n                searchInput.addEventListener('keydown', (e) => {\n                    if (e.key === 'Enter') {\n                        e.preventDefault();\n                        this.performSearch(e.target.value);\n                    }\n                });\n            }\n    \n            // 分类筛选事件\n            categoryFilters.forEach(filter => {\n                filter.addEventListener('click', (e) => {\n                    const category = e.target.dataset.category;\n                    this.filterByCategory(category);\n                    this.updateActiveFilter(e.target);\n    \n                    // 如果不是\"全部\"，则滚动到对应分类\n                    if (category !== 'all') {\n                        this.scrollToCategory(category);\n                    }\n                });\n            });\n        },\n    \n        performSearch(keyword) {\n            keyword = keyword.toLowerCase().trim();\n    \n            if (keyword === '') {\n                this.showAllItems();\n                this.updateResultsCount();\n                return;\n            }\n    \n            let visibleCount = 0;\n            const categoryVisibility = {};\n    \n            this.data.forEach(item => {\n                const matchName = item.name.toLowerCase().includes(keyword);\n                const matchDesc = item.description.toLowerCase().includes(keyword);\n                const isVisible = matchName || matchDesc;\n    \n                // 根据当前分类过滤\n                const categoryMatch = this.currentCategory === 'all' || item.category === this.currentCategory;\n                const shouldShow = isVisible && categoryMatch;\n    \n                if (shouldShow) {\n                    visibleCount++;\n                    // 高亮显示匹配的关键词\n                    this.highlightKeyword(item, keyword);\n                    item.element.classList.remove('hidden');\n    \n                    // 记录该分类下有可见项目\n                    categoryVisibility[item.category] = true;\n                } else {\n                    item.element.classList.add('hidden');\n                }\n            });\n    \n            // 显示/隐藏分类标题\n            this.toggleCategoryTitles(categoryVisibility);\n            this.updateResultsCount(visibleCount, keyword);\n        },\n    \n        filterByCategory(category) {\n            this.currentCategory = category;\n            const searchInput = document.getElementById('useful-links-search');\n            const keyword = searchInput ? searchInput.value : '';\n    \n            if (keyword) {\n                this.performSearch(keyword);\n            } else {\n                this.showCategoryItems(category);\n            }\n        },\n    \n        showCategoryItems(category) {\n            let visibleCount = 0;\n            const categoryVisibility = {};\n    \n            this.data.forEach(item => {\n                const shouldShow = category === 'all' || item.category === category;\n    \n                if (shouldShow) {\n                    visibleCount++;\n                    item.element.classList.remove('hidden');\n                    categoryVisibility[item.category] = true;\n                } else {\n                    item.element.classList.add('hidden');\n                }\n            });\n    \n            this.toggleCategoryTitles(categoryVisibility);\n            this.updateResultsCount(visibleCount);\n        },\n    \n        showAllItems() {\n            this.data.forEach(item => {\n                item.element.classList.remove('hidden');\n                this.removeHighlight(item);\n            });\n    \n            // 显示所有分类标题\n            document.querySelectorAll('.flink h2').forEach(title => {\n                title.style.display = '';\n            });\n        },\n    \n        highlightKeyword(item, keyword) {\n            const nameEl = item.element.querySelector('.flink-item-name');\n            const descEl = item.element.querySelector('.flink-item-desc');\n    \n            if (nameEl) {\n                nameEl.innerHTML = this.highlightText(item.name, keyword);\n            }\n            if (descEl) {\n                descEl.innerHTML = this.highlightText(item.description, keyword);\n            }\n        },\n    \n        removeHighlight(item) {\n            const nameEl = item.element.querySelector('.flink-item-name');\n            const descEl = item.element.querySelector('.flink-item-desc');\n    \n            if (nameEl) {\n                nameEl.textContent = item.name;\n            }\n            if (descEl) {\n                descEl.textContent = item.description;\n            }\n        },\n    \n        highlightText(text, keyword) {\n            if (!text || !keyword) return text;\n    \n            const regex = new RegExp(`(${keyword})`, 'gi');\n            return text.replace(regex, '<span class=\"search-highlight\">$1</span>');\n        },\n    \n        toggleCategoryTitles(categoryVisibility) {\n            document.querySelectorAll('.flink h2').forEach(title => {\n                const category = title.dataset.category;\n                if (this.currentCategory === 'all') {\n                    title.style.display = categoryVisibility[category] ? '' : 'none';\n                } else {\n                    title.style.display = category === this.currentCategory ? '' : 'none';\n                }\n            });\n        },\n    \n        updateActiveFilter(activeFilter) {\n            document.querySelectorAll('.category-filter').forEach(filter => {\n                filter.classList.remove('active');\n            });\n            activeFilter.classList.add('active');\n        },\n    \n        scrollToCategory(categoryName) {\n            const targetTitle = document.querySelector(`h2[data-category=\"${categoryName}\"]`);\n            if (targetTitle && typeof anzhiyu !== 'undefined' && anzhiyu.scrollToDest) {\n                // 使用主题自带的平滑滚动函数\n                const targetTop = anzhiyu.getEleTop(targetTitle) - 120; // 预留搜索框高度\n                anzhiyu.scrollToDest(targetTop, 500);\n            } else if (targetTitle) {\n                // 备用滚动方案\n                targetTitle.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'start',\n                    inline: 'nearest'\n                });\n            }\n        },\n    \n        updateResultsCount(count = null, keyword = '') {\n            const statsEl = document.getElementById('search-results-count');\n            if (!statsEl) return;\n    \n            if (count === null) {\n                count = this.data.filter(item => !item.element.classList.contains('hidden')).length;\n            }\n    \n            if (keyword) {\n                statsEl.textContent = `找到 ${count} 个包含 \"${keyword}\" 的网站`;\n            } else if (this.currentCategory !== 'all') {\n                statsEl.textContent = `${this.currentCategory} 分类下共 ${count} 个网站`;\n            } else {\n                statsEl.textContent = `共 ${count} 个实用网站`;\n            }\n        },\n    \n        // 防抖函数\n        debounce(func, wait) {\n            let timeout;\n            return function executedFunction(...args) {\n                const later = () => {\n                    clearTimeout(timeout);\n                    func(...args);\n                };\n                clearTimeout(timeout);\n                timeout = setTimeout(later, wait);\n            };\n        }\n    };\n    \n    // 页面加载完成后初始化搜索功能\n    document.addEventListener('DOMContentLoaded', () => {\n        // 确保在实用网站导航页面才初始化\n        if (document.getElementById('useful-links-search-container')) {\n            UsefulLinksSearch.init();\n        }\n    });\n    \n    // 支持 PJAX 刷新\n    if (typeof anzhiyu !== 'undefined') {\n        anzhiyu.addGlobalFn('pjax', () => {\n            if (document.getElementById('useful-links-search-container')) {\n                setTimeout(() => {\n                    UsefulLinksSearch.init();\n                }, 100);\n            }\n        });\n    } \n    ```\n\n    6.创建对应的样式文件：`themes/anzhiyu/source/css/useful-links-search.css`\n\n    ```css\n    /**\n     * 实用网站导航搜索功能样式\n     * 适配 AnZhiYu 主题\n     */\n    \n    /* 搜索容器 */\n    #useful-links-search-container {\n      margin-bottom: 2rem;\n      padding: 1.5rem;\n      background: var(--anzhiyu-card-bg);\n      border-radius: 12px;\n      border: var(--style-border);\n      box-shadow: var(--anzhiyu-shadow-border);\n      transition: all 0.3s ease;\n    }\n    \n    #useful-links-search-container:hover {\n      border: var(--style-border-hover);\n      box-shadow: var(--anzhiyu-shadow-theme);\n    }\n    \n    /* 搜索头部 */\n    .search-header {\n      margin-bottom: 1rem;\n    }\n    \n    /* 搜索框样式 */\n    .search-box {\n      position: relative;\n      margin-bottom: 1rem;\n    }\n    \n    .search-box input {\n      width: 100%;\n      padding: 0.75rem 3rem 0.75rem 1rem;\n      border: var(--style-border);\n      border-radius: 8px;\n      background: var(--anzhiyu-secondbg);\n      color: var(--anzhiyu-fontcolor);\n      font-size: 1rem;\n      transition: all 0.3s ease;\n      outline: none;\n    }\n    \n    .search-box input:focus {\n      border: var(--style-border-hover);\n      box-shadow: 0 0 0 3px rgba(var(--anzhiyu-main-rgb), 0.1);\n    }\n    \n    .search-box input::placeholder {\n      color: var(--anzhiyu-secondtext);\n      opacity: 0.8;\n    }\n    \n    /* 搜索图标 */\n    .search-icon {\n      position: absolute;\n      right: 1rem;\n      top: 50%;\n      transform: translateY(-50%);\n      color: var(--anzhiyu-secondtext);\n      font-size: 1.1rem;\n      pointer-events: none;\n      transition: color 0.3s ease;\n    }\n    \n    .search-box input:focus + .search-icon {\n      color: var(--anzhiyu-main);\n    }\n    \n    /* 分类过滤器 */\n    .category-filters {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.5rem;\n      margin-bottom: 0.5rem;\n    }\n    \n    .category-filter {\n      padding: 0.5rem 1rem;\n      background: var(--anzhiyu-secondbg);\n      border: var(--style-border);\n      border-radius: 20px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      font-size: 0.9rem;\n      color: var(--anzhiyu-fontcolor);\n      user-select: none;\n      white-space: nowrap;\n    }\n    \n    .category-filter:hover {\n      background: var(--anzhiyu-main);\n      color: var(--anzhiyu-white);\n      border-color: var(--anzhiyu-main);\n      transform: translateY(-1px);\n    }\n    \n    .category-filter.active {\n      background: var(--anzhiyu-main);\n      color: var(--anzhiyu-white);\n      border-color: var(--anzhiyu-main);\n      box-shadow: 0 2px 8px rgba(var(--anzhiyu-main-rgb), 0.3);\n    }\n    \n    /* 搜索统计信息 */\n    .search-stats {\n      margin-top: 0.5rem;\n      padding-top: 0.5rem;\n      border-top: var(--style-border);\n    }\n    \n    #search-results-count {\n      color: var(--anzhiyu-secondtext);\n      font-size: 0.9rem;\n      font-weight: 500;\n    }\n    \n    /* 搜索结果高亮 */\n    .search-highlight {\n      background: var(--anzhiyu-main);\n      color: var(--anzhiyu-white);\n      padding: 2px 4px;\n      border-radius: 3px;\n      font-weight: bold;\n      box-decoration-break: clone;\n      -webkit-box-decoration-break: clone;\n    }\n    \n    /* 隐藏不匹配的项目 */\n    .flink-list-item.hidden {\n      display: none !important;\n    }\n    \n    .flink h2.hidden {\n      display: none !important;\n    }\n    \n    /* 响应式设计 */\n    @media (max-width: 768px) {\n      #useful-links-search-container {\n        padding: 1rem;\n        margin-bottom: 1.5rem;\n      }\n      \n      .search-box input {\n        padding: 0.6rem 2.5rem 0.6rem 0.8rem;\n        font-size: 0.9rem;\n      }\n      \n      .search-icon {\n        right: 0.8rem;\n        font-size: 1rem;\n      }\n      \n      .category-filter {\n        padding: 0.4rem 0.8rem;\n        font-size: 0.85rem;\n      }\n      \n      .category-filters {\n        gap: 0.4rem;\n      }\n      \n      #search-results-count {\n        font-size: 0.85rem;\n      }\n    }\n    \n    @media (max-width: 480px) {\n      #useful-links-search-container {\n        padding: 0.8rem;\n      }\n      \n      .search-box input {\n        padding: 0.5rem 2rem 0.5rem 0.6rem;\n        font-size: 0.85rem;\n      }\n      \n      .category-filter {\n        padding: 0.3rem 0.6rem;\n        font-size: 0.8rem;\n      }\n    }\n    \n    /* 深色模式适配 */\n    [data-theme='dark'] #useful-links-search-container {\n      border-color: var(--style-border);\n    }\n    \n    [data-theme='dark'] .search-box input {\n      background: var(--anzhiyu-secondbg);\n      border-color: var(--style-border);\n      color: var(--anzhiyu-fontcolor);\n    }\n    \n    [data-theme='dark'] .category-filter {\n      background: var(--anzhiyu-secondbg);\n      border-color: var(--style-border);\n      color: var(--anzhiyu-fontcolor);\n    }\n    \n    /* 动画效果 */\n    .category-filter {\n      position: relative;\n      overflow: hidden;\n    }\n    \n    .category-filter::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: -100%;\n      width: 100%;\n      height: 100%;\n      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n      transition: left 0.5s;\n    }\n    \n    .category-filter:hover::before {\n      left: 100%;\n    }\n    \n    /* 搜索框聚焦动画 */\n    .search-box {\n      position: relative;\n    }\n    \n    .search-box::after {\n      content: '';\n      position: absolute;\n      bottom: 0;\n      left: 50%;\n      width: 0;\n      height: 2px;\n      background: var(--anzhiyu-main);\n      transition: all 0.3s ease;\n      transform: translateX(-50%);\n    }\n    \n    .search-box input:focus ~ ::after {\n      width: 100%;\n    }\n    \n    /* 加载状态 */\n    .search-loading {\n      opacity: 0.6;\n      pointer-events: none;\n    }\n    \n    .search-loading .search-icon::before {\n      content: '\\e6cd'; /* loading icon */\n      animation: spin 1s linear infinite;\n    }\n    \n    @keyframes spin {\n      from { transform: translateY(-50%) rotate(0deg); }\n      to { transform: translateY(-50%) rotate(360deg); }\n    }\n    \n    /* 无结果状态 */\n    .no-results-message {\n      text-align: center;\n      padding: 2rem;\n      color: var(--anzhiyu-secondtext);\n      font-size: 1rem;\n    }\n    \n    .no-results-message i {\n      font-size: 3rem;\n      margin-bottom: 1rem;\n      opacity: 0.5;\n    }\n    \n    /* 滚动到顶部按钮增强（搜索时显示） */\n    .search-active #nav-totop {\n      opacity: 1 !important;\n      transform: translateX(-58px) !important;\n    }\n    \n    /* 分类标题动画 */\n    .flink h2 {\n      transition: all 0.3s ease;\n    }\n    \n    .flink h2[style*=\"display: none\"] {\n      opacity: 0;\n      transform: translateY(-10px);\n    }\n    \n    /* 搜索结果项目动画 */\n    .flink-list-item {\n      transition: all 0.3s ease;\n    }\n    \n    .flink-list-item.hidden {\n      opacity: 0;\n      transform: scale(0.9) translateY(-10px);\n      pointer-events: none;\n    }\n    \n    /* 高亮动画 */\n    .search-highlight {\n      animation: highlight-pulse 1s ease-in-out;\n    }\n    \n    @keyframes highlight-pulse {\n      0% {\n        box-shadow: 0 0 0 0 rgba(var(--anzhiyu-main-rgb), 0.7);\n      }\n      70% {\n        box-shadow: 0 0 0 6px rgba(var(--anzhiyu-main-rgb), 0);\n      }\n      100% {\n        box-shadow: 0 0 0 0 rgba(var(--anzhiyu-main-rgb), 0);\n      }\n    } \n    ```\n\n    7.可以使用`inject`直接注入js和css，但是我们这里就用自己写的按需载入js脚本了\n\n    ```js\n    /**\n     * 按需加载资源管理器\n     * 用于优化网站性能，只在需要时加载特定资源\n     */\n    \n    class ResourceLoader {\n        constructor() {\n            this.loadedCSS = new Set();\n            this.loadedJS = new Set();\n        }\n    \n        /**\n         * 动态加载CSS文件\n         * @param {string} href - CSS文件路径\n         * @param {string} id - 可选的link元素ID\n         */\n        loadCSS(href, id = null) {\n            if (this.loadedCSS.has(href) || document.querySelector(`link[href=\"${href}\"]`)) {\n                return Promise.resolve();\n            }\n    \n            return new Promise((resolve, reject) => {\n                const link = document.createElement('link');\n                link.rel = 'stylesheet';\n                link.href = href;\n                if (id) link.id = id;\n    \n                link.onload = () => {\n                    this.loadedCSS.add(href);\n                    resolve();\n                };\n                link.onerror = reject;\n    \n                document.head.appendChild(link);\n            });\n        }\n    \n        /**\n         * 动态加载JS文件\n         * @param {string} src - JS文件路径\n         * @param {string} id - 可选的script元素ID\n         */\n        loadJS(src, id = null) {\n            if (this.loadedJS.has(src) || document.querySelector(`script[src=\"${src}\"]`)) {\n                return Promise.resolve();\n            }\n    \n            return new Promise((resolve, reject) => {\n                const script = document.createElement('script');\n                script.src = src;\n                if (id) script.id = id;\n    \n                script.onload = () => {\n                    this.loadedJS.add(src);\n                    resolve();\n                };\n                script.onerror = reject;\n    \n                document.body.appendChild(script);\n            });\n        }\n    \n        /**\n     * 检测页面内容并按需加载相关资源\n     */\n        autoDetectAndLoad() {\n            // 检测是否为首页\n            if (window.location.pathname === '/' || window.location.pathname === '/index.html') {\n                // 修复：index_media.css 现在由头部优先加载，只需加载JS\n                this.loadJS('/js/index_media.js', 'index-media-script');\n            }\n    \n            // 检测是否为文章页\n            if (document.querySelector('#post') || document.querySelector('.post-content')) {\n                this.loadCSS('/css/custom-comment.css', 'custom-comment-style');\n                this.loadCSS('/custom/css/tip_style.css', 'tip-style');\n                this.loadJS('/js/fixed_comment.js', 'fixed-comment-script');\n                this.loadJS('/custom/js/tip_main.js', 'tip-main-script');\n            }\n    \n            // 检测B站视频内容\n            if (document.querySelector('iframe[src*=\"bilibili.com\"]') ||\n                document.querySelector('iframe[src*=\"player.bilibili.com\"]')) {\n                this.loadCSS('/css/bilibili.css', 'bilibili-style');\n            }\n    \n            // 检测代码块\n            if (document.querySelector('pre code') || document.querySelector('.highlight')) {\n                this.loadCSS('/custom/css/sandbox_style.css', 'sandbox-style');\n            }\n    \n            // 检测评论区\n            if (document.querySelector('#twikoo') ||\n                document.querySelector('#waline') ||\n                document.querySelector('#valine')) {\n                this.loadJS('/js/comments.js', 'comments-script');\n            }\n    \n            // 检测即刻短文页面\n            if (window.location.pathname.includes('/essay/') || document.querySelector('#essay_page')) {\n                this.loadCSS('/css/essay-style.css', 'essay-style');\n            }\n    \n            // 检测待办清单页面\n            if (window.location.pathname.includes('/todolist/') || document.querySelector('#todolist-box')) {\n                this.loadCSS('/custom/css/todolist.css', 'todolist-style');\n            }\n    \n            // 检测实用网站导航页面\n            if (window.location.pathname.includes('/awesome-links/') ||\n                document.querySelector('#useful-links-container') ||\n                document.querySelector('.useful-links-page')) {\n                this.loadCSS('/css/useful-links-search.css', 'useful-links-search-style');\n                this.loadJS('/js/useful-links-search.js', 'useful-links-search-script');\n            }\n    \n            // 检测侧边栏相关功能\n            if (document.querySelector('#sidebar')) {\n                this.loadCSS('/custom/css/schedule.css', 'schedule-style');\n                this.loadCSS('/custom/css/background-box.css', 'background-style');\n                this.loadJS('https://cdn.jsdelivr.net/npm/winbox@0.2.82/dist/winbox.bundle.min.js', 'winbox-lib')\n                    .then(() => this.loadJS('/custom/js/chineselunar.js', 'chineselunar-script'))\n                    .then(() => this.loadJS('/custom/js/schedule.js', 'schedule-script'))\n                    .then(() => this.loadJS('/custom/js/background-box.js', 'background-script'))\n                    .catch(err => console.warn('侧边栏脚本加载失败:', err));\n            }\n        }\n    }\n    \n    // 创建全局实例\n    window.resourceLoader = new ResourceLoader();\n    \n    // 页面加载完成后自动检测\n    document.addEventListener('DOMContentLoaded', () => {\n        window.resourceLoader.autoDetectAndLoad();\n    });\n    \n    // 为PJAX提供支持\n    document.addEventListener('pjax:complete', () => {\n        window.resourceLoader.autoDetectAndLoad();\n    }); \n    ```\n\n    \n\n---\n###### **第三步：在菜单中添加入口**\n\n1.  打开您**主题的配置文件** (`_config.yml`)。\n2.  在 `menu:` 部分添加一个新链接，指向我们刚刚创建的页面。\n    ```yaml\n    menu:\n      # ...\n      百宝箱: # 或者您喜欢的任何名字\n        实用网站: /awesome-links/ || anzhiyu-icon-compass\n      # ...\n    ```\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-3"><a class="toc-link" href="#22-%E5%86%85%E5%AE%B9%E6%89%A9%E5%B1%95%EF%BC%9A%E5%88%9B%E5%BB%BA%E2%80%9C%E5%AE%9E%E7%94%A8%E7%BD%91%E7%AB%99%E2%80%9D%E5%AF%BC%E8%88%AA%E9%A1%B5"><span class="toc-number">1.</span> <span class="toc-text">22.内容扩展：创建“实用网站”导航页</span></a><ol class="toc-child"><li class="toc-item toc-level-6"><a class="toc-link" href="#%E5%89%8D%E8%A8%80%EF%BC%9A%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D"><span class="toc-number">1.0.0.1.</span> <span class="toc-text">前言：功能介绍</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E5%88%9B%E5%BB%BA%E5%B9%B6%E7%BC%96%E8%BE%91%E6%82%A8%E7%9A%84%E9%93%BE%E6%8E%A5%E6%95%B0%E6%8D%AE%E6%96%87%E4%BB%B6"><span class="toc-number">1.0.0.2.</span> <span class="toc-text">第一步：创建并编辑您的链接数据文件</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E5%88%9B%E5%BB%BA%E9%A1%B5%E9%9D%A2%E5%B9%B6%E4%BD%BF%E7%94%A8%E2%80%9C%E9%AD%94%E6%B3%95%E4%BB%A3%E7%A0%81%E2%80%9D"><span class="toc-number">1.0.0.3.</span> <span class="toc-text">第二步：创建页面并使用“魔法代码”</span></a></li><li class="toc-item toc-level-6"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E6%AD%A5%EF%BC%9A%E5%9C%A8%E8%8F%9C%E5%8D%95%E4%B8%AD%E6%B7%BB%E5%8A%A0%E5%85%A5%E5%8F%A3"><span class="toc-number">1.0.0.4.</span> <span class="toc-text">第三步：在菜单中添加入口</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>