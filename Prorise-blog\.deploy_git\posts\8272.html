<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理进阶（二）：第二章：电商项目立项 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理进阶（二）：第二章：电商项目立项"><meta name="application-name" content="产品经理进阶（二）：第二章：电商项目立项"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="产品经理进阶（二）：第二章：电商项目立项"><meta property="og:url" content="https://prorise666.site/posts/8272.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="第二章：电商项目立项在这一章，我们将正式代入一个实战角色：我们是 “大P超级电商有限公司” 的一名产品经理。 我们的项目背景是：  公司作为集团的子公司，手上有两大王牌资源：一是集团积累的丰富B端商家资源； 二是我们之前搭建的内容资讯类项目，已经吸引了上千万C端用户，其中80%是消费能力和意愿都很强"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://bu.dusays.com/2025/07/25/6882f31a48223.webp"><meta name="description" content="第二章：电商项目立项在这一章，我们将正式代入一个实战角色：我们是 “大P超级电商有限公司” 的一名产品经理。 我们的项目背景是：  公司作为集团的子公司，手上有两大王牌资源：一是集团积累的丰富B端商家资源； 二是我们之前搭建的内容资讯类项目，已经吸引了上千万C端用户，其中80%是消费能力和意愿都很强"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/8272.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"产品经理进阶（二）：第二章：电商项目立项",postAI:"true",pageFillDescription:"第二章：电商项目立项, 2.1 项目立项流程概述, 2.1.1 学习目标, 2.1.2 项目流程与启动节点, 2.1.3 立项评审会议的作用, 2.2 立项说明书的核心构成, 2.2.1 产品概述, 1. 产品定位, 2. 目标用户, 3. 主要功能, 2.2.2 市场分析, 1. 目标市场现状（规模、趋势、结论）, 2. 目标用户分析（分类、特征、需求）, 3. 主要竞争对手（竞品分析、总结）, 2.2.3 产品规划与架构, 1. 认识产品架构, 2. 构建方法与流程：划分角色、需求推导、功能归类, 3. 核心流程图, 4. 初始功能清单, 2.3 产品设计思路[核心], 1. 角色 (Role) - 我们在为谁而设计？, 2. 流程 (Process) - 他们如何完成任务？, 3. 功能 (Function) - 我们需要提供什么界面？, 4. 信息 (Information) - 需要管理哪些数据？, 2.4 制定迭代计划, 2.4.1 学习目标, 2.4.2 迭代计划编制流程, 2.4.3 确立版本目标与功能, 2.5 本章总结第二章电商项目立项在这一章我们将正式代入一个实战角色我们是大超级电商有限公司的一名产品经理我们的项目背景是公司作为集团的子公司手上有两大王牌资源一是集团积累的丰富端商家资源二是我们之前搭建的内容资讯类项目已经吸引了上千万端用户其中是消费能力和意愿都很强的后现在公司高层已经拍板决定正式进军电商领域搭建一个全新的电商平台并且基于我们手有商家心中有用户的现状初步确定版本将采用招商模式主营数码服装家电快消品等类目作为这个项目的核心产品经理我的第一项任务就是要正式地把这个项目立起来项目立项流程概述一个产品的诞生不是一蹴而就的它遵循着一个清晰的生命周期上图就展示了一个经典的产品流程它包含启动规划执行跟进上线这五个核心阶段我们这一章要聚焦的项目立项就是这个流程的第一步也是最关键的启动阶段学习目标在本节中我的目标是带大家清晰地理解项目启动阶段的核心工作我们将学习一个标准的项目流程是怎样的并重点理解立项评审会在整个流程中的关键节点作用项目流程与启动节点在我看来图中的启动阶段包含了行业调研市场调研等一系列前期研究工作而这个阶段的终点和下一规划阶段的起点就是由一个标志性的事件来连接的这个事件就是立项评审会议立项评审会就是整个项目能否正式启动的发令枪只有在这场会议上我的立项方案得到了公司决策层老板各部门负责人的认可和批准这个项目才算真正活了过来可以正式地进入后续的规划和执行阶段获得公司资源的支持立项评审会议的作用为什么这场会议如此重要因为我作为产品经理必须在这场会议上像一名创业者面对投资人一样清晰有力地回答一系列关于这个项目的灵魂拷问我必须为我们的大超级电商项目准备好以下问题的答案决策层最关心的问题我作为需要给出的回答方向这个产品做出来给谁用能帮他们什么给我们现有的千万级后用户帮他们在一个信得过的平台方便地购物给我们已有的端商家帮他们找到新的精准的销售渠道这个产品预计能带来多大的营收初期采用招商模式我们的盈利将主要来自商家的交易提成和平台服务费基于现有用户规模我们预计第一年能实现几亿的商品交易总额带来万的平台收入现在市面上竞争对手有哪些我们有什么竞争力我们的对手是淘宝京东等巨头但我们的核心竞争力在于我们已经拥有了一个庞大的画像清晰的年轻用户群体这能为我们的入驻商家提供更精准的人货匹配降低他们的获客成本这个产品怎么做核心功能是什么的核心功能将围绕招商模式展开包括商家入驻与店铺管理系统商品发布与管理系统用户端交易流程浏览下单支付平台运营后台这个产品大概要多久做出来节奏计划是怎样的我们计划用个月的时间分三个大的里程碑完成的上线第一个里程碑的目标是在个月内完成商家后台的核心功能让第一批种子商家成功入驻而我用来承载以上所有问题答案的我在会前精心准备的关键性文件就是我们下一节要学习的立项说明书立项说明书的核心构成在我开始撰写立项说明书时我不会长篇大论一份好的立项说明书应该是简洁有力逻辑清晰的它的核心目的是在最短的时间内让决策者理解并认同我的项目价值因此我通常会将整个文档划分为三大核心模块产品概述市场分析产品规划这三个模块层层递进分别回答了我们要做什么我们为什么能做和我们准备怎么做这三个终极问题现在我们先来完成第一个也是最重要的模块产品概述这一部分是整个立项说明书的门面是我向决策者展示项目核心价值的电梯演讲我必须用最精炼的语言把产品的定位目标用户和主要功能说清楚产品定位我做的第一件事就是用一句话给我们的产品下一个清晰的定义我习惯使用下面这个公式为目标用户搭建一个什么样的平台提供哪些核心功能来满足他们的什么核心需求现在我们把这个公式应用到我们大超级电商的项目中我们的产品定位是为我们平台已有的千万级后年轻用户搭建一个内容与消费深度融合的招商模式电商平台提供商品搜索智能推荐达人直播担保交易等功能来满足他们追求品质潮流与个性化购物体验的核心需求目标用户在明确了产品定位后我需要对我们的目标用户和目标市场进行更具体的描述目标人群核心人群我们内容资讯平台已有的上千万后用户人群特征互联网原住民消费意愿强是潮流和个性的追随者信任和社区的推荐习惯于在娱乐和内容消费中完成种草和拔草目标市场我们将切入主流的面向年轻消费者的综合电商市场版本主营类目将覆盖数码服装家电快消品等主要功能最后我需要罗列出为了实现我们的产品定位在版本中我们计划提供的主要功能模块这并不是一份详尽的功能清单而是一个高层级的功能蓝图商品导购功能包括商品搜索多级分类品牌馆等帮助用户高效发现商品核心交易功能包括购物车下单集成第三方支付微信支付宝订单管理等构成完整的交易闭环商家店铺功能为我们的端商家提供店铺装修商品上下架订单管理营销工具等后台能力内容与社交功能这是我们的差异化优势包括引入达人直播好物推荐用户评价社区等将内容与电商深度结合基础会员功能包括用户注册登录个人中心地址管理售后服务等市场分析在我完成了对产品的初步构想产品概述之后我必须用冷静客观的数据和分析来向决策者证明我们这个构想不是空中楼阁而是建立在坚实的市场机会之上的市场分析就是我用来提供这份证据的核心模块我通常会从三个维度层层递进地展开我的论证目标市场有多大目标用户是谁主要对手是谁目标市场现状规模趋势结论首先我会从宏观视角来描绘我们即将进入的战场的全貌市场规模这个市场的盘子有多大我会引用权威的行业报告数据如艾瑞易观国家统计局来展示中国网络零售市场的总交易额总用户数等证明这是一个万亿级的足够大的市场市场趋势这个市场是在增长还是萎缩未来的风口在哪里我会分析近几年的数据指出移动电商用户规模增速虽然放缓但存量巨大同时我也会特别指出后乃至后已经成为线上消费的主力军他们的消费习惯如兴趣驱动信任是市场最大的新趋势分析结论我的结论是中国电商市场已从增量竞争进入存量竞争时代未来的机会在于对特定人群的深度运营我们大超级电商项目所拥有的千万级后用户恰好是这个时代最具价值的核心消费人群因此我们进入这个市场具备天然的精准的用户基础市场时机完全吻合目标用户分析分类特征需求在描绘了宏观市场之后我需要将镜头拉近聚焦到我们具体要服务的人身上用户分类我会基于我们现有的用户数据将后这个庞大的群体进一步细分为几个典型的用户画像用户特征我会描述每个分类用户的关键特征用户需求我会提炼出每个分类用户在电商购物这个场景下的核心需求用户分类用户特征核心电商需求潮流大学生无固定收入追求性价比和潮流新品极易受和社区内容种草影响寻找高性价比的潮流服饰数码产品需要分期付款等金融工具渴望通过商品彰显个性职场新人有一定的可支配收入工作繁忙注重效率和生活品质愿意为兴趣和悦己买单需要一站式购齐生活快消品追求品牌和品质愿意为提升效率和体验的服务付费购物决策受内容推荐影响大内容创作者我们平台上的意见领袖拥有自己的粉丝群体有将自身影响力变现的强烈需求需要一个便捷的与内容深度结合的带货渠道将自己推荐的商品高效地销售给粉丝主要竞争对手竞品分析总结最后我需要理性地分析战场上已经存在的强大敌人选择竞品我会选择市场上最主流的与我们目标用户重合度最高的平台作为我们的主要竞争对手即淘宝和京东分析内容我会从零售三要素等维度对竞品进行拆解并与我们自身进行对比分析总结得出我们与竞品相比的优势劣势并最终找到我们的差异化突破口对比维度淘宝京东我们大超级电商的机会产品定位万能的商品市场品质家电数码高效物流内容驱动的潮流社区电商核心优势极其丰富生态成熟自营品控物流体验无与伦比已拥有千万级精准的年轻用户流量获客成本低核心劣势模式品控难用户决策成本高平台模式的商品丰富度不足用户群体偏成熟商业和物流体系需要从到搭建品牌心智未建立我的分析总结是我们无法在多上胜过淘宝也无法在快上胜过京东但我们可以在精和准上建立优势我们的突破口就是深度服务好我们已有的这群年轻用户通过将我们擅长的内容生态与电商交易进行无缝融合打造一个最懂年轻人的内容电商社区以此来建立我们独特的竞争壁垒产品规划与架构在我完成了产品概述和市场分析向决策者们清晰地阐述了我们要做什么和为什么我们能做之后就必须回答最后一个也是最关键的问题我们具体准备怎么做产品规划与架构这一部分就是我用来回答这个问题的施工蓝图正如思考题所提示的在立项会议上一份长长的功能清单往往会让领导感到乏味和困惑我需要用更直观更结构化的方式来呈现我的产品规划认识产品架构在我开始画图之前我必须先澄清两个非常重要但极易混淆的概念产品架构图和产品结构图产品架构图我把它定义为我们产品的城市规划总览图它的核心是表达整个产品业务的宏观框架它通过层级划分和模块组合来呈现产品包含哪些大的业务板块和核心系统它的特点高度抽象重在框架忽略细节它的读者主要是老板业务负责人等决策层目的是让他们在秒内就能看懂我们整个产品的版图产品结构图我把它定义为我们产品的单栋建筑施工图它的核心是将某一个具体的产品模块比如用户端所包含的所有功能和信息进行一次彻底详细的拆解它的特点非常具体巨细靡遗它就像是原型的一种简化表现方式是给我们的项目团队设计师开发测试看的确保大家对某个模块的功能范围有全面统一的认知在立项说明书这个阶段我主要使用的是产品架构图因为它更能服务于我向决策层汇报的宏观视角构建方法与流程划分角色需求推导功能归类那么我是如何为我们的大超级电商项目从零开始一步步地构建出它的产品架构图的呢我遵循一个非常严谨的自顶向下的三步推导思路第一步划分角色确定端在开始任何功能规划之前我首先要识别出我们这个电商生态系统中的核心玩家是谁根据我们招商模式的业务定位我将所有参与者明确地划分为三大角色而这三大角色也直接对应了我们需要建设的三个产品端用户对应我们需要开发的用户端服务于我们千万级的端消费者商家对应我们需要开发的商家端后台服务于入驻我们平台的端商家平台对应我们需要开发的平台端后台服务于我们自己公司的运营和管理人员第二步分析需求推导功能确定了端之后我就需要深入到每一个端的内部站在对应角色的视角去分析他们在具体场景下的核心需求并从中推导出我们必须为他们提供的功能对于用户端我需要思考一个普通消费者在使用我们时他的核心诉求是什么他想买个手机要找一个颜值高性能比较高的这个需求就推导出我们需要提供强大的商品搜索与商品筛选功能他看到一个东西好像不错想看看买过的人有没有说过这个好用这个需求就推导出我们需要建立种草社区或完善的用户评价体系他付钱时不想用支付宝和微信支付想用银行信用卡这个需求就推导出我们的支付模块需要支持多种主流的支付方式对于商家端我需要思考一个入驻商家他的核心诉求是什么他发现这个商品不卖了不要再让用户下单了这个需求就推导出我们需要提供便捷的商品管理功能如商品上下架他想知道这段时间生意不错看看最近卖了多少单这个需求就推导出我们需要提供清晰的订单统计功能他需要看看今天下单的有没有没发货要不要取消订单这个需求就推导出我们需要提供高效的订单管理功能如发货管理对于平台端我需要思考我们作为平台的管理者核心诉令是什么有些店铺入驻后违规操作需要处理下这个诉求就推导出我们需要店铺管理功能如封禁店铺有些用户价格敏感得做一些促销活动这个诉求就推导出我们需要营销管理功能如优惠券配置公司员工有人负责审核店铺有人负责审核商品系统功能不能乱这个诉求就推导出我们需要严谨的权限管理功能第三步功能归类绘制架构最后一步就是将我们在第二步中为各个端推导出的所有功能进行系统性的梳理和归类最终汇总成一张清晰的宏观的产品架构图这张图就是我们整个电商平台版本的总设计蓝图它直观地展示了用户端商家端平台端这三大系统各自包含了哪些核心的功能模块明确了我们本次立项需要投入资源进行建设的全部范围核心流程图除了用架构图来展示静态的有什么我还会附上一到两张最核心的业务流程图来展示动态的怎么用对于我们的电商项目我至少会提供用户核心交易流程图从用户浏览商品到加入购物车再到下单支付的完整流程商家核心操作流程图从商家入驻到发布商品再到处理订单的完整流程初始功能清单最后作为架构图和流程图的补充我会提供一份相对详细的初始功能清单它通常是一份表格会比架构图更具体一些将大的功能模块初步拆解到二级或三级功能点这份清单是对我节中主要功能的进一步细化也是我们下一节制定迭代计划的基础一级模块二级模块三级功能点功能描述优先级备注用户端商品导购商品搜索用户可通过关键词搜索商品需支持模糊搜索商家端商品管理商品列表商品上下架商家可控制商品的在售状态平台端商家管理商家审核查看审核列表运营可查看所有待审核的商家入驻申请产品设计思路核心在我看来一个专业的产品设计绝不是天马行空的艺术创作而是一个严谨的结构化的逻辑推演过程为了确保我的设计不偏离用户价值和商业目标我始终围绕着产品设计四要素来进行思考角色流程功能信息这个框架能帮助我把一个模糊的需求层层剖析最终转化为一个清晰完整可执行的产品方案角色我们在为谁而设计这是我所有思考的绝对起点核心问题这个设计是给谁用的我的实践我从不为一个抽象的模糊的用户做设计我必须清晰地定义出具体的操作角色及其职责例如在我们大超级电商平台中消费者商家和平台运营就是三个完全不同的角色他们的目标诉求使用场景甚至专业能力都截然不同一个为商家设计的追求效率和数据丰富的订单管理后台和一个为消费者设计的追求简洁和美观的我的订单页面其设计思路必然是天壤之别深刻地理解角色是做好用户中心设计的第一步流程他们如何完成任务明确了为谁设计下一步就是思考他要如何完成他的任务核心问题各个角色的操作顺序和规则是怎样的我的实践我通常会从两个角度来梳理流程从目标出发思考角色要达成的最终目标是什么比如消费者的目标是成功购买到心仪的商品我就会围绕这个目标去绘制他从浏览商品到下单支付的完整流程图从角色的分工出发当一个业务流程涉及到多个角色时比如一笔完整的交易我必须梳理清楚他们之间的协作关系和任务交接比如消费者下单付款后流程就交接给了商家去审核订单并发货这就需要我绘制泳道图来清晰地表达功能我们需要提供什么界面当角色的流程被梳理清楚后功能的设计就成了水到渠成的事情核心问题为了支撑上述流程的每一步我们需要提供什么样的功能界面我的实践功能是我们为用户搭建的桥梁用来帮助他们走通流程流程中的每一个动作节点都对应着一个或多个功能比如为了支撑商家审核订单并发货这个流程节点我就需要为他设计一个订单管理的功能界面我的拓展思考在设计功能时我必须时刻带着角色的视角功能是为角色服务的因此我需要为不同角色合理地规划他们可见的功能路径及操作权限比如一个商家的主账号可以看到财务报表功能而客服子账号则无权查看信息需要管理哪些数据这是四个要素中最偏向于技术实现也最容易被产品经理忽略但却至关重要的一环核心问题为了让功能运转起来我们需要管理什么数据我的实践任何一个功能界面其本质都是在对后台的数据进行增删改查在设计功能时我必须同步思考其背后的信息结构有哪些字段这个功能需要展示和编辑哪些数据字段比如订单管理功能就需要处理订单商品名称价格收货人地址等字段有哪些状态这些数据有哪些不同的状态比如一个订单它会有待付款待发货已发货已完成已取消等多种状态我必须定义清楚所有状态以及它们之间流转的规则关联的其他数据这些数据还和哪些其他数据有关联比如一个订单它必然关联着一个用户数据和一个或多个商品数据我始终将角色流程功能信息这四要素作为一个密不可分的整体来进行思考它能保证我的产品设计既有血肉服务于真实的角色和流程又有骨架由清晰的功能和信息构成制定迭代计划在我们完成了立项说明书的撰写并获得了决策层的认可之后我们就拥有了一份包含了海量功能点的总功能清单但是我们不可能也绝不应该在第一个版本里就把所有功能都做完这样做周期太长风险太高因此我必须将这个庞大的清单进行拆解排序分批最终制定出一份清晰合理分阶段的迭代计划学习目标在本节中我的目标是带大家掌握一套从总功能清单到版本计划的完整编制流程我们将学习如何拆解需求设定优先级并最终确立清晰的版本目标和版本功能迭代计划编制流程我编制一份迭代计划通常会遵循一个严谨的四步走的流程第一步明确总体目标首先我会为整个项目设定一个最高层级的带有时间限制的目标对于我们的大超级电商项目这个目标就是在个月内搭建并上线一个能让用户顺利购物商家可以正常经营的招商模式电商平台第二步基于目标拆解需求然后我会围绕这个总体目标将我们在节中制定的初始功能清单进行进一步的细化和补充确保它包含了支撑用户和商家顺利购物正常经营所需要的全部功能点第三步功能拆解与优先级设定这是整个流程中最考验产品经理功力的一步面对长长的功能清单我需要对它们进行分类和排序我通常会将所有功能分为两大类基础共性需求桌子腿这些是整个行业的标配功能用户已经习以为常我们不能没有比如对于电商平台购物车在线支付商品搜索用户评价就属于这类需求没有它们平台的核心流程都跑不通差异化需求亮点这些是体现我们产品特色构建我们核心竞争力的功能是我们有了会更好的部分比如在我们项目中达人直播内容种草社区购物功能如示例中提到的就属于这类需求我的最小可行产品优先级排序原则是优先做完所有桌子腿先让桌子能稳稳地站起来即核心流程能跑通第四步制定迭代计划最后我会将排序后的功能分批地装入到不同的版本中去第一阶段第一版本我会把所有优先级最高的基础共性需求打包进来形成一个能满足最核心业务流程的最小闭环正如示例中所示满足基本购物业务流程需要一大堆功能但第一版本可能只实现了其中最核心的商品列表立即购买支付查看物流退货后续版本在的基础上我再逐步地有节奏地去增加那些能体现我们特色的差异化需求以及一些次要的基础需求确立版本目标与功能迭代计划中的每一个版本我都会用三个要素来对它进行清晰的定义版本号我采用行业标准的语义化版本命名规范大版本号小版本号临时版本号如大版本号当我上线了重量级的新模块比如我们未来上线了直播功能我就会提升大版本号如从升级到小版本号当我只是增加了一些新功能或对现有模块进行优化时我就会提升小版本号如从升级到临时版本号通常用于发布一些紧急的修复如从升级到版本目标每一个版本都必须有一个清晰聚焦的使命比如我们大超级电商的第一个版本版本目标上线一个稳定可用的招商模式电商平台核心目标是跑通用户的核心交易流程从浏览到支付和商家的核心履约流程从发布商品到订单发货版本功能这是为了达成上述目标我最终决定纳入这个版本的功能清单它是我们总功能清单的一个子集本章总结至此我们已经完整地学习了电商项目从到的立项阶段的全部工作项目立项流程概述我们了解了项目启动的标志性节点立项评审会以及我们作为产品经理需要在会上回答的核心问题立项说明书的核心构成我们系统地学习了立项说明书的三大核心模块产品概述确立是什么市场分析论证为什么能产品规划与架构描绘怎么做的蓝图制定迭代计划我们掌握了如何将宏伟的蓝图拆解为一份分阶段有重点可执行的迭代计划明确了我们版本的方向当我们带着这份经过深度思考并获得决策层认可的立项说明书和迭代计划走出立项评审会时我们的电商项目才算真正地正式地扬帆起航了最后我们附上立项说明书模板快速完成立项需求的任务引用站外地址产品立项说明书模板",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-26 21:14:43",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E9%A1%B9%E7%9B%AE%E7%AB%8B%E9%A1%B9"><span class="toc-text">第二章：电商项目立项</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#2-1-%E9%A1%B9%E7%9B%AE%E7%AB%8B%E9%A1%B9%E6%B5%81%E7%A8%8B%E6%A6%82%E8%BF%B0"><span class="toc-text">2.1 项目立项流程概述</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">2.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-2-%E9%A1%B9%E7%9B%AE%E6%B5%81%E7%A8%8B%E4%B8%8E%E5%90%AF%E5%8A%A8%E8%8A%82%E7%82%B9"><span class="toc-text">2.1.2 项目流程与启动节点</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-3-%E7%AB%8B%E9%A1%B9%E8%AF%84%E5%AE%A1%E4%BC%9A%E8%AE%AE%E7%9A%84%E4%BD%9C%E7%94%A8"><span class="toc-text">2.1.3 立项评审会议的作用</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-2-%E7%AB%8B%E9%A1%B9%E8%AF%B4%E6%98%8E%E4%B9%A6%E7%9A%84%E6%A0%B8%E5%BF%83%E6%9E%84%E6%88%90"><span class="toc-text">2.2 立项说明书的核心构成</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-1-%E4%BA%A7%E5%93%81%E6%A6%82%E8%BF%B0"><span class="toc-text">2.2.1 产品概述</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%BA%A7%E5%93%81%E5%AE%9A%E4%BD%8D"><span class="toc-text">1. 产品定位</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%9B%AE%E6%A0%87%E7%94%A8%E6%88%B7"><span class="toc-text">2. 目标用户</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E4%B8%BB%E8%A6%81%E5%8A%9F%E8%83%BD"><span class="toc-text">3. 主要功能</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-2-%E5%B8%82%E5%9C%BA%E5%88%86%E6%9E%90"><span class="toc-text">2.2.2 市场分析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%9B%AE%E6%A0%87%E5%B8%82%E5%9C%BA%E7%8E%B0%E7%8A%B6%EF%BC%88%E8%A7%84%E6%A8%A1%E3%80%81%E8%B6%8B%E5%8A%BF%E3%80%81%E7%BB%93%E8%AE%BA%EF%BC%89"><span class="toc-text">1. 目标市场现状（规模、趋势、结论）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%9B%AE%E6%A0%87%E7%94%A8%E6%88%B7%E5%88%86%E6%9E%90%EF%BC%88%E5%88%86%E7%B1%BB%E3%80%81%E7%89%B9%E5%BE%81%E3%80%81%E9%9C%80%E6%B1%82%EF%BC%89"><span class="toc-text">2. 目标用户分析（分类、特征、需求）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E4%B8%BB%E8%A6%81%E7%AB%9E%E4%BA%89%E5%AF%B9%E6%89%8B%EF%BC%88%E7%AB%9E%E5%93%81%E5%88%86%E6%9E%90%E3%80%81%E6%80%BB%E7%BB%93%EF%BC%89"><span class="toc-text">3. 主要竞争对手（竞品分析、总结）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-3-%E4%BA%A7%E5%93%81%E8%A7%84%E5%88%92%E4%B8%8E%E6%9E%B6%E6%9E%84"><span class="toc-text">2.2.3 产品规划与架构</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%AE%A4%E8%AF%86%E4%BA%A7%E5%93%81%E6%9E%B6%E6%9E%84"><span class="toc-text">1. 认识产品架构</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%9E%84%E5%BB%BA%E6%96%B9%E6%B3%95%E4%B8%8E%E6%B5%81%E7%A8%8B%EF%BC%9A%E5%88%92%E5%88%86%E8%A7%92%E8%89%B2%E3%80%81%E9%9C%80%E6%B1%82%E6%8E%A8%E5%AF%BC%E3%80%81%E5%8A%9F%E8%83%BD%E5%BD%92%E7%B1%BB"><span class="toc-text">2. 构建方法与流程：划分角色、需求推导、功能归类</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E6%A0%B8%E5%BF%83%E6%B5%81%E7%A8%8B%E5%9B%BE"><span class="toc-text">3. 核心流程图</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E5%88%9D%E5%A7%8B%E5%8A%9F%E8%83%BD%E6%B8%85%E5%8D%95"><span class="toc-text">4. 初始功能清单</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-3-%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF-%E6%A0%B8%E5%BF%83"><span class="toc-text">2.3 产品设计思路[核心]</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E8%A7%92%E8%89%B2-Role-%E6%88%91%E4%BB%AC%E5%9C%A8%E4%B8%BA%E8%B0%81%E8%80%8C%E8%AE%BE%E8%AE%A1%EF%BC%9F"><span class="toc-text">1. 角色 (Role) - 我们在为谁而设计？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E6%B5%81%E7%A8%8B-Process-%E4%BB%96%E4%BB%AC%E5%A6%82%E4%BD%95%E5%AE%8C%E6%88%90%E4%BB%BB%E5%8A%A1%EF%BC%9F"><span class="toc-text">2. 流程 (Process) - 他们如何完成任务？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%8A%9F%E8%83%BD-Function-%E6%88%91%E4%BB%AC%E9%9C%80%E8%A6%81%E6%8F%90%E4%BE%9B%E4%BB%80%E4%B9%88%E7%95%8C%E9%9D%A2%EF%BC%9F"><span class="toc-text">3. 功能 (Function) - 我们需要提供什么界面？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-%E4%BF%A1%E6%81%AF-Information-%E9%9C%80%E8%A6%81%E7%AE%A1%E7%90%86%E5%93%AA%E4%BA%9B%E6%95%B0%E6%8D%AE%EF%BC%9F"><span class="toc-text">4. 信息 (Information) - 需要管理哪些数据？</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-4-%E5%88%B6%E5%AE%9A%E8%BF%AD%E4%BB%A3%E8%AE%A1%E5%88%92"><span class="toc-text">2.4 制定迭代计划</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-4-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">2.4.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-4-2-%E8%BF%AD%E4%BB%A3%E8%AE%A1%E5%88%92%E7%BC%96%E5%88%B6%E6%B5%81%E7%A8%8B"><span class="toc-text">2.4.2 迭代计划编制流程</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-4-3-%E7%A1%AE%E7%AB%8B%E7%89%88%E6%9C%AC%E7%9B%AE%E6%A0%87%E4%B8%8E%E5%8A%9F%E8%83%BD"><span class="toc-text">2.4.3 确立版本目标与功能</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-5-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-text">2.5 本章总结</span></a></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理进阶（二）：第二章：电商项目立项</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-24T09:13:45.000Z" title="发表于 2025-07-24 17:13:45">2025-07-24</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-26T13:14:43.192Z" title="更新于 2025-07-26 21:14:43">2025-07-26</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">8k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>23分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理进阶（二）：第二章：电商项目立项"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/8272.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/8272.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理进阶（二）：第二章：电商项目立项</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-24T09:13:45.000Z" title="发表于 2025-07-24 17:13:45">2025-07-24</time><time itemprop="dateCreated datePublished" datetime="2025-07-26T13:14:43.192Z" title="更新于 2025-07-26 21:14:43">2025-07-26</time></header><div id="postchat_postcontent"><h1 id="第二章：电商项目立项"><a href="#第二章：电商项目立项" class="headerlink" title="第二章：电商项目立项"></a>第二章：电商项目立项</h1><p>在这一章，我们将正式代入一个实战角色：我们是 <strong>“大P超级电商有限公司”</strong> 的一名产品经理。</p><p><strong>我们的项目背景是：</strong></p><blockquote><p>公司作为集团的子公司，手上有两大王牌资源：一是集团积累的<strong>丰富B端商家资源</strong>；</p><p>二是我们之前搭建的内容资讯类项目，已经吸引了<strong>上千万C端用户</strong>，其中80%是消费能力和意愿都很强的“90后”。</p><p>现在，公司高层已经拍板，决定正式进军电商领域，搭建一个全新的电商平台。并且，基于我们“手有商家、心中有用户”的现状，初步确定V1.0版本将采用<strong>招商模式</strong>，主营数码、服装、家电、快消品等类目。</p></blockquote><p>作为这个项目的核心产品经理，我的第一项任务，就是要<strong>正式地把这个项目“立”起来</strong>。</p><h2 id="2-1-项目立项流程概述"><a href="#2-1-项目立项流程概述" class="headerlink" title="2.1 项目立项流程概述"></a>2.1 项目立项流程概述</h2><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721193939182.png" alt="image-20250721193939182"></p><p>一个产品的诞生，不是一蹴而就的，它遵循着一个清晰的生命周期。上图就展示了一个经典的产品流程，它包含<strong>启动、规划、执行、跟进、上线</strong>这五个核心阶段。</p><p>我们这一章要聚焦的“项目立项”，就是这个流程的第一步，也是最关键的“<strong>启动</strong>”阶段。</p><h3 id="2-1-1-学习目标"><a href="#2-1-1-学习目标" class="headerlink" title="2.1.1 学习目标"></a>2.1.1 学习目标</h3><p>在本节中，我的目标是带大家清晰地理解项目“启动”阶段的核心工作。</p><p>我们将学习一个标准的项目流程是怎样的，并重点理解“<strong>立项评审会</strong>”在整个流程中的关键节点作用。</p><h3 id="2-1-2-项目流程与启动节点"><a href="#2-1-2-项目流程与启动节点" class="headerlink" title="2.1.2 项目流程与启动节点"></a>2.1.2 项目流程与启动节点</h3><p>在我看来，图中的“<strong>启动</strong>”阶段，包含了<code>行业调研</code>、<code>市场调研</code>等一系列前期研究工作。而这个阶段的终点，和下一“<strong>规划</strong>”阶段的起点，就是由一个标志性的事件来连接的，这个事件就是“<strong>立项评审会议</strong>”。</p><p><strong>立项评审会</strong>，就是整个项目能否正式启动的“<strong>发令枪</strong>”。只有在这场会议上，我的立项方案得到了公司决策层（老板、各部门负责人）的认可和批准，这个项目才算真正“活了过来”，可以正式地进入后续的规划和执行阶段，获得公司资源的支持。</p><h3 id="2-1-3-立项评审会议的作用"><a href="#2-1-3-立项评审会议的作用" class="headerlink" title="2.1.3 立项评审会议的作用"></a>2.1.3 立项评审会议的作用</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721194117623.png" alt="image-20250721194117623"></p><p>为什么这场会议如此重要？因为我作为产品经理，必须在这场会议上，像一名“创业者”面对投资人一样，清晰、有力地回答一系列关于这个项目的灵魂拷问。</p><p>我必须为我们的“<strong>大P超级电商</strong>”项目，准备好以下问题的答案：</p><table><thead><tr><th align="left"><strong>决策层最关心的问题</strong></th><th align="left"><strong>我（作为PM）需要给出的回答方向</strong></th></tr></thead><tbody><tr><td align="left"><strong>1. 这个产品做出来给谁用？能帮他们什么？</strong></td><td align="left"><strong>（WHO &amp; WHY）</strong> 给我们现有的千万级“90后”用户，帮他们在一个信得过的平台方便地购物；给我们已有的B端商家，帮他们找到新的、精准的销售渠道。</td></tr><tr><td align="left"><strong>2. 这个产品预计能带来多大的营收？</strong></td><td align="left"><strong>（HOW MUCH）</strong> 初期采用招商模式，我们的盈利将主要来自商家的<strong>交易提成</strong>和<strong>平台服务费</strong>。基于现有用户规模，我们预计第一年能实现几亿的GMV（商品交易总额），带来XX万的平台收入。</td></tr><tr><td align="left"><strong>3. 现在市面上竞争对手有哪些？我们有什么竞争力？</strong></td><td align="left"><strong>（COMPETITION）</strong> 我们的对手是淘宝、京东等巨头。但我们的核心竞争力在于，我们<strong>已经拥有了一个庞大的、画像清晰的年轻用户群体</strong>，这能为我们的入驻商家，提供更精准的“人货匹配”，降低他们的获客成本。</td></tr><tr><td align="left"><strong>4. 这个产品怎么做？核心功能是什么？</strong></td><td align="left"><strong>（WHAT）</strong> V1.0的核心功能，将围绕招商模式展开，包括：商家入驻与店铺管理系统、商品发布与管理系统、用户端交易流程（浏览-下单-支付）、平台运营后台。</td></tr><tr><td align="left"><strong>5. 这个产品大概要多久做出来？节奏计划是怎样的？</strong></td><td align="left"><strong>（WHEN）</strong> 我们计划用6个月的时间，分三个大的里程碑，完成V1.0的上线。第一个里程碑的目标，是在2个月内，完成商家后台的核心功能，让第一批种子商家成功入驻。</td></tr></tbody></table><p>而我用来承载以上所有问题答案的、我在会前精心准备的“<strong>关键性文件</strong>”，就是我们下一节要学习的“<strong>立项说明书</strong>”。</p><hr><h2 id="2-2-立项说明书的核心构成"><a href="#2-2-立项说明书的核心构成" class="headerlink" title="2.2 立项说明书的核心构成"></a>2.2 立项说明书的核心构成</h2><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721194740655.png" alt="image-20250721194740655"></p><p>在我开始撰写立项说明书时，我不会长篇大论。一份好的立项说明书，应该是<strong>简洁、有力、逻辑清晰</strong>的。它的核心目的，是在最短的时间内，让决策者理解并认同我的项目价值。</p><p>因此，我通常会将整个文档，划分为三大核心模块：<strong>产品概述、市场分析、产品规划</strong>。这三个模块，层层递进，分别回答了</p><p>“<strong>我们要做什么？</strong>”、“<strong>我们为什么能做？</strong>”和“<strong>我们准备怎么做？</strong>”这三个终极问题。</p><p>现在，我们先来完成第一个，也是最重要的模块。</p><h3 id="2-2-1-产品概述"><a href="#2-2-1-产品概述" class="headerlink" title="2.2.1 产品概述"></a>2.2.1 产品概述</h3><p>这一部分，是整个立项说明书的“门面”，是我向决策者展示项目核心价值的“电梯演讲”。我必须用最精炼的语言，把产品的定位、目标用户和主要功能说清楚。</p><h4 id="1-产品定位"><a href="#1-产品定位" class="headerlink" title="1. 产品定位"></a>1. 产品定位</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721194856262.png" alt="image-20250721194856262"></p><p>我做的第一件事，就是用一句话，给我们的产品下一个清晰的<strong>定义</strong>。我习惯使用下面这个公式：<br><strong>为【目标用户】，搭建一个【什么样的平台】，提供【哪些核心功能】，来满足他们的【什么核心需求】。</strong></p><p>现在，我们把这个公式，应用到我们“<strong>大P超级电商</strong>”的项目中：</p><blockquote><p><strong>我们的产品定位是：</strong><br><strong>为</strong>我们平台已有的千万级“90后”年轻用户，<strong>搭建</strong>一个内容与消费深度融合的<strong>招商模式电商平台</strong>，<strong>提供</strong>商品搜索、智能推荐、达人直播、担保交易<strong>等功能</strong>，来<strong>满足</strong>他们追求品质、潮流与个性化购物体验的<strong>核心需求</strong>。</p></blockquote><h4 id="2-目标用户"><a href="#2-目标用户" class="headerlink" title="2. 目标用户"></a>2. 目标用户</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721194947268.png" alt="image-20250721194947268"></p><p>在明确了产品定位后，我需要对我们的“<strong>目标用户</strong>”和“<strong>目标市场</strong>”，进行更具体的描述。</p><ul><li><p><strong>目标人群</strong>：</p><ul><li><strong>核心人群</strong>：我们内容资讯平台已有的<strong>上千万“90后”用户</strong>。</li><li><strong>人群特征</strong>：互联网原住民，消费意愿强，是潮流和个性的追随者；信任KOL和社区的推荐，习惯于在娱乐和内容消费中，完成“种草”和“拔草”。</li></ul></li><li><p><strong>目标市场</strong>：</p><ul><li>我们将切入主流的、面向年轻消费者的<strong>B2C综合电商市场</strong>，V1.0版本主营类目将覆盖<strong>数码、服装、家电、快消品</strong>等。</li></ul></li></ul><h4 id="3-主要功能"><a href="#3-主要功能" class="headerlink" title="3. 主要功能"></a>3. 主要功能</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195036522.png" alt="image-20250721195036522"></p><p>最后，我需要罗列出，为了实现我们的产品定位，在V1.0版本中，我们计划提供的<strong>主要功能模块</strong>。这并不是一份详尽的功能清单，而是一个高层级的“功能蓝图”。</p><ul><li><strong>1. 商品导购功能</strong>：包括商品搜索、多级分类、品牌馆等，帮助用户高效发现商品。</li><li><strong>2. 核心交易功能</strong>：包括购物车、下单、集成第三方支付（微信/支付宝）、订单管理等，构成完整的交易闭环。</li><li><strong>3. 商家店铺功能</strong>：为我们的B端商家提供店铺装修、商品上下架、订单管理、营销工具等后台能力。</li><li><strong>4. 内容与社交功能</strong>：这是我们的差异化优势。包括引入达人直播、好物推荐、用户评价社区等，将内容与电商深度结合。</li><li><strong>5. 基础会员功能</strong>：包括用户注册登录、个人中心、地址管理、售后服务等。</li></ul><hr><h3 id="2-2-2-市场分析"><a href="#2-2-2-市场分析" class="headerlink" title="2.2.2 市场分析"></a>2.2.2 市场分析</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195532876.png" alt="image-20250721195532876"></p><p>在我完成了对产品的初步构想（产品概述）之后，我必须用<strong>冷静、客观的数据和分析</strong>，来向决策者证明：我们这个构想，不是空中楼阁，而是建立在坚实的市场机会之上的。</p><p><strong>市场分析</strong>，就是我用来提供这份“证据”的核心模块。我通常会从三个维度，层层递进地展开我的论证：<strong>目标市场有多大？目标用户是谁？主要对手是谁？</strong></p><h4 id="1-目标市场现状（规模、趋势、结论）"><a href="#1-目标市场现状（规模、趋势、结论）" class="headerlink" title="1. 目标市场现状（规模、趋势、结论）"></a>1. 目标市场现状（规模、趋势、结论）</h4><p>首先，我会从宏观视角，来描绘我们即将进入的“战场”的全貌。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195631374.png" alt="image-20250721195631374"></p><ul><li><strong>市场规模 (Market Scale)</strong>：这个市场的“盘子”有多大？我会引用权威的行业报告数据（如艾瑞、易观、国家统计局），来展示中国网络零售市场的总交易额（GMV）、总用户数等，证明这是一个万亿级的、足够大的市场。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195653877.png" alt="image-20250721195653877"></p><ul><li><strong>市场趋势 (Market Trends)</strong>：这个市场是在增长还是萎缩？未来的风口在哪里？我会分析近几年的数据，指出移动电商用户规模增速虽然放缓，但存量巨大。同时，我也会特别指出，“90后”乃至“00后”已经成为线上消费的主力军，他们的消费习惯（如兴趣驱动、信任KOL）是市场最大的新趋势。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195704847.png" alt="image-20250721195704847"></p><ul><li><p><strong>分析结论 (Analysis Conclusion)</strong>：</p><blockquote><p><strong>我的结论是</strong>：中国电商市场已从增量竞争，进入存量竞争时代。未来的机会，在于<strong>对特定人群的深度运营</strong>。我们“大P超级电商”项目，所拥有的“千万级90后用户”，恰好是这个时代最具价值的核心消费人群。因此，我们进入这个市场，具备天然的、精准的用户基础，<strong>市场时机完全吻合</strong>。</p></blockquote></li></ul><h4 id="2-目标用户分析（分类、特征、需求）"><a href="#2-目标用户分析（分类、特征、需求）" class="headerlink" title="2. 目标用户分析（分类、特征、需求）"></a>2. 目标用户分析（分类、特征、需求）</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721202152110.png" alt="image-20250721202152110"></p><p>在描绘了宏观市场之后，我需要将镜头拉近，聚焦到我们“<strong>具体要服务的人</strong>”身上。</p><ul><li><strong>用户分类 (User Classification)</strong>：我会基于我们现有的用户数据，将“90后”这个庞大的群体，进一步细分为几个典型的用户画像（Persona）。</li><li><strong>用户特征 (User Characteristics)</strong>：我会描述每个分类用户的关键特征。</li><li><strong>用户需求 (User Needs)</strong>：我会提炼出每个分类用户，在“电商购物”这个场景下的核心需求。</li></ul><table><thead><tr><th align="left"><strong>用户分类</strong></th><th align="left"><strong>用户特征</strong></th><th align="left"><strong>核心电商需求</strong></th></tr></thead><tbody><tr><td align="left"><strong>潮流大学生</strong></td><td align="left">无固定收入，追求性价比和潮流新品，极易受KOL和社区内容“种草”影响。</td><td align="left">寻找高性价比的潮流服饰、数码产品；需要分期付款等金融工具；渴望通过商品彰显个性。</td></tr><tr><td align="left"><strong>职场新人</strong></td><td align="left">有一定的可支配收入，工作繁忙，注重效率和生活品质，愿意为兴趣和“悦己”买单。</td><td align="left">需要一站式购齐生活快消品；追求品牌和品质；愿意为提升效率和体验的服务付费；购物决策受内容推荐影响大。</td></tr><tr><td align="left"><strong>内容创作者/KOL</strong></td><td align="left">我们平台上的意见领袖，拥有自己的粉丝群体，有将自身影响力变现的强烈需求。</td><td align="left">需要一个便捷的、与内容深度结合的“带货”渠道，将自己推荐的商品，高效地销售给粉丝。</td></tr></tbody></table><h4 id="3-主要竞争对手（竞品分析、总结）"><a href="#3-主要竞争对手（竞品分析、总结）" class="headerlink" title="3. 主要竞争对手（竞品分析、总结）"></a>3. 主要竞争对手（竞品分析、总结）</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721202259417.png" alt="image-20250721202259417"></p><p>最后，我需要理性地分析“战场”上已经存在的“强大敌人”。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721202310828.png" alt="image-20250721202310828"></p><ul><li><strong>选择竞品</strong>：我会选择市场上最主流的、与我们目标用户重合度最高的平台作为我们的主要竞争对手，即<strong>淘宝</strong>和<strong>京东</strong>。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721202400825.png" alt="image-20250721202400825"></p><ul><li><p><strong>分析内容</strong>：我会从“零售三要素”等维度，对竞品进行拆解，并与我们自身进行对比。</p></li><li><p><strong>分析总结</strong>：得出我们与竞品相比的优势、劣势，并最终找到我们的“<strong>差异化突破口</strong>”。</p></li></ul><table><thead><tr><th align="left"><strong>对比维度</strong></th><th align="left"><strong>淘宝 (Taobao)</strong></th><th align="left"><strong>京东 (JD.com)</strong></th><th align="left"><strong>我们 (大P超级电商) 的机会</strong></th></tr></thead><tbody><tr><td align="left"><strong>产品定位</strong></td><td align="left">万能的商品市场</td><td align="left">品质家电数码、高效物流</td><td align="left"><strong>内容驱动的潮流社区电商</strong></td></tr><tr><td align="left"><strong>核心优势</strong></td><td align="left">SKU极其丰富、生态成熟</td><td align="left">自营品控、物流体验无与伦比</td><td align="left"><strong>已拥有千万级精准的年轻用户流量，获客成本低</strong></td></tr><tr><td align="left"><strong>核心劣势</strong></td><td align="left">C2C模式品控难，用户决策成本高</td><td align="left">平台模式的商品丰富度不足，用户群体偏成熟</td><td align="left">商业和物流体系需要从0到1搭建，品牌心智未建立</td></tr></tbody></table><p><strong>我的分析总结是</strong>：我们无法在“多”上胜过淘宝，也无法在“快”上胜过京东。</p><p>但我们可以在“<strong>精</strong>”和“<strong>准</strong>”上建立优势。我们的突破口，就是<strong>深度服务好我们已有的这群年轻用户</strong>，通过将我们擅长的<strong>内容生态</strong>与<strong>电商交易</strong>进行无缝融合，打造一个“<strong>最懂年轻人的内容电商社区</strong>”，以此来建立我们独特的竞争壁垒。</p><hr><h3 id="2-2-3-产品规划与架构"><a href="#2-2-3-产品规划与架构" class="headerlink" title="2.2.3 产品规划与架构"></a>2.2.3 产品规划与架构</h3><p>在我完成了产品概述和市场分析，向决策者们清晰地阐述了“<strong>我们要做什么</strong>”和“<strong>为什么我们能做</strong>”之后，就必须回答最后一个，也是最关键的问题：“<strong>我们具体准备怎么做？</strong>”</p><p><strong>产品规划与架构</strong>这一部分，就是我用来回答这个问题的“施工蓝图”。正如思考题所提示的，在立项会议上，一份长长的功能清单，往往会让领导感到乏味和困惑。我需要用更直观、更结构化的方式，来呈现我的产品规划。</p><h4 id="1-认识产品架构"><a href="#1-认识产品架构" class="headerlink" title="1. 认识产品架构"></a>1. 认识产品架构</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721203548034.png" alt="image-20250721203548034"></p><p>在我开始画图之前，我必须先澄清两个非常重要、但极易混淆的概念：<strong>产品架构图</strong>和<strong>产品结构图</strong>。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721203749198.png" alt="image-20250721203749198"></p><ul><li><p><strong>产品架构图</strong><br>我把它定义为，我们产品的“<strong>城市规划总览图</strong>”。</p><ul><li><strong>它的核心</strong>：是表达整个产品业务的<strong>宏观框架</strong>。它通过层级划分和模块组合，来呈现产品包含哪些大的业务板块和核心系统。</li><li><strong>它的特点</strong>：<strong>高度抽象，重在框架，忽略细节</strong>。它的读者主要是老板、业务负责人等决策层，目的是让他们在30秒内，就能看懂我们整个产品的版图。</li></ul></li><li><p><strong>产品结构图 (Product Structure Diagram)</strong><br>我把它定义为，我们产品的“<strong>单栋建筑施工图</strong>”。</p><ul><li><strong>它的核心</strong>：是将某一个具体的产品模块（比如用户端App），所包含的<strong>所有功能和信息</strong>，进行一次彻底、详细的拆解。</li><li><strong>它的特点</strong>：<strong>非常具体，巨细靡遗</strong>。它就像是原型的一种“简化表现方式”，是给我们的项目团队（设计师、开发、测试）看的，确保大家对某个模块的功能范围，有全面、统一的认知。</li></ul></li></ul><p>在“<strong>立项说明书</strong>”这个阶段，我主要使用的是“<strong>产品架构图</strong>”，因为它更能服务于我向决策层汇报的宏观视角。</p><hr><h4 id="2-构建方法与流程：划分角色、需求推导、功能归类"><a href="#2-构建方法与流程：划分角色、需求推导、功能归类" class="headerlink" title="2. 构建方法与流程：划分角色、需求推导、功能归类"></a>2. 构建方法与流程：划分角色、需求推导、功能归类</h4><p>那么，我是如何为我们的“大P超级电商”项目，从零开始，一步步地构建出它的产品架构图的呢？我遵循一个非常严谨的、自顶向下的三步推导思路。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210258871.png" alt="image-20250721210258871"></p><p><strong>第一步：划分角色，确定“端”</strong></p><p>在开始任何功能规划之前，我首先要识别出我们这个电商生态系统中的核心“玩家”是谁。根据我们“招商模式”的业务定位，我将所有参与者，明确地划分为三大角色，而这三大角色，也直接对应了我们需要建设的三个产品“端”：</p><ul><li><strong>用户 (User)</strong>：对应我们需要开发的 <strong>用户端</strong> App，服务于我们千万级的C端消费者。</li><li><strong>商家 (Merchant)</strong>：对应我们需要开发的 <strong>商家端</strong> 后台，服务于入驻我们平台的B端商家。</li><li><strong>平台 (Platform)</strong>：对应我们需要开发的 <strong>平台端</strong> 后台，服务于我们自己公司的运营和管理人员。</li></ul><p><strong>第二步：分析需求，推导功能</strong></p><p>确定了“端”之后，我就需要深入到每一个“端”的内部，站在对应角色的视角，去分析他们在具体场景下的核心需求，并从中推导出我们必须为他们提供的功能。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210755456.png" alt="image-20250721210755456"></p><ul><li><strong>对于用户端</strong>：我需要思考，一个普通消费者在使用我们App时，他的核心诉求是什么？<ul><li>他“想买个手机，要找一个颜值高、性能比较高的”，这个需求就推导出我们需要提供强大的 <strong>商品搜索</strong> 与 <strong>商品筛选</strong> 功能。</li><li>他“看到一个东西好像不错，想看看买过的人有没有说过这个好用”，这个需求就推导出我们需要建立 <strong>种草</strong> 社区或完善的 <strong>用户评价</strong> 体系。</li><li>他“付钱时不想用支付宝和微信支付，想用银行信用卡”，这个需求就推导出我们的 <strong>支付</strong> 模块，需要支持多种主流的支付方式。</li></ul></li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210806766.png" alt="image-20250721210806766"></p><ul><li><strong>对于商家端</strong>：我需要思考，一个入驻商家，他的核心诉求是什么？<ul><li>他发现“这个商品不卖了，不要再让用户下单了”，这个需求就推导出我们需要提供便捷的 <strong>商品管理</strong> 功能（如：商品上下架）。</li><li>他想知道“这段时间生意不错，看看最近卖了多少单？”，这个需求就推导出我们需要提供清晰的 <strong>订单统计</strong> 功能。</li><li>他需要“看看今天下单的有没有没发货，要不要取消订单”，这个需求就推导出我们需要提供高效的 <strong>订单管理</strong> 功能（如：发货管理）。</li></ul></li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210819557.png" alt="image-20250721210819557"></p><ul><li><strong>对于平台端</strong>：我需要思考，我们作为平台的管理者，核心诉令是什么？<ul><li>“有些店铺入驻后违规操作，需要处理下”，这个诉求就推导出我们需要 <strong>店铺管理</strong> 功能（如：封禁店铺）。</li><li>“有些用户价格敏感，得做一些促销活动”，这个诉求就推导出我们需要 <strong>营销管理</strong> 功能（如：优惠券配置）。</li><li>“公司员工有人负责审核店铺，有人负责审核商品，系统功能不能乱”，这个诉求就推导出我们需要严谨的 <strong>权限管理</strong> 功能。</li></ul></li></ul><p><strong>第三步：功能归类，绘制架构</strong></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210949605.png" alt="image-20250721210949605"></p><p>最后一步，就是将我们在第二步中，为各个“端”推导出的所有功能，进行系统性的梳理和归类，最终汇总成一张清晰的、宏观的“<strong>产品架构图</strong>”。</p><p>这张图，就是我们整个电商平台V1.0版本的“总设计蓝图”。它直观地展示了用户端、商家端、平台端这三大系统，各自包含了哪些核心的功能模块，明确了我们本次立项需要投入资源进行建设的全部范围。</p><h4 id="3-核心流程图"><a href="#3-核心流程图" class="headerlink" title="3. 核心流程图"></a>3. 核心流程图</h4><p>除了用“架构图”来展示静态的“<strong>有什么</strong>”，我还会附上一到两张最核心的“<strong>业务流程图</strong>”，来展示动态的“<strong>怎么用</strong>”。</p><p>对于我们的电商项目，我至少会提供：</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E4%B8%9A%E5%8A%A1%E6%B5%81%E7%A8%8B%E5%9B%BE.png" alt="业务流程图"></p><ul><li><p><strong>用户核心交易流程图</strong>：从用户浏览商品，到加入购物车，再到下单、支付的完整流程。</p></li><li><p><strong>商家核心操作流程图</strong>：从商家入驻，到发布商品，再到处理订单的完整流程。</p></li></ul><h4 id="4-初始功能清单"><a href="#4-初始功能清单" class="headerlink" title="4. 初始功能清单"></a>4. 初始功能清单</h4><p>最后，作为架构图和流程图的补充，我会提供一份相对详细的“<strong>初始功能清单（Function List）</strong>”。</p><p>它通常是一份Excel表格，会比架构图更具体一些，将大的功能模块，初步拆解到二级或三级功能点。这份清单，是对我<code>2.2.1</code>节中“主要功能”的进一步细化，也是我们下一节“制定迭代计划”的基础。</p><table><thead><tr><th align="left">一级模块</th><th align="left">二级模块</th><th align="left">三级功能点</th><th align="left">功能描述</th><th align="left">优先级</th><th align="left">备注</th></tr></thead><tbody><tr><td align="left"><strong>用户端</strong></td><td align="left">商品导购</td><td align="left">商品搜索</td><td align="left">用户可通过关键词搜索商品</td><td align="left">P0</td><td align="left">需支持模糊搜索</td></tr><tr><td align="left"><strong>商家端</strong></td><td align="left">商品管理</td><td align="left">商品列表</td><td align="left">商品上下架</td><td align="left">P0</td><td align="left">商家可控制商品的在售状态</td></tr><tr><td align="left"><strong>平台端</strong></td><td align="left">商家管理</td><td align="left">商家审核</td><td align="left">查看审核列表</td><td align="left">P0</td><td align="left">运营可查看所有待审核的商家入驻申请</td></tr></tbody></table><hr><h2 id="2-3-产品设计思路-核心"><a href="#2-3-产品设计思路-核心" class="headerlink" title="2.3 产品设计思路[核心]"></a>2.3 <code>产品设计思路[核心]</code></h2><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721212555967.png" alt="image-20250721212555967"></p><p>在我看来，一个专业的产品设计，绝不是天马行空的艺术创作，而是一个<strong>严谨的、结构化的逻辑推演过程</strong>。为了确保我的设计不偏离用户价值和商业目标，我始终围绕着<code>“产品设计四要素”</code>来进行思考：<strong>角色、流程、功能、信息</strong>。</p><p>这个框架，能帮助我把一个模糊的需求，层层剖析，最终转化为一个清晰、完整、可执行的产品方案。</p><h3 id="1-角色-Role-我们在为谁而设计？"><a href="#1-角色-Role-我们在为谁而设计？" class="headerlink" title="1. 角色 (Role) - 我们在为谁而设计？"></a>1. 角色 (Role) - 我们在为谁而设计？</h3><p>这是我所有思考的<strong>绝对起点</strong>。</p><ul><li><strong>核心问题</strong>：“<strong>这个设计是给谁用的？</strong>”</li><li><strong>我的实践</strong>：我从不为一个抽象的、模糊的“用户”做设计。我必须清晰地定义出<strong>具体的操作角色及其职责</strong>。例如，在我们“大P超级电商”平台中，<code>消费者</code>、<code>商家</code>和<code>平台运营</code>就是三个完全不同的角色。他们的目标、诉求、使用场景、甚至专业能力都截然不同。一个为<code>商家</code>设计的、追求效率和数据丰富的“订单管理”后台，和一个为<code>消费者</code>设计的、追求简洁和美观的“我的订单”页面，其设计思路必然是天壤之别。<strong>深刻地理解“角色”，是做好用户中心设计的第一步。</strong></li></ul><h3 id="2-流程-Process-他们如何完成任务？"><a href="#2-流程-Process-他们如何完成任务？" class="headerlink" title="2. 流程 (Process) - 他们如何完成任务？"></a>2. 流程 (Process) - 他们如何完成任务？</h3><p>明确了“为谁设计”，下一步就是思考“<strong>他要如何完成他的任务</strong>”。</p><ul><li><strong>核心问题</strong>：“<strong>各个角色的操作顺序和规则是怎样的？</strong>”</li><li><strong>我的实践</strong>：我通常会从两个角度来梳理流程：<ul><li><strong>从目标出发</strong>：思考角色要达成的最终目标是什么。比如，<code>消费者</code>的目标是“成功购买到心仪的商品”。我就会围绕这个目标，去绘制他从“浏览商品”到“下单支付”的完整流程图。</li><li><strong>从角色的分工出发</strong>：当一个业务流程涉及到多个角色时（比如一笔完整的交易），我必须梳理清楚他们之间的<strong>协作关系和任务交接</strong>。比如，<code>消费者</code>“下单付款”后，流程就交接给了<code>商家</code>去“审核订单并发货”，这就需要我绘制“泳道图”来清晰地表达。</li></ul></li></ul><h3 id="3-功能-Function-我们需要提供什么界面？"><a href="#3-功能-Function-我们需要提供什么界面？" class="headerlink" title="3. 功能 (Function) - 我们需要提供什么界面？"></a>3. 功能 (Function) - 我们需要提供什么界面？</h3><p>当角色的流程被梳理清楚后，“功能”的设计就成了水到渠成的事情。</p><ul><li><strong>核心问题</strong>：“<strong>为了支撑上述流程的每一步，我们需要提供什么样的功能界面？</strong>”</li><li><strong>我的实践</strong>：“功能”是我们为用户搭建的“桥梁”，用来帮助他们走通“流程”。流程中的每一个“动作节点”，都对应着一个或多个“功能”。比如，为了支撑<code>商家</code>“审核订单并发货”这个流程节点，我就需要为他设计一个“<strong>订单管理</strong>”的功能界面。</li><li><strong>我的拓展思考</strong>：在设计功能时，我必须时刻带着“角色”的视角。<strong>功能是为角色服务的</strong>，因此，我需要为不同角色，合理地规划他们可见的功能路径及操作<strong>权限</strong>。比如，一个<code>商家</code>的“主账号”可以看到“财务报表”功能，而“客服子账号”则无权查看。</li></ul><h3 id="4-信息-Information-需要管理哪些数据？"><a href="#4-信息-Information-需要管理哪些数据？" class="headerlink" title="4. 信息 (Information) - 需要管理哪些数据？"></a>4. 信息 (Information) - 需要管理哪些数据？</h3><p>这是四个要素中，最偏向于技术实现，也最容易被产品经理忽略，但却至关重要的一环。</p><ul><li><strong>核心问题</strong>：“<strong>为了让功能运转起来，我们需要管理什么数据？</strong>”</li><li><strong>我的实践</strong>：任何一个功能界面，其本质都是在对后台的“<strong>数据</strong>”进行增、删、改、查。在设计功能时，我必须同步思考其背后的“信息”结构。<ul><li><strong>有哪些字段</strong>：这个功能需要展示和编辑哪些数据字段？比如，“订单管理”功能，就需要处理<code>订单ID</code>、<code>商品名称</code>、<code>价格</code>、<code>收货人地址</code>等字段。</li><li><strong>有哪些状态</strong>：这些数据有哪些不同的状态？比如，一个<code>订单</code>，它会有<code>待付款</code>、<code>待发货</code>、<code>已发货</code>、<code>已完成</code>、<code>已取消</code>等多种状态。我必须定义清楚所有状态，以及它们之间流转的规则。</li><li><strong>关联的其他数据</strong>：这些数据还和哪些其他数据有关联？比如，一个<code>订单</code>，它必然关联着一个<code>用户</code>数据和一个或多个<code>商品</code>数据。</li></ul></li></ul><p>我始终将“<strong>角色 → 流程 → 功能 → 信息</strong>”这四要素，作为一个密不可分的整体来进行思考。它能保证我的产品设计，既有血肉（服务于真实的角色和流程），又有骨架（由清晰的功能和信息构成）。</p><hr><h2 id="2-4-制定迭代计划"><a href="#2-4-制定迭代计划" class="headerlink" title="2.4 制定迭代计划"></a>2.4 制定迭代计划</h2><p>在我们完成了立项说明书的撰写，并获得了决策层的认可之后，我们就拥有了一份包含了海量功能点的“<strong>总功能清单</strong>”。</p><p>但是，我们不可能、也绝不应该，在第一个版本里，就把所有功能都做完。这样做周期太长、风险太高。因此，我必须将这个庞大的清单，进行<strong>拆解、排序、分批</strong>，最终制定出一份清晰、合理、分阶段的<strong>迭代计划（Roadmap）</strong>。</p><h3 id="2-4-1-学习目标"><a href="#2-4-1-学习目标" class="headerlink" title="2.4.1 学习目标"></a>2.4.1 学习目标</h3><p>在本节中，我的目标是带大家掌握一套从“总功能清单”到“V1.0版本计划”的完整编制流程。我们将学习如何拆解需求、设定优先级，并最终确立清晰的版本目标和版本功能。</p><h3 id="2-4-2-迭代计划编制流程"><a href="#2-4-2-迭代计划编制流程" class="headerlink" title="2.4.2 迭代计划编制流程"></a>2.4.2 迭代计划编制流程</h3><p>我编制一份迭代计划，通常会遵循一个严谨的、四步走的流程。</p><p><strong>第一步：明确总体目标</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214148288.png" alt="image-20250721214148288"><br>首先，我会为整个项目，设定一个最高层级的、带有时间限制的目标。对于我们的“大P超级电商”项目，这个目标就是：</p><blockquote><p><strong>在6个月内，搭建并上线一个能让用户顺利购物、商家可以正常经营的招商模式电商平台V1.0。</strong></p></blockquote><p><strong>第二步：基于目标拆解需求</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214213738.png" alt="image-20250721214213738"><br>然后，我会围绕这个总体目标，将我们在<code>2.2.3</code>节中制定的“初始功能清单”，进行进一步的细化和补充，确保它包含了支撑用户和商家“顺利购物”、“正常经营”所需要的全部功能点。</p><p><strong>第三步：功能拆解与优先级设定</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214248935.png" alt="image-20250721214248935"><br>这是整个流程中最考验产品经理功力的一步。面对长长的功能清单，我需要对它们进行“<strong>分类</strong>”和“<strong>排序</strong>”。</p><p>我通常会将所有功能，分为两大类：</p><ul><li><strong>基础共性需求（桌子腿）</strong>：这些是整个行业的“标配”功能，用户已经习以为常，我们“<strong>不能没有</strong>”。比如，对于电商平台，<code>购物车</code>、<code>在线支付</code>、<code>商品搜索</code>、<code>用户评价</code>，就属于这类需求。没有它们，平台的核心流程都跑不通。</li><li><strong>差异化需求（亮点）</strong>：这些是体现我们产品特色、构建我们核心竞争力的功能，是我们“<strong>有了会更好</strong>”的部分。比如，在我们项目中，<code>达人直播</code>、<code>内容种草社区</code>、<code>VR购物功能</code>（如示例中提到的），就属于这类需求。</li></ul><p>我的MVP（最小可行产品）优先级排序原则是：<strong>优先做完所有“桌子腿”，先让桌子能稳稳地站起来（即，核心流程能跑通）。</strong></p><p><strong>第四步：制定迭代计划</strong><br><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214346994.png" alt="image-20250721214346994"><br>最后，我会将排序后的功能，分批地“装入”到不同的版本（Version）中去。</p><ul><li><strong>第一阶段/第一版本（MVP）</strong>：我会把所有优先级最高的“基础共性需求”打包进来，<strong>形成一个能满足最核心业务流程的最小闭环</strong>。<ul><li>正如示例中所示，“满足基本购物业务流程”需要一大堆功能，但“第一版本”可能只实现了其中最核心的<code>商品列表</code>、<code>立即购买</code>、<code>支付</code>、<code>查看物流</code>、<code>退货</code>。</li></ul></li><li><strong>后续版本（V1.1, V2.0…）</strong>：在MVP的基础上，我再逐步地、有节奏地，去增加那些能体现我们特色的“差异化需求”，以及一些次要的“基础需求”。</li></ul><h3 id="2-4-3-确立版本目标与功能"><a href="#2-4-3-确立版本目标与功能" class="headerlink" title="2.4.3 确立版本目标与功能"></a>2.4.3 确立版本目标与功能</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214520120.png" alt="image-20250721214520120"></p><p>迭代计划中的每一个“版本”，我都会用三个要素，来对它进行清晰的定义。</p><p><strong>1. 版本号 (Version Number)</strong><br>我采用行业标准的“<strong>语义化版本</strong>”命名规范：<code>大版本号.小版本号.临时版本号</code> (如: 1.2.1)</p><ul><li><strong>大版本号</strong>：当我上线了重量级的新模块（比如，我们未来上线了“直播”功能），我就会提升大版本号（如：从1.x 升级到 2.0.0）。</li><li><strong>小版本号</strong>：当我只是增加了一些新功能或对现有模块进行优化时，我就会提升小版本号（如：从1.0.0 升级到 1.1.0）。</li><li><strong>临时版本号</strong>：通常用于发布一些紧急的Bug修复（如：从1.0.0 升级到 1.0.1）。</li></ul><p><strong>2. 版本目标 (Version Goal)</strong><br>每一个版本，都必须有一个清晰、聚焦的使命。比如，我们“大P超级电商”的第一个版本：</p><blockquote><p><strong>V1.0.0 版本目标</strong>：上线一个稳定、可用的招商模式电商平台。核心目标是<strong>跑通用户的核心交易流程</strong>（从浏览到支付）和<strong>商家的核心履约流程</strong>（从发布商品到订单发货）。</p></blockquote><p><strong>3. 版本功能 (Version Features)</strong><br>这是为了达成上述目标，我最终决定纳入这个版本的功能清单。它是我们总功能清单的一个“<strong>子集</strong>”。</p><hr><h2 id="2-5-本章总结"><a href="#2-5-本章总结" class="headerlink" title="2.5 本章总结"></a>2.5 本章总结</h2><p>至此，我们已经完整地学习了电商项目从0到1的“<strong>立项</strong>”阶段的全部工作。</p><ul><li><strong>项目立项流程概述</strong>：我们了解了项目启动的标志性节点——<strong>立项评审会</strong>，以及我们作为产品经理，需要在会上回答的核心问题。</li><li><strong>立项说明书的核心构成</strong>：我们系统地学习了立项说明书的三大核心模块——<strong>产品概述</strong>（确立“是什么”）、<strong>市场分析</strong>（论证“为什么能”）、<strong>产品规划与架构</strong>（描绘“怎么做”的蓝图）。</li><li><strong>制定迭代计划</strong>：我们掌握了如何将宏伟的蓝-图，拆解为一份<strong>分阶段、有重点、可执行</strong>的迭代计划，明确了我们MVP版本的方向。</li></ul><p>当我们带着这份经过深度思考、并获得决策层认可的“立项说明书”和“V1.0迭代计划”，走出立项评审会时，我们的电商项目，才算真正地、正式地，扬帆起航了。</p><p>最后，我们附上立项说明书模板快速完成立项需求的任务</p><div calss="anzhiyu-tag-link"><a class="tag-Link" target="_blank" href="/go.html?u=aHR0cHM6Ly9wcm9yaXNlLWJsb2cub3NzLWNuLWd1YW5nemhvdS5hbGl5dW5jcy5jb20vY292ZXIvJUU0JUJBJUE3JUU1JTkzJTgxJUU3JUFCJThCJUU5JUExJUI5JUU4JUFGJUI0JUU2JTk4JThFJUU0JUI5JUE2JUU2JUE4JUExJUU2JTlEJUJGLmRvY3g" rel="external nofollow noopener noreferrer"><div class="tag-link-tips">引用站外地址</div><div class="tag-link-bottom"><div class="tag-link-left" style="background-image:url(https://bu.dusays.com/2025/07/19/687b2cf24c5db.png)"><i class="anzhiyufont anzhiyu-icon-link" style="display:none"></i></div><div class="tag-link-right"><div class="tag-link-title">产品立项说明书模板.docx</div><div class="tag-link-sitename">Prorise</div></div><i class="anzhiyufont anzhiyu-icon-angle-right"></i></div></a></div><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/8272.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/8272.html&quot;)">产品经理进阶（二）：第二章：电商项目立项</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/8272.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/8272.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/8823.html"><img class="prev-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/585815.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理进阶（一）：第一章：电商基础</div></div></a></div><div class="next-post pull-right"><a href="/posts/17683.html"><img class="next-cover" src="https://bu.dusays.com/2025/07/25/6882f31a48223.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理进阶（三）：第三章：电商用户端产品设计</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理进阶（二）：第二章：电商项目立项",date:"2025-07-24 17:13:45",updated:"2025-07-26 21:14:43",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第二章：电商项目立项\n\n在这一章，我们将正式代入一个实战角色：我们是 **“大P超级电商有限公司”** 的一名产品经理。\n\n**我们的项目背景是：**\n> 公司作为集团的子公司，手上有两大王牌资源：一是集团积累的**丰富B端商家资源**；\n>\n> 二是我们之前搭建的内容资讯类项目，已经吸引了**上千万C端用户**，其中80%是消费能力和意愿都很强的“90后”。\n>\n> 现在，公司高层已经拍板，决定正式进军电商领域，搭建一个全新的电商平台。并且，基于我们“手有商家、心中有用户”的现状，初步确定V1.0版本将采用**招商模式**，主营数码、服装、家电、快消品等类目。\n\n作为这个项目的核心产品经理，我的第一项任务，就是要**正式地把这个项目“立”起来**。\n\n## 2.1 项目立项流程概述\n\n![image-20250721193939182](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721193939182.png)\n\n一个产品的诞生，不是一蹴而就的，它遵循着一个清晰的生命周期。上图就展示了一个经典的产品流程，它包含**启动、规划、执行、跟进、上线**这五个核心阶段。\n\n我们这一章要聚焦的“项目立项”，就是这个流程的第一步，也是最关键的“**启动**”阶段。\n\n### 2.1.1 学习目标\n\n在本节中，我的目标是带大家清晰地理解项目“启动”阶段的核心工作。\n\n我们将学习一个标准的项目流程是怎样的，并重点理解“**立项评审会**”在整个流程中的关键节点作用。\n\n### 2.1.2 项目流程与启动节点\n\n在我看来，图中的“**启动**”阶段，包含了`行业调研`、`市场调研`等一系列前期研究工作。而这个阶段的终点，和下一“**规划**”阶段的起点，就是由一个标志性的事件来连接的，这个事件就是“**立项评审会议**”。\n\n**立项评审会**，就是整个项目能否正式启动的“**发令枪**”。只有在这场会议上，我的立项方案得到了公司决策层（老板、各部门负责人）的认可和批准，这个项目才算真正“活了过来”，可以正式地进入后续的规划和执行阶段，获得公司资源的支持。\n\n### 2.1.3 立项评审会议的作用\n\n![image-20250721194117623](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721194117623.png)\n\n为什么这场会议如此重要？因为我作为产品经理，必须在这场会议上，像一名“创业者”面对投资人一样，清晰、有力地回答一系列关于这个项目的灵魂拷问。\n\n我必须为我们的“**大P超级电商**”项目，准备好以下问题的答案：\n| **决策层最关心的问题** | **我（作为PM）需要给出的回答方向** |\n| :--- | :--- |\n| **1. 这个产品做出来给谁用？能帮他们什么？** | **（WHO & WHY）** 给我们现有的千万级“90后”用户，帮他们在一个信得过的平台方便地购物；给我们已有的B端商家，帮他们找到新的、精准的销售渠道。 |\n| **2. 这个产品预计能带来多大的营收？** | **（HOW MUCH）** 初期采用招商模式，我们的盈利将主要来自商家的**交易提成**和**平台服务费**。基于现有用户规模，我们预计第一年能实现几亿的GMV（商品交易总额），带来XX万的平台收入。 |\n| **3. 现在市面上竞争对手有哪些？我们有什么竞争力？** | **（COMPETITION）** 我们的对手是淘宝、京东等巨头。但我们的核心竞争力在于，我们**已经拥有了一个庞大的、画像清晰的年轻用户群体**，这能为我们的入驻商家，提供更精准的“人货匹配”，降低他们的获客成本。 |\n| **4. 这个产品怎么做？核心功能是什么？** | **（WHAT）** V1.0的核心功能，将围绕招商模式展开，包括：商家入驻与店铺管理系统、商品发布与管理系统、用户端交易流程（浏览-下单-支付）、平台运营后台。 |\n| **5. 这个产品大概要多久做出来？节奏计划是怎样的？**| **（WHEN）** 我们计划用6个月的时间，分三个大的里程碑，完成V1.0的上线。第一个里程碑的目标，是在2个月内，完成商家后台的核心功能，让第一批种子商家成功入驻。 |\n\n而我用来承载以上所有问题答案的、我在会前精心准备的“**关键性文件**”，就是我们下一节要学习的“**立项说明书**”。\n\n\n\n\n---\n## 2.2 立项说明书的核心构成\n\n![image-20250721194740655](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721194740655.png)\n\n在我开始撰写立项说明书时，我不会长篇大论。一份好的立项说明书，应该是**简洁、有力、逻辑清晰**的。它的核心目的，是在最短的时间内，让决策者理解并认同我的项目价值。\n\n因此，我通常会将整个文档，划分为三大核心模块：**产品概述、市场分析、产品规划**。这三个模块，层层递进，分别回答了\n\n“**我们要做什么？**”、“**我们为什么能做？**”和“**我们准备怎么做？**”这三个终极问题。\n\n现在，我们先来完成第一个，也是最重要的模块。\n\n### 2.2.1 产品概述\n\n这一部分，是整个立项说明书的“门面”，是我向决策者展示项目核心价值的“电梯演讲”。我必须用最精炼的语言，把产品的定位、目标用户和主要功能说清楚。\n\n#### 1. 产品定位\n\n![image-20250721194856262](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721194856262.png)\n\n我做的第一件事，就是用一句话，给我们的产品下一个清晰的**定义**。我习惯使用下面这个公式：\n**为【目标用户】，搭建一个【什么样的平台】，提供【哪些核心功能】，来满足他们的【什么核心需求】。**\n\n现在，我们把这个公式，应用到我们“**大P超级电商**”的项目中：\n\n> **我们的产品定位是：**\n> **为**我们平台已有的千万级“90后”年轻用户，**搭建**一个内容与消费深度融合的**招商模式电商平台**，**提供**商品搜索、智能推荐、达人直播、担保交易**等功能**，来**满足**他们追求品质、潮流与个性化购物体验的**核心需求**。\n\n#### 2. 目标用户\n\n![image-20250721194947268](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721194947268.png)\n\n在明确了产品定位后，我需要对我们的“**目标用户**”和“**目标市场**”，进行更具体的描述。\n\n* **目标人群**：\n    * **核心人群**：我们内容资讯平台已有的**上千万“90后”用户**。\n    * **人群特征**：互联网原住民，消费意愿强，是潮流和个性的追随者；信任KOL和社区的推荐，习惯于在娱乐和内容消费中，完成“种草”和“拔草”。\n\n* **目标市场**：\n    * 我们将切入主流的、面向年轻消费者的**B2C综合电商市场**，V1.0版本主营类目将覆盖**数码、服装、家电、快消品**等。\n\n#### 3. 主要功能\n\n![image-20250721195036522](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195036522.png)\n\n最后，我需要罗列出，为了实现我们的产品定位，在V1.0版本中，我们计划提供的**主要功能模块**。这并不是一份详尽的功能清单，而是一个高层级的“功能蓝图”。\n\n* **1. 商品导购功能**：包括商品搜索、多级分类、品牌馆等，帮助用户高效发现商品。\n* **2. 核心交易功能**：包括购物车、下单、集成第三方支付（微信/支付宝）、订单管理等，构成完整的交易闭环。\n* **3. 商家店铺功能**：为我们的B端商家提供店铺装修、商品上下架、订单管理、营销工具等后台能力。\n* **4. 内容与社交功能**：这是我们的差异化优势。包括引入达人直播、好物推荐、用户评价社区等，将内容与电商深度结合。\n* **5. 基础会员功能**：包括用户注册登录、个人中心、地址管理、售后服务等。\n\n\n\n---\n### 2.2.2 市场分析\n\n![image-20250721195532876](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195532876.png)\n\n在我完成了对产品的初步构想（产品概述）之后，我必须用**冷静、客观的数据和分析**，来向决策者证明：我们这个构想，不是空中楼阁，而是建立在坚实的市场机会之上的。\n\n**市场分析**，就是我用来提供这份“证据”的核心模块。我通常会从三个维度，层层递进地展开我的论证：**目标市场有多大？目标用户是谁？主要对手是谁？**\n\n#### 1. 目标市场现状（规模、趋势、结论）\n\n\n\n\n\n\n\n首先，我会从宏观视角，来描绘我们即将进入的“战场”的全貌。\n\n![image-20250721195631374](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195631374.png)\n\n* **市场规模 (Market Scale)**：这个市场的“盘子”有多大？我会引用权威的行业报告数据（如艾瑞、易观、国家统计局），来展示中国网络零售市场的总交易额（GMV）、总用户数等，证明这是一个万亿级的、足够大的市场。\n\n![image-20250721195653877](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195653877.png)\n\n* **市场趋势 (Market Trends)**：这个市场是在增长还是萎缩？未来的风口在哪里？我会分析近几年的数据，指出移动电商用户规模增速虽然放缓，但存量巨大。同时，我也会特别指出，“90后”乃至“00后”已经成为线上消费的主力军，他们的消费习惯（如兴趣驱动、信任KOL）是市场最大的新趋势。\n\n![image-20250721195704847](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721195704847.png)\n\n* **分析结论 (Analysis Conclusion)**：\n  \n    > **我的结论是**：中国电商市场已从增量竞争，进入存量竞争时代。未来的机会，在于**对特定人群的深度运营**。我们“大P超级电商”项目，所拥有的“千万级90后用户”，恰好是这个时代最具价值的核心消费人群。因此，我们进入这个市场，具备天然的、精准的用户基础，**市场时机完全吻合**。\n\n#### 2. 目标用户分析（分类、特征、需求）\n\n![image-20250721202152110](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721202152110.png)\n\n在描绘了宏观市场之后，我需要将镜头拉近，聚焦到我们“**具体要服务的人**”身上。\n\n* **用户分类 (User Classification)**：我会基于我们现有的用户数据，将“90后”这个庞大的群体，进一步细分为几个典型的用户画像（Persona）。\n* **用户特征 (User Characteristics)**：我会描述每个分类用户的关键特征。\n* **用户需求 (User Needs)**：我会提炼出每个分类用户，在“电商购物”这个场景下的核心需求。\n\n| **用户分类** | **用户特征** | **核心电商需求** |\n| :--- | :--- | :--- |\n| **潮流大学生** | 无固定收入，追求性价比和潮流新品，极易受KOL和社区内容“种草”影响。 | 寻找高性价比的潮流服饰、数码产品；需要分期付款等金融工具；渴望通过商品彰显个性。 |\n| **职场新人** | 有一定的可支配收入，工作繁忙，注重效率和生活品质，愿意为兴趣和“悦己”买单。 | 需要一站式购齐生活快消品；追求品牌和品质；愿意为提升效率和体验的服务付费；购物决策受内容推荐影响大。 |\n| **内容创作者/KOL** | 我们平台上的意见领袖，拥有自己的粉丝群体，有将自身影响力变现的强烈需求。 | 需要一个便捷的、与内容深度结合的“带货”渠道，将自己推荐的商品，高效地销售给粉丝。 |\n\n#### 3. 主要竞争对手（竞品分析、总结）\n\n![image-20250721202259417](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721202259417.png)\n\n最后，我需要理性地分析“战场”上已经存在的“强大敌人”。\n\n![image-20250721202310828](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721202310828.png)\n\n* **选择竞品**：我会选择市场上最主流的、与我们目标用户重合度最高的平台作为我们的主要竞争对手，即**淘宝**和**京东**。\n\n![image-20250721202400825](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721202400825.png)\n\n* **分析内容**：我会从“零售三要素”等维度，对竞品进行拆解，并与我们自身进行对比。\n\n* **分析总结**：得出我们与竞品相比的优势、劣势，并最终找到我们的“**差异化突破口**”。\n\n| **对比维度** | **淘宝 (Taobao)** | **京东 (JD.com)** | **我们 (大P超级电商) 的机会** |\n| :--- | :--- | :--- | :--- |\n| **产品定位** | 万能的商品市场 | 品质家电数码、高效物流 | **内容驱动的潮流社区电商** |\n| **核心优势** | SKU极其丰富、生态成熟 | 自营品控、物流体验无与伦比 | **已拥有千万级精准的年轻用户流量，获客成本低** |\n| **核心劣势**| C2C模式品控难，用户决策成本高 | 平台模式的商品丰富度不足，用户群体偏成熟 | 商业和物流体系需要从0到1搭建，品牌心智未建立 |\n\n**我的分析总结是**：我们无法在“多”上胜过淘宝，也无法在“快”上胜过京东。\n\n但我们可以在“**精**”和“**准**”上建立优势。我们的突破口，就是**深度服务好我们已有的这群年轻用户**，通过将我们擅长的**内容生态**与**电商交易**进行无缝融合，打造一个“**最懂年轻人的内容电商社区**”，以此来建立我们独特的竞争壁垒。\n\n\n\n\n---\n### 2.2.3 产品规划与架构\n\n在我完成了产品概述和市场分析，向决策者们清晰地阐述了“**我们要做什么**”和“**为什么我们能做**”之后，就必须回答最后一个，也是最关键的问题：“**我们具体准备怎么做？**”\n\n**产品规划与架构**这一部分，就是我用来回答这个问题的“施工蓝图”。正如思考题所提示的，在立项会议上，一份长长的功能清单，往往会让领导感到乏味和困惑。我需要用更直观、更结构化的方式，来呈现我的产品规划。\n\n#### 1. 认识产品架构\n\n![image-20250721203548034](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721203548034.png)\n\n在我开始画图之前，我必须先澄清两个非常重要、但极易混淆的概念：**产品架构图**和**产品结构图**。\n\n![image-20250721203749198](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721203749198.png)\n\n* **产品架构图**\n    我把它定义为，我们产品的“**城市规划总览图**”。\n    * **它的核心**：是表达整个产品业务的**宏观框架**。它通过层级划分和模块组合，来呈现产品包含哪些大的业务板块和核心系统。\n    * **它的特点**：**高度抽象，重在框架，忽略细节**。它的读者主要是老板、业务负责人等决策层，目的是让他们在30秒内，就能看懂我们整个产品的版图。\n\n\n* **产品结构图 (Product Structure Diagram)**\n    我把它定义为，我们产品的“**单栋建筑施工图**”。\n    * **它的核心**：是将某一个具体的产品模块（比如用户端App），所包含的**所有功能和信息**，进行一次彻底、详细的拆解。\n    * **它的特点**：**非常具体，巨细靡遗**。它就像是原型的一种“简化表现方式”，是给我们的项目团队（设计师、开发、测试）看的，确保大家对某个模块的功能范围，有全面、统一的认知。\n\n在“**立项说明书**”这个阶段，我主要使用的是“**产品架构图**”，因为它更能服务于我向决策层汇报的宏观视角。\n\n\n---\n\n#### 2. 构建方法与流程：划分角色、需求推导、功能归类\n\n那么，我是如何为我们的“大P超级电商”项目，从零开始，一步步地构建出它的产品架构图的呢？我遵循一个非常严谨的、自顶向下的三步推导思路。\n\n![image-20250721210258871](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210258871.png)\n\n**第一步：划分角色，确定“端”**\n\n在开始任何功能规划之前，我首先要识别出我们这个电商生态系统中的核心“玩家”是谁。根据我们“招商模式”的业务定位，我将所有参与者，明确地划分为三大角色，而这三大角色，也直接对应了我们需要建设的三个产品“端”：\n\n* **用户 (User)**：对应我们需要开发的 **用户端** App，服务于我们千万级的C端消费者。\n* **商家 (Merchant)**：对应我们需要开发的 **商家端** 后台，服务于入驻我们平台的B端商家。\n* **平台 (Platform)**：对应我们需要开发的 **平台端** 后台，服务于我们自己公司的运营和管理人员。\n\n**第二步：分析需求，推导功能**\n\n确定了“端”之后，我就需要深入到每一个“端”的内部，站在对应角色的视角，去分析他们在具体场景下的核心需求，并从中推导出我们必须为他们提供的功能。\n\n![image-20250721210755456](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210755456.png)\n\n* **对于用户端**：我需要思考，一个普通消费者在使用我们App时，他的核心诉求是什么？\n    * 他“想买个手机，要找一个颜值高、性能比较高的”，这个需求就推导出我们需要提供强大的 **商品搜索** 与 **商品筛选** 功能。\n    * 他“看到一个东西好像不错，想看看买过的人有没有说过这个好用”，这个需求就推导出我们需要建立 **种草** 社区或完善的 **用户评价** 体系。\n    * 他“付钱时不想用支付宝和微信支付，想用银行信用卡”，这个需求就推导出我们的 **支付** 模块，需要支持多种主流的支付方式。\n\n\n![image-20250721210806766](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210806766.png)\n\n* **对于商家端**：我需要思考，一个入驻商家，他的核心诉求是什么？\n    * 他发现“这个商品不卖了，不要再让用户下单了”，这个需求就推导出我们需要提供便捷的 **商品管理** 功能（如：商品上下架）。\n    * 他想知道“这段时间生意不错，看看最近卖了多少单？”，这个需求就推导出我们需要提供清晰的 **订单统计** 功能。\n    * 他需要“看看今天下单的有没有没发货，要不要取消订单”，这个需求就推导出我们需要提供高效的 **订单管理** 功能（如：发货管理）。\n\n![image-20250721210819557](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210819557.png)\n\n* **对于平台端**：我需要思考，我们作为平台的管理者，核心诉令是什么？\n    * “有些店铺入驻后违规操作，需要处理下”，这个诉求就推导出我们需要 **店铺管理** 功能（如：封禁店铺）。\n    * “有些用户价格敏感，得做一些促销活动”，这个诉求就推导出我们需要 **营销管理** 功能（如：优惠券配置）。\n    * “公司员工有人负责审核店铺，有人负责审核商品，系统功能不能乱”，这个诉求就推导出我们需要严谨的 **权限管理** 功能。\n\n**第三步：功能归类，绘制架构**\n\n![image-20250721210949605](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721210949605.png)\n\n最后一步，就是将我们在第二步中，为各个“端”推导出的所有功能，进行系统性的梳理和归类，最终汇总成一张清晰的、宏观的“**产品架构图**”。\n\n这张图，就是我们整个电商平台V1.0版本的“总设计蓝图”。它直观地展示了用户端、商家端、平台端这三大系统，各自包含了哪些核心的功能模块，明确了我们本次立项需要投入资源进行建设的全部范围。\n\n#### 3. 核心流程图\n\n除了用“架构图”来展示静态的“**有什么**”，我还会附上一到两张最核心的“**业务流程图**”，来展示动态的“**怎么用**”。\n\n对于我们的电商项目，我至少会提供：\n\n![业务流程图](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E4%B8%9A%E5%8A%A1%E6%B5%81%E7%A8%8B%E5%9B%BE.png)\n\n* **用户核心交易流程图**：从用户浏览商品，到加入购物车，再到下单、支付的完整流程。\n\n* **商家核心操作流程图**：从商家入驻，到发布商品，再到处理订单的完整流程。\n\n#### 4. 初始功能清单\n\n最后，作为架构图和流程图的补充，我会提供一份相对详细的“**初始功能清单（Function List）**”。\n\n它通常是一份Excel表格，会比架构图更具体一些，将大的功能模块，初步拆解到二级或三级功能点。这份清单，是对我`2.2.1`节中“主要功能”的进一步细化，也是我们下一节“制定迭代计划”的基础。\n\n| 一级模块   | 二级模块 | 三级功能点 | 功能描述                 | 优先级 | 备注                               |\n| :--------- | :------- | :--------- | :----------------------- | :----- | :--------------------------------- |\n| **用户端** | 商品导购 | 商品搜索   | 用户可通过关键词搜索商品 | P0     | 需支持模糊搜索                     |\n| **商家端** | 商品管理 | 商品列表   | 商品上下架               | P0     | 商家可控制商品的在售状态           |\n| **平台端** | 商家管理 | 商家审核   | 查看审核列表             | P0     | 运营可查看所有待审核的商家入驻申请 |\n\n---\n\n## 2.3 `产品设计思路[核心]`\n\n![image-20250721212555967](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721212555967.png)\n\n在我看来，一个专业的产品设计，绝不是天马行空的艺术创作，而是一个**严谨的、结构化的逻辑推演过程**。为了确保我的设计不偏离用户价值和商业目标，我始终围绕着`“产品设计四要素”`来进行思考：**角色、流程、功能、信息**。\n\n这个框架，能帮助我把一个模糊的需求，层层剖析，最终转化为一个清晰、完整、可执行的产品方案。\n\n### 1. 角色 (Role) - 我们在为谁而设计？\n\n这是我所有思考的**绝对起点**。\n* **核心问题**：“**这个设计是给谁用的？**”\n* **我的实践**：我从不为一个抽象的、模糊的“用户”做设计。我必须清晰地定义出**具体的操作角色及其职责**。例如，在我们“大P超级电商”平台中，`消费者`、`商家`和`平台运营`就是三个完全不同的角色。他们的目标、诉求、使用场景、甚至专业能力都截然不同。一个为`商家`设计的、追求效率和数据丰富的“订单管理”后台，和一个为`消费者`设计的、追求简洁和美观的“我的订单”页面，其设计思路必然是天壤之别。**深刻地理解“角色”，是做好用户中心设计的第一步。**\n\n### 2. 流程 (Process) - 他们如何完成任务？\n\n明确了“为谁设计”，下一步就是思考“**他要如何完成他的任务**”。\n* **核心问题**：“**各个角色的操作顺序和规则是怎样的？**”\n* **我的实践**：我通常会从两个角度来梳理流程：\n    * **从目标出发**：思考角色要达成的最终目标是什么。比如，`消费者`的目标是“成功购买到心仪的商品”。我就会围绕这个目标，去绘制他从“浏览商品”到“下单支付”的完整流程图。\n    * **从角色的分工出发**：当一个业务流程涉及到多个角色时（比如一笔完整的交易），我必须梳理清楚他们之间的**协作关系和任务交接**。比如，`消费者`“下单付款”后，流程就交接给了`商家`去“审核订单并发货”，这就需要我绘制“泳道图”来清晰地表达。\n\n### 3. 功能 (Function) - 我们需要提供什么界面？\n\n当角色的流程被梳理清楚后，“功能”的设计就成了水到渠成的事情。\n* **核心问题**：“**为了支撑上述流程的每一步，我们需要提供什么样的功能界面？**”\n* **我的实践**：“功能”是我们为用户搭建的“桥梁”，用来帮助他们走通“流程”。流程中的每一个“动作节点”，都对应着一个或多个“功能”。比如，为了支撑`商家`“审核订单并发货”这个流程节点，我就需要为他设计一个“**订单管理**”的功能界面。\n* **我的拓展思考**：在设计功能时，我必须时刻带着“角色”的视角。**功能是为角色服务的**，因此，我需要为不同角色，合理地规划他们可见的功能路径及操作**权限**。比如，一个`商家`的“主账号”可以看到“财务报表”功能，而“客服子账号”则无权查看。\n\n### 4. 信息 (Information) - 需要管理哪些数据？\n\n这是四个要素中，最偏向于技术实现，也最容易被产品经理忽略，但却至关重要的一环。\n* **核心问题**：“**为了让功能运转起来，我们需要管理什么数据？**”\n* **我的实践**：任何一个功能界面，其本质都是在对后台的“**数据**”进行增、删、改、查。在设计功能时，我必须同步思考其背后的“信息”结构。\n    * **有哪些字段**：这个功能需要展示和编辑哪些数据字段？比如，“订单管理”功能，就需要处理`订单ID`、`商品名称`、`价格`、`收货人地址`等字段。\n    * **有哪些状态**：这些数据有哪些不同的状态？比如，一个`订单`，它会有`待付款`、`待发货`、`已发货`、`已完成`、`已取消`等多种状态。我必须定义清楚所有状态，以及它们之间流转的规则。\n    * **关联的其他数据**：这些数据还和哪些其他数据有关联？比如，一个`订单`，它必然关联着一个`用户`数据和一个或多个`商品`数据。\n\n我始终将“**角色 → 流程 → 功能 → 信息**”这四要素，作为一个密不可分的整体来进行思考。它能保证我的产品设计，既有血肉（服务于真实的角色和流程），又有骨架（由清晰的功能和信息构成）。\n\n---\n## 2.4 制定迭代计划\n\n在我们完成了立项说明书的撰写，并获得了决策层的认可之后，我们就拥有了一份包含了海量功能点的“**总功能清单**”。\n\n但是，我们不可能、也绝不应该，在第一个版本里，就把所有功能都做完。这样做周期太长、风险太高。因此，我必须将这个庞大的清单，进行**拆解、排序、分批**，最终制定出一份清晰、合理、分阶段的**迭代计划（Roadmap）**。\n\n### 2.4.1 学习目标\n\n在本节中，我的目标是带大家掌握一套从“总功能清单”到“V1.0版本计划”的完整编制流程。我们将学习如何拆解需求、设定优先级，并最终确立清晰的版本目标和版本功能。\n\n### 2.4.2 迭代计划编制流程\n\n我编制一份迭代计划，通常会遵循一个严谨的、四步走的流程。\n\n**第一步：明确总体目标**\n![image-20250721214148288](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214148288.png)\n首先，我会为整个项目，设定一个最高层级的、带有时间限制的目标。对于我们的“大P超级电商”项目，这个目标就是：\n\n> **在6个月内，搭建并上线一个能让用户顺利购物、商家可以正常经营的招商模式电商平台V1.0。**\n\n**第二步：基于目标拆解需求**\n![image-20250721214213738](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214213738.png)\n然后，我会围绕这个总体目标，将我们在`2.2.3`节中制定的“初始功能清单”，进行进一步的细化和补充，确保它包含了支撑用户和商家“顺利购物”、“正常经营”所需要的全部功能点。\n\n**第三步：功能拆解与优先级设定**\n![image-20250721214248935](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214248935.png)\n这是整个流程中最考验产品经理功力的一步。面对长长的功能清单，我需要对它们进行“**分类**”和“**排序**”。\n\n我通常会将所有功能，分为两大类：\n* **基础共性需求（桌子腿）**：这些是整个行业的“标配”功能，用户已经习以为常，我们“**不能没有**”。比如，对于电商平台，`购物车`、`在线支付`、`商品搜索`、`用户评价`，就属于这类需求。没有它们，平台的核心流程都跑不通。\n* **差异化需求（亮点）**：这些是体现我们产品特色、构建我们核心竞争力的功能，是我们“**有了会更好**”的部分。比如，在我们项目中，`达人直播`、`内容种草社区`、`VR购物功能`（如示例中提到的），就属于这类需求。\n\n我的MVP（最小可行产品）优先级排序原则是：**优先做完所有“桌子腿”，先让桌子能稳稳地站起来（即，核心流程能跑通）。**\n\n**第四步：制定迭代计划**\n![image-20250721214346994](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214346994.png)\n最后，我会将排序后的功能，分批地“装入”到不同的版本（Version）中去。\n\n* **第一阶段/第一版本（MVP）**：我会把所有优先级最高的“基础共性需求”打包进来，**形成一个能满足最核心业务流程的最小闭环**。\n    * 正如示例中所示，“满足基本购物业务流程”需要一大堆功能，但“第一版本”可能只实现了其中最核心的`商品列表`、`立即购买`、`支付`、`查看物流`、`退货`。\n* **后续版本（V1.1, V2.0...）**：在MVP的基础上，我再逐步地、有节奏地，去增加那些能体现我们特色的“差异化需求”，以及一些次要的“基础需求”。\n\n### 2.4.3 确立版本目标与功能\n\n![image-20250721214520120](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721214520120.png)\n\n迭代计划中的每一个“版本”，我都会用三个要素，来对它进行清晰的定义。\n\n**1. 版本号 (Version Number)**\n我采用行业标准的“**语义化版本**”命名规范：`大版本号.小版本号.临时版本号` (如: 1.2.1)\n* **大版本号**：当我上线了重量级的新模块（比如，我们未来上线了“直播”功能），我就会提升大版本号（如：从1.x 升级到 2.0.0）。\n* **小版本号**：当我只是增加了一些新功能或对现有模块进行优化时，我就会提升小版本号（如：从1.0.0 升级到 1.1.0）。\n* **临时版本号**：通常用于发布一些紧急的Bug修复（如：从1.0.0 升级到 1.0.1）。\n\n**2. 版本目标 (Version Goal)**\n每一个版本，都必须有一个清晰、聚焦的使命。比如，我们“大P超级电商”的第一个版本：\n> **V1.0.0 版本目标**：上线一个稳定、可用的招商模式电商平台。核心目标是**跑通用户的核心交易流程**（从浏览到支付）和**商家的核心履约流程**（从发布商品到订单发货）。\n\n**3. 版本功能 (Version Features)**\n这是为了达成上述目标，我最终决定纳入这个版本的功能清单。它是我们总功能清单的一个“**子集**”。\n\n\n---\n\n## 2.5 本章总结\n\n至此，我们已经完整地学习了电商项目从0到1的“**立项**”阶段的全部工作。\n* **项目立项流程概述**：我们了解了项目启动的标志性节点——**立项评审会**，以及我们作为产品经理，需要在会上回答的核心问题。\n* **立项说明书的核心构成**：我们系统地学习了立项说明书的三大核心模块——**产品概述**（确立“是什么”）、**市场分析**（论证“为什么能”）、**产品规划与架构**（描绘“怎么做”的蓝图）。\n* **制定迭代计划**：我们掌握了如何将宏伟的蓝-图，拆解为一份**分阶段、有重点、可执行**的迭代计划，明确了我们MVP版本的方向。\n\n当我们带着这份经过深度思考、并获得决策层认可的“立项说明书”和“V1.0迭代计划”，走出立项评审会时，我们的电商项目，才算真正地、正式地，扬帆起航了。\n\n\n最后，我们附上立项说明书模板快速完成立项需求的任务\n\n{% link 产品立项说明书模板.docx,Prorise,https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/%E4%BA%A7%E5%93%81%E7%AB%8B%E9%A1%B9%E8%AF%B4%E6%98%8E%E4%B9%A6%E6%A8%A1%E6%9D%BF.docx,https://bu.dusays.com/2025/07/19/687b2cf24c5db.png %}\n\n\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E7%AB%A0%EF%BC%9A%E7%94%B5%E5%95%86%E9%A1%B9%E7%9B%AE%E7%AB%8B%E9%A1%B9"><span class="toc-number">1.</span> <span class="toc-text">第二章：电商项目立项</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#2-1-%E9%A1%B9%E7%9B%AE%E7%AB%8B%E9%A1%B9%E6%B5%81%E7%A8%8B%E6%A6%82%E8%BF%B0"><span class="toc-number">1.1.</span> <span class="toc-text">2.1 项目立项流程概述</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.1.1.</span> <span class="toc-text">2.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-2-%E9%A1%B9%E7%9B%AE%E6%B5%81%E7%A8%8B%E4%B8%8E%E5%90%AF%E5%8A%A8%E8%8A%82%E7%82%B9"><span class="toc-number">1.1.2.</span> <span class="toc-text">2.1.2 项目流程与启动节点</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-1-3-%E7%AB%8B%E9%A1%B9%E8%AF%84%E5%AE%A1%E4%BC%9A%E8%AE%AE%E7%9A%84%E4%BD%9C%E7%94%A8"><span class="toc-number">1.1.3.</span> <span class="toc-text">2.1.3 立项评审会议的作用</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-2-%E7%AB%8B%E9%A1%B9%E8%AF%B4%E6%98%8E%E4%B9%A6%E7%9A%84%E6%A0%B8%E5%BF%83%E6%9E%84%E6%88%90"><span class="toc-number">1.2.</span> <span class="toc-text">2.2 立项说明书的核心构成</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-1-%E4%BA%A7%E5%93%81%E6%A6%82%E8%BF%B0"><span class="toc-number">1.2.1.</span> <span class="toc-text">2.2.1 产品概述</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E4%BA%A7%E5%93%81%E5%AE%9A%E4%BD%8D"><span class="toc-number">1.2.1.1.</span> <span class="toc-text">1. 产品定位</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%9B%AE%E6%A0%87%E7%94%A8%E6%88%B7"><span class="toc-number">1.2.1.2.</span> <span class="toc-text">2. 目标用户</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E4%B8%BB%E8%A6%81%E5%8A%9F%E8%83%BD"><span class="toc-number">1.2.1.3.</span> <span class="toc-text">3. 主要功能</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-2-%E5%B8%82%E5%9C%BA%E5%88%86%E6%9E%90"><span class="toc-number">1.2.2.</span> <span class="toc-text">2.2.2 市场分析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%9B%AE%E6%A0%87%E5%B8%82%E5%9C%BA%E7%8E%B0%E7%8A%B6%EF%BC%88%E8%A7%84%E6%A8%A1%E3%80%81%E8%B6%8B%E5%8A%BF%E3%80%81%E7%BB%93%E8%AE%BA%EF%BC%89"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">1. 目标市场现状（规模、趋势、结论）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%9B%AE%E6%A0%87%E7%94%A8%E6%88%B7%E5%88%86%E6%9E%90%EF%BC%88%E5%88%86%E7%B1%BB%E3%80%81%E7%89%B9%E5%BE%81%E3%80%81%E9%9C%80%E6%B1%82%EF%BC%89"><span class="toc-number">1.2.2.2.</span> <span class="toc-text">2. 目标用户分析（分类、特征、需求）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E4%B8%BB%E8%A6%81%E7%AB%9E%E4%BA%89%E5%AF%B9%E6%89%8B%EF%BC%88%E7%AB%9E%E5%93%81%E5%88%86%E6%9E%90%E3%80%81%E6%80%BB%E7%BB%93%EF%BC%89"><span class="toc-number">1.2.2.3.</span> <span class="toc-text">3. 主要竞争对手（竞品分析、总结）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-2-3-%E4%BA%A7%E5%93%81%E8%A7%84%E5%88%92%E4%B8%8E%E6%9E%B6%E6%9E%84"><span class="toc-number">1.2.3.</span> <span class="toc-text">2.2.3 产品规划与架构</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E8%AE%A4%E8%AF%86%E4%BA%A7%E5%93%81%E6%9E%B6%E6%9E%84"><span class="toc-number">1.2.3.1.</span> <span class="toc-text">1. 认识产品架构</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%9E%84%E5%BB%BA%E6%96%B9%E6%B3%95%E4%B8%8E%E6%B5%81%E7%A8%8B%EF%BC%9A%E5%88%92%E5%88%86%E8%A7%92%E8%89%B2%E3%80%81%E9%9C%80%E6%B1%82%E6%8E%A8%E5%AF%BC%E3%80%81%E5%8A%9F%E8%83%BD%E5%BD%92%E7%B1%BB"><span class="toc-number">1.2.3.2.</span> <span class="toc-text">2. 构建方法与流程：划分角色、需求推导、功能归类</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E6%A0%B8%E5%BF%83%E6%B5%81%E7%A8%8B%E5%9B%BE"><span class="toc-number">1.2.3.3.</span> <span class="toc-text">3. 核心流程图</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-%E5%88%9D%E5%A7%8B%E5%8A%9F%E8%83%BD%E6%B8%85%E5%8D%95"><span class="toc-number">1.2.3.4.</span> <span class="toc-text">4. 初始功能清单</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-3-%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1%E6%80%9D%E8%B7%AF-%E6%A0%B8%E5%BF%83"><span class="toc-number">1.3.</span> <span class="toc-text">2.3 产品设计思路[核心]</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#1-%E8%A7%92%E8%89%B2-Role-%E6%88%91%E4%BB%AC%E5%9C%A8%E4%B8%BA%E8%B0%81%E8%80%8C%E8%AE%BE%E8%AE%A1%EF%BC%9F"><span class="toc-number">1.3.1.</span> <span class="toc-text">1. 角色 (Role) - 我们在为谁而设计？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-%E6%B5%81%E7%A8%8B-Process-%E4%BB%96%E4%BB%AC%E5%A6%82%E4%BD%95%E5%AE%8C%E6%88%90%E4%BB%BB%E5%8A%A1%EF%BC%9F"><span class="toc-number">1.3.2.</span> <span class="toc-text">2. 流程 (Process) - 他们如何完成任务？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-%E5%8A%9F%E8%83%BD-Function-%E6%88%91%E4%BB%AC%E9%9C%80%E8%A6%81%E6%8F%90%E4%BE%9B%E4%BB%80%E4%B9%88%E7%95%8C%E9%9D%A2%EF%BC%9F"><span class="toc-number">1.3.3.</span> <span class="toc-text">3. 功能 (Function) - 我们需要提供什么界面？</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-%E4%BF%A1%E6%81%AF-Information-%E9%9C%80%E8%A6%81%E7%AE%A1%E7%90%86%E5%93%AA%E4%BA%9B%E6%95%B0%E6%8D%AE%EF%BC%9F"><span class="toc-number">1.3.4.</span> <span class="toc-text">4. 信息 (Information) - 需要管理哪些数据？</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-4-%E5%88%B6%E5%AE%9A%E8%BF%AD%E4%BB%A3%E8%AE%A1%E5%88%92"><span class="toc-number">1.4.</span> <span class="toc-text">2.4 制定迭代计划</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#2-4-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.4.1.</span> <span class="toc-text">2.4.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-4-2-%E8%BF%AD%E4%BB%A3%E8%AE%A1%E5%88%92%E7%BC%96%E5%88%B6%E6%B5%81%E7%A8%8B"><span class="toc-number">1.4.2.</span> <span class="toc-text">2.4.2 迭代计划编制流程</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#2-4-3-%E7%A1%AE%E7%AB%8B%E7%89%88%E6%9C%AC%E7%9B%AE%E6%A0%87%E4%B8%8E%E5%8A%9F%E8%83%BD"><span class="toc-number">1.4.3.</span> <span class="toc-text">2.4.3 确立版本目标与功能</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#2-5-%E6%9C%AC%E7%AB%A0%E6%80%BB%E7%BB%93"><span class="toc-number">1.5.</span> <span class="toc-text">2.5 本章总结</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>