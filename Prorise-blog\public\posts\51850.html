<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>SpringAI（四）：4. 深入 Prompt 工程与结构化输出 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="Java微服务篇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="SpringAI（四）：4. 深入 Prompt 工程与结构化输出"><meta name="application-name" content="SpringAI（四）：4. 深入 Prompt 工程与结构化输出"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="SpringAI（四）：4. 深入 Prompt 工程与结构化输出"><meta property="og:url" content="https://prorise666.site/posts/51850.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="4. 深入 Prompt 工程与结构化输出引言：当程序员遇上简历筛选 “这个候选人到底是什么水平？”  这个问题，往往能让我们在堆积如山的简历中反复阅读、查找关键信息，最后得出一个模糊的印象。本章，我们将用技术解决这个难题：通过深入探索 Prompt 工程，让大语言模型成为您的私人招聘助理，并按我们"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta name="description" content="4. 深入 Prompt 工程与结构化输出引言：当程序员遇上简历筛选 “这个候选人到底是什么水平？”  这个问题，往往能让我们在堆积如山的简历中反复阅读、查找关键信息，最后得出一个模糊的印象。本章，我们将用技术解决这个难题：通过深入探索 Prompt 工程，让大语言模型成为您的私人招聘助理，并按我们"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/51850.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"SpringAI（四）：4. 深入 Prompt 工程与结构化输出",postAI:"true",pageFillDescription:"4. 深入 Prompt 工程与结构化输出, 引言：当程序员遇上简历筛选, 4.1 Prompt 的重要性与基本原则, 4.2 Prompt 与 Message 详解, 4.2.1 Prompt 类：与 AI 对话的标准容器, 4.2.2 Message 接口：定义对话角色, 4.3 PromptTemplate：让 Prompt 动起来, 4.3.1 API 详解, 4.3.2 实战：动态翻译器, 4.4 结构化输出：将 AI 响应映射为 POJO, 4.4.1 烦恼与优势：为何需要结构化？, 4.4.2 API 详解：OutputConverter 家族, 4.4.3 BeanOutputConverter实战：从简历中提取结构化数据, 4.4.4 ListOutputConverter扩展：处理集合类型, 接口文档, 4.4.5 拓展：MapOutputConverter - 输出动态键值对, 4.4.6 深度揭秘：ParameterizedTypeReference 的魔法深入工程与结构化输出引言当程序员遇上简历筛选这个候选人到底是什么水平这个问题往往能让我们在堆积如山的简历中反复阅读查找关键信息最后得出一个模糊的印象本章我们将用技术解决这个难题通过深入探索工程让大语言模型成为您的私人招聘助理并按我们指定的精确的剧本格式输出结构化的候选人报告的重要性与基本原则如果说是连接你和的电话线那么就是你在这条电话线上说的话你说得是否清晰准确有技巧直接决定了电话那头的能否理解你的意图并给出满意的答复工程就是一门研究如何与高效沟通的艺术与科学是与沟通的就像调用一个软件需要遵循其定义的参数和格式一样与沟通也需要遵循一定的范式才能获得稳定可控的输出以下是编写高效的几个基本原则清晰具体避免使用模糊的词语提供上下文如果问题需要背景知识请在中提供设定角色这是最有效的技巧之一可以极大地约束的行为和语言风格施加约束明确告诉模型你不想要什么或者输出必须遵循的格式提供示例如果需要模型遵循特定的输出格式或风格最好的方法就是给它一两个例子与详解类与对话的标准容器在中类是所有与交互的载体它不仅仅是一个简单的字符串包装器而是一个结构化的对象用于封装发送给模型的完整指令集你可以把这种便捷方式看作是寄一个信封而使用则是打包一个可定制的快递包裹功能更强大构造函数说明最简单的形式内部会将字符串包装成一个最通用最强大的形式允许传入一个由不同角色消息组成的列表在以上形式的基础上附加一次性的请求级别的模型参数接口定义对话角色是构成的基本单位定义了四种核心的类型消息类型核心作用与职责系统指令用于设定的角色行为准则个性目标和任何高级指令用户输入代表最终用户的提问指令或对话内容助手回复代表自己之前的回复是构建多轮对话历史的关键函数结果用于函数调用场景将外部工具的执行结果返回给让动起来允许你定义一个包含占位符如的模板字符串然后用一个来填充这些变量最终渲染出一个完整的对象让你的变得动态和可复用详解方法说明构造函数传入包含占位符的模板字符串接收一个用中的键值对替换模板中的占位符并返回一个完整的对象实战动态翻译器我们将为添加一个新方法该方法可以根据传入的目标语言动态地构建一个翻译在中添加新方法原有方法你是一个专业的精通多种语言的翻译家请将用户接下来发送的所有内容都翻译成不要添加任何与翻译结果无关的解释或寒暄在中添加新端点原有方法结构化输出将响应映射为现在我们进入本章的核心我们将学习如何让从一个自由的创作者转变为一个能精确输出结构化数据的数据工程师烦恼与优势为何需要结构化原始输出的烦恼如果我们直接要求总结一份简历得到的可能是对程序极不友好的纯文本这位候选人叫张三看起来有超过年的开发经验技能方面他提到了微服务和问题显而易见格式不稳定每次请求的措辞和格式都可能变化难以解析我们需要编写脆弱的正则表达式来提取年等关键词无法直接使用无法直接将这段文本映射到我们的对象上结构化的优势如果我们能让直接输出程序就可以无缝解析无需任何复杂的后处理张三正是通过在提示词中追加格式化指令并结合内置的转换器来实现这一目标的详解家族转换器输出类型核心用途您自定义的最常用最强大能将的响应直接映射到一个定义好的类或当输出的键值对不固定时使用当您只需要一个简单的字符串列表时实战从简历中提取结构化数据我们将构建一个全新的独立的服务用于演示如何从非结构化的简历文本中提取出结构化的信息定义数据模型候选人姓名候选人工作年限候选人掌握的技能列表创建解析服务从下面的简历文本中提取信息简历文本创建端点运行与测试请求体张三是一名资深软件工程师拥有超过年的开发经验他精通和技术预期响应张三扩展处理集合类型的强大之处在于它也能处理泛型集合现在我们举一个电影推荐案例来展示如何输出一个定义电影名称上映时间导演名称电影简介在和中实现对于来说大模型输出转换为实体对象看起来还是比较复杂的不过还提供更简易的方式直接在后面调用把对应的类型传递进去即可并且在提示词中占位符不需要再手动添加普通方法实现并不优雅使用来精确指定我们期望的复杂泛型类型帮我找五部主演的电影使用更优雅的写法帮我找五部主演的电影接口文档示例请求汤姆汉克斯响应示例成功返回的响应体将是一个电影对象列表示例如下荒岛余生罗伯特泽米吉斯一名联邦快递员工在南太平洋荒岛求生的故事阿甘正传罗伯特泽米吉斯智商仅相当于孩童的善良男子见证美国近代历史的故事阿波罗号朗霍华德宇航员在月球执行任务时遭遇意外的故事玩具总动员约翰拉塞特玩具牛仔胡迪和太空战警巴斯光年的冒险故事达芬奇密码朗霍华德哈佛大学教授破解中世纪符号学的惊悚故事拓展输出动态键值对我们已经学会了如何将的响应输出为单个对象或对象列表但有时我们希望输出的的键本身就是动态的例如以电影名作为键电影信息作为值来构建一个正是为此类场景而生它适用于当您需要一个灵活的键值对不固定的作为输出而不想为此创建专门的类时在中添加新方法我们将添加一个方法要求以电影名为键来组织返回的数据正常的写法是这样的但是使用为核心的写法我们就无需指定了使用获取以电影名为键的电影信息帮我找五部的电影以电影名为分组键值为电影信息电影信息需要包含电影名称上映时间导演名电影简介等内容在中添加新端点华语流行为了简洁我们直接使用的优雅写法帮我找五部的电影以电影名为分组键值为电影信息电影信息需要包含电影名称上映时间导演名电影简介等内容启动应用后访问此新端点经典武侠预期响应内容可能不同东邪西毒电影名称东邪西毒上映时间导演名王家卫电影简介讲述欧阳锋黄药师等江湖人物的爱恨情仇与宿命纠葛新龙门客栈电影名称新龙门客栈上映时间导演名徐克电影简介明朝东厂与江湖侠客在龙门客栈展开生死对决的故事笑傲江湖电影名称笑傲江湖上映时间导演名胡金铨电影简介令狐冲卷入江湖纷争最终领悟武学真谛的传奇故事卧虎藏龙电影名称卧虎藏龙上映时间导演名李安电影简介围绕青冥剑展开的江湖恩怨与儿女情长的武侠传奇英雄电影名称英雄上映时间导演名张艺谋电影简介刺客无名向秦王讲述刺杀真相的多重视角武侠史诗深度揭秘的魔法您一定很好奇为什么在处理或这样的复杂类型时我们不能像那样直接传递而是需要用到这样看起来有些古怪的语法答案在于语言的一个核心特性类型擦除问题根源类型擦除我们可以用一个简单的比喻来理解它想象一下您要寄一个快递在打包时您在箱子上贴了一个标签易碎品玻璃花瓶这就像是编译时的泛型类型编译器打包员会检查您的标签是否正确但是一旦您的包裹被放上快递车快递员运行时的只关心这是一个包裹一个原始的而不再关心里面具体是玻璃花瓶还是铁块箱子上关于玻璃花瓶的具体信息在运输过程中被擦除了这就是类型擦除在运行时并不知道一个对象的泛型参数具体是还是因此像这样的语法在中是非法的因为在运行时根本不存在一个与相区别的它们都是同一个这就给我们的程序带来了麻烦当返回一段数组时它需要知道是应该把这个数组里的每个元素都转成对象还是转成别的什么对象如果只给它信息就不足了解决方案超级类型令牌的巧计正是绕过类型擦除的天才魔术这个魔术的核心在于书写的那个看似多余的大括号当您写下时做的不仅仅是创建一个对象而是创建了一个匿名的内部类这个匿名内部类是这样的代码编译器在背后实际创建了一个类似这样的东西类体是空的这里的魔法就发生了虽然会擦除字段和方法参数的泛型信息但它会把一个类所继承的父类或实现的接口的泛型信息作为元数据永久地记录在子类的文件中所以这个匿名类它永远地记住了自己是的一个子类框架就可以利用这一点来反向破解方法接收到您创建的这个匿名内部类的实例它通过的反射机制调用这个实例的方法这个方法会返回一个对象可以从这个对象中安全地精确地提取出那个被擦除的泛型参数魔法完成现在知道了您想要的完整类型从而可以正确地将数组反序列化为您期望的对象",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-08 13:53:35",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#4-%E6%B7%B1%E5%85%A5-Prompt-%E5%B7%A5%E7%A8%8B%E4%B8%8E%E7%BB%93%E6%9E%84%E5%8C%96%E8%BE%93%E5%87%BA"><span class="toc-text">4. 深入 Prompt 工程与结构化输出</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%BC%95%E8%A8%80%EF%BC%9A%E5%BD%93%E7%A8%8B%E5%BA%8F%E5%91%98%E9%81%87%E4%B8%8A%E7%AE%80%E5%8E%86%E7%AD%9B%E9%80%89"><span class="toc-text">引言：当程序员遇上简历筛选</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-1-Prompt-%E7%9A%84%E9%87%8D%E8%A6%81%E6%80%A7%E4%B8%8E%E5%9F%BA%E6%9C%AC%E5%8E%9F%E5%88%99"><span class="toc-text">4.1 Prompt 的重要性与基本原则</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-Prompt-%E4%B8%8E-Message-%E8%AF%A6%E8%A7%A3"><span class="toc-text">4.2 Prompt 与 Message 详解</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-2-1-Prompt-%E7%B1%BB%EF%BC%9A%E4%B8%8E-AI-%E5%AF%B9%E8%AF%9D%E7%9A%84%E6%A0%87%E5%87%86%E5%AE%B9%E5%99%A8"><span class="toc-text">4.2.1 Prompt 类：与 AI 对话的标准容器</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-2-2-Message-%E6%8E%A5%E5%8F%A3%EF%BC%9A%E5%AE%9A%E4%B9%89%E5%AF%B9%E8%AF%9D%E8%A7%92%E8%89%B2"><span class="toc-text">4.2.2 Message 接口：定义对话角色</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-PromptTemplate%EF%BC%9A%E8%AE%A9-Prompt-%E5%8A%A8%E8%B5%B7%E6%9D%A5"><span class="toc-text">4.3 PromptTemplate：让 Prompt 动起来</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-3-1-API-%E8%AF%A6%E8%A7%A3"><span class="toc-text">4.3.1 API 详解</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-3-2-%E5%AE%9E%E6%88%98%EF%BC%9A%E5%8A%A8%E6%80%81%E7%BF%BB%E8%AF%91%E5%99%A8"><span class="toc-text">4.3.2 实战：动态翻译器</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-%E7%BB%93%E6%9E%84%E5%8C%96%E8%BE%93%E5%87%BA%EF%BC%9A%E5%B0%86-AI-%E5%93%8D%E5%BA%94%E6%98%A0%E5%B0%84%E4%B8%BA-POJO"><span class="toc-text">4.4 结构化输出：将 AI 响应映射为 POJO</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-1-%E7%83%A6%E6%81%BC%E4%B8%8E%E4%BC%98%E5%8A%BF%EF%BC%9A%E4%B8%BA%E4%BD%95%E9%9C%80%E8%A6%81%E7%BB%93%E6%9E%84%E5%8C%96%EF%BC%9F"><span class="toc-text">4.4.1 烦恼与优势：为何需要结构化？</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-2-API-%E8%AF%A6%E8%A7%A3%EF%BC%9AOutputConverter-%E5%AE%B6%E6%97%8F"><span class="toc-text">4.4.2 API 详解：OutputConverter 家族</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-3-BeanOutputConverter%E5%AE%9E%E6%88%98%EF%BC%9A%E4%BB%8E%E7%AE%80%E5%8E%86%E4%B8%AD%E6%8F%90%E5%8F%96%E7%BB%93%E6%9E%84%E5%8C%96%E6%95%B0%E6%8D%AE"><span class="toc-text">4.4.3 BeanOutputConverter实战：从简历中提取结构化数据</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-4-ListOutputConverter%E6%89%A9%E5%B1%95%EF%BC%9A%E5%A4%84%E7%90%86%E9%9B%86%E5%90%88%E7%B1%BB%E5%9E%8B"><span class="toc-text">4.4.4 ListOutputConverter扩展：处理集合类型</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%8E%A5%E5%8F%A3%E6%96%87%E6%A1%A3"><span class="toc-text">接口文档</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-5-%E6%8B%93%E5%B1%95%EF%BC%9AMapOutputConverter-%E8%BE%93%E5%87%BA%E5%8A%A8%E6%80%81%E9%94%AE%E5%80%BC%E5%AF%B9"><span class="toc-text">4.4.5 拓展：MapOutputConverter - 输出动态键值对</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-6-%E6%B7%B1%E5%BA%A6%E6%8F%AD%E7%A7%98%EF%BC%9AParameterizedTypeReference-%E7%9A%84%E2%80%9C%E9%AD%94%E6%B3%95%E2%80%9D"><span class="toc-text">4.4.6 深度揭秘：ParameterizedTypeReference 的“魔法”</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Java微服务篇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">SpringAI（四）：4. 深入 Prompt 工程与结构化输出</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-03-20T17:13:45.000Z" title="发表于 2025-03-21 01:13:45">2025-03-21</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:35.725Z" title="更新于 2025-07-08 13:53:35">2025-07-08</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">4.6k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>18分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="SpringAI（四）：4. 深入 Prompt 工程与结构化输出"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/51850.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/51850.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url">Java微服务篇</a><h1 id="CrawlerTitle" itemprop="name headline">SpringAI（四）：4. 深入 Prompt 工程与结构化输出</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-03-20T17:13:45.000Z" title="发表于 2025-03-21 01:13:45">2025-03-21</time><time itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:35.725Z" title="更新于 2025-07-08 13:53:35">2025-07-08</time></header><div id="postchat_postcontent"><h2 id="4-深入-Prompt-工程与结构化输出"><a href="#4-深入-Prompt-工程与结构化输出" class="headerlink" title="4. 深入 Prompt 工程与结构化输出"></a><strong>4. 深入 Prompt 工程与结构化输出</strong></h2><h3 id="引言：当程序员遇上简历筛选"><a href="#引言：当程序员遇上简历筛选" class="headerlink" title="引言：当程序员遇上简历筛选"></a><strong>引言：当程序员遇上简历筛选</strong></h3><blockquote><p>“这个候选人到底是什么水平？”</p></blockquote><p>这个问题，往往能让我们在堆积如山的简历中反复阅读、查找关键信息，最后得出一个模糊的印象。本章，我们将用技术解决这个难题：通过深入探索 Prompt 工程，让大语言模型成为您的私人招聘助理，并按我们指定的、精确的“剧本”格式，输出结构化的候选人报告。</p><h3 id="4-1-Prompt-的重要性与基本原则"><a href="#4-1-Prompt-的重要性与基本原则" class="headerlink" title="4.1 Prompt 的重要性与基本原则"></a><strong>4.1 Prompt 的重要性与基本原则</strong></h3><p>如果说 <code>ChatClient</code> 是连接你和 AI 的电话线，那么 <strong>Prompt 就是你在这条电话线上说的话</strong>。你说得是否清晰、准确、有技巧，直接决定了电话那头的 AI 能否理解你的意图并给出满意的答复。Prompt 工程（Prompt Engineering）就是一门研究如何与 AI 高效沟通的艺术与科学。</p><blockquote><p><strong>Prompt 是与 AI 沟通的 API</strong>。就像调用一个软件 API 需要遵循其定义的参数和格式一样，与 AI 沟通也需要遵循一定的范式，才能获得稳定、可控的输出。</p></blockquote><p>以下是编写高效 Prompt 的几个基本原则：</p><ol><li><strong>清晰具体</strong>：避免使用模糊的词语。</li><li><strong>提供上下文</strong>：如果问题需要背景知识，请在 Prompt 中提供。</li><li><strong>设定角色</strong>：这是最有效的技巧之一，可以极大地约束 AI 的行为和语言风格。</li><li><strong>施加约束</strong>：明确告诉模型你<strong>不</strong>想要什么，或者输出必须遵循的格式。</li><li><strong>提供示例</strong>：如果需要模型遵循特定的输出格式或风格，最好的方法就是给它一两个例子。</li></ol><h3 id="4-2-Prompt-与-Message-详解"><a href="#4-2-Prompt-与-Message-详解" class="headerlink" title="4.2 Prompt 与 Message 详解"></a><strong>4.2 <code>Prompt</code> 与 <code>Message</code> 详解</strong></h3><h4 id="4-2-1-Prompt-类：与-AI-对话的标准容器"><a href="#4-2-1-Prompt-类：与-AI-对话的标准容器" class="headerlink" title="4.2.1 Prompt 类：与 AI 对话的标准容器"></a><strong>4.2.1 <code>Prompt</code> 类：与 AI 对话的标准容器</strong></h4><p>在 Spring AI 中，<code>org.springframework.ai.chat.prompt.Prompt</code> 类是所有与 <code>ChatClient</code> 交互的载体。它不仅仅是一个简单的字符串包装器，而是一个结构化的对象，用于封装发送给模型的完整指令集。</p><p>你可以把 <code>chatClient.prompt().user("...")</code> 这种便捷方式看作是寄一个<strong>信封</strong>，而使用 <code>new Prompt(...)</code> 则是打包一个可定制的<strong>快递包裹</strong>，功能更强大。</p><table><thead><tr><th align="left">构造函数</th><th align="left">说明</th></tr></thead><tbody><tr><td align="left"><strong><code>Prompt(String contents)</code></strong></td><td align="left">最简单的形式，内部会将字符串包装成一个 <code>UserMessage</code>。</td></tr><tr><td align="left"><strong><code>Prompt(List&lt;Message&gt; messages)</code></strong></td><td align="left"><strong>最通用、最强大</strong>的形式。允许传入一个由不同角色消息组成的列表。</td></tr><tr><td align="left"><strong><code>Prompt(..., PromptOptions options)</code></strong></td><td align="left">在以上形式的基础上，附加一次性的、请求级别的模型参数。</td></tr></tbody></table><h4 id="4-2-2-Message-接口：定义对话角色"><a href="#4-2-2-Message-接口：定义对话角色" class="headerlink" title="4.2.2 Message 接口：定义对话角色"></a><strong>4.2.2 <code>Message</code> 接口：定义对话角色</strong></h4><p><code>Message</code> 是构成 <code>Prompt</code> 的基本单位。Spring AI 定义了四种核心的 <code>Message</code> 类型。</p><table><thead><tr><th align="left">消息类型 (Message Type)</th><th align="left">核心作用与职责</th></tr></thead><tbody><tr><td align="left"><strong><code>SystemMessage</code></strong></td><td align="left"><strong>系统指令</strong>：用于设定 AI 的角色、行为准则、个性、目标和任何高级指令。</td></tr><tr><td align="left"><strong><code>UserMessage</code></strong></td><td align="left"><strong>用户输入</strong>：代表最终用户的提问、指令或对话内容。</td></tr><tr><td align="left"><strong><code>AssistantMessage</code></strong></td><td align="left"><strong>助手回复</strong>：代表 AI 自己<strong>之前</strong>的回复，是构建多轮对话历史的关键。</td></tr><tr><td align="left"><strong><code>FunctionMessage</code></strong></td><td align="left"><strong>函数结果</strong>：用于函数调用场景，将外部工具的执行结果返回给 AI。</td></tr></tbody></table><h3 id="4-3-PromptTemplate：让-Prompt-动起来"><a href="#4-3-PromptTemplate：让-Prompt-动起来" class="headerlink" title="4.3 PromptTemplate：让 Prompt 动起来"></a><strong>4.3 <code>PromptTemplate</code>：让 Prompt 动起来</strong></h3><p><code>PromptTemplate</code> 允许你定义一个包含占位符（如 <code>{variable}</code>）的模板字符串，然后用一个 <code>Map</code> 来填充这些变量，最终渲染出一个完整的 <code>Prompt</code> 对象，让你的 Prompt 变得动态和可复用。</p><h4 id="4-3-1-API-详解"><a href="#4-3-1-API-详解" class="headerlink" title="4.3.1 API 详解"></a><strong>4.3.1 API 详解</strong></h4><table><thead><tr><th align="left">方法</th><th align="left">说明</th></tr></thead><tbody><tr><td align="left"><strong><code>PromptTemplate(String template)</code></strong></td><td align="left">构造函数，传入包含占位符的模板字符串。</td></tr><tr><td align="left"><strong><code>create(Map&lt;String, Object&gt; model)</code></strong></td><td align="left">接收一个 Map，用 Map 中的键值对替换模板中的占位符，并返回一个完整的 <code>Prompt</code> 对象。</td></tr></tbody></table><h4 id="4-3-2-实战：动态翻译器"><a href="#4-3-2-实战：动态翻译器" class="headerlink" title="4.3.2 实战：动态翻译器"></a><strong>4.3.2 实战：动态翻译器</strong></h4><p>我们将为 <code>ChatService</code> 添加一个新方法，该方法可以根据传入的目标语言，动态地构建一个翻译 Prompt。</p><ol><li><p><strong>在 <code>ChatService.java</code> 中添加新方法</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// src/main/java/com/copilot/aicopilotbackend/service/ChatService.java</span></span><br><span class="line"><span class="meta">@Service</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">ChatService</span> {</span><br><span class="line">    <span class="comment">// ... 原有方法 ...</span></span><br><span class="line">    <span class="keyword">public</span> Flux&lt;String&gt; <span class="title function_">getDynamicTranslatedStream</span><span class="params">(String targetLanguage, String text)</span> {</span><br><span class="line">        <span class="type">String</span> <span class="variable">systemPromptTemplate</span> <span class="operator">=</span> <span class="string">"""</span></span><br><span class="line"><span class="string">            你是一个专业的、精通多种语言的翻译家。</span></span><br><span class="line"><span class="string">            请将用户接下来发送的所有内容都翻译成 {targetLanguage}。</span></span><br><span class="line"><span class="string">            不要添加任何与翻译结果无关的解释或寒暄。</span></span><br><span class="line"><span class="string">            """</span>;</span><br><span class="line">        <span class="keyword">return</span> chatClient.prompt()</span><br><span class="line">                .system(spec -&gt; spec.text(systemPromptTemplate).param(<span class="string">"targetLanguage"</span>, targetLanguage))</span><br><span class="line">                .user(text)</span><br><span class="line">                .stream()</span><br><span class="line">                .content();</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>在 <code>ChatController.java</code> 中添加新端点</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// src/main/java/com/copilot/aicopilotbackend/controller/ChatController.java</span></span><br><span class="line"><span class="meta">@RestController</span></span><br><span class="line"><span class="meta">@RequestMapping("/api/v1/chat")</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">ChatController</span> {</span><br><span class="line">    <span class="comment">// ... 原有方法 ...</span></span><br><span class="line">    <span class="meta">@GetMapping(value = "/translate", produces = MediaType.TEXT_EVENT_STREAM_VALUE)</span></span><br><span class="line">    <span class="keyword">public</span> Flux&lt;String&gt; <span class="title function_">translate</span><span class="params">(<span class="meta">@RequestParam</span> String targetLanguage, <span class="meta">@RequestParam</span> String text)</span> {</span><br><span class="line">        <span class="keyword">return</span> chatService.getDynamicTranslatedStream(targetLanguage, text);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ol><h3 id="4-4-结构化输出：将-AI-响应映射为-POJO"><a href="#4-4-结构化输出：将-AI-响应映射为-POJO" class="headerlink" title="4.4 结构化输出：将 AI 响应映射为 POJO"></a><strong>4.4 结构化输出：将 AI 响应映射为 POJO</strong></h3><p>现在，我们进入本章的核心。我们将学习如何让 AI 从一个自由的“创作者”，转变为一个能精确输出结构化数据的“数据工程师”。</p><h4 id="4-4-1-烦恼与优势：为何需要结构化？"><a href="#4-4-1-烦恼与优势：为何需要结构化？" class="headerlink" title="4.4.1 烦恼与优势：为何需要结构化？"></a><strong>4.4.1 烦恼与优势：为何需要结构化？</strong></h4><p><strong>1. 原始输出的烦恼</strong></p><p>如果我们直接要求 AI 总结一份简历，得到的可能是对程序极不友好的纯文本：</p><blockquote><p>“这位候选人叫张三，看起来有超过8年的Java开发经验，技能方面，他提到了Spring Boot、微服务和Docker…”</p></blockquote><p><strong>问题显而易见</strong>：</p><ul><li><strong>格式不稳定</strong>：每次请求的措辞和格式都可能变化。</li><li><strong>难以解析</strong>：我们需要编写脆弱的正则表达式来提取“8年”、“Java”等关键词。</li><li><strong>无法直接使用</strong>：无法直接将这段文本映射到我们的 <code>Candidate</code> 对象上。</li></ul><p><strong>2. 结构化的优势</strong></p><p>如果我们能让 AI 直接输出 JSON，程序就可以无缝解析，无需任何复杂的后处理。</p><figure class="highlight json"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="punctuation">{</span></span><br><span class="line">  <span class="attr">"name"</span><span class="punctuation">:</span> <span class="string">"张三"</span><span class="punctuation">,</span></span><br><span class="line">  <span class="attr">"yearsOfExperience"</span><span class="punctuation">:</span> <span class="number">8</span><span class="punctuation">,</span></span><br><span class="line">  <span class="attr">"skills"</span><span class="punctuation">:</span> <span class="punctuation">[</span><span class="string">"Java"</span><span class="punctuation">,</span> <span class="string">"Spring Boot"</span><span class="punctuation">,</span> <span class="string">"Microservices"</span><span class="punctuation">,</span> <span class="string">"Docker"</span><span class="punctuation">]</span></span><br><span class="line"><span class="punctuation">}</span></span><br></pre></td></tr></tbody></table></figure><p>Spring AI 正是通过在<strong>提示词中追加格式化指令</strong>，并结合内置的**转换器（Converter）**来实现这一目标的。</p><h4 id="4-4-2-API-详解：OutputConverter-家族"><a href="#4-4-2-API-详解：OutputConverter-家族" class="headerlink" title="4.4.2 API 详解：OutputConverter 家族"></a><strong>4.4.2 API 详解：<code>OutputConverter</code> 家族</strong></h4><table><thead><tr><th align="left">转换器 (Converter)</th><th align="left">输出类型</th><th align="left">核心用途</th></tr></thead><tbody><tr><td align="left"><strong><code>BeanOutputConverter&lt;T&gt;</code></strong></td><td align="left"><code>T</code> (您自定义的 POJO)</td><td align="left"><strong>最常用、最强大</strong>。能将 AI 的响应直接映射到一个定义好的 Java 类或 Record。</td></tr><tr><td align="left"><strong><code>MapOutputConverter</code></strong></td><td align="left"><code>Map&lt;String, Object&gt;</code></td><td align="left">当输出的键值对不固定时使用。</td></tr><tr><td align="left"><strong><code>ListOutputConverter</code></strong></td><td align="left"><code>List&lt;String&gt;</code></td><td align="left">当您只需要一个简单的字符串列表时。</td></tr></tbody></table><h4 id="4-4-3-BeanOutputConverter实战：从简历中提取结构化数据"><a href="#4-4-3-BeanOutputConverter实战：从简历中提取结构化数据" class="headerlink" title="4.4.3 BeanOutputConverter实战：从简历中提取结构化数据"></a><strong>4.4.3 BeanOutputConverter实战：从简历中提取结构化数据</strong></h4><p>我们将构建一个全新的、独立的服务，用于演示如何从非结构化的简历文本中，提取出结构化的 <code>Candidate</code> 信息。</p><p><strong>1. 定义数据模型 (<code>dto/request/Candidate.java</code>)</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// src/main/java/com/copilot/aicopilotbackend/dto/request/Candidate.java</span></span><br><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.dto.request;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> com.fasterxml.jackson.annotation.JsonPropertyDescription;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">record</span> <span class="title class_">Candidate</span><span class="params">(</span></span><br><span class="line"><span class="params">        @JsonPropertyDescription("候选人姓名")</span> String name,</span><br><span class="line">        <span class="meta">@JsonPropertyDescription("候选人工作年限")</span> <span class="type">int</span> yearsOfExperience,</span><br><span class="line">        <span class="meta">@JsonPropertyDescription("候选人掌握的技能列表")</span> List&lt;String&gt; skills</span><br><span class="line">) {}</span><br></pre></td></tr></tbody></table></figure><p><strong>2. 创建解析服务 (<code>service/ExtractionService.java</code>)</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// src/main/java/com/copilot/aicopilotbackend/service/ExtractionService.java</span></span><br><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.service;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.dto.request.Candidate;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.client.ChatClient;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.prompt.Prompt;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.prompt.PromptTemplate;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.converter.BeanOutputConverter;</span><br><span class="line"><span class="keyword">import</span> org.springframework.stereotype.Service;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Service</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">ExtractionService</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> ChatClient chatClient;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">ExtractionService</span><span class="params">(ChatClient chatClient)</span> {</span><br><span class="line">        <span class="built_in">this</span>.chatClient = chatClient;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> Candidate <span class="title function_">extractCandidateFrom</span><span class="params">(String resumeText)</span> {</span><br><span class="line">        <span class="type">var</span> <span class="variable">outputConverter</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">BeanOutputConverter</span>&lt;&gt;(Candidate.class);</span><br><span class="line">        <span class="type">String</span> <span class="variable">formatInstructions</span> <span class="operator">=</span> outputConverter.getFormat();</span><br><span class="line"></span><br><span class="line">        <span class="type">String</span> <span class="variable">promptTemplateString</span> <span class="operator">=</span> <span class="string">"""</span></span><br><span class="line"><span class="string">                从下面的简历文本中提取信息。</span></span><br><span class="line"><span class="string">                {format}</span></span><br><span class="line"><span class="string">                简历文本:</span></span><br><span class="line"><span class="string">                {resume}</span></span><br><span class="line"><span class="string">                """</span>;</span><br><span class="line">        <span class="type">PromptTemplate</span> <span class="variable">promptTemplate</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">PromptTemplate</span>(promptTemplateString);</span><br><span class="line">        <span class="type">Prompt</span> <span class="variable">prompt</span> <span class="operator">=</span> promptTemplate.create(Map.of(</span><br><span class="line">                <span class="string">"resume"</span>, resumeText,</span><br><span class="line">                <span class="string">"format"</span>, formatInstructions</span><br><span class="line">        ));</span><br><span class="line"></span><br><span class="line">        <span class="keyword">return</span> chatClient.prompt(prompt)</span><br><span class="line">                .call()</span><br><span class="line">                .entity(outputConverter);</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>3. 创建 API 端点 (<code>controller/ExtractionController.java</code>)</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// src/main/java/com/copilot/aicopilotbackend/controller/ExtractionController.java</span></span><br><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.controller;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.dto.request.Candidate;</span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.service.ExtractionService;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.*;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"></span><br><span class="line"><span class="meta">@RestController</span></span><br><span class="line"><span class="meta">@RequestMapping("/api/v1/extraction")</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">ExtractionController</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> ExtractionService extractionService;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">ExtractionController</span><span class="params">(ExtractionService extractionService)</span> {</span><br><span class="line">        <span class="built_in">this</span>.extractionService = extractionService;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="meta">@PostMapping("/candidate")</span></span><br><span class="line">    <span class="keyword">public</span> Candidate <span class="title function_">extractData</span><span class="params">(<span class="meta">@RequestBody</span> Map&lt;String, String&gt; request)</span> {</span><br><span class="line">        <span class="keyword">return</span> extractionService.extractCandidateFrom(request.get(<span class="string">"resumeText"</span>));</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>4. 运行与测试</strong></p><ul><li><strong>请求体</strong>:<figure class="highlight json"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="punctuation">{</span></span><br><span class="line">  <span class="attr">"resumeText"</span><span class="punctuation">:</span> <span class="string">"张三是一名资深软件工程师，拥有超过8年的Java开发经验。他精通Spring Boot, Microservices, 和 Docker技术。"</span></span><br><span class="line"><span class="punctuation">}</span></span><br></pre></td></tr></tbody></table></figure></li><li><strong>预期 JSON 响应</strong>:<figure class="highlight json"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="punctuation">{</span></span><br><span class="line">  <span class="attr">"name"</span><span class="punctuation">:</span> <span class="string">"张三"</span><span class="punctuation">,</span></span><br><span class="line">  <span class="attr">"yearsOfExperience"</span><span class="punctuation">:</span> <span class="number">8</span><span class="punctuation">,</span></span><br><span class="line">  <span class="attr">"skills"</span><span class="punctuation">:</span> <span class="punctuation">[</span> <span class="string">"Java"</span><span class="punctuation">,</span> <span class="string">"Spring Boot"</span><span class="punctuation">,</span> <span class="string">"Microservices"</span><span class="punctuation">,</span> <span class="string">"Docker"</span> <span class="punctuation">]</span></span><br><span class="line"><span class="punctuation">}</span></span><br></pre></td></tr></tbody></table></figure></li></ul><h4 id="4-4-4-ListOutputConverter扩展：处理集合类型"><a href="#4-4-4-ListOutputConverter扩展：处理集合类型" class="headerlink" title="4.4.4 ListOutputConverter扩展：处理集合类型"></a><strong>4.4.4 ListOutputConverter扩展：处理集合类型</strong></h4><p><code>BeanOutputConverter</code> 的强大之处在于它也能处理泛型集合。现在我们举一个电影推荐案例，来展示如何输出一个 <code>List&lt;Film&gt;</code></p><p><strong>1. 定义 <code>Film</code> DTO (<code>dto/response/Film.java</code>)</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// src/main/java/com/copilot/aicopilotbackend/dto/response/Film.java</span></span><br><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.dto.response;</span><br><span class="line"><span class="keyword">import</span> com.fasterxml.jackson.annotation.JsonPropertyDescription;</span><br><span class="line"></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">record</span> <span class="title class_">Film</span><span class="params">(</span></span><br><span class="line"><span class="params">    @JsonPropertyDescription("电影名称")</span> String name,</span><br><span class="line">    <span class="meta">@JsonPropertyDescription("上映时间")</span> String releaseDate,</span><br><span class="line">    <span class="meta">@JsonPropertyDescription("导演名称")</span> String directorName,</span><br><span class="line">    <span class="meta">@JsonPropertyDescription("电影简介")</span> String desc</span><br><span class="line">) {}</span><br></pre></td></tr></tbody></table></figure><p><strong>2. 在 <code>FilmService</code> 和 <code>FilmController</code> 中实现</strong></p><p>对于Enity来说：大模型输出转换为实体对象看起来还是比较复杂的，不过Spring AI还提供更简易的方式：直接在 <code>call()</code>后面调用 <code>entity</code>，把对应的class类型传递进去即可， <strong>并且在提示词中</strong> <code>**{format}**</code><strong>占位符不需要再手动添加</strong> 。</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.copilot.aicopilotbackend.controller;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.copilot.aicopilotbackend.dto.response.Film;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.client.ChatClient;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.prompt.Prompt;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.prompt.PromptTemplate;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.converter.BeanOutputConverter;</span><br><span class="line"><span class="keyword">import</span> org.springframework.core.ParameterizedTypeReference;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.GetMapping;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.RequestMapping;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.RestController;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"><span class="keyword">import</span> java.util.Map;</span><br><span class="line"><span class="meta">@RestController</span></span><br><span class="line"><span class="meta">@RequestMapping("api/v1/films")</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">FilmController</span> {</span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> ChatClient chatClient;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">FilmController</span><span class="params">(ChatClient chatClient)</span> {</span><br><span class="line">        <span class="built_in">this</span>.chatClient = chatClient;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line"><span class="comment">//    @GetMapping("films")</span></span><br><span class="line"><span class="comment">//    /**</span></span><br><span class="line"><span class="comment">//     * 普通方法实现，并不优雅</span></span><br><span class="line"><span class="comment">//     */</span></span><br><span class="line"><span class="comment">//    public List&lt;Film&gt; getFilmsByActor(String actor) {</span></span><br><span class="line"><span class="comment">//        // 使用 ParameterizedTypeReference 来精确指定我们期望的复杂泛型类型</span></span><br><span class="line"><span class="comment">//        var converter = new BeanOutputConverter&lt;&gt;(new ParameterizedTypeReference&lt;List&lt;Film&gt;&gt;() {});</span></span><br><span class="line"><span class="comment">//        String format = converter.getFormat();</span></span><br><span class="line"><span class="comment">//        String userMessage = "帮我找五部 {actor} 主演的电影。 {format}";</span></span><br><span class="line"><span class="comment">//        Prompt prompt = new PromptTemplate(userMessage).create(Map.of("actor", actor, "format", format));</span></span><br><span class="line"><span class="comment">//        String content = chatClient.prompt(prompt).call().content();</span></span><br><span class="line"><span class="comment">//        return converter.convert(content);</span></span><br><span class="line"><span class="comment">//    }</span></span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 使用更优雅的 .entity() 写法</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@param</span> actor</span></span><br><span class="line"><span class="comment">     * <span class="doctag">@return</span></span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="meta">@GetMapping("as-list")</span></span><br><span class="line">    <span class="keyword">public</span> List&lt;Film&gt; <span class="title function_">getFilmsByActor</span><span class="params">(String actor)</span> {</span><br><span class="line">        <span class="keyword">return</span>  chatClient.prompt()</span><br><span class="line">                .user(u-&gt; u.text(<span class="string">"帮我找五部 {actor} 主演的电影"</span>).param(<span class="string">"actor"</span>, actor))</span><br><span class="line">                .call()</span><br><span class="line">                .entity(<span class="keyword">new</span> <span class="title class_">ParameterizedTypeReference</span>&lt;List&lt;Film&gt;&gt;() {});</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line"></span><br><span class="line">}</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><h5 id="接口文档"><a href="#接口文档" class="headerlink" title="接口文档"></a>接口文档</h5><p><strong>示例请求</strong></p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">http://localhost:8080/api/v1/films/as-list?actor=汤姆·汉克斯</span><br></pre></td></tr></tbody></table></figure><p><strong>响应示例</strong></p><p>成功返回的响应体将是一个电影对象列表，示例如下：</p><figure class="highlight json"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br></pre></td><td class="code"><pre><span class="line"><span class="punctuation">[</span></span><br><span class="line">    <span class="punctuation">{</span></span><br><span class="line">        <span class="attr">"name"</span><span class="punctuation">:</span> <span class="string">"荒岛余生"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"releaseDate"</span><span class="punctuation">:</span> <span class="string">"2000-12-22"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"directorName"</span><span class="punctuation">:</span> <span class="string">"罗伯特·泽米吉斯"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"desc"</span><span class="punctuation">:</span> <span class="string">"一名联邦快递员工在南太平洋荒岛求生的故事"</span></span><br><span class="line">    <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">    <span class="punctuation">{</span></span><br><span class="line">        <span class="attr">"name"</span><span class="punctuation">:</span> <span class="string">"阿甘正传"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"releaseDate"</span><span class="punctuation">:</span> <span class="string">"1994-07-06"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"directorName"</span><span class="punctuation">:</span> <span class="string">"罗伯特·泽米吉斯"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"desc"</span><span class="punctuation">:</span> <span class="string">"智商仅相当于孩童的善良男子见证美国近代历史的故事"</span></span><br><span class="line">    <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">    <span class="punctuation">{</span></span><br><span class="line">        <span class="attr">"name"</span><span class="punctuation">:</span> <span class="string">"阿波罗13号"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"releaseDate"</span><span class="punctuation">:</span> <span class="string">"1995-06-30"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"directorName"</span><span class="punctuation">:</span> <span class="string">"朗·霍华德"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"desc"</span><span class="punctuation">:</span> <span class="string">"宇航员在月球执行任务时遭遇意外的故事"</span></span><br><span class="line">    <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">    <span class="punctuation">{</span></span><br><span class="line">        <span class="attr">"name"</span><span class="punctuation">:</span> <span class="string">"玩具总动员"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"releaseDate"</span><span class="punctuation">:</span> <span class="string">"1995-11-22"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"directorName"</span><span class="punctuation">:</span> <span class="string">"约翰·拉塞特"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"desc"</span><span class="punctuation">:</span> <span class="string">"玩具牛仔胡迪和太空战警巴斯光年的冒险故事"</span></span><br><span class="line">    <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">    <span class="punctuation">{</span></span><br><span class="line">        <span class="attr">"name"</span><span class="punctuation">:</span> <span class="string">"达芬奇密码"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"releaseDate"</span><span class="punctuation">:</span> <span class="string">"2006-05-19"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"directorName"</span><span class="punctuation">:</span> <span class="string">"朗·霍华德"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"desc"</span><span class="punctuation">:</span> <span class="string">"哈佛大学教授破解中世纪符号学的惊悚故事"</span></span><br><span class="line">    <span class="punctuation">}</span></span><br><span class="line"><span class="punctuation">]</span></span><br></pre></td></tr></tbody></table></figure><hr><h4 id="4-4-5-拓展：MapOutputConverter-输出动态键值对"><a href="#4-4-5-拓展：MapOutputConverter-输出动态键值对" class="headerlink" title="4.4.5 拓展：MapOutputConverter - 输出动态键值对"></a><strong>4.4.5 拓展：<code>MapOutputConverter</code> - 输出动态键值对</strong></h4><p>我们已经学会了如何将AI的响应输出为单个对象（<code>Film</code>）或对象列表（<code>List&lt;Film&gt;</code>）。但有时，我们希望输出的JSON的<strong>键（key）本身就是动态的</strong>。例如，以电影名作为键，电影信息作为值，来构建一个 <code>Map</code>。</p><p><code>MapOutputConverter</code> 正是为此类场景而生。它适用于当您需要一个灵活的、键值对不固定的 <code>Map&lt;String, Object&gt;</code> 作为输出，而不想为此创建专门的Java类时。</p><p><strong>1. 在 <code>FilmService.java</code> 中添加新方法</strong></p><p>我们将添加一个 <code>getFilmsAsMap</code> 方法，要求 AI 以电影名为键来组织返回的数据。</p><p>正常的写法是这样的，但是使用Entity为核心的写法我们就无需指定<code>mapOutputConverter</code>了</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 使用 MapOutputConverter 获取以电影名为键的电影信息 Map</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"><span class="keyword">public</span> Map&lt;String, Object&gt; <span class="title function_">getFilmsAsMap</span><span class="params">(String style)</span> {</span><br><span class="line">    <span class="type">var</span> <span class="variable">mapOutputConverter</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">MapOutputConverter</span>();</span><br><span class="line">    </span><br><span class="line">    <span class="type">String</span> <span class="variable">userMessage</span> <span class="operator">=</span> <span class="string">"""</span></span><br><span class="line"><span class="string">        帮我找五部{style}的电影，以电影名为分组键，值为电影信息。</span></span><br><span class="line"><span class="string">        电影信息需要包含电影名称、上映时间、导演名、电影简介等内容。</span></span><br><span class="line"><span class="string">        {format}</span></span><br><span class="line"><span class="string">        """</span>;</span><br><span class="line">    </span><br><span class="line">    <span class="type">String</span> <span class="variable">format</span> <span class="operator">=</span> mapOutputConverter.getFormat();</span><br><span class="line">    <span class="type">Prompt</span> <span class="variable">prompt</span> <span class="operator">=</span> <span class="keyword">new</span> <span class="title class_">PromptTemplate</span>(userMessage).create(Map.of(<span class="string">"style"</span>, style, <span class="string">"format"</span>, format));</span><br><span class="line">    </span><br><span class="line">    <span class="type">String</span> <span class="variable">content</span> <span class="operator">=</span> chatClient.prompt(prompt).call().content();</span><br><span class="line">    <span class="keyword">return</span> mapOutputConverter.convert(content);</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>在 <code>FilmController.java</code> 中添加新端点</strong></p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line"><span class="meta">@GetMapping("/as-map")</span></span><br><span class="line"><span class="keyword">public</span> Map&lt;String, Object&gt; <span class="title function_">getFilmsAsMap</span><span class="params">(<span class="meta">@RequestParam(defaultValue = "华语流行")</span> String style)</span> {</span><br><span class="line">    <span class="comment">// 为了简洁，我们直接使用 .entity() 的优雅写法</span></span><br><span class="line">    <span class="type">String</span> <span class="variable">userMessage</span> <span class="operator">=</span> <span class="string">"""</span></span><br><span class="line"><span class="string">            帮我找五部{style}的电影，以电影名为分组键，值为电影信息。</span></span><br><span class="line"><span class="string">            电影信息需要包含电影名称、上映时间、导演名、电影简介等内容。</span></span><br><span class="line"><span class="string">            """</span>;</span><br><span class="line">    <span class="keyword">return</span> chatClient.prompt()</span><br><span class="line">            .user(u -&gt; u.text(userMessage).param(<span class="string">"style"</span>, style))</span><br><span class="line">            .call()</span><br><span class="line">            .entity(<span class="keyword">new</span> <span class="title class_">ParameterizedTypeReference</span>&lt;Map&lt;String, Object&gt;&gt;() {</span><br><span class="line">            });</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p>启动应用后，访问此新端点 <code>http://localhost:8080/api/v1/films/as-map?style=经典武侠</code>。</p><ul><li><p><strong>预期 JSON 响应 (内容可能不同)</strong>：</p><figure class="highlight json"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br></pre></td><td class="code"><pre><span class="line"><span class="punctuation">{</span></span><br><span class="line">    <span class="attr">"东邪西毒"</span><span class="punctuation">:</span> <span class="punctuation">{</span></span><br><span class="line">        <span class="attr">"电影名称"</span><span class="punctuation">:</span> <span class="string">"东邪西毒"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"上映时间"</span><span class="punctuation">:</span> <span class="string">"1994-09-17"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"导演名"</span><span class="punctuation">:</span> <span class="string">"王家卫"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"电影简介"</span><span class="punctuation">:</span> <span class="string">"讲述欧阳锋、黄药师等江湖人物的爱恨情仇与宿命纠葛。"</span></span><br><span class="line">    <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">    <span class="attr">"新龙门客栈"</span><span class="punctuation">:</span> <span class="punctuation">{</span></span><br><span class="line">        <span class="attr">"电影名称"</span><span class="punctuation">:</span> <span class="string">"新龙门客栈"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"上映时间"</span><span class="punctuation">:</span> <span class="string">"1992-08-27"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"导演名"</span><span class="punctuation">:</span> <span class="string">"徐克"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"电影简介"</span><span class="punctuation">:</span> <span class="string">"明朝东厂与江湖侠客在龙门客栈展开生死对决的故事。"</span></span><br><span class="line">    <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">    <span class="attr">"笑傲江湖"</span><span class="punctuation">:</span> <span class="punctuation">{</span></span><br><span class="line">        <span class="attr">"电影名称"</span><span class="punctuation">:</span> <span class="string">"笑傲江湖"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"上映时间"</span><span class="punctuation">:</span> <span class="string">"1990-01-27"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"导演名"</span><span class="punctuation">:</span> <span class="string">"胡金铨"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"电影简介"</span><span class="punctuation">:</span> <span class="string">"令狐冲卷入江湖纷争，最终领悟武学真谛的传奇故事。"</span></span><br><span class="line">    <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">    <span class="attr">"卧虎藏龙"</span><span class="punctuation">:</span> <span class="punctuation">{</span></span><br><span class="line">        <span class="attr">"电影名称"</span><span class="punctuation">:</span> <span class="string">"卧虎藏龙"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"上映时间"</span><span class="punctuation">:</span> <span class="string">"2000-07-07"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"导演名"</span><span class="punctuation">:</span> <span class="string">"李安"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"电影简介"</span><span class="punctuation">:</span> <span class="string">"围绕青冥剑展开的江湖恩怨与儿女情长的武侠传奇。"</span></span><br><span class="line">    <span class="punctuation">}</span><span class="punctuation">,</span></span><br><span class="line">    <span class="attr">"英雄"</span><span class="punctuation">:</span> <span class="punctuation">{</span></span><br><span class="line">        <span class="attr">"电影名称"</span><span class="punctuation">:</span> <span class="string">"英雄"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"上映时间"</span><span class="punctuation">:</span> <span class="string">"2002-12-19"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"导演名"</span><span class="punctuation">:</span> <span class="string">"张艺谋"</span><span class="punctuation">,</span></span><br><span class="line">        <span class="attr">"电影简介"</span><span class="punctuation">:</span> <span class="string">"刺客无名向秦王讲述刺杀真相的多重视角武侠史诗。"</span></span><br><span class="line">    <span class="punctuation">}</span></span><br><span class="line"><span class="punctuation">}</span></span><br></pre></td></tr></tbody></table></figure></li></ul><hr><h4 id="4-4-6-深度揭秘：ParameterizedTypeReference-的“魔法”"><a href="#4-4-6-深度揭秘：ParameterizedTypeReference-的“魔法”" class="headerlink" title="4.4.6 深度揭秘：ParameterizedTypeReference 的“魔法”"></a><strong>4.4.6 深度揭秘：<code>ParameterizedTypeReference</code> 的“魔法”</strong></h4><p>您一定很好奇，为什么在处理 <code>List&lt;Film&gt;</code> 或 <code>Map&lt;String, Object&gt;</code> 这样的复杂类型时，我们不能像 <code>Film.class</code> 那样直接传递 <code>List&lt;Film&gt;.class</code>，而是需要用到 <code>new ParameterizedTypeReference&lt;List&lt;Film&gt;&gt;() {}</code> 这样看起来有些古怪的语法？</p><p>答案在于 Java 语言的一个核心特性：**类型擦除 **。</p><p><strong>1. 问题根源：类型擦除</strong></p><p>我们可以用一个简单的比喻来理解它：</p><blockquote><p>想象一下您要寄一个快递。在打包时，您在箱子上贴了一个标签：“易碎品：玻璃花瓶”（这就像是<strong>编译时</strong>的泛型类型 <code>List&lt;Film&gt;</code>）。编译器（打包员）会检查您的标签是否正确。</p><p>但是，一旦您的包裹被放上快递车，快递员（<strong>运行时</strong>的 JVM）只关心这是“一个包裹”（一个原始的 <code>List</code>)，而不再关心里面具体是“玻璃花瓶”还是“铁块”。箱子上关于“玻璃花瓶”的具体信息，在运输过程中被“擦除”了。</p></blockquote><p>这就是类型擦除。在运行时，JVM 并不知道一个 <code>List</code> 对象的泛型参数具体是 <code>Film</code> 还是 <code>String</code>。因此，像 <code>List&lt;Film&gt;.class</code> 这样的语法在 Java 中是<strong>非法</strong>的，因为在运行时根本不存在一个与 <code>List&lt;String&gt;.class</code> 相区别的 <code>List&lt;Film&gt;.class</code>，它们都是同一个 <code>List.class</code>。</p><p>这就给我们的程序带来了麻烦：当 <code>chatClient</code> 返回一段 JSON 数组时，它需要知道是应该把这个数组里的每个元素都转成 <code>Film</code> 对象，还是转成别的什么对象。如果只给它 <code>List.class</code>，信息就不足了。</p><p><strong>2. 解决方案：“超级类型令牌” 的巧计</strong></p><p><code>ParameterizedTypeReference</code> 正是绕过类型擦除的“天才魔术”。这个魔术的核心在于书写的那个看似多余的大括号 <code>{}</code>。</p><p>当您写下 <code>new ParameterizedTypeReference&lt;List&lt;Film&gt;&gt;() {}</code> 时，做的<strong>不仅仅是</strong>创建一个对象，而是<strong>创建了一个匿名的内部类</strong>！</p><p>这个匿名内部类是这样的：</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 代码:</span></span><br><span class="line"><span class="keyword">new</span> <span class="title class_">ParameterizedTypeReference</span>&lt;List&lt;Film&gt;&gt;() {};</span><br><span class="line"></span><br><span class="line"><span class="comment">// 编译器在背后实际创建了一个类似这样的东西:</span></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">SomeAnonymousClass</span> <span class="keyword">extends</span> <span class="title class_">ParameterizedTypeReference</span>&lt;List&lt;Film&gt;&gt; {</span><br><span class="line">    <span class="comment">// 类体是空的</span></span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p><strong>这里的“魔法”就发生了</strong>：虽然 Java 会擦除字段和方法参数的泛型信息，但它<strong>会把一个类所继承的父类（或实现的接口）的泛型信息，作为元数据永久地记录在子类的 <code>.class</code> 文件中</strong>。</p><p>所以，<code>SomeAnonymousClass</code> 这个匿名类，它永远地“记住”了自己是 <code>ParameterizedTypeReference&lt;List&lt;Film&gt;&gt;</code> 的一个子类。</p><p><strong>Spring 框架就可以利用这一点来“反向破解”：</strong></p><ol><li><code>.entity()</code> 方法接收到您创建的这个匿名内部类的实例。</li><li>它通过 Java 的<strong>反射</strong>机制，调用这个实例的 <code>getClass().getGenericSuperclass()</code> 方法。</li><li>这个方法会返回一个 <code>ParameterizedType</code> 对象，Spring 可以从这个对象中，安全地、精确地提取出那个“被擦除”的泛型参数——<code>List&lt;Film&gt;</code>。</li><li>“魔法”完成！Spring 现在知道了您想要的完整类型，从而可以正确地将 JSON 数组反序列化为您期望的 <code>List&lt;Film&gt;</code> 对象。</li></ol><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/51850.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/51850.html&quot;)">SpringAI（四）：4. 深入 Prompt 工程与结构化输出</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/51850.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/51850.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Java<span class="categoryesPageCount">20</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Java微服务篇<span class="tagsPageCount">11</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/52289.html"><img class="prev-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">SpringAI（三）：3. 会话核心 API 深度解析</div></div></a></div><div class="next-post pull-right"><a href="/posts/60609.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/5770.html" title="SpringAI（七）：7. Embedding Models：万物皆可向量化"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（七）：7. Embedding Models：万物皆可向量化</div></div></a></div><div><a href="/posts/59358.html" title="SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元</div></div></a></div><div><a href="/posts/52289.html" title="SpringAI（三）：3. 会话核心 API 深度解析"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（三）：3. 会话核心 API 深度解析</div></div></a></div><div><a href="/posts/18714.html" title="SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用</div></div></a></div><div><a href="/posts/22322.html" title="SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”</div></div></a></div><div><a href="/posts/60609.html" title="SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"SpringAI（四）：4. 深入 Prompt 工程与结构化输出",date:"2025-03-21 01:13:45",updated:"2025-07-08 13:53:35",tags:["Java微服务篇"],categories:["后端技术","Java"],content:'\n## **4. 深入 Prompt 工程与结构化输出**\n\n### **引言：当程序员遇上简历筛选**\n\n> “这个候选人到底是什么水平？”\n\n这个问题，往往能让我们在堆积如山的简历中反复阅读、查找关键信息，最后得出一个模糊的印象。本章，我们将用技术解决这个难题：通过深入探索 Prompt 工程，让大语言模型成为您的私人招聘助理，并按我们指定的、精确的“剧本”格式，输出结构化的候选人报告。\n\n### **4.1 Prompt 的重要性与基本原则**\n\n如果说 `ChatClient` 是连接你和 AI 的电话线，那么 **Prompt 就是你在这条电话线上说的话**。你说得是否清晰、准确、有技巧，直接决定了电话那头的 AI 能否理解你的意图并给出满意的答复。Prompt 工程（Prompt Engineering）就是一门研究如何与 AI 高效沟通的艺术与科学。\n\n> **Prompt 是与 AI 沟通的 API**。就像调用一个软件 API 需要遵循其定义的参数和格式一样，与 AI 沟通也需要遵循一定的范式，才能获得稳定、可控的输出。\n\n以下是编写高效 Prompt 的几个基本原则：\n\n1.  **清晰具体**：避免使用模糊的词语。\n2.  **提供上下文**：如果问题需要背景知识，请在 Prompt 中提供。\n3.  **设定角色**：这是最有效的技巧之一，可以极大地约束 AI 的行为和语言风格。\n4.  **施加约束**：明确告诉模型你**不**想要什么，或者输出必须遵循的格式。\n5.  **提供示例**：如果需要模型遵循特定的输出格式或风格，最好的方法就是给它一两个例子。\n\n### **4.2 `Prompt` 与 `Message` 详解**\n\n#### **4.2.1 `Prompt` 类：与 AI 对话的标准容器**\n\n在 Spring AI 中，`org.springframework.ai.chat.prompt.Prompt` 类是所有与 `ChatClient` 交互的载体。它不仅仅是一个简单的字符串包装器，而是一个结构化的对象，用于封装发送给模型的完整指令集。\n\n你可以把 `chatClient.prompt().user("...")` 这种便捷方式看作是寄一个**信封**，而使用 `new Prompt(...)` 则是打包一个可定制的**快递包裹**，功能更强大。\n\n| 构造函数 | 说明 |\n| :--- | :--- |\n| **`Prompt(String contents)`** | 最简单的形式，内部会将字符串包装成一个 `UserMessage`。 |\n| **`Prompt(List<Message> messages)`** | **最通用、最强大**的形式。允许传入一个由不同角色消息组成的列表。 |\n| **`Prompt(..., PromptOptions options)`**| 在以上形式的基础上，附加一次性的、请求级别的模型参数。 |\n\n#### **4.2.2 `Message` 接口：定义对话角色**\n\n`Message` 是构成 `Prompt` 的基本单位。Spring AI 定义了四种核心的 `Message` 类型。\n\n| 消息类型 (Message Type) | 核心作用与职责 |\n| :--- | :--- |\n| **`SystemMessage`** | **系统指令**：用于设定 AI 的角色、行为准则、个性、目标和任何高级指令。 |\n| **`UserMessage`** | **用户输入**：代表最终用户的提问、指令或对话内容。 |\n| **`AssistantMessage`** | **助手回复**：代表 AI 自己**之前**的回复，是构建多轮对话历史的关键。 |\n| **`FunctionMessage`** | **函数结果**：用于函数调用场景，将外部工具的执行结果返回给 AI。 |\n\n### **4.3 `PromptTemplate`：让 Prompt 动起来**\n\n`PromptTemplate` 允许你定义一个包含占位符（如 `{variable}`）的模板字符串，然后用一个 `Map` 来填充这些变量，最终渲染出一个完整的 `Prompt` 对象，让你的 Prompt 变得动态和可复用。\n\n#### **4.3.1 API 详解**\n\n| 方法 | 说明 |\n| :--- | :--- |\n| **`PromptTemplate(String template)`** | 构造函数，传入包含占位符的模板字符串。 |\n| **`create(Map<String, Object> model)`** | 接收一个 Map，用 Map 中的键值对替换模板中的占位符，并返回一个完整的 `Prompt` 对象。 |\n\n#### **4.3.2 实战：动态翻译器**\n\n我们将为 `ChatService` 添加一个新方法，该方法可以根据传入的目标语言，动态地构建一个翻译 Prompt。\n\n1.  **在 `ChatService.java` 中添加新方法**\n\n    ```java\n    // src/main/java/com/copilot/aicopilotbackend/service/ChatService.java\n    @Service\n    public class ChatService {\n        // ... 原有方法 ...\n        public Flux<String> getDynamicTranslatedStream(String targetLanguage, String text) {\n            String systemPromptTemplate = """\n                你是一个专业的、精通多种语言的翻译家。\n                请将用户接下来发送的所有内容都翻译成 {targetLanguage}。\n                不要添加任何与翻译结果无关的解释或寒暄。\n                """;\n            return chatClient.prompt()\n                    .system(spec -> spec.text(systemPromptTemplate).param("targetLanguage", targetLanguage))\n                    .user(text)\n                    .stream()\n                    .content();\n        }\n    }\n    ```\n\n2.  **在 `ChatController.java` 中添加新端点**\n\n    ```java\n    // src/main/java/com/copilot/aicopilotbackend/controller/ChatController.java\n    @RestController\n    @RequestMapping("/api/v1/chat")\n    public class ChatController {\n        // ... 原有方法 ...\n        @GetMapping(value = "/translate", produces = MediaType.TEXT_EVENT_STREAM_VALUE)\n        public Flux<String> translate(@RequestParam String targetLanguage, @RequestParam String text) {\n            return chatService.getDynamicTranslatedStream(targetLanguage, text);\n        }\n    }\n    ```\n\n### **4.4 结构化输出：将 AI 响应映射为 POJO**\n\n现在，我们进入本章的核心。我们将学习如何让 AI 从一个自由的“创作者”，转变为一个能精确输出结构化数据的“数据工程师”。\n\n#### **4.4.1 烦恼与优势：为何需要结构化？**\n\n**1. 原始输出的烦恼**\n\n如果我们直接要求 AI 总结一份简历，得到的可能是对程序极不友好的纯文本：\n\n> "这位候选人叫张三，看起来有超过8年的Java开发经验，技能方面，他提到了Spring Boot、微服务和Docker..."\n\n**问题显而易见**：\n\n  * **格式不稳定**：每次请求的措辞和格式都可能变化。\n  * **难以解析**：我们需要编写脆弱的正则表达式来提取“8年”、“Java”等关键词。\n  * **无法直接使用**：无法直接将这段文本映射到我们的 `Candidate` 对象上。\n\n**2. 结构化的优势**\n\n如果我们能让 AI 直接输出 JSON，程序就可以无缝解析，无需任何复杂的后处理。\n\n```json\n{\n  "name": "张三",\n  "yearsOfExperience": 8,\n  "skills": ["Java", "Spring Boot", "Microservices", "Docker"]\n}\n```\n\nSpring AI 正是通过在**提示词中追加格式化指令**，并结合内置的**转换器（Converter）**来实现这一目标的。\n\n#### **4.4.2 API 详解：`OutputConverter` 家族**\n\n| 转换器 (Converter) | 输出类型 | 核心用途 |\n| :--- | :--- | :--- |\n| **`BeanOutputConverter<T>`** | `T` (您自定义的 POJO) | **最常用、最强大**。能将 AI 的响应直接映射到一个定义好的 Java 类或 Record。 |\n| **`MapOutputConverter`** | `Map<String, Object>`| 当输出的键值对不固定时使用。 |\n| **`ListOutputConverter`** | `List<String>` | 当您只需要一个简单的字符串列表时。 |\n\n#### **4.4.3 BeanOutputConverter实战：从简历中提取结构化数据**\n\n我们将构建一个全新的、独立的服务，用于演示如何从非结构化的简历文本中，提取出结构化的 `Candidate` 信息。\n\n**1. 定义数据模型 (`dto/request/Candidate.java`)**\n\n```java\n// src/main/java/com/copilot/aicopilotbackend/dto/request/Candidate.java\npackage com.copilot.aicopilotbackend.dto.request;\n\nimport java.util.List;\nimport com.fasterxml.jackson.annotation.JsonPropertyDescription;\n\npublic record Candidate(\n        @JsonPropertyDescription("候选人姓名") String name,\n        @JsonPropertyDescription("候选人工作年限") int yearsOfExperience,\n        @JsonPropertyDescription("候选人掌握的技能列表") List<String> skills\n) {}\n```\n\n**2. 创建解析服务 (`service/ExtractionService.java`)**\n\n```java\n// src/main/java/com/copilot/aicopilotbackend/service/ExtractionService.java\npackage com.copilot.aicopilotbackend.service;\n\nimport com.copilot.aicopilotbackend.dto.request.Candidate;\nimport org.springframework.ai.chat.client.ChatClient;\nimport org.springframework.ai.chat.prompt.Prompt;\nimport org.springframework.ai.chat.prompt.PromptTemplate;\nimport org.springframework.ai.converter.BeanOutputConverter;\nimport org.springframework.stereotype.Service;\nimport java.util.Map;\n\n@Service\npublic class ExtractionService {\n\n    private final ChatClient chatClient;\n\n    public ExtractionService(ChatClient chatClient) {\n        this.chatClient = chatClient;\n    }\n\n    public Candidate extractCandidateFrom(String resumeText) {\n        var outputConverter = new BeanOutputConverter<>(Candidate.class);\n        String formatInstructions = outputConverter.getFormat();\n\n        String promptTemplateString = """\n                从下面的简历文本中提取信息。\n                {format}\n                简历文本:\n                {resume}\n                """;\n        PromptTemplate promptTemplate = new PromptTemplate(promptTemplateString);\n        Prompt prompt = promptTemplate.create(Map.of(\n                "resume", resumeText,\n                "format", formatInstructions\n        ));\n\n        return chatClient.prompt(prompt)\n                .call()\n                .entity(outputConverter);\n    }\n}\n```\n\n**3. 创建 API 端点 (`controller/ExtractionController.java`)**\n\n```java\n// src/main/java/com/copilot/aicopilotbackend/controller/ExtractionController.java\npackage com.copilot.aicopilotbackend.controller;\n\nimport com.copilot.aicopilotbackend.dto.request.Candidate;\nimport com.copilot.aicopilotbackend.service.ExtractionService;\nimport org.springframework.web.bind.annotation.*;\nimport java.util.Map;\n\n@RestController\n@RequestMapping("/api/v1/extraction")\npublic class ExtractionController {\n\n    private final ExtractionService extractionService;\n\n    public ExtractionController(ExtractionService extractionService) {\n        this.extractionService = extractionService;\n    }\n\n    @PostMapping("/candidate")\n    public Candidate extractData(@RequestBody Map<String, String> request) {\n        return extractionService.extractCandidateFrom(request.get("resumeText"));\n    }\n}\n```\n\n**4. 运行与测试**\n\n  * **请求体**:\n    ```json\n    {\n      "resumeText": "张三是一名资深软件工程师，拥有超过8年的Java开发经验。他精通Spring Boot, Microservices, 和 Docker技术。"\n    }\n    ```\n  * **预期 JSON 响应**:\n    ```json\n    {\n      "name": "张三",\n      "yearsOfExperience": 8,\n      "skills": [ "Java", "Spring Boot", "Microservices", "Docker" ]\n    }\n    ```\n\n#### **4.4.4 ListOutputConverter扩展：处理集合类型**\n\n`BeanOutputConverter` 的强大之处在于它也能处理泛型集合。现在我们举一个电影推荐案例，来展示如何输出一个 `List<Film>`\n\n**1. 定义 `Film` DTO (`dto/response/Film.java`)**\n\n```java\n// src/main/java/com/copilot/aicopilotbackend/dto/response/Film.java\npackage com.copilot.aicopilotbackend.dto.response;\nimport com.fasterxml.jackson.annotation.JsonPropertyDescription;\n\npublic record Film(\n    @JsonPropertyDescription("电影名称") String name,\n    @JsonPropertyDescription("上映时间") String releaseDate,\n    @JsonPropertyDescription("导演名称") String directorName,\n    @JsonPropertyDescription("电影简介") String desc\n) {}\n```\n\n**2. 在 `FilmService` 和 `FilmController` 中实现**\n\n对于Enity来说：大模型输出转换为实体对象看起来还是比较复杂的，不过Spring AI还提供更简易的方式：直接在 `call() `后面调用 `entity `，把对应的class类型传递进去即可， **并且在提示词中** `**{format}** `**占位符不需要再手动添加** 。\n\n```java\npackage com.copilot.aicopilotbackend.controller;\n\nimport com.copilot.aicopilotbackend.dto.response.Film;\nimport org.springframework.ai.chat.client.ChatClient;\nimport org.springframework.ai.chat.prompt.Prompt;\nimport org.springframework.ai.chat.prompt.PromptTemplate;\nimport org.springframework.ai.converter.BeanOutputConverter;\nimport org.springframework.core.ParameterizedTypeReference;\nimport org.springframework.web.bind.annotation.GetMapping;\nimport org.springframework.web.bind.annotation.RequestMapping;\nimport org.springframework.web.bind.annotation.RestController;\n\nimport java.util.List;\nimport java.util.Map;\n@RestController\n@RequestMapping("api/v1/films")\npublic class FilmController {\n    private final ChatClient chatClient;\n\n    public FilmController(ChatClient chatClient) {\n        this.chatClient = chatClient;\n    }\n\n//    @GetMapping("films")\n//    /**\n//     * 普通方法实现，并不优雅\n//     */\n//    public List<Film> getFilmsByActor(String actor) {\n//        // 使用 ParameterizedTypeReference 来精确指定我们期望的复杂泛型类型\n//        var converter = new BeanOutputConverter<>(new ParameterizedTypeReference<List<Film>>() {});\n//        String format = converter.getFormat();\n//        String userMessage = "帮我找五部 {actor} 主演的电影。 {format}";\n//        Prompt prompt = new PromptTemplate(userMessage).create(Map.of("actor", actor, "format", format));\n//        String content = chatClient.prompt(prompt).call().content();\n//        return converter.convert(content);\n//    }\n\n    /**\n     * 使用更优雅的 .entity() 写法\n     * @param actor\n     * @return\n     */\n    @GetMapping("as-list")\n    public List<Film> getFilmsByActor(String actor) {\n        return  chatClient.prompt()\n                .user(u-> u.text("帮我找五部 {actor} 主演的电影").param("actor", actor))\n                .call()\n                .entity(new ParameterizedTypeReference<List<Film>>() {});\n    }\n\n\n}\n\n```\n\n##### 接口文档\n\n**示例请求**\n\n```\nhttp://localhost:8080/api/v1/films/as-list?actor=汤姆·汉克斯\n```\n\n**响应示例**\n\n成功返回的响应体将是一个电影对象列表，示例如下：\n\n```json\n[\n    {\n        "name": "荒岛余生",\n        "releaseDate": "2000-12-22",\n        "directorName": "罗伯特·泽米吉斯",\n        "desc": "一名联邦快递员工在南太平洋荒岛求生的故事"\n    },\n    {\n        "name": "阿甘正传",\n        "releaseDate": "1994-07-06",\n        "directorName": "罗伯特·泽米吉斯",\n        "desc": "智商仅相当于孩童的善良男子见证美国近代历史的故事"\n    },\n    {\n        "name": "阿波罗13号",\n        "releaseDate": "1995-06-30",\n        "directorName": "朗·霍华德",\n        "desc": "宇航员在月球执行任务时遭遇意外的故事"\n    },\n    {\n        "name": "玩具总动员",\n        "releaseDate": "1995-11-22",\n        "directorName": "约翰·拉塞特",\n        "desc": "玩具牛仔胡迪和太空战警巴斯光年的冒险故事"\n    },\n    {\n        "name": "达芬奇密码",\n        "releaseDate": "2006-05-19",\n        "directorName": "朗·霍华德",\n        "desc": "哈佛大学教授破解中世纪符号学的惊悚故事"\n    }\n]\n```\n\n-----\n\n#### **4.4.5 拓展：`MapOutputConverter` - 输出动态键值对**\n\n我们已经学会了如何将AI的响应输出为单个对象（`Film`）或对象列表（`List<Film>`）。但有时，我们希望输出的JSON的**键（key）本身就是动态的**。例如，以电影名作为键，电影信息作为值，来构建一个 `Map`。\n\n`MapOutputConverter` 正是为此类场景而生。它适用于当您需要一个灵活的、键值对不固定的 `Map<String, Object>` 作为输出，而不想为此创建专门的Java类时。\n\n**1. 在 `FilmService.java` 中添加新方法**\n\n我们将添加一个 `getFilmsAsMap` 方法，要求 AI 以电影名为键来组织返回的数据。\n\n正常的写法是这样的，但是使用Entity为核心的写法我们就无需指定`mapOutputConverter`了\n\n```java\n/**\n * 使用 MapOutputConverter 获取以电影名为键的电影信息 Map\n */\npublic Map<String, Object> getFilmsAsMap(String style) {\n    var mapOutputConverter = new MapOutputConverter();\n    \n    String userMessage = """\n        帮我找五部{style}的电影，以电影名为分组键，值为电影信息。\n        电影信息需要包含电影名称、上映时间、导演名、电影简介等内容。\n        {format}\n        """;\n    \n    String format = mapOutputConverter.getFormat();\n    Prompt prompt = new PromptTemplate(userMessage).create(Map.of("style", style, "format", format));\n    \n    String content = chatClient.prompt(prompt).call().content();\n    return mapOutputConverter.convert(content);\n}\n```\n\n\n\n**在 `FilmController.java` 中添加新端点**\n\n```java\n@GetMapping("/as-map")\npublic Map<String, Object> getFilmsAsMap(@RequestParam(defaultValue = "华语流行") String style) {\n    // 为了简洁，我们直接使用 .entity() 的优雅写法\n    String userMessage = """\n            帮我找五部{style}的电影，以电影名为分组键，值为电影信息。\n            电影信息需要包含电影名称、上映时间、导演名、电影简介等内容。\n            """;\n    return chatClient.prompt()\n            .user(u -> u.text(userMessage).param("style", style))\n            .call()\n            .entity(new ParameterizedTypeReference<Map<String, Object>>() {\n            });\n}\n```\n\n启动应用后，访问此新端点 `http://localhost:8080/api/v1/films/as-map?style=经典武侠`。\n\n  * **预期 JSON 响应 (内容可能不同)**：\n\n    ```json\n    {\n        "东邪西毒": {\n            "电影名称": "东邪西毒",\n            "上映时间": "1994-09-17",\n            "导演名": "王家卫",\n            "电影简介": "讲述欧阳锋、黄药师等江湖人物的爱恨情仇与宿命纠葛。"\n        },\n        "新龙门客栈": {\n            "电影名称": "新龙门客栈",\n            "上映时间": "1992-08-27",\n            "导演名": "徐克",\n            "电影简介": "明朝东厂与江湖侠客在龙门客栈展开生死对决的故事。"\n        },\n        "笑傲江湖": {\n            "电影名称": "笑傲江湖",\n            "上映时间": "1990-01-27",\n            "导演名": "胡金铨",\n            "电影简介": "令狐冲卷入江湖纷争，最终领悟武学真谛的传奇故事。"\n        },\n        "卧虎藏龙": {\n            "电影名称": "卧虎藏龙",\n            "上映时间": "2000-07-07",\n            "导演名": "李安",\n            "电影简介": "围绕青冥剑展开的江湖恩怨与儿女情长的武侠传奇。"\n        },\n        "英雄": {\n            "电影名称": "英雄",\n            "上映时间": "2002-12-19",\n            "导演名": "张艺谋",\n            "电影简介": "刺客无名向秦王讲述刺杀真相的多重视角武侠史诗。"\n        }\n    }\n    ```\n\n-----\n\n#### **4.4.6 深度揭秘：`ParameterizedTypeReference` 的“魔法”**\n\n您一定很好奇，为什么在处理 `List<Film>` 或 `Map<String, Object>` 这样的复杂类型时，我们不能像 `Film.class` 那样直接传递 `List<Film>.class`，而是需要用到 `new ParameterizedTypeReference<List<Film>>() {}` 这样看起来有些古怪的语法？\n\n答案在于 Java 语言的一个核心特性：**类型擦除 **。\n\n**1. 问题根源：类型擦除**\n\n我们可以用一个简单的比喻来理解它：\n\n> 想象一下您要寄一个快递。在打包时，您在箱子上贴了一个标签：“易碎品：玻璃花瓶”（这就像是**编译时**的泛型类型 `List<Film>`）。编译器（打包员）会检查您的标签是否正确。\n>\n> 但是，一旦您的包裹被放上快递车，快递员（**运行时**的 JVM）只关心这是“一个包裹”（一个原始的 `List`)，而不再关心里面具体是“玻璃花瓶”还是“铁块”。箱子上关于“玻璃花瓶”的具体信息，在运输过程中被“擦除”了。\n\n这就是类型擦除。在运行时，JVM 并不知道一个 `List` 对象的泛型参数具体是 `Film` 还是 `String`。因此，像 `List<Film>.class` 这样的语法在 Java 中是**非法**的，因为在运行时根本不存在一个与 `List<String>.class` 相区别的 `List<Film>.class`，它们都是同一个 `List.class`。\n\n这就给我们的程序带来了麻烦：当 `chatClient` 返回一段 JSON 数组时，它需要知道是应该把这个数组里的每个元素都转成 `Film` 对象，还是转成别的什么对象。如果只给它 `List.class`，信息就不足了。\n\n**2. 解决方案：“超级类型令牌” 的巧计**\n\n`ParameterizedTypeReference` 正是绕过类型擦除的“天才魔术”。这个魔术的核心在于书写的那个看似多余的大括号 `{}`。\n\n当您写下 `new ParameterizedTypeReference<List<Film>>() {}` 时，做的**不仅仅是**创建一个对象，而是**创建了一个匿名的内部类**！\n\n这个匿名内部类是这样的：\n\n```java\n// 代码:\nnew ParameterizedTypeReference<List<Film>>() {};\n\n// 编译器在背后实际创建了一个类似这样的东西:\nclass SomeAnonymousClass extends ParameterizedTypeReference<List<Film>> {\n    // 类体是空的\n}\n```\n\n**这里的“魔法”就发生了**：虽然 Java 会擦除字段和方法参数的泛型信息，但它**会把一个类所继承的父类（或实现的接口）的泛型信息，作为元数据永久地记录在子类的 `.class` 文件中**。\n\n所以，`SomeAnonymousClass` 这个匿名类，它永远地“记住”了自己是 `ParameterizedTypeReference<List<Film>>` 的一个子类。\n\n**Spring 框架就可以利用这一点来“反向破解”：**\n\n1.  `.entity()` 方法接收到您创建的这个匿名内部类的实例。\n2.  它通过 Java 的**反射**机制，调用这个实例的 `getClass().getGenericSuperclass()` 方法。\n3.  这个方法会返回一个 `ParameterizedType` 对象，Spring 可以从这个对象中，安全地、精确地提取出那个“被擦除”的泛型参数——`List<Film>`。\n4.  “魔法”完成！Spring 现在知道了您想要的完整类型，从而可以正确地将 JSON 数组反序列化为您期望的 `List<Film>` 对象。\n\n\n\n\n\n---'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#4-%E6%B7%B1%E5%85%A5-Prompt-%E5%B7%A5%E7%A8%8B%E4%B8%8E%E7%BB%93%E6%9E%84%E5%8C%96%E8%BE%93%E5%87%BA"><span class="toc-number">1.</span> <span class="toc-text">4. 深入 Prompt 工程与结构化输出</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%BC%95%E8%A8%80%EF%BC%9A%E5%BD%93%E7%A8%8B%E5%BA%8F%E5%91%98%E9%81%87%E4%B8%8A%E7%AE%80%E5%8E%86%E7%AD%9B%E9%80%89"><span class="toc-number">1.1.</span> <span class="toc-text">引言：当程序员遇上简历筛选</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-1-Prompt-%E7%9A%84%E9%87%8D%E8%A6%81%E6%80%A7%E4%B8%8E%E5%9F%BA%E6%9C%AC%E5%8E%9F%E5%88%99"><span class="toc-number">1.2.</span> <span class="toc-text">4.1 Prompt 的重要性与基本原则</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-2-Prompt-%E4%B8%8E-Message-%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.3.</span> <span class="toc-text">4.2 Prompt 与 Message 详解</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-2-1-Prompt-%E7%B1%BB%EF%BC%9A%E4%B8%8E-AI-%E5%AF%B9%E8%AF%9D%E7%9A%84%E6%A0%87%E5%87%86%E5%AE%B9%E5%99%A8"><span class="toc-number">1.3.1.</span> <span class="toc-text">4.2.1 Prompt 类：与 AI 对话的标准容器</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-2-2-Message-%E6%8E%A5%E5%8F%A3%EF%BC%9A%E5%AE%9A%E4%B9%89%E5%AF%B9%E8%AF%9D%E8%A7%92%E8%89%B2"><span class="toc-number">1.3.2.</span> <span class="toc-text">4.2.2 Message 接口：定义对话角色</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-3-PromptTemplate%EF%BC%9A%E8%AE%A9-Prompt-%E5%8A%A8%E8%B5%B7%E6%9D%A5"><span class="toc-number">1.4.</span> <span class="toc-text">4.3 PromptTemplate：让 Prompt 动起来</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-3-1-API-%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.4.1.</span> <span class="toc-text">4.3.1 API 详解</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-3-2-%E5%AE%9E%E6%88%98%EF%BC%9A%E5%8A%A8%E6%80%81%E7%BF%BB%E8%AF%91%E5%99%A8"><span class="toc-number">1.4.2.</span> <span class="toc-text">4.3.2 实战：动态翻译器</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#4-4-%E7%BB%93%E6%9E%84%E5%8C%96%E8%BE%93%E5%87%BA%EF%BC%9A%E5%B0%86-AI-%E5%93%8D%E5%BA%94%E6%98%A0%E5%B0%84%E4%B8%BA-POJO"><span class="toc-number">1.5.</span> <span class="toc-text">4.4 结构化输出：将 AI 响应映射为 POJO</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-1-%E7%83%A6%E6%81%BC%E4%B8%8E%E4%BC%98%E5%8A%BF%EF%BC%9A%E4%B8%BA%E4%BD%95%E9%9C%80%E8%A6%81%E7%BB%93%E6%9E%84%E5%8C%96%EF%BC%9F"><span class="toc-number">1.5.1.</span> <span class="toc-text">4.4.1 烦恼与优势：为何需要结构化？</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-2-API-%E8%AF%A6%E8%A7%A3%EF%BC%9AOutputConverter-%E5%AE%B6%E6%97%8F"><span class="toc-number">1.5.2.</span> <span class="toc-text">4.4.2 API 详解：OutputConverter 家族</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-3-BeanOutputConverter%E5%AE%9E%E6%88%98%EF%BC%9A%E4%BB%8E%E7%AE%80%E5%8E%86%E4%B8%AD%E6%8F%90%E5%8F%96%E7%BB%93%E6%9E%84%E5%8C%96%E6%95%B0%E6%8D%AE"><span class="toc-number">1.5.3.</span> <span class="toc-text">4.4.3 BeanOutputConverter实战：从简历中提取结构化数据</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-4-ListOutputConverter%E6%89%A9%E5%B1%95%EF%BC%9A%E5%A4%84%E7%90%86%E9%9B%86%E5%90%88%E7%B1%BB%E5%9E%8B"><span class="toc-number">1.5.4.</span> <span class="toc-text">4.4.4 ListOutputConverter扩展：处理集合类型</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%8E%A5%E5%8F%A3%E6%96%87%E6%A1%A3"><span class="toc-number">1.5.4.1.</span> <span class="toc-text">接口文档</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-5-%E6%8B%93%E5%B1%95%EF%BC%9AMapOutputConverter-%E8%BE%93%E5%87%BA%E5%8A%A8%E6%80%81%E9%94%AE%E5%80%BC%E5%AF%B9"><span class="toc-number">1.5.5.</span> <span class="toc-text">4.4.5 拓展：MapOutputConverter - 输出动态键值对</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-4-6-%E6%B7%B1%E5%BA%A6%E6%8F%AD%E7%A7%98%EF%BC%9AParameterizedTypeReference-%E7%9A%84%E2%80%9C%E9%AD%94%E6%B3%95%E2%80%9D"><span class="toc-number">1.5.6.</span> <span class="toc-text">4.4.6 深度揭秘：ParameterizedTypeReference 的“魔法”</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>