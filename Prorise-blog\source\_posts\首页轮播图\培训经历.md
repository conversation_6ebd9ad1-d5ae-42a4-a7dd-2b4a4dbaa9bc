---
title: 培训经历
categories:
  - 个人介绍
tags:
  - 个人
cover: 'https://bu.dusays.com/2025/07/27/6885a3049ae9a.jpg'
swiper_index: 3
description: 记录我的学习和培训经历，包括技术培训、认证考试和持续学习的过程
abbrlink: 3041
date: 2025-01-14 08:00:00
---
# 编程里程碑
{% p center logo large, 我的编程编年史 %}
{% p center small, A journey of a thousand miles begins with a single step. %}

{% note modern 'anzhiyufont anzhiyu-icon-python' %}
我的技术之旅，始于 Python 优雅而强大的逻辑世界。这份编年史详细记录了我如何从一名 Python 爱好者，逐步被前端的创造力所吸引，最终下定决心，将视野扩展至整个软件开发的星辰大海，构建起如今全面而深入的技术版图。
{% endnote %}

{% timeline 2022, blue, 奠基之年 · Python 与前端的交汇 %}

<!-- timeline 01-03 -->
**一至三月：启航 · Python 核心编程**
我编程生涯的第一站。系统性地学习了 Python 的基础语法、数据结构、函数式编程与面向对象思想，为其后的所有探索打下了坚实的逻辑基础。
{% folding blue, 核心技能 %}
{% kbd Python %} {% kbd OOP %} {% kbd 数据结构 %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 04-05 -->
**四至五月：实践 · Python Web 开发初探**
我开始用 Python 构建 Web 应用。通过学习 Flask 和 Django，我第一次将后端的逻辑服务于一个可交互的网页，感受到了创造的乐趣。
{% folding blue, 核心技能 %}
{% kbd Flask %} {% kbd Django %} {% kbd RESTful API %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 06 -->
**六月：应用 · Python 数据采集**
为了给我的应用提供数据，我学习并使用了 Scrapy 框架，掌握了网络爬虫的基本原理和实践，体会到自动化数据获取的强大。
{% folding blue, 核心技能 %}
{% kbd Scrapy %} {% kbd 网络爬虫 %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 07-08 -->
**七至八月：转向 · 拥抱前端世界**
在构建后端接口时，我对前端产生了浓厚的兴趣。我决定系统性地学习它，从最基础的 HTML5 和 CSS3 开始，并使用 Bootstrap 快速构建布局。
{% folding blue, 核心技能 %}
{% kbd HTML5 %} {% kbd CSS3 %} {% kbd Bootstrap %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 09-10 -->
**九至十月：核心 · JavaScript 深度学习**
我投入了大量精力来掌握这门前端的灵魂语言。从基础语法到 ES6+ 新特性，再到 DOM/BOM 操作，我为构建复杂的应用做好了准备。
{% folding blue, 核心技能 %}
{% kbd JavaScript %} {% kbd ES6+ %} {% kbd DOM/BOM %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 11-12 -->
**十一至十二月：现代 · Vue.js 与工程化**
我学习了第一个现代前端框架 Vue.js，并开始使用 Vite 和 Webpack 来构建项目。这是我从“写页面”到“做工程”的关键转变。
{% folding blue, 核心技能 %}
{% kbd Vue.js %} {% kbd Vite %} {% kbd Webpack %} {% kbd Node.js %}
{% endfolding %}
<!-- endtimeline -->

{% endtimeline %}


{% timeline 2023, green, 拓展之年 · 全栈能力构建 %}

<!-- timeline 01-03 -->
**一至三月：跨界 · 进军 Java & Spring 生态**
为了构建更稳定、更大型的系统，我决定学习企业级开发的首选——Java。我系统地学习了 Java SE，并迅速上手了强大的 Spring Boot 框架。
{% folding green, 核心技能 %}
{% kbd Java %} {% kbd Spring Boot %} {% kbd Spring MVC %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 04-05 -->
**四至五月：存储 · 数据库与持久化**
我深入学习了关系型数据库 MySQL 和 NoSQL 数据库 MongoDB，并掌握了 MyBatis 与 MyBatis-Plus，实现了 Java 应用与数据的高效交互。
{% folding green, 核心技能 %}
{% kbd MySQL %} {% kbd MongoDB %} {% kbd MyBatis-Plus %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 06-08 -->
**六至八月：双修 · React 与 TypeScript**
为了成为一名更全面的前端工程师，我系统学习了 React 生态，并开始全面拥抱 TypeScript，享受类型安全带来的开发优势。
{% folding green, 核心技能 %}
{% kbd React %} {% kbd TypeScript %} {% kbd Element Plus %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 09-10 -->
**九至十月：运维 · DevOps 基础实践**
我将视野延伸到部署和维护。学习了 Linux 基础操作、Docker 容器化以及 Nginx 的配置，并使用 Git 进行版本控制。
{% folding green, 核心技能 %}
{% kbd Linux %} {% kbd Docker %} {% kbd Nginx %} {% kbd Git %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 11-12 -->
**十一至十二月：精进 · CSS 工程化与 UI 框架**
我开始追求更高效、更优雅的 CSS 解决方案，深入学习了 Sass，并掌握了原子化 CSS 框架 TailwindCSS 及其组件库 DaisyUI。
{% folding green, 核心技能 %}
{% kbd Sass %} {% kbd TailwindCSS %} {% kbd DaisyUI %}
{% endfolding %}
<!-- endtimeline -->

{% endtimeline %}


{% timeline 2024, orange, 深化之年 · 跨平台与框架应用 %}

<!-- timeline 01-03 -->
**一至三月：跨平台 · 从桌面到移动端**
我开始探索超越浏览器的应用形态，使用 Electron 构建桌面应用，并利用 uniapp 开发可以多端发布的小程序。
{% folding orange, 核心技能 %}
{% kbd Electron %} {% kbd uniapp %} {% kbd 小程序开发 %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 04-06 -->
**四至六月：动画 · 视觉体验升级**
为了让应用更具吸引力，我深入研究了 Web 动画。从底层的 Canvas API 到强大的 GSAP 动画库，我掌握了创造流畅交互体验的能力。
{% folding orange, 核心技能 %}
{% kbd Canvas动画 %} {% kbd GSAP动画库 %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 07-09 -->
**七至九月：整合 · RuoYi 框架实战**
我通过对 RuoYi 框架的深度使用和二次开发，将之前学到的前后端、数据库、运维知识进行了全面的整合与实践，对企业级项目有了宏观的认识。
{% folding orange, 核心技能 %}
{% kbd RuoYi框架 %} {% kbd 全栈整合 %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 10-12 -->
**十至十二月：探索 · PHP 与 C++ 语言涉猎**
保持技术好奇心，我快速学习了 PHP 及其 ThinkPHP 框架，并回顾了 C/C++ 的底层知识，这加深了我对不同编程范式的理解。
{% folding orange, 核心技能 %}
{% kbd PHP %} {% kbd ThinkPHP %} {% kbd C/C++ %}
{% endfolding %}
<!-- endtimeline -->

{% endtimeline %}


{% timeline 2025, purple, 飞跃之年 · AI 与设计思维融合 %}

<!-- timeline 01-03 -->
**一至三月：AI 赋能 · 提示词工程实践**
我将 AI 全面融入我的工作流。系统学习了提示词工程，并将其应用于代码生成、文档撰写和问题排查，极大地提升了开发效率。
{% folding purple, 核心技能 %}
{% kbd 提示词工程 %} {% kbd AIGC 工具 %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 04-05 -->
**四至五月：产品视角 · UI/UE 设计学习**
为了做出更好的产品，我开始系统学习 UI/UE 设计原则。通过对 Figma、Adobe XD 等工具的学习，我能更好地理解设计师的意图并提出建设性意见。
{% folding purple, 核心技能 %}
{% kbd UI设计 %} {% kbd UE设计 %} {% kbd Figma %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 06 -->
**六月：AI开发 · SpringAI 应用**
我接到了需要在Spring框架上搭载AI功能的需求，所以系统学习并实践了 SpringAi，将其运用到了项目中
{% folding purple, 核心技能 %}
{% kbd embedding %} {% kbd rag %}
{% endfolding %}
<!-- endtimeline -->

<!-- timeline 06 -->
**七月：产品经理 · 产品从0到1的落地应用**
为了开拓视界，以及了解到产品经理在企业中的核心作用，我深度的学习了产出PRD，使用墨刀白板产出低保真原型图，打通了产品开发的第一条链路
{% folding purple, 核心技能 %}
{% kbd PRD文档产出 %} {% kbd B端/C端深度解析 %}
{% endfolding %}
<!-- endtimeline -->



{% endtimeline %}