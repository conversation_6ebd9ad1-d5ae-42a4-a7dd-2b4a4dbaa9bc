<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>Python（二十二）：第二十一章：项目结构规范与最佳实践 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="Python基础知识总汇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="Python（二十二）：第二十一章：项目结构规范与最佳实践"><meta name="application-name" content="Python（二十二）：第二十一章：项目结构规范与最佳实践"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="Python（二十二）：第二十一章：项目结构规范与最佳实践"><meta property="og:url" content="https://prorise666.site/posts/43091.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="第二十一章：项目结构规范与最佳实践一个清晰、一致的项目结构和高质量的代码是任何成功软件项目的基石。良好的项目组织不仅能让其他开发者（以及未来的你）更容易理解和维护代码，还能简化构建、测试、部署和分发流程。同样，遵循统一的代码风格和最佳实践能够显著提高代码的可读性、减少错误，并促进团队协作。 本章将探"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta name="description" content="第二十一章：项目结构规范与最佳实践一个清晰、一致的项目结构和高质量的代码是任何成功软件项目的基石。良好的项目组织不仅能让其他开发者（以及未来的你）更容易理解和维护代码，还能简化构建、测试、部署和分发流程。同样，遵循统一的代码风格和最佳实践能够显著提高代码的可读性、减少错误，并促进团队协作。 本章将探"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/43091.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"Python（二十二）：第二十一章：项目结构规范与最佳实践",postAI:"true",pageFillDescription:"第二十一章：项目结构规范与最佳实践, 一、推荐的 Python 项目结构, 1. 目录树概览, 2. 各部分详解, a. my_project/ (项目根目录), b. my_package/ (主应用包), c. tests/ (测试目录), d. docs/ (文档目录), e. examples/ (示例代码目录), f. .gitignore, g. LICENSE, h. README.md, i. requirements.txt, j. setup.py (或 pyproject.toml + setup.cfg), 二、代码质量与风格指南 (PEP 8 核心实践), 三、总结与工具第二十一章项目结构规范与最佳实践一个清晰一致的项目结构和高质量的代码是任何成功软件项目的基石良好的项目组织不仅能让其他开发者以及未来的你更容易理解和维护代码还能简化构建测试部署和分发流程同样遵循统一的代码风格和最佳实践能够显著提高代码的可读性减少错误并促进团队协作本章将探讨项目的推荐结构并总结一些核心的代码质量与风格指南主要基于一推荐的项目结构一个组织良好的项目通常包含应用代码测试文档依赖管理和打包配置等部分下面是一个推荐的通用项目结构示例目录树概览项目根目录例如仓库的根主要的包你的应用或库的核心代码将标记为一个包可包含包级别初始化代码包内的模块文件另一个模块文件包内可以有子包使成为一个子包子包内的模块常用的工具或辅助函数可以组织在子包中辅助函数模块存放所有测试代码的目录使成为一个包有些测试运行器需要针对的测试针对子包模块的测试项目文档例如使用或生成配置文件如果使用主文档文件或文档等如何使用你的包或库的示例代码指定应忽略的文件和目录项目的许可证文件例如项目的详细说明安装指南使用方法等格式列出项目运行所需的基本依赖包及其版本传统方式项目的构建脚本用于打包分发现代方式项目元数据和构建系统配置推荐与结合或替代可选配置文件用于自动化测试不同环境各部分详解项目根目录这是你项目的最顶层目录通常对应你的版本控制系统如的仓库根目录主应用包这是存放你项目核心代码的地方它是一个包意味着它包含一个文件这个文件可以为空仅用于将目录标记为一个包使得其中的模块可以使用点分路径导入例如它也可以包含包级别的初始化代码定义变量来控制的行为或者直接从子模块中导出常用的类和函数以提供更简洁的包示例仅为演示打印实际通常不直接打印包正在被初始化定义包版本从子模块导出方便外部调用控制的行为一个包级别的辅助函数示例例如这些是包内的实际模块文件包含相关的函数类和变量大型包可以进一步组织为子包每个子包同样包含一个文件例如通常用于存放项目中可复用的辅助函数工具类等测试目录存放单元测试集成测试等所有测试代码测试文件名通常以开头或者测试类以开头以便测试运行器如能够自动发现和执行文档目录存放项目的用户文档文档等常用的工具有配合或或纯示例代码目录提供如何使用你的库或应用程序的简单可运行的示例代码告知哪些文件或目录不应被跟踪和提交例如虚拟环境目录配置文件等包含项目的开源许可证文本例如等明确许可证对于代码的分享和使用非常重要项目的入口点和门面通常包含项目名称和简短描述安装说明基本用法示例如何运行测试如何贡献许可证信息链接列出项目运行所必需的第三方依赖包及其版本通常由生成或者手动维护更现代的项目可能会使用中的示例或用于构建打包和分发项目的脚本传统示例传统方式仅为演示通常不应有副作用打印执行避免在导入时执行打印读取作为长描述未找到长描述将为空示例依赖具体根据项目需求包的唯一名称上当前版本您的名字这是一个关于我的超级包的简短描述如果是项目主页会自动查找所有包含的目录作为包参数可以排除特定目录如指定包内需要包含的非代码文件例如模板数据文件如果使用项目兼容的最低版本项目的核心运行时依赖可选用于开发和测试的额外依赖如果你的包提供了命令行工具分类器帮助用户发现你的包搜索关键词配置完成现代和推荐使用文件来声明构建依赖和项目元数据仍然可以作为构建后端示例可选如果在子目录这是一个关于我的超级包的简短描述您的名字您的名字其他分类器当使用时可以非常简单甚至在某些情况下不需要如果所有元数据都在且构建后端支持二代码质量与风格指南核心实践是官方的代码风格指南遵循它可以使代码更易读更易于维护并促进团队协作的一致性除了还有一些通用的最佳实践下面是一个演示这些规范的示例脚本代码质量与实践演示标准库导入标准库导入标准库导入类型注解第三方库导入示例本地应用库导入示例模块级别文档字符串与常量代码质量与核心实践演示本模块旨在演示风格指南中的常见规则和一些编码最佳实践官方文档常量名应全部大写单词间用下划线分隔类定义与文档字符串类定义与文档字符串一个示例类用于演示规范和良好实践这个类的主要目的是展示命名约定文档字符串格式方法定义以及属性的组织方式对象的名称一个可选的整数值一个受保护的成员变量一个私有的成员变量初始化对象对象的名称一个可选的整数值默认为受保护成员以下划线开头私有成员以双下划线开头会被名称修饰显示对象的信息如果参数列表过长可以像下面这样换行和缩进信息输出的前缀默认为格式化后的信息字符串行长度通常建议不超过个字符或团队约定的长度如长字符串或表达式可以分行通常在操作符之后换行并适当缩进访问被名称修饰的私有变量在二元运算符之前或之后换行都是允许的但团队内应保持一致一个只读属性返回大写的名称函数定义与文档字符串函数定义与文档字符串计算一个数字列表的总和与平均值需要计算的浮点数列表一个包含总和与平均值的元组如果输入列表为空如果列表中包含非数字类型的元素输入列表不能为空以计算平均值操作符两侧通常有空格捕获特定异常列表中的所有元素都必须是数字正确的空格使用示例运算符两侧逗号后括号内侧通常无空格除非是元组只有一个元素索引切片操作符内侧无空格冒号后有空格前面无花括号内侧通常无空格条件语句与表达条件语句与表达演示条件语句的推荐写法检查使用或项目为检查布尔真值直接使用或而不是或适用于空列表空字符串空字典等项目为空或具有布尔假值复杂条件表达式如果过长可以分行并用括号括起来此处的仅为示例位运算示例项目是一个长度大于的活动字符串且不紧急项目具有其他状态推导式与生成器表达式推导式与生成器表达式演示列表推导式和生成器表达式列表推导式生成器表达式更节省内存按需生成生成器表达式在中使用字典推导式避免过于复杂的嵌套推导式如果可读性受损应拆分为普通循环尚可接受异常处理异常处理一个演示安全操作的函数包含完整的异常处理假设这是一个可能抛出多种异常的操作输入值必须是数字类型除数不能为零自定义而非直接捕获特定的预期的异常操作失败实际项目中用日志操作失败捕获其他所有子类的异常作为最后防线发生未知错误如果块中没有发生任何异常则执行块操作成功完成结果无论块中是否发生异常块总会执行通常用于资源清理操作尝试已结束块执行上下文管理器语句上下文管理器语句使用上下文管理器安全地读取文件内容语句确保文件在操作完成后即使发生异常也会被正确关闭成功读取文件文件未找到读取文件时发生错误主程序块用于调用演示函数开始演示开始演示对于列表总和平均值捕获到预期错误开始演示开始演示开始演示开始演示创建一个临时文件用于读取演示文件内容前字符测试文件不存在的情况清理临时文件已删除临时读取文件代码质量与实践演示结束三总结与工具遵循良好的项目结构和编码规范是开发高质量可维护应用的关键项目结构清晰的目录划分有助于分离关注点使得代码测试文档和配置易于查找和管理代码风格一致的编码风格提高了代码的可读性是团队协作的基础文档与注释良好的文档字符串和必要的注释使代码更易于理解和使用代码利用语言的特性如推导式上下文管理器可以写出更简洁更高效更易读的代码辅助工具为了帮助开发者遵循最佳实践并保持代码质量社区提供了许多优秀的工具代码风格和错误检查结合了错误检查风格检查和复杂度检查功能更全面提供更广泛的代码分析错误检测风格检查和重构建议一个用编写的极快的和可以替代等多种工具代码自动格式化一个固执己见的代码格式化工具能自动将代码格式化为符合子集的一致风格自动将代码格式化为符合风格也包含了格式化功能静态类型检查官方的静态类型检查器由微软开发快速且功能强大的类型检查器也是中插件的核心开发的类型检查器可以推断类型将这些工具集成到你的开发流程和持续集成系统中可以有效地提升团队的整体代码质量和开发效率",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-13 22:13:01",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E5%8D%81%E4%B8%80%E7%AB%A0%EF%BC%9A%E9%A1%B9%E7%9B%AE%E7%BB%93%E6%9E%84%E8%A7%84%E8%8C%83%E4%B8%8E%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5"><span class="toc-text">第二十一章：项目结构规范与最佳实践</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%B8%80%E3%80%81%E6%8E%A8%E8%8D%90%E7%9A%84-Python-%E9%A1%B9%E7%9B%AE%E7%BB%93%E6%9E%84"><span class="toc-text">一、推荐的 Python 项目结构</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%9B%AE%E5%BD%95%E6%A0%91%E6%A6%82%E8%A7%88"><span class="toc-text">1. 目录树概览</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%90%84%E9%83%A8%E5%88%86%E8%AF%A6%E8%A7%A3"><span class="toc-text">2. 各部分详解</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-my-project-%E9%A1%B9%E7%9B%AE%E6%A0%B9%E7%9B%AE%E5%BD%95"><span class="toc-text">a. my_project/ (项目根目录)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-my-package-%E4%B8%BB%E5%BA%94%E7%94%A8%E5%8C%85"><span class="toc-text">b. my_package/ (主应用包)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#c-tests-%E6%B5%8B%E8%AF%95%E7%9B%AE%E5%BD%95"><span class="toc-text">c. tests/ (测试目录)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#d-docs-%E6%96%87%E6%A1%A3%E7%9B%AE%E5%BD%95"><span class="toc-text">d. docs/ (文档目录)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#e-examples-%E7%A4%BA%E4%BE%8B%E4%BB%A3%E7%A0%81%E7%9B%AE%E5%BD%95"><span class="toc-text">e. examples/ (示例代码目录)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#f-gitignore"><span class="toc-text">f. .gitignore</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#g-LICENSE"><span class="toc-text">g. LICENSE</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#h-README-md"><span class="toc-text">h. README.md</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#i-requirements-txt"><span class="toc-text">i. requirements.txt</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#j-setup-py-%E6%88%96-pyproject-toml-setup-cfg"><span class="toc-text">j. setup.py (或 pyproject.toml + setup.cfg)</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%BA%8C%E3%80%81%E4%BB%A3%E7%A0%81%E8%B4%A8%E9%87%8F%E4%B8%8E%E9%A3%8E%E6%A0%BC%E6%8C%87%E5%8D%97-PEP-8-%E6%A0%B8%E5%BF%83%E5%AE%9E%E8%B7%B5"><span class="toc-text">二、代码质量与风格指南 (PEP 8 核心实践)</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%B8%89%E3%80%81%E6%80%BB%E7%BB%93%E4%B8%8E%E5%B7%A5%E5%85%B7"><span class="toc-text">三、总结与工具</span></a></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Python基础知识总汇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">Python（二十二）：第二十一章：项目结构规范与最佳实践</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-04-19T05:13:45.000Z" title="发表于 2025-04-19 13:13:45">2025-04-19</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.519Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">5.1k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>21分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="Python（二十二）：第二十一章：项目结构规范与最佳实践"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/43091.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/43091.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url">Python基础知识总汇</a><h1 id="CrawlerTitle" itemprop="name headline">Python（二十二）：第二十一章：项目结构规范与最佳实践</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-04-19T05:13:45.000Z" title="发表于 2025-04-19 13:13:45">2025-04-19</time><time itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.519Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></header><div id="postchat_postcontent"><h2 id="第二十一章：项目结构规范与最佳实践"><a href="#第二十一章：项目结构规范与最佳实践" class="headerlink" title="第二十一章：项目结构规范与最佳实践"></a>第二十一章：项目结构规范与最佳实践</h2><p>一个清晰、一致的项目结构和高质量的代码是任何成功软件项目的基石。良好的项目组织不仅能让其他开发者（以及未来的你）更容易理解和维护代码，还能简化构建、测试、部署和分发流程。同样，遵循统一的代码风格和最佳实践能够显著提高代码的可读性、减少错误，并促进团队协作。</p><p>本章将探讨 Python 项目的推荐结构，并总结一些核心的代码质量与风格指南，主要基于 PEP 8。</p><h3 id="一、推荐的-Python-项目结构"><a href="#一、推荐的-Python-项目结构" class="headerlink" title="一、推荐的 Python 项目结构"></a>一、推荐的 Python 项目结构</h3><p>一个组织良好的 Python 项目通常包含应用代码、测试、文档、依赖管理和打包配置等部分。下面是一个推荐的通用项目结构示例：</p><h4 id="1-目录树概览"><a href="#1-目录树概览" class="headerlink" title="1. 目录树概览"></a>1. 目录树概览</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br></pre></td><td class="code"><pre><span class="line">my_project/                  <span class="comment"># 项目根目录 (例如，Git 仓库的根)</span></span><br><span class="line">│</span><br><span class="line">├── my_package/              <span class="comment"># 主要的 Python 包 (你的应用或库的核心代码)</span></span><br><span class="line">│   ├── __init__.py          <span class="comment"># 将 'my_package' 标记为一个 Python 包，可包含包级别初始化代码</span></span><br><span class="line">│   ├── module1.py           <span class="comment"># 包内的模块文件</span></span><br><span class="line">│   ├── module2.py           <span class="comment"># 另一个模块文件</span></span><br><span class="line">│   ├── subpackage/          <span class="comment"># 包内可以有子包</span></span><br><span class="line">│   │   ├── __init__.py      <span class="comment"># 使 'subpackage' 成为一个子包</span></span><br><span class="line">│   │   └── submodule.py     <span class="comment"># 子包内的模块</span></span><br><span class="line">│   └── utils/               <span class="comment"># 常用的工具或辅助函数可以组织在子包中</span></span><br><span class="line">│       ├── __init__.py</span><br><span class="line">│       └── helpers.py       <span class="comment"># 辅助函数模块</span></span><br><span class="line">│</span><br><span class="line">├── tests/                   <span class="comment"># 存放所有测试代码的目录</span></span><br><span class="line">│   ├── __init__.py          <span class="comment"># 使 'tests' 成为一个包 (有些测试运行器需要)</span></span><br><span class="line">│   ├── test_module1.py      <span class="comment"># 针对 my_package.module1 的测试</span></span><br><span class="line">│   └── test_subpackage_submodule.py <span class="comment"># 针对子包模块的测试</span></span><br><span class="line">│</span><br><span class="line">├── docs/                    <span class="comment"># 项目文档 (例如使用 Sphinx 或 MkDocs 生成)</span></span><br><span class="line">│   ├── conf.py              <span class="comment"># Sphinx 配置文件 (如果使用 Sphinx)</span></span><br><span class="line">│   ├── index.rst            <span class="comment"># Sphinx 主文档文件 (或 index.md)</span></span><br><span class="line">│   └── api.rst              <span class="comment"># API 文档等</span></span><br><span class="line">│</span><br><span class="line">├── examples/                <span class="comment"># 如何使用你的包或库的示例代码</span></span><br><span class="line">│   └── usage_example.py</span><br><span class="line">│</span><br><span class="line">├── .gitignore               <span class="comment"># 指定 Git 应忽略的文件和目录</span></span><br><span class="line">├── LICENSE                  <span class="comment"># 项目的许可证文件 (例如 MIT, Apache 2.0)</span></span><br><span class="line">├── README.md                <span class="comment"># 项目的详细说明、安装指南、使用方法等 (Markdown 格式)</span></span><br><span class="line">├── requirements.txt         <span class="comment"># 列出项目运行所需的基本依赖包及其版本</span></span><br><span class="line">├── setup.py                 <span class="comment"># (传统方式) Python 项目的构建脚本 (用于打包、分发)</span></span><br><span class="line">├── pyproject.toml           <span class="comment"># (现代方式) 项目元数据和构建系统配置 (推荐与 setup.py/setup.cfg 结合或替代)</span></span><br><span class="line">└── tox.ini                  <span class="comment"># (可选) tox 配置文件，用于自动化测试不同 Python 环境</span></span><br></pre></td></tr></tbody></table></figure><h4 id="2-各部分详解"><a href="#2-各部分详解" class="headerlink" title="2. 各部分详解"></a>2. 各部分详解</h4><h5 id="a-my-project-项目根目录"><a href="#a-my-project-项目根目录" class="headerlink" title="a. my_project/ (项目根目录)"></a>a. <code>my_project/</code> (项目根目录)</h5><p>这是你项目的最顶层目录，通常对应你的版本控制系统（如 Git）的仓库根目录。</p><h5 id="b-my-package-主应用包"><a href="#b-my-package-主应用包" class="headerlink" title="b. my_package/ (主应用包)"></a>b. <code>my_package/</code> (主应用包)</h5><p>这是存放你项目核心 Python 代码的地方。它是一个 Python 包，意味着它包含一个 <code>__init__.py</code> 文件。</p><ul><li><p><strong><code>__init__.py</code></strong>:<br>这个文件可以为空，仅用于将目录标记为一个 Python 包，使得其中的模块可以使用点分路径导入 (例如 <code>from my_package import module1</code>)。<br>它也可以包含包级别的初始化代码、定义 <code>__all__</code> 变量来控制 <code>from my_package import *</code> 的行为，或者直接从子模块中导出常用的类和函数，以提供更简洁的包 API。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === my_package/__init__.py 示例 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> print_info <span class="comment"># 仅为演示打印，实际 __init__.py 通常不直接打印</span></span><br><span class="line"></span><br><span class="line">print_info(<span class="string">"包 'my_package' 正在被初始化..."</span>)</span><br><span class="line"></span><br><span class="line">__version__: <span class="built_in">str</span> = <span class="string">"0.1.0"</span> <span class="comment"># 定义包版本</span></span><br><span class="line">APP_NAME: <span class="built_in">str</span> = <span class="string">"My Awesome Application"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 从子模块导出，方便外部调用</span></span><br><span class="line"><span class="comment"># from .module1 import important_function</span></span><br><span class="line"><span class="comment"># from .subpackage.submodule import UsefulClass</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># __all__ 控制 from my_package import * 的行为</span></span><br><span class="line"><span class="comment"># __all__ = ["important_function", "UsefulClass", "APP_NAME"]</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">package_level_utility</span>() -&gt; <span class="built_in">str</span>:</span><br><span class="line">    <span class="string">"""一个包级别的辅助函数示例。"""</span></span><br><span class="line">    <span class="keyword">return</span> <span class="string">f"Utility from <span class="subst">{APP_NAME}</span> v<span class="subst">{__version__}</span>"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># print_info(f"  {package_level_utility()}")</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong><code>moduleX.py</code></strong> (例如 <code>module1.py</code>, <code>module2.py</code>):<br>这些是包内的实际模块文件，包含相关的函数、类和变量。</p></li><li><p><strong><code>subpackage/</code></strong>:<br>大型包可以进一步组织为子包，每个子包同样包含一个 <code>__init__.py</code> 文件。</p></li><li><p><strong><code>utils/</code></strong> (例如 <code>helpers.py</code>):<br>通常用于存放项目中可复用的辅助函数、工具类等。</p></li></ul><h5 id="c-tests-测试目录"><a href="#c-tests-测试目录" class="headerlink" title="c. tests/ (测试目录)"></a>c. <code>tests/</code> (测试目录)</h5><p>存放单元测试、集成测试等所有测试代码。测试文件名通常以 <code>test_</code> 开头，或者测试类以 <code>Test</code> 开头，以便测试运行器（如 <code>pytest</code>, <code>unittest</code>) 能够自动发现和执行。</p><h5 id="d-docs-文档目录"><a href="#d-docs-文档目录" class="headerlink" title="d. docs/ (文档目录)"></a>d. <code>docs/</code> (文档目录)</h5><p>存放项目的用户文档、API 文档等。常用的工具有 Sphinx (配合 reStructuredText 或 MyST Markdown) 或 MkDocs (纯 Markdown)。</p><h5 id="e-examples-示例代码目录"><a href="#e-examples-示例代码目录" class="headerlink" title="e. examples/ (示例代码目录)"></a>e. <code>examples/</code> (示例代码目录)</h5><p>提供如何使用你的库或应用程序的简单、可运行的示例代码。</p><h5 id="f-gitignore"><a href="#f-gitignore" class="headerlink" title="f. .gitignore"></a>f. <code>.gitignore</code></h5><p>告知 Git哪些文件或目录不应被跟踪和提交 (例如 <code>__pycache__/</code>, <code>*.pyc</code>, 虚拟环境目录, IDE 配置文件等)。</p><h5 id="g-LICENSE"><a href="#g-LICENSE" class="headerlink" title="g. LICENSE"></a>g. <code>LICENSE</code></h5><p>包含项目的开源许可证文本，例如 MIT, Apache 2.0, GPL 等。明确许可证对于代码的分享和使用非常重要。</p><h5 id="h-README-md"><a href="#h-README-md" class="headerlink" title="h. README.md"></a>h. <code>README.md</code></h5><p>项目的入口点和门面。通常包含：</p><ul><li>项目名称和简短描述。</li><li>安装说明。</li><li>基本用法示例。</li><li>如何运行测试。</li><li>如何贡献。</li><li>许可证信息链接。</li></ul><h5 id="i-requirements-txt"><a href="#i-requirements-txt" class="headerlink" title="i. requirements.txt"></a>i. <code>requirements.txt</code></h5><p>列出项目运行所必需的第三方依赖包及其版本。通常由 <code>pip freeze &gt; requirements.txt</code> 生成，或者手动维护。更现代的项目可能会使用 <code>pyproject.toml</code> 中的 <code>[project.dependencies]</code>。</p><figure class="highlight text"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"># === requirements.txt 示例 ===</span><br><span class="line"># requests==2.28.1</span><br><span class="line"># numpy&gt;=1.20.0,&lt;2.0.0</span><br><span class="line"># pandas</span><br><span class="line"># loguru</span><br></pre></td></tr></tbody></table></figure><h5 id="j-setup-py-或-pyproject-toml-setup-cfg"><a href="#j-setup-py-或-pyproject-toml-setup-cfg" class="headerlink" title="j. setup.py (或 pyproject.toml + setup.cfg)"></a>j. <code>setup.py</code> (或 <code>pyproject.toml</code> + <code>setup.cfg</code>)</h5><p>用于构建、打包和分发 Python 项目的脚本。</p><ul><li><p><strong>传统 <code>setup.py</code></strong>:</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === setup.py 示例 (传统方式) ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> print_info <span class="comment"># 仅为演示，setup.py 通常不应有副作用打印</span></span><br><span class="line"><span class="keyword">from</span> setuptools <span class="keyword">import</span> setup, find_packages</span><br><span class="line"><span class="keyword">from</span> typing <span class="keyword">import</span> <span class="type">List</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># print_header("执行 setup.py") # 避免在导入时执行打印</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">get_long_description</span>() -&gt; <span class="built_in">str</span>:</span><br><span class="line">    <span class="string">"""读取 README.md 作为长描述。"""</span></span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">"README.md"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">            <span class="keyword">return</span> f.read()</span><br><span class="line">    <span class="keyword">except</span> FileNotFoundError:</span><br><span class="line">        <span class="comment"># print_warning("README.md 未找到，长描述将为空。")</span></span><br><span class="line">        <span class="keyword">return</span> <span class="string">"A fantastic Python package."</span></span><br><span class="line">    </span><br><span class="line">    </span><br><span class="line">    REQUIRED_PACKAGES: <span class="type">List</span>[<span class="built_in">str</span>] = [</span><br><span class="line">    <span class="string">"requests&gt;=2.25.1"</span>, <span class="comment"># 示例依赖，具体根据项目需求</span></span><br><span class="line">    <span class="string">"loguru~=0.7.0"</span></span><br><span class="line">    <span class="comment"># "pandas&gt;=1.3.0,&lt;2.0.0",</span></span><br><span class="line">]</span><br><span class="line"></span><br><span class="line">setup(</span><br><span class="line">    name=<span class="string">"my_super_package"</span>, <span class="comment"># 包的唯一名称 (PyPI 上)</span></span><br><span class="line">    version=<span class="string">"0.1.0"</span>,       <span class="comment"># 当前版本</span></span><br><span class="line">    author=<span class="string">"您的名字"</span>,</span><br><span class="line">    author_email=<span class="string">"<EMAIL>"</span>,</span><br><span class="line">    description=<span class="string">"这是一个关于我的超级包的简短描述。"</span>,</span><br><span class="line">    long_description=get_long_description(),</span><br><span class="line">    long_description_content_type=<span class="string">"text/markdown"</span>, <span class="comment"># 如果 long_description 是 Markdown</span></span><br><span class="line">    url=<span class="string">"https://github.com/yourusername/my_super_package"</span>, <span class="comment"># 项目主页</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># find_packages() 会自动查找所有包含 __init__.py 的目录作为包</span></span><br><span class="line">    <span class="comment"># exclude 参数可以排除特定目录 (如 tests)</span></span><br><span class="line">    packages=find_packages(exclude=(<span class="string">"tests*"</span>, <span class="string">"docs*"</span>, <span class="string">"examples*"</span>)),</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 指定包内需要包含的非代码文件 (例如模板、数据文件)</span></span><br><span class="line">    <span class="comment"># package_data={</span></span><br><span class="line">    <span class="comment">#     'my_package': ['data/*.json', 'templates/*.html'],</span></span><br><span class="line">    <span class="comment"># },</span></span><br><span class="line">    <span class="comment"># include_package_data=True, # 如果使用 MANIFEST.in</span></span><br><span class="line">    </span><br><span class="line">    python_requires=<span class="string">"&gt;=3.8"</span>, <span class="comment"># 项目兼容的 Python 最低版本</span></span><br><span class="line">    </span><br><span class="line">    install_requires=REQUIRED_PACKAGES, <span class="comment"># 项目的核心运行时依赖</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 可选：用于开发和测试的额外依赖</span></span><br><span class="line">    <span class="comment"># extras_require={</span></span><br><span class="line">    <span class="comment">#     "dev": ["pytest&gt;=6.0", "mypy&gt;=0.900", "flake8", "black"],</span></span><br><span class="line">    <span class="comment">#     "docs": ["sphinx", "sphinx-rtd-theme"],</span></span><br><span class="line">    <span class="comment"># },</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 如果你的包提供了命令行工具</span></span><br><span class="line">    <span class="comment"># entry_points={</span></span><br><span class="line">    <span class="comment">#     "console_scripts": [</span></span><br><span class="line">    <span class="comment">#         "my-tool=my_package.cli:main_cli_function",</span></span><br><span class="line">    <span class="comment">#     ],</span></span><br><span class="line">    <span class="comment"># },</span></span><br><span class="line">    </span><br><span class="line">    classifiers=[ <span class="comment"># PyPI 分类器，帮助用户发现你的包</span></span><br><span class="line">        <span class="string">"Development Status :: 3 - Alpha"</span>,</span><br><span class="line">        <span class="string">"Intended Audience :: Developers"</span>,</span><br><span class="line">        <span class="string">"License :: OSI Approved :: MIT License"</span>,</span><br><span class="line">        <span class="string">"Programming Language :: Python :: 3"</span>,</span><br><span class="line">        <span class="string">"Programming Language :: Python :: 3.8"</span>,</span><br><span class="line">        <span class="string">"Programming Language :: Python :: 3.9"</span>,</span><br><span class="line">        <span class="string">"Programming Language :: Python :: 3.10"</span>,</span><br><span class="line">        <span class="string">"Programming Language :: Python :: 3.11"</span>,</span><br><span class="line">        <span class="string">"Programming Language :: Python :: 3.12"</span>,</span><br><span class="line">        <span class="string">"Operating System :: OS Independent"</span>,</span><br><span class="line">        <span class="string">"Topic :: Software Development :: Libraries :: Python Modules"</span>,</span><br><span class="line">    ],</span><br><span class="line">    keywords=<span class="string">"python package example utility"</span>, <span class="comment"># 搜索关键词</span></span><br><span class="line">)</span><br><span class="line"><span class="comment"># print_success("setup.py 配置完成。")</span></span><br><span class="line">​```</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>现代 <code>pyproject.toml</code></strong>:<br>PEP 517 和 PEP 518 推荐使用 <code>pyproject.toml</code> 文件来声明构建依赖和项目元数据。<code>setuptools</code> 仍然可以作为构建后端。</p><figure class="highlight toml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === pyproject.toml 示例 ===</span></span><br><span class="line"><span class="comment"># [build-system]</span></span><br><span class="line"><span class="comment"># requires = ["setuptools&gt;=61.0", "wheel"]</span></span><br><span class="line"><span class="comment"># build-backend = "setuptools.build_meta"</span></span><br><span class="line"><span class="comment"># backend-path = ["."] # 可选，如果 setup.py 在子目录</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># [project]</span></span><br><span class="line"><span class="comment"># name = "my_super_package"</span></span><br><span class="line"><span class="comment"># version = "0.1.0"</span></span><br><span class="line"><span class="comment"># description = "这是一个关于我的超级包的简短描述。"</span></span><br><span class="line"><span class="comment"># readme = "README.md"</span></span><br><span class="line"><span class="comment"># requires-python = "&gt;=3.8"</span></span><br><span class="line"><span class="comment"># license = {file = "LICENSE"}</span></span><br><span class="line"><span class="comment"># keywords = ["python", "package", "example"]</span></span><br><span class="line"><span class="comment"># authors = [</span></span><br><span class="line"><span class="comment">#   {name = "您的名字", email = "<EMAIL>" }</span></span><br><span class="line"><span class="comment"># ]</span></span><br><span class="line"><span class="comment"># maintainers = [</span></span><br><span class="line"><span class="comment">#   {name = "您的名字", email = "<EMAIL>" }</span></span><br><span class="line"><span class="comment"># ]</span></span><br><span class="line"><span class="comment"># classifiers = [</span></span><br><span class="line"><span class="comment">#     "Development Status :: 3 - Alpha",</span></span><br><span class="line"><span class="comment">#     "Programming Language :: Python :: 3",</span></span><br><span class="line"><span class="comment">#     # ... 其他分类器</span></span><br><span class="line"><span class="comment"># ]</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># dependencies = [</span></span><br><span class="line"><span class="comment">#   "requests&gt;=2.25.1",</span></span><br><span class="line"><span class="comment">#   "loguru~=0.7.0",</span></span><br><span class="line"><span class="comment"># ]</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># [project.urls]</span></span><br><span class="line"><span class="comment"># "Homepage" = "https://github.com/yourusername/my_super_package"</span></span><br><span class="line"><span class="comment"># "Bug Tracker" = "https://github.com/yourusername/my_super_package/issues"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># [project.scripts]</span></span><br><span class="line"><span class="comment"># my-tool = "my_package.cli:main_cli_function"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># [project.optional-dependencies]</span></span><br><span class="line"><span class="comment"># dev = [</span></span><br><span class="line"><span class="comment">#   "pytest&gt;=6.0",</span></span><br><span class="line"><span class="comment">#   "mypy",</span></span><br><span class="line"><span class="comment"># ]</span></span><br></pre></td></tr></tbody></table></figure><p>当使用 <code>pyproject.toml</code> 时，<code>setup.py</code> 可以非常简单，甚至在某些情况下不需要（如果所有元数据都在 <code>pyproject.toml</code> 且构建后端支持）。</p></li></ul><h3 id="二、代码质量与风格指南-PEP-8-核心实践"><a href="#二、代码质量与风格指南-PEP-8-核心实践" class="headerlink" title="二、代码质量与风格指南 (PEP 8 核心实践)"></a>二、代码质量与风格指南 (PEP 8 核心实践)</h3><p>PEP 8 是 Python 官方的代码风格指南，遵循它可以使代码更易读、更易于维护，并促进团队协作的一致性。除了 PEP 8，还有一些通用的最佳实践。</p><p>下面是一个演示这些规范的示例脚本</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br><span class="line">142</span><br><span class="line">143</span><br><span class="line">144</span><br><span class="line">145</span><br><span class="line">146</span><br><span class="line">147</span><br><span class="line">148</span><br><span class="line">149</span><br><span class="line">150</span><br><span class="line">151</span><br><span class="line">152</span><br><span class="line">153</span><br><span class="line">154</span><br><span class="line">155</span><br><span class="line">156</span><br><span class="line">157</span><br><span class="line">158</span><br><span class="line">159</span><br><span class="line">160</span><br><span class="line">161</span><br><span class="line">162</span><br><span class="line">163</span><br><span class="line">164</span><br><span class="line">165</span><br><span class="line">166</span><br><span class="line">167</span><br><span class="line">168</span><br><span class="line">169</span><br><span class="line">170</span><br><span class="line">171</span><br><span class="line">172</span><br><span class="line">173</span><br><span class="line">174</span><br><span class="line">175</span><br><span class="line">176</span><br><span class="line">177</span><br><span class="line">178</span><br><span class="line">179</span><br><span class="line">180</span><br><span class="line">181</span><br><span class="line">182</span><br><span class="line">183</span><br><span class="line">184</span><br><span class="line">185</span><br><span class="line">186</span><br><span class="line">187</span><br><span class="line">188</span><br><span class="line">189</span><br><span class="line">190</span><br><span class="line">191</span><br><span class="line">192</span><br><span class="line">193</span><br><span class="line">194</span><br><span class="line">195</span><br><span class="line">196</span><br><span class="line">197</span><br><span class="line">198</span><br><span class="line">199</span><br><span class="line">200</span><br><span class="line">201</span><br><span class="line">202</span><br><span class="line">203</span><br><span class="line">204</span><br><span class="line">205</span><br><span class="line">206</span><br><span class="line">207</span><br><span class="line">208</span><br><span class="line">209</span><br><span class="line">210</span><br><span class="line">211</span><br><span class="line">212</span><br><span class="line">213</span><br><span class="line">214</span><br><span class="line">215</span><br><span class="line">216</span><br><span class="line">217</span><br><span class="line">218</span><br><span class="line">219</span><br><span class="line">220</span><br><span class="line">221</span><br><span class="line">222</span><br><span class="line">223</span><br><span class="line">224</span><br><span class="line">225</span><br><span class="line">226</span><br><span class="line">227</span><br><span class="line">228</span><br><span class="line">229</span><br><span class="line">230</span><br><span class="line">231</span><br><span class="line">232</span><br><span class="line">233</span><br><span class="line">234</span><br><span class="line">235</span><br><span class="line">236</span><br><span class="line">237</span><br><span class="line">238</span><br><span class="line">239</span><br><span class="line">240</span><br><span class="line">241</span><br><span class="line">242</span><br><span class="line">243</span><br><span class="line">244</span><br><span class="line">245</span><br><span class="line">246</span><br><span class="line">247</span><br><span class="line">248</span><br><span class="line">249</span><br><span class="line">250</span><br><span class="line">251</span><br><span class="line">252</span><br><span class="line">253</span><br><span class="line">254</span><br><span class="line">255</span><br><span class="line">256</span><br><span class="line">257</span><br><span class="line">258</span><br><span class="line">259</span><br><span class="line">260</span><br><span class="line">261</span><br><span class="line">262</span><br><span class="line">263</span><br><span class="line">264</span><br><span class="line">265</span><br><span class="line">266</span><br><span class="line">267</span><br><span class="line">268</span><br><span class="line">269</span><br><span class="line">270</span><br><span class="line">271</span><br><span class="line">272</span><br><span class="line">273</span><br><span class="line">274</span><br><span class="line">275</span><br><span class="line">276</span><br><span class="line">277</span><br><span class="line">278</span><br><span class="line">279</span><br><span class="line">280</span><br><span class="line">281</span><br><span class="line">282</span><br><span class="line">283</span><br><span class="line">284</span><br><span class="line">285</span><br><span class="line">286</span><br><span class="line">287</span><br><span class="line">288</span><br><span class="line">289</span><br><span class="line">290</span><br><span class="line">291</span><br><span class="line">292</span><br><span class="line">293</span><br><span class="line">294</span><br><span class="line">295</span><br><span class="line">296</span><br><span class="line">297</span><br><span class="line">298</span><br><span class="line">299</span><br><span class="line">300</span><br><span class="line">301</span><br><span class="line">302</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># === 代码质量与 PEP 8 实践演示 ===</span></span><br><span class="line"><span class="keyword">from</span> print_utils <span class="keyword">import</span> * </span><br><span class="line"><span class="keyword">import</span> os <span class="comment"># 标准库导入</span></span><br><span class="line"><span class="keyword">import</span> sys <span class="comment"># 标准库导入</span></span><br><span class="line"><span class="keyword">from</span> collections <span class="keyword">import</span> defaultdict <span class="comment"># 标准库导入，from ... import ...</span></span><br><span class="line"><span class="keyword">from</span> typing <span class="keyword">import</span> <span class="type">List</span>, <span class="type">Tuple</span>, <span class="type">Any</span>, <span class="type">Optional</span>, <span class="type">Union</span>, <span class="type">Dict</span> <span class="comment"># 类型注解</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 第三方库导入 (示例)</span></span><br><span class="line"><span class="comment"># import numpy as np</span></span><br><span class="line"><span class="comment"># import pandas as pd</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 本地应用/库导入 (示例)</span></span><br><span class="line"><span class="comment"># from my_package import my_module_example </span></span><br><span class="line"></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line"><span class="comment"># 0. 模块级别文档字符串与常量</span></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line">print_header(<span class="string">"代码质量与 PEP 8 核心实践演示"</span>)</span><br><span class="line"></span><br><span class="line"><span class="string">"""</span></span><br><span class="line"><span class="string">本模块旨在演示 Python PEP 8 风格指南中的常见规则和一些编码最佳实践。</span></span><br><span class="line"><span class="string">PEP 8 官方文档: https://www.python.org/dev/peps/pep-0008/</span></span><br><span class="line"><span class="string">"""</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 常量名应全部大写，单词间用下划线分隔</span></span><br><span class="line">MAX_RETRIES: <span class="built_in">int</span> = <span class="number">3</span></span><br><span class="line">DEFAULT_GREETING: <span class="built_in">str</span> = <span class="string">"Hello"</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line"><span class="comment"># 1. 类定义与文档字符串</span></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line">print_subheader(<span class="string">"1. 类定义与文档字符串"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">SampleClass</span>:</span><br><span class="line">    <span class="string">"""</span></span><br><span class="line"><span class="string">    一个示例类，用于演示 PEP 8 规范和良好实践。</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">    这个类的主要目的是展示命名约定、文档字符串格式、</span></span><br><span class="line"><span class="string">    方法定义以及属性的组织方式。</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">    Attributes:</span></span><br><span class="line"><span class="string">        name (str): 对象的名称。</span></span><br><span class="line"><span class="string">        value (Optional[int]): 一个可选的整数值。</span></span><br><span class="line"><span class="string">        _protected_member (str): 一个受保护的成员变量。</span></span><br><span class="line"><span class="string">        __private_member (float): 一个私有的成员变量。</span></span><br><span class="line"><span class="string">    """</span></span><br><span class="line">    CLASS_LEVEL_CONSTANT: <span class="built_in">str</span> = <span class="string">"This is a class-level constant"</span></span><br><span class="line"></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">__init__</span>(<span class="params">self, name: <span class="built_in">str</span>, value: <span class="type">Optional</span>[<span class="built_in">int</span>] = <span class="literal">None</span></span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">        <span class="string">"""</span></span><br><span class="line"><span class="string">        初始化 SampleClass 对象。</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">        Args:</span></span><br><span class="line"><span class="string">            name (str): 对象的名称。</span></span><br><span class="line"><span class="string">            value (Optional[int], optional): 一个可选的整数值。默认为 None。</span></span><br><span class="line"><span class="string">        """</span></span><br><span class="line">        print_info(<span class="string">f"  Initializing SampleClass(name='<span class="subst">{name}</span>', value=<span class="subst">{value}</span>)"</span>)</span><br><span class="line">        <span class="variable language_">self</span>.name: <span class="built_in">str</span> = name</span><br><span class="line">        <span class="variable language_">self</span>.value: <span class="type">Optional</span>[<span class="built_in">int</span>] = value</span><br><span class="line">        <span class="variable language_">self</span>._protected_member: <span class="built_in">str</span> = <span class="string">"I am protected"</span> <span class="comment"># 受保护成员以下划线开头</span></span><br><span class="line">        <span class="variable language_">self</span>.__private_member: <span class="built_in">float</span> = <span class="number">3.14</span>          <span class="comment"># 私有成员以双下划线开头 (会被名称修饰)</span></span><br><span class="line"></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">display_info</span>(<span class="params">self, prefix: <span class="built_in">str</span> = <span class="string">"Info"</span></span>) -&gt; <span class="built_in">str</span>:</span><br><span class="line">        <span class="string">"""</span></span><br><span class="line"><span class="string">        显示对象的信息。</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">        如果参数列表过长，可以像下面这样换行和缩进：</span></span><br><span class="line"><span class="string">        def another_method(</span></span><br><span class="line"><span class="string">            self,</span></span><br><span class="line"><span class="string">            param1: type,</span></span><br><span class="line"><span class="string">            param2: type,</span></span><br><span class="line"><span class="string">            very_long_parameter_name: type = default_value</span></span><br><span class="line"><span class="string">        ) -&gt; ReturnType:</span></span><br><span class="line"><span class="string">            pass</span></span><br><span class="line"><span class="string">        </span></span><br><span class="line"><span class="string">        Args:</span></span><br><span class="line"><span class="string">            prefix (str, optional): 信息输出的前缀。默认为 "Info"。</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">        Returns:</span></span><br><span class="line"><span class="string">            str: 格式化后的信息字符串。</span></span><br><span class="line"><span class="string">        """</span></span><br><span class="line">        <span class="comment"># 行长度通常建议不超过 79 个字符 (或团队约定的长度，如 88, 100, 120)</span></span><br><span class="line">        <span class="comment"># 长字符串或表达式可以分行，通常在操作符之后换行，并适当缩进</span></span><br><span class="line">        info_string: <span class="built_in">str</span> = (</span><br><span class="line">            <span class="string">f"<span class="subst">{prefix}</span>: Name='<span class="subst">{self.name}</span>', Value=<span class="subst">{self.value}</span>, "</span></span><br><span class="line">            <span class="string">f"Protected='<span class="subst">{self._protected_member}</span>', "</span></span><br><span class="line">            <span class="string">f"Private (mangled name access): <span class="subst">{self._SampleClass__private_member}</span>"</span> <span class="comment"># 访问被名称修饰的私有变量</span></span><br><span class="line">        )</span><br><span class="line">        <span class="comment"># 在二元运算符之前或之后换行都是PEP 8允许的，但团队内应保持一致</span></span><br><span class="line">        <span class="comment"># calculation_example = (var1 + var2 + var3</span></span><br><span class="line">        <span class="comment">#                        - var4 - var5)</span></span><br><span class="line">        <span class="keyword">return</span> info_string</span><br><span class="line"></span><br><span class="line"><span class="meta">    @property</span></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">name_uppercase</span>(<span class="params">self</span>) -&gt; <span class="built_in">str</span>:</span><br><span class="line">        <span class="string">"""一个只读属性，返回大写的名称。"""</span></span><br><span class="line">        <span class="keyword">return</span> <span class="variable language_">self</span>.name.upper()</span><br><span class="line"></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line"><span class="comment"># 2. 函数定义与文档字符串</span></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line">print_subheader(<span class="string">"2. 函数定义与文档字符串"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">calculate_sum_and_average</span>(<span class="params">numbers: <span class="type">List</span>[<span class="built_in">float</span>]</span>) -&gt; <span class="type">Tuple</span>[<span class="built_in">float</span>, <span class="built_in">float</span>]:</span><br><span class="line">    <span class="string">"""</span></span><br><span class="line"><span class="string">    计算一个数字列表的总和与平均值。</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">    Args:</span></span><br><span class="line"><span class="string">        numbers (List[float]): 需要计算的浮点数列表。</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">    Returns:</span></span><br><span class="line"><span class="string">        Tuple[float, float]: 一个包含总和与平均值的元组。</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">    Raises:</span></span><br><span class="line"><span class="string">        ValueError: 如果输入列表为空。</span></span><br><span class="line"><span class="string">        TypeError: 如果列表中包含非数字类型的元素。</span></span><br><span class="line"><span class="string">    """</span></span><br><span class="line">    print_info(<span class="string">f"  Calculating sum and average for: <span class="subst">{numbers}</span>"</span>)</span><br><span class="line">    <span class="keyword">if</span> <span class="keyword">not</span> numbers:</span><br><span class="line">        <span class="keyword">raise</span> ValueError(<span class="string">"输入列表不能为空以计算平均值。"</span>)</span><br><span class="line">    </span><br><span class="line">    current_sum: <span class="built_in">float</span> = <span class="number">0.0</span></span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="keyword">for</span> num <span class="keyword">in</span> numbers:</span><br><span class="line">            current_sum += num <span class="comment"># 操作符两侧通常有空格</span></span><br><span class="line">    <span class="keyword">except</span> TypeError <span class="keyword">as</span> e: <span class="comment"># 捕获特定异常</span></span><br><span class="line">        <span class="keyword">raise</span> TypeError(<span class="string">f"列表中的所有元素都必须是数字: <span class="subst">{e}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    count: <span class="built_in">int</span> = <span class="built_in">len</span>(numbers)</span><br><span class="line">    average: <span class="built_in">float</span> = current_sum / count</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 正确的空格使用示例:</span></span><br><span class="line">    <span class="comment"># x = 1 + 2  # 运算符两侧</span></span><br><span class="line">    <span class="comment"># my_func(arg1, arg2) # 逗号后，括号内侧通常无空格 (除非是元组只有一个元素 my_tuple = (1,))</span></span><br><span class="line">    <span class="comment"># my_list[0] # 索引/切片操作符内侧无空格</span></span><br><span class="line">    <span class="comment"># my_dict = {"key": "value"} # 冒号后有空格，前面无；花括号内侧通常无空格</span></span><br><span class="line"></span><br><span class="line">    <span class="keyword">return</span> current_sum, average</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line"><span class="comment"># 3. 条件语句与 Pythonic 表达</span></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line">print_subheader(<span class="string">"3. 条件语句与 Pythonic 表达"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">check_item_status</span>(<span class="params">item: <span class="type">Optional</span>[<span class="type">Any</span>], status_flags: <span class="built_in">int</span> = <span class="number">0</span></span>) -&gt; <span class="built_in">str</span>:</span><br><span class="line">    <span class="string">"""演示条件语句的推荐写法。"""</span></span><br><span class="line">    print_info(<span class="string">f"  Checking status for item: <span class="subst">{item}</span>, flags: <span class="subst">{status_flags}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 检查 None: 使用 `is None` 或 `is not None`</span></span><br><span class="line">    <span class="keyword">if</span> item <span class="keyword">is</span> <span class="literal">None</span>:</span><br><span class="line">        <span class="keyword">return</span> <span class="string">"项目为 None。"</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 检查布尔真值: 直接使用 `if item:` 或 `if not item:`</span></span><br><span class="line">    <span class="comment"># 而不是 `if item == True:` 或 `if item == []:`</span></span><br><span class="line">    <span class="keyword">if</span> <span class="keyword">not</span> item: <span class="comment"># 适用于空列表、空字符串、空字典、0、None 等</span></span><br><span class="line">        <span class="keyword">return</span> <span class="string">"项目为空或具有布尔假值。"</span></span><br><span class="line">        </span><br><span class="line">    <span class="comment"># 复杂条件表达式，如果过长，可以分行并用括号括起来</span></span><br><span class="line">    <span class="comment"># (此处的 status_flags 仅为示例)</span></span><br><span class="line">    IS_ACTIVE_FLAG = <span class="number">1</span></span><br><span class="line">    IS_URGENT_FLAG = <span class="number">2</span></span><br><span class="line">    <span class="keyword">if</span> (<span class="built_in">isinstance</span>(item, <span class="built_in">str</span>) <span class="keyword">and</span></span><br><span class="line">            <span class="built_in">len</span>(item) &gt; <span class="number">5</span> <span class="keyword">and</span></span><br><span class="line">            (status_flags &amp; IS_ACTIVE_FLAG) <span class="keyword">and</span> <span class="comment"># 位运算示例</span></span><br><span class="line">            <span class="keyword">not</span> (status_flags &amp; IS_URGENT_FLAG)):</span><br><span class="line">        <span class="keyword">return</span> <span class="string">f"项目 '<span class="subst">{item}</span>' 是一个长度大于5的活动字符串且不紧急。"</span></span><br><span class="line">        </span><br><span class="line">    <span class="keyword">return</span> <span class="string">f"项目 '<span class="subst">{item}</span>' 具有其他状态。"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line"><span class="comment"># 4. 推导式与生成器表达式</span></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line">print_subheader(<span class="string">"4. 推导式与生成器表达式"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">demonstrate_comprehensions</span>(<span class="params">data: <span class="type">List</span>[<span class="built_in">int</span>]</span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="string">"""演示列表推导式和生成器表达式。"""</span></span><br><span class="line">    print_info(<span class="string">f"  Original data: <span class="subst">{data}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 列表推导式</span></span><br><span class="line">    squared_numbers: <span class="type">List</span>[<span class="built_in">int</span>] = [x**<span class="number">2</span> <span class="keyword">for</span> x <span class="keyword">in</span> data]</span><br><span class="line">    print_success(<span class="string">f"    Squared numbers (list comprehension): <span class="subst">{squared_numbers}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    even_squared_numbers: <span class="type">List</span>[<span class="built_in">int</span>] = [x**<span class="number">2</span> <span class="keyword">for</span> x <span class="keyword">in</span> data <span class="keyword">if</span> x % <span class="number">2</span> == <span class="number">0</span>]</span><br><span class="line">    print_success(<span class="string">f"    Even squared numbers (with condition): <span class="subst">{even_squared_numbers}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 生成器表达式 (更节省内存，按需生成)</span></span><br><span class="line">    sum_of_cubes: <span class="built_in">int</span> = <span class="built_in">sum</span>(x**<span class="number">3</span> <span class="keyword">for</span> x <span class="keyword">in</span> data) <span class="comment"># 生成器表达式在 sum() 中使用</span></span><br><span class="line">    print_success(<span class="string">f"    Sum of cubes (generator expression): <span class="subst">{sum_of_cubes}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 字典推导式</span></span><br><span class="line">    number_to_square_dict: <span class="type">Dict</span>[<span class="built_in">int</span>, <span class="built_in">int</span>] = {x: x**<span class="number">2</span> <span class="keyword">for</span> x <span class="keyword">in</span> data}</span><br><span class="line">    print_success(<span class="string">f"    Number to square dict: <span class="subst">{number_to_square_dict}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="comment"># 避免过于复杂的嵌套推导式，如果可读性受损，应拆分为普通循环</span></span><br><span class="line">    <span class="comment"># matrix = [[1, 2], [3, 4]]</span></span><br><span class="line">    <span class="comment"># flattened = [element for row in matrix for element in row] # 尚可接受</span></span><br><span class="line">    <span class="comment"># print_info(f"    Flattened matrix: {flattened}")</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line"><span class="comment"># 5. 异常处理 (try-except-else-finally)</span></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line">print_subheader(<span class="string">"5. 异常处理"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">perform_safe_operation</span>(<span class="params">value1: <span class="type">Any</span>, value2: <span class="type">Any</span></span>) -&gt; <span class="type">Optional</span>[<span class="built_in">float</span>]:</span><br><span class="line">    <span class="string">"""一个演示安全操作的函数，包含完整的异常处理。"""</span></span><br><span class="line">    print_info(<span class="string">f"  Attempting operation with <span class="subst">{value1}</span> and <span class="subst">{value2}</span>"</span>)</span><br><span class="line">    result: <span class="type">Optional</span>[<span class="built_in">float</span>] = <span class="literal">None</span></span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="comment"># 假设这是一个可能抛出多种异常的操作</span></span><br><span class="line">        <span class="keyword">if</span> <span class="keyword">not</span> (<span class="built_in">isinstance</span>(value1, (<span class="built_in">int</span>, <span class="built_in">float</span>)) <span class="keyword">and</span> <span class="built_in">isinstance</span>(value2, (<span class="built_in">int</span>, <span class="built_in">float</span>))):</span><br><span class="line">            <span class="keyword">raise</span> TypeError(<span class="string">"输入值必须是数字类型。"</span>)</span><br><span class="line">        <span class="keyword">if</span> value2 == <span class="number">0</span>:</span><br><span class="line">            <span class="keyword">raise</span> ValueError(<span class="string">"除数不能为零。"</span>) <span class="comment"># 自定义 ValueError 而非直接 ZeroDivisionError</span></span><br><span class="line">        result = <span class="built_in">float</span>(value1 / value2)</span><br><span class="line">    <span class="keyword">except</span> ValueError <span class="keyword">as</span> ve: <span class="comment"># 捕获特定的、预期的异常</span></span><br><span class="line">        print_error(<span class="string">f"    操作失败 (ValueError): <span class="subst">{ve}</span>"</span>)</span><br><span class="line">        <span class="comment"># logger.error(f"ValueError during operation: {ve}", exc_info=True) # 实际项目中用日志</span></span><br><span class="line">    <span class="keyword">except</span> TypeError <span class="keyword">as</span> te:</span><br><span class="line">        print_error(<span class="string">f"    操作失败 (TypeError): <span class="subst">{te}</span>"</span>)</span><br><span class="line">    <span class="keyword">except</span> Exception <span class="keyword">as</span> e: <span class="comment"># 捕获其他所有 Exception 子类的异常 (作为最后防线)</span></span><br><span class="line">        print_error(<span class="string">f"    发生未知错误: <span class="subst">{<span class="built_in">type</span>(e).__name__}</span> - <span class="subst">{e}</span>"</span>)</span><br><span class="line">    <span class="keyword">else</span>:</span><br><span class="line">        <span class="comment"># 如果 try 块中没有发生任何异常，则执行 else 块</span></span><br><span class="line">        print_success(<span class="string">f"    操作成功完成，结果: <span class="subst">{result}</span>"</span>)</span><br><span class="line">    <span class="keyword">finally</span>:</span><br><span class="line">        <span class="comment"># 无论 try 块中是否发生异常，finally 块总会执行</span></span><br><span class="line">        <span class="comment"># 通常用于资源清理</span></span><br><span class="line">        print_info(<span class="string">"    操作尝试已结束 (finally 块执行)。"</span>)</span><br><span class="line">    <span class="keyword">return</span> result</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line"><span class="comment"># 6. 上下文管理器 (with 语句)</span></span><br><span class="line"><span class="comment"># =============================================================</span></span><br><span class="line">print_subheader(<span class="string">"6. 上下文管理器 (with 语句)"</span>)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">read_content_from_file_safely</span>(<span class="params">filepath: <span class="built_in">str</span></span>) -&gt; <span class="type">Optional</span>[<span class="built_in">str</span>]:</span><br><span class="line">    <span class="string">"""使用上下文管理器安全地读取文件内容。"""</span></span><br><span class="line">    print_info(<span class="string">f"  Attempting to read file: <span class="subst">{filepath}</span>"</span>)</span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="comment"># 'with' 语句确保文件在操作完成后（即使发生异常）也会被正确关闭</span></span><br><span class="line">        <span class="keyword">with</span> <span class="built_in">open</span>(filepath, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> file_handle:</span><br><span class="line">            content: <span class="built_in">str</span> = file_handle.read()</span><br><span class="line">            print_success(<span class="string">f"    成功读取文件 '<span class="subst">{filepath}</span>'。"</span>)</span><br><span class="line">            <span class="keyword">return</span> content</span><br><span class="line">    <span class="keyword">except</span> FileNotFoundError:</span><br><span class="line">        print_error(<span class="string">f"    文件 '<span class="subst">{filepath}</span>' 未找到。"</span>)</span><br><span class="line">        <span class="keyword">return</span> <span class="literal">None</span></span><br><span class="line">    <span class="keyword">except</span> IOError <span class="keyword">as</span> e:</span><br><span class="line">        print_error(<span class="string">f"    读取文件 '<span class="subst">{filepath}</span>' 时发生 IO 错误: <span class="subst">{e}</span>"</span>)</span><br><span class="line">        <span class="keyword">return</span> <span class="literal">None</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">"__main__"</span>:</span><br><span class="line">    <span class="comment"># 主程序块，用于调用演示函数</span></span><br><span class="line">    print_info(<span class="string">"--- 开始演示 SampleClass ---"</span>)</span><br><span class="line">    my_object = SampleClass(name=<span class="string">"ProriseInstance"</span>, value=<span class="number">100</span>)</span><br><span class="line">    print_success(my_object.display_info(prefix=<span class="string">"Object"</span>))</span><br><span class="line">    print_success(<span class="string">f"  Uppercase name: <span class="subst">{my_object.name_uppercase}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    print_info(<span class="string">"\n--- 开始演示 calculate_sum_and_average ---"</span>)</span><br><span class="line">    numbers_list: <span class="type">List</span>[<span class="built_in">float</span>] = [<span class="number">10.0</span>, <span class="number">15.5</span>, <span class="number">20.0</span>, <span class="number">5.5</span>]</span><br><span class="line">    total, avg = calculate_sum_and_average(numbers_list)</span><br><span class="line">    print_success(<span class="string">f"  对于列表 <span class="subst">{numbers_list}</span>，总和: <span class="subst">{total}</span>, 平均值: <span class="subst">{avg:<span class="number">.2</span>f}</span>"</span>)</span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        calculate_sum_and_average([])</span><br><span class="line">    <span class="keyword">except</span> ValueError <span class="keyword">as</span> e:</span><br><span class="line">        print_warning(<span class="string">f"  捕获到预期错误: <span class="subst">{e}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    print_info(<span class="string">"\n--- 开始演示 check_item_status ---"</span>)</span><br><span class="line">    print_info(<span class="string">f"  <span class="subst">{check_item_status(<span class="literal">None</span>)}</span>"</span>)</span><br><span class="line">    print_info(<span class="string">f"  <span class="subst">{check_item_status(<span class="string">''</span>)}</span>"</span>)</span><br><span class="line">    print_info(<span class="string">f"  <span class="subst">{check_item_status(<span class="string">'A very long active string'</span>, status_flags=<span class="number">1</span>)}</span>"</span>) <span class="comment"># IS_ACTIVE_FLAG = 1</span></span><br><span class="line"></span><br><span class="line">    print_info(<span class="string">"\n--- 开始演示 demonstrate_comprehensions ---"</span>)</span><br><span class="line">    demonstrate_comprehensions([<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>])</span><br><span class="line"></span><br><span class="line">    print_info(<span class="string">"\n--- 开始演示 perform_safe_operation ---"</span>)</span><br><span class="line">    perform_safe_operation(<span class="number">10</span>, <span class="number">2</span>)</span><br><span class="line">    perform_safe_operation(<span class="number">10</span>, <span class="number">0</span>)</span><br><span class="line">    perform_safe_operation(<span class="string">"abc"</span>, <span class="number">2</span>)</span><br><span class="line">    perform_safe_operation(<span class="number">10</span>, <span class="string">"xyz"</span>)</span><br><span class="line">    </span><br><span class="line">    print_info(<span class="string">"\n--- 开始演示 read_content_from_file_safely ---"</span>)</span><br><span class="line">    <span class="comment"># 创建一个临时文件用于读取演示</span></span><br><span class="line">    temp_file_for_read = <span class="string">"temp_read_example.txt"</span></span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(temp_file_for_read, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f_temp:</span><br><span class="line">        f_temp.write(<span class="string">"Hello from with statement example!\nSecond line."</span>)</span><br><span class="line">    </span><br><span class="line">    file_contents = read_content_from_file_safely(temp_file_for_read)</span><br><span class="line">    <span class="keyword">if</span> file_contents:</span><br><span class="line">        print_info(<span class="string">f"    文件内容 (前50字符): '<span class="subst">{file_contents[:<span class="number">50</span>]}</span>...'"</span>)</span><br><span class="line">    read_content_from_file_safely(<span class="string">"non_existent_file.txt"</span>) <span class="comment"># 测试文件不存在的情况</span></span><br><span class="line">    </span><br><span class="line">    <span class="keyword">if</span> os.path.exists(temp_file_for_read): <span class="comment"># 清理临时文件</span></span><br><span class="line">        os.remove(temp_file_for_read)</span><br><span class="line">        print_info(<span class="string">f"  已删除临时读取文件: <span class="subst">{temp_file_for_read}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    print_header(<span class="string">"代码质量与 PEP 8 实践演示结束。"</span>)</span><br></pre></td></tr></tbody></table></figure><h3 id="三、总结与工具"><a href="#三、总结与工具" class="headerlink" title="三、总结与工具"></a>三、总结与工具</h3><p>遵循良好的项目结构和编码规范是开发高质量、可维护 Python 应用的关键。</p><ul><li><strong>项目结构</strong>：清晰的目录划分有助于分离关注点，使得代码、测试、文档和配置易于查找和管理。</li><li><strong>代码风格 (PEP 8)</strong>：一致的编码风格提高了代码的可读性，是团队协作的基础。</li><li><strong>文档与注释</strong>: 良好的文档字符串 (docstrings) 和必要的注释使代码更易于理解和使用。</li><li><strong>Pythonic 代码</strong>: 利用 Python 语言的特性（如推导式、上下文管理器）可以写出更简洁、更高效、更易读的代码。</li></ul><p><strong>辅助工具</strong>:</p><p>为了帮助开发者遵循最佳实践并保持代码质量，社区提供了许多优秀的工具：</p><ul><li><strong>Linters (代码风格和错误检查)</strong>:<ul><li><code>Flake8</code>: 结合了 PyFlakes (错误检查), PEP 8 (风格检查) 和 McCabe (复杂度检查)。</li><li><code>Pylint</code>: 功能更全面，提供更广泛的代码分析、错误检测、风格检查和重构建议。</li><li><code>Ruff</code>: 一个用 Rust 编写的极快的 Python linter 和 formatter，可以替代 Flake8, isort, pydocstyle 等多种工具。</li></ul></li><li><strong>Formatters (代码自动格式化)</strong>:<ul><li><code>Black</code>: 一个固执己见 (opinionated) 的代码格式化工具，能自动将代码格式化为符合 PEP 8 子集的一致风格。</li><li><code>Autopep8</code>: 自动将 Python 代码格式化为符合 PEP 8 风格。</li><li><code>Ruff Formatter</code>: <code>Ruff</code> 也包含了格式化功能。</li></ul></li><li><strong>Type Checkers (静态类型检查)</strong>:<ul><li><code>MyPy</code>: Python 官方的静态类型检查器。</li><li><code>Pyright</code>: 由微软开发，快速且功能强大的类型检查器，也是 VS Code 中 Pylance 插件的核心。</li><li><code>Pytype</code>: Google 开发的类型检查器，可以推断类型。</li></ul></li></ul><p>将这些工具集成到你的开发流程和持续集成 (CI) 系统中，可以有效地提升团队的整体代码质量和开发效率。</p><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/43091.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/43091.html&quot;)">Python（二十二）：第二十一章：项目结构规范与最佳实践</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/43091.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/43091.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Python<span class="categoryesPageCount">22</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Python基础知识总汇<span class="tagsPageCount">22</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/55902.html"><img class="prev-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Python（二十一）：第二十章：Python 语法新特性总结</div></div></a></div><div class="next-post pull-right"><a href="/posts/30645.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/14/551210.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Java（一）：1.0 Java语言概述与核心生态</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/45310.html" title="Python（七）：第六章：条件循环分支"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（七）：第六章：条件循环分支</div></div></a></div><div><a href="/posts/8019.html" title="Python（三）：第二章：转义字符"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（三）：第二章：转义字符</div></div></a></div><div><a href="/posts/56572.html" title="Python（九）：第八章： 函数知识总结"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（九）：第八章： 函数知识总结</div></div></a></div><div><a href="/posts/55902.html" title="Python（二十一）：第二十章：Python 语法新特性总结"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（二十一）：第二十章：Python 语法新特性总结</div></div></a></div><div><a href="/posts/2501.html" title="Python（二）：第一章：字符串打印格式化与PyCharm模板变量"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（二）：第一章：字符串打印格式化与PyCharm模板变量</div></div></a></div><div><a href="/posts/17730.html" title="Python（一）：Python 语言特性"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（一）：Python 语言特性</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"Python（二十二）：第二十一章：项目结构规范与最佳实践",date:"2025-04-19 13:13:45",updated:"2025-07-13 22:13:01",tags:["Python基础知识总汇"],categories:["后端技术","Python"],content:'\n## 第二十一章：项目结构规范与最佳实践\n\n一个清晰、一致的项目结构和高质量的代码是任何成功软件项目的基石。良好的项目组织不仅能让其他开发者（以及未来的你）更容易理解和维护代码，还能简化构建、测试、部署和分发流程。同样，遵循统一的代码风格和最佳实践能够显著提高代码的可读性、减少错误，并促进团队协作。\n\n本章将探讨 Python 项目的推荐结构，并总结一些核心的代码质量与风格指南，主要基于 PEP 8。\n\n### 一、推荐的 Python 项目结构\n\n一个组织良好的 Python 项目通常包含应用代码、测试、文档、依赖管理和打包配置等部分。下面是一个推荐的通用项目结构示例：\n\n#### 1\\. 目录树概览\n\n```python\nmy_project/                  # 项目根目录 (例如，Git 仓库的根)\n│\n├── my_package/              # 主要的 Python 包 (你的应用或库的核心代码)\n│   ├── __init__.py          # 将 \'my_package\' 标记为一个 Python 包，可包含包级别初始化代码\n│   ├── module1.py           # 包内的模块文件\n│   ├── module2.py           # 另一个模块文件\n│   ├── subpackage/          # 包内可以有子包\n│   │   ├── __init__.py      # 使 \'subpackage\' 成为一个子包\n│   │   └── submodule.py     # 子包内的模块\n│   └── utils/               # 常用的工具或辅助函数可以组织在子包中\n│       ├── __init__.py\n│       └── helpers.py       # 辅助函数模块\n│\n├── tests/                   # 存放所有测试代码的目录\n│   ├── __init__.py          # 使 \'tests\' 成为一个包 (有些测试运行器需要)\n│   ├── test_module1.py      # 针对 my_package.module1 的测试\n│   └── test_subpackage_submodule.py # 针对子包模块的测试\n│\n├── docs/                    # 项目文档 (例如使用 Sphinx 或 MkDocs 生成)\n│   ├── conf.py              # Sphinx 配置文件 (如果使用 Sphinx)\n│   ├── index.rst            # Sphinx 主文档文件 (或 index.md)\n│   └── api.rst              # API 文档等\n│\n├── examples/                # 如何使用你的包或库的示例代码\n│   └── usage_example.py\n│\n├── .gitignore               # 指定 Git 应忽略的文件和目录\n├── LICENSE                  # 项目的许可证文件 (例如 MIT, Apache 2.0)\n├── README.md                # 项目的详细说明、安装指南、使用方法等 (Markdown 格式)\n├── requirements.txt         # 列出项目运行所需的基本依赖包及其版本\n├── setup.py                 # (传统方式) Python 项目的构建脚本 (用于打包、分发)\n├── pyproject.toml           # (现代方式) 项目元数据和构建系统配置 (推荐与 setup.py/setup.cfg 结合或替代)\n└── tox.ini                  # (可选) tox 配置文件，用于自动化测试不同 Python 环境\n```\n\n#### 2\\. 各部分详解\n\n##### a. `my_project/` (项目根目录)\n\n这是你项目的最顶层目录，通常对应你的版本控制系统（如 Git）的仓库根目录。\n\n##### b. `my_package/` (主应用包)\n\n这是存放你项目核心 Python 代码的地方。它是一个 Python 包，意味着它包含一个 `__init__.py` 文件。\n\n  * **`__init__.py`**:\n    这个文件可以为空，仅用于将目录标记为一个 Python 包，使得其中的模块可以使用点分路径导入 (例如 `from my_package import module1`)。\n    它也可以包含包级别的初始化代码、定义 `__all__` 变量来控制 `from my_package import *` 的行为，或者直接从子模块中导出常用的类和函数，以提供更简洁的包 API。\n\n    ```python\n    # === my_package/__init__.py 示例 ===\n    from print_utils import print_info # 仅为演示打印，实际 __init__.py 通常不直接打印\n\n    print_info("包 \'my_package\' 正在被初始化...")\n\n    __version__: str = "0.1.0" # 定义包版本\n    APP_NAME: str = "My Awesome Application"\n\n    # 从子模块导出，方便外部调用\n    # from .module1 import important_function\n    # from .subpackage.submodule import UsefulClass\n\n    # __all__ 控制 from my_package import * 的行为\n    # __all__ = ["important_function", "UsefulClass", "APP_NAME"]\n\n    def package_level_utility() -> str:\n        """一个包级别的辅助函数示例。"""\n        return f"Utility from {APP_NAME} v{__version__}"\n\n    # print_info(f"  {package_level_utility()}")\n    ```\n\n  * **`moduleX.py`** (例如 `module1.py`, `module2.py`):\n    这些是包内的实际模块文件，包含相关的函数、类和变量。\n\n  * **`subpackage/`**:\n    大型包可以进一步组织为子包，每个子包同样包含一个 `__init__.py` 文件。\n\n  * **`utils/`** (例如 `helpers.py`):\n    通常用于存放项目中可复用的辅助函数、工具类等。\n\n##### c. `tests/` (测试目录)\n\n存放单元测试、集成测试等所有测试代码。测试文件名通常以 `test_` 开头，或者测试类以 `Test` 开头，以便测试运行器（如 `pytest`, `unittest`) 能够自动发现和执行。\n\n##### d. `docs/` (文档目录)\n\n存放项目的用户文档、API 文档等。常用的工具有 Sphinx (配合 reStructuredText 或 MyST Markdown) 或 MkDocs (纯 Markdown)。\n\n##### e. `examples/` (示例代码目录)\n\n提供如何使用你的库或应用程序的简单、可运行的示例代码。\n\n##### f. `.gitignore`\n\n告知 Git哪些文件或目录不应被跟踪和提交 (例如 `__pycache__/`, `*.pyc`, 虚拟环境目录, IDE 配置文件等)。\n\n##### g. `LICENSE`\n\n包含项目的开源许可证文本，例如 MIT, Apache 2.0, GPL 等。明确许可证对于代码的分享和使用非常重要。\n\n##### h. `README.md`\n\n项目的入口点和门面。通常包含：\n\n  * 项目名称和简短描述。\n  * 安装说明。\n  * 基本用法示例。\n  * 如何运行测试。\n  * 如何贡献。\n  * 许可证信息链接。\n\n##### i. `requirements.txt`\n\n列出项目运行所必需的第三方依赖包及其版本。通常由 `pip freeze > requirements.txt` 生成，或者手动维护。更现代的项目可能会使用 `pyproject.toml` 中的 `[project.dependencies]`。\n\n```text\n# === requirements.txt 示例 ===\n# requests==2.28.1\n# numpy>=1.20.0,<2.0.0\n# pandas\n# loguru\n```\n\n##### j. `setup.py` (或 `pyproject.toml` + `setup.cfg`)\n\n用于构建、打包和分发 Python 项目的脚本。\n\n  * **传统 `setup.py`**:\n\n    ```python\n    # === setup.py 示例 (传统方式) ===\n    from print_utils import print_info # 仅为演示，setup.py 通常不应有副作用打印\n    from setuptools import setup, find_packages\n    from typing import List\n\n    # print_header("执行 setup.py") # 避免在导入时执行打印\n\n    def get_long_description() -> str:\n        """读取 README.md 作为长描述。"""\n        try:\n            with open("README.md", "r", encoding="utf-8") as f:\n                return f.read()\n        except FileNotFoundError:\n            # print_warning("README.md 未找到，长描述将为空。")\n            return "A fantastic Python package."\n        \n        \n        REQUIRED_PACKAGES: List[str] = [\n        "requests>=2.25.1", # 示例依赖，具体根据项目需求\n        "loguru~=0.7.0"\n        # "pandas>=1.3.0,<2.0.0",\n    ]\n    \n    setup(\n        name="my_super_package", # 包的唯一名称 (PyPI 上)\n        version="0.1.0",       # 当前版本\n        author="您的名字",\n        author_email="<EMAIL>",\n        description="这是一个关于我的超级包的简短描述。",\n        long_description=get_long_description(),\n        long_description_content_type="text/markdown", # 如果 long_description 是 Markdown\n        url="https://github.com/yourusername/my_super_package", # 项目主页\n        \n        # find_packages() 会自动查找所有包含 __init__.py 的目录作为包\n        # exclude 参数可以排除特定目录 (如 tests)\n        packages=find_packages(exclude=("tests*", "docs*", "examples*")),\n        \n        # 指定包内需要包含的非代码文件 (例如模板、数据文件)\n        # package_data={\n        #     \'my_package\': [\'data/*.json\', \'templates/*.html\'],\n        # },\n        # include_package_data=True, # 如果使用 MANIFEST.in\n        \n        python_requires=">=3.8", # 项目兼容的 Python 最低版本\n        \n        install_requires=REQUIRED_PACKAGES, # 项目的核心运行时依赖\n        \n        # 可选：用于开发和测试的额外依赖\n        # extras_require={\n        #     "dev": ["pytest>=6.0", "mypy>=0.900", "flake8", "black"],\n        #     "docs": ["sphinx", "sphinx-rtd-theme"],\n        # },\n        \n        # 如果你的包提供了命令行工具\n        # entry_points={\n        #     "console_scripts": [\n        #         "my-tool=my_package.cli:main_cli_function",\n        #     ],\n        # },\n        \n        classifiers=[ # PyPI 分类器，帮助用户发现你的包\n            "Development Status :: 3 - Alpha",\n            "Intended Audience :: Developers",\n            "License :: OSI Approved :: MIT License",\n            "Programming Language :: Python :: 3",\n            "Programming Language :: Python :: 3.8",\n            "Programming Language :: Python :: 3.9",\n            "Programming Language :: Python :: 3.10",\n            "Programming Language :: Python :: 3.11",\n            "Programming Language :: Python :: 3.12",\n            "Operating System :: OS Independent",\n            "Topic :: Software Development :: Libraries :: Python Modules",\n        ],\n        keywords="python package example utility", # 搜索关键词\n    )\n    # print_success("setup.py 配置完成。")\n    ​```\n    ```\n\n  * **现代 `pyproject.toml`**:\n    PEP 517 和 PEP 518 推荐使用 `pyproject.toml` 文件来声明构建依赖和项目元数据。`setuptools` 仍然可以作为构建后端。\n\n    ```toml\n    # === pyproject.toml 示例 ===\n    # [build-system]\n    # requires = ["setuptools>=61.0", "wheel"]\n    # build-backend = "setuptools.build_meta"\n    # backend-path = ["."] # 可选，如果 setup.py 在子目录\n\n    # [project]\n    # name = "my_super_package"\n    # version = "0.1.0"\n    # description = "这是一个关于我的超级包的简短描述。"\n    # readme = "README.md"\n    # requires-python = ">=3.8"\n    # license = {file = "LICENSE"}\n    # keywords = ["python", "package", "example"]\n    # authors = [\n    #   {name = "您的名字", email = "<EMAIL>" }\n    # ]\n    # maintainers = [\n    #   {name = "您的名字", email = "<EMAIL>" }\n    # ]\n    # classifiers = [\n    #     "Development Status :: 3 - Alpha",\n    #     "Programming Language :: Python :: 3",\n    #     # ... 其他分类器\n    # ]\n\n    # dependencies = [\n    #   "requests>=2.25.1",\n    #   "loguru~=0.7.0",\n    # ]\n\n    # [project.urls]\n    # "Homepage" = "https://github.com/yourusername/my_super_package"\n    # "Bug Tracker" = "https://github.com/yourusername/my_super_package/issues"\n\n    # [project.scripts]\n    # my-tool = "my_package.cli:main_cli_function"\n\n    # [project.optional-dependencies]\n    # dev = [\n    #   "pytest>=6.0",\n    #   "mypy",\n    # ]\n    ```\n\n    当使用 `pyproject.toml` 时，`setup.py` 可以非常简单，甚至在某些情况下不需要（如果所有元数据都在 `pyproject.toml` 且构建后端支持）。\n\n### 二、代码质量与风格指南 (PEP 8 核心实践)\n\nPEP 8 是 Python 官方的代码风格指南，遵循它可以使代码更易读、更易于维护，并促进团队协作的一致性。除了 PEP 8，还有一些通用的最佳实践。\n\n下面是一个演示这些规范的示例脚本\n\n```python\n# === 代码质量与 PEP 8 实践演示 ===\nfrom print_utils import * \nimport os # 标准库导入\nimport sys # 标准库导入\nfrom collections import defaultdict # 标准库导入，from ... import ...\nfrom typing import List, Tuple, Any, Optional, Union, Dict # 类型注解\n\n# 第三方库导入 (示例)\n# import numpy as np\n# import pandas as pd\n\n# 本地应用/库导入 (示例)\n# from my_package import my_module_example \n\n# =============================================================\n# 0. 模块级别文档字符串与常量\n# =============================================================\nprint_header("代码质量与 PEP 8 核心实践演示")\n\n"""\n本模块旨在演示 Python PEP 8 风格指南中的常见规则和一些编码最佳实践。\nPEP 8 官方文档: https://www.python.org/dev/peps/pep-0008/\n"""\n\n# 常量名应全部大写，单词间用下划线分隔\nMAX_RETRIES: int = 3\nDEFAULT_GREETING: str = "Hello"\n\n\n# =============================================================\n# 1. 类定义与文档字符串\n# =============================================================\nprint_subheader("1. 类定义与文档字符串")\n\nclass SampleClass:\n    """\n    一个示例类，用于演示 PEP 8 规范和良好实践。\n\n    这个类的主要目的是展示命名约定、文档字符串格式、\n    方法定义以及属性的组织方式。\n\n    Attributes:\n        name (str): 对象的名称。\n        value (Optional[int]): 一个可选的整数值。\n        _protected_member (str): 一个受保护的成员变量。\n        __private_member (float): 一个私有的成员变量。\n    """\n    CLASS_LEVEL_CONSTANT: str = "This is a class-level constant"\n\n    def __init__(self, name: str, value: Optional[int] = None) -> None:\n        """\n        初始化 SampleClass 对象。\n\n        Args:\n            name (str): 对象的名称。\n            value (Optional[int], optional): 一个可选的整数值。默认为 None。\n        """\n        print_info(f"  Initializing SampleClass(name=\'{name}\', value={value})")\n        self.name: str = name\n        self.value: Optional[int] = value\n        self._protected_member: str = "I am protected" # 受保护成员以下划线开头\n        self.__private_member: float = 3.14          # 私有成员以双下划线开头 (会被名称修饰)\n\n    def display_info(self, prefix: str = "Info") -> str:\n        """\n        显示对象的信息。\n\n        如果参数列表过长，可以像下面这样换行和缩进：\n        def another_method(\n            self,\n            param1: type,\n            param2: type,\n            very_long_parameter_name: type = default_value\n        ) -> ReturnType:\n            pass\n        \n        Args:\n            prefix (str, optional): 信息输出的前缀。默认为 "Info"。\n\n        Returns:\n            str: 格式化后的信息字符串。\n        """\n        # 行长度通常建议不超过 79 个字符 (或团队约定的长度，如 88, 100, 120)\n        # 长字符串或表达式可以分行，通常在操作符之后换行，并适当缩进\n        info_string: str = (\n            f"{prefix}: Name=\'{self.name}\', Value={self.value}, "\n            f"Protected=\'{self._protected_member}\', "\n            f"Private (mangled name access): {self._SampleClass__private_member}" # 访问被名称修饰的私有变量\n        )\n        # 在二元运算符之前或之后换行都是PEP 8允许的，但团队内应保持一致\n        # calculation_example = (var1 + var2 + var3\n        #                        - var4 - var5)\n        return info_string\n\n    @property\n    def name_uppercase(self) -> str:\n        """一个只读属性，返回大写的名称。"""\n        return self.name.upper()\n\n# =============================================================\n# 2. 函数定义与文档字符串\n# =============================================================\nprint_subheader("2. 函数定义与文档字符串")\n\ndef calculate_sum_and_average(numbers: List[float]) -> Tuple[float, float]:\n    """\n    计算一个数字列表的总和与平均值。\n\n    Args:\n        numbers (List[float]): 需要计算的浮点数列表。\n\n    Returns:\n        Tuple[float, float]: 一个包含总和与平均值的元组。\n\n    Raises:\n        ValueError: 如果输入列表为空。\n        TypeError: 如果列表中包含非数字类型的元素。\n    """\n    print_info(f"  Calculating sum and average for: {numbers}")\n    if not numbers:\n        raise ValueError("输入列表不能为空以计算平均值。")\n    \n    current_sum: float = 0.0\n    try:\n        for num in numbers:\n            current_sum += num # 操作符两侧通常有空格\n    except TypeError as e: # 捕获特定异常\n        raise TypeError(f"列表中的所有元素都必须是数字: {e}")\n\n    count: int = len(numbers)\n    average: float = current_sum / count\n    \n    # 正确的空格使用示例:\n    # x = 1 + 2  # 运算符两侧\n    # my_func(arg1, arg2) # 逗号后，括号内侧通常无空格 (除非是元组只有一个元素 my_tuple = (1,))\n    # my_list[0] # 索引/切片操作符内侧无空格\n    # my_dict = {"key": "value"} # 冒号后有空格，前面无；花括号内侧通常无空格\n\n    return current_sum, average\n\n\n# =============================================================\n# 3. 条件语句与 Pythonic 表达\n# =============================================================\nprint_subheader("3. 条件语句与 Pythonic 表达")\n\ndef check_item_status(item: Optional[Any], status_flags: int = 0) -> str:\n    """演示条件语句的推荐写法。"""\n    print_info(f"  Checking status for item: {item}, flags: {status_flags}")\n    \n    # 检查 None: 使用 `is None` 或 `is not None`\n    if item is None:\n        return "项目为 None。"\n    \n    # 检查布尔真值: 直接使用 `if item:` 或 `if not item:`\n    # 而不是 `if item == True:` 或 `if item == []:`\n    if not item: # 适用于空列表、空字符串、空字典、0、None 等\n        return "项目为空或具有布尔假值。"\n        \n    # 复杂条件表达式，如果过长，可以分行并用括号括起来\n    # (此处的 status_flags 仅为示例)\n    IS_ACTIVE_FLAG = 1\n    IS_URGENT_FLAG = 2\n    if (isinstance(item, str) and\n            len(item) > 5 and\n            (status_flags & IS_ACTIVE_FLAG) and # 位运算示例\n            not (status_flags & IS_URGENT_FLAG)):\n        return f"项目 \'{item}\' 是一个长度大于5的活动字符串且不紧急。"\n        \n    return f"项目 \'{item}\' 具有其他状态。"\n\n# =============================================================\n# 4. 推导式与生成器表达式\n# =============================================================\nprint_subheader("4. 推导式与生成器表达式")\n\ndef demonstrate_comprehensions(data: List[int]) -> None:\n    """演示列表推导式和生成器表达式。"""\n    print_info(f"  Original data: {data}")\n    \n    # 列表推导式\n    squared_numbers: List[int] = [x**2 for x in data]\n    print_success(f"    Squared numbers (list comprehension): {squared_numbers}")\n    \n    even_squared_numbers: List[int] = [x**2 for x in data if x % 2 == 0]\n    print_success(f"    Even squared numbers (with condition): {even_squared_numbers}")\n    \n    # 生成器表达式 (更节省内存，按需生成)\n    sum_of_cubes: int = sum(x**3 for x in data) # 生成器表达式在 sum() 中使用\n    print_success(f"    Sum of cubes (generator expression): {sum_of_cubes}")\n    \n    # 字典推导式\n    number_to_square_dict: Dict[int, int] = {x: x**2 for x in data}\n    print_success(f"    Number to square dict: {number_to_square_dict}")\n\n    # 避免过于复杂的嵌套推导式，如果可读性受损，应拆分为普通循环\n    # matrix = [[1, 2], [3, 4]]\n    # flattened = [element for row in matrix for element in row] # 尚可接受\n    # print_info(f"    Flattened matrix: {flattened}")\n\n\n# =============================================================\n# 5. 异常处理 (try-except-else-finally)\n# =============================================================\nprint_subheader("5. 异常处理")\n\ndef perform_safe_operation(value1: Any, value2: Any) -> Optional[float]:\n    """一个演示安全操作的函数，包含完整的异常处理。"""\n    print_info(f"  Attempting operation with {value1} and {value2}")\n    result: Optional[float] = None\n    try:\n        # 假设这是一个可能抛出多种异常的操作\n        if not (isinstance(value1, (int, float)) and isinstance(value2, (int, float))):\n            raise TypeError("输入值必须是数字类型。")\n        if value2 == 0:\n            raise ValueError("除数不能为零。") # 自定义 ValueError 而非直接 ZeroDivisionError\n        result = float(value1 / value2)\n    except ValueError as ve: # 捕获特定的、预期的异常\n        print_error(f"    操作失败 (ValueError): {ve}")\n        # logger.error(f"ValueError during operation: {ve}", exc_info=True) # 实际项目中用日志\n    except TypeError as te:\n        print_error(f"    操作失败 (TypeError): {te}")\n    except Exception as e: # 捕获其他所有 Exception 子类的异常 (作为最后防线)\n        print_error(f"    发生未知错误: {type(e).__name__} - {e}")\n    else:\n        # 如果 try 块中没有发生任何异常，则执行 else 块\n        print_success(f"    操作成功完成，结果: {result}")\n    finally:\n        # 无论 try 块中是否发生异常，finally 块总会执行\n        # 通常用于资源清理\n        print_info("    操作尝试已结束 (finally 块执行)。")\n    return result\n\n\n# =============================================================\n# 6. 上下文管理器 (with 语句)\n# =============================================================\nprint_subheader("6. 上下文管理器 (with 语句)")\n\ndef read_content_from_file_safely(filepath: str) -> Optional[str]:\n    """使用上下文管理器安全地读取文件内容。"""\n    print_info(f"  Attempting to read file: {filepath}")\n    try:\n        # \'with\' 语句确保文件在操作完成后（即使发生异常）也会被正确关闭\n        with open(filepath, "r", encoding="utf-8") as file_handle:\n            content: str = file_handle.read()\n            print_success(f"    成功读取文件 \'{filepath}\'。")\n            return content\n    except FileNotFoundError:\n        print_error(f"    文件 \'{filepath}\' 未找到。")\n        return None\n    except IOError as e:\n        print_error(f"    读取文件 \'{filepath}\' 时发生 IO 错误: {e}")\n        return None\n\n\nif __name__ == "__main__":\n    # 主程序块，用于调用演示函数\n    print_info("--- 开始演示 SampleClass ---")\n    my_object = SampleClass(name="ProriseInstance", value=100)\n    print_success(my_object.display_info(prefix="Object"))\n    print_success(f"  Uppercase name: {my_object.name_uppercase}")\n\n    print_info("\\n--- 开始演示 calculate_sum_and_average ---")\n    numbers_list: List[float] = [10.0, 15.5, 20.0, 5.5]\n    total, avg = calculate_sum_and_average(numbers_list)\n    print_success(f"  对于列表 {numbers_list}，总和: {total}, 平均值: {avg:.2f}")\n    try:\n        calculate_sum_and_average([])\n    except ValueError as e:\n        print_warning(f"  捕获到预期错误: {e}")\n\n    print_info("\\n--- 开始演示 check_item_status ---")\n    print_info(f"  {check_item_status(None)}")\n    print_info(f"  {check_item_status(\'\')}")\n    print_info(f"  {check_item_status(\'A very long active string\', status_flags=1)}") # IS_ACTIVE_FLAG = 1\n\n    print_info("\\n--- 开始演示 demonstrate_comprehensions ---")\n    demonstrate_comprehensions([1, 2, 3, 4, 5, 6])\n\n    print_info("\\n--- 开始演示 perform_safe_operation ---")\n    perform_safe_operation(10, 2)\n    perform_safe_operation(10, 0)\n    perform_safe_operation("abc", 2)\n    perform_safe_operation(10, "xyz")\n    \n    print_info("\\n--- 开始演示 read_content_from_file_safely ---")\n    # 创建一个临时文件用于读取演示\n    temp_file_for_read = "temp_read_example.txt"\n    with open(temp_file_for_read, "w", encoding="utf-8") as f_temp:\n        f_temp.write("Hello from with statement example!\\nSecond line.")\n    \n    file_contents = read_content_from_file_safely(temp_file_for_read)\n    if file_contents:\n        print_info(f"    文件内容 (前50字符): \'{file_contents[:50]}...\'")\n    read_content_from_file_safely("non_existent_file.txt") # 测试文件不存在的情况\n    \n    if os.path.exists(temp_file_for_read): # 清理临时文件\n        os.remove(temp_file_for_read)\n        print_info(f"  已删除临时读取文件: {temp_file_for_read}")\n\n    print_header("代码质量与 PEP 8 实践演示结束。")\n```\n\n### 三、总结与工具\n\n遵循良好的项目结构和编码规范是开发高质量、可维护 Python 应用的关键。\n\n  * **项目结构**：清晰的目录划分有助于分离关注点，使得代码、测试、文档和配置易于查找和管理。\n  * **代码风格 (PEP 8)**：一致的编码风格提高了代码的可读性，是团队协作的基础。\n  * **文档与注释**: 良好的文档字符串 (docstrings) 和必要的注释使代码更易于理解和使用。\n  * **Pythonic 代码**: 利用 Python 语言的特性（如推导式、上下文管理器）可以写出更简洁、更高效、更易读的代码。\n\n**辅助工具**:\n\n为了帮助开发者遵循最佳实践并保持代码质量，社区提供了许多优秀的工具：\n\n  * **Linters (代码风格和错误检查)**:\n      * `Flake8`: 结合了 PyFlakes (错误检查), PEP 8 (风格检查) 和 McCabe (复杂度检查)。\n      * `Pylint`: 功能更全面，提供更广泛的代码分析、错误检测、风格检查和重构建议。\n      * `Ruff`: 一个用 Rust 编写的极快的 Python linter 和 formatter，可以替代 Flake8, isort, pydocstyle 等多种工具。\n  * **Formatters (代码自动格式化)**:\n      * `Black`: 一个固执己见 (opinionated) 的代码格式化工具，能自动将代码格式化为符合 PEP 8 子集的一致风格。\n      * `Autopep8`: 自动将 Python 代码格式化为符合 PEP 8 风格。\n      * `Ruff Formatter`: `Ruff` 也包含了格式化功能。\n  * **Type Checkers (静态类型检查)**:\n      * `MyPy`: Python 官方的静态类型检查器。\n      * `Pyright`: 由微软开发，快速且功能强大的类型检查器，也是 VS Code 中 Pylance 插件的核心。\n      * `Pytype`: Google 开发的类型检查器，可以推断类型。\n\n将这些工具集成到你的开发流程和持续集成 (CI) 系统中，可以有效地提升团队的整体代码质量和开发效率。\n\n-----'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E5%8D%81%E4%B8%80%E7%AB%A0%EF%BC%9A%E9%A1%B9%E7%9B%AE%E7%BB%93%E6%9E%84%E8%A7%84%E8%8C%83%E4%B8%8E%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5"><span class="toc-number">1.</span> <span class="toc-text">第二十一章：项目结构规范与最佳实践</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%B8%80%E3%80%81%E6%8E%A8%E8%8D%90%E7%9A%84-Python-%E9%A1%B9%E7%9B%AE%E7%BB%93%E6%9E%84"><span class="toc-number">1.1.</span> <span class="toc-text">一、推荐的 Python 项目结构</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%9B%AE%E5%BD%95%E6%A0%91%E6%A6%82%E8%A7%88"><span class="toc-number">1.1.1.</span> <span class="toc-text">1. 目录树概览</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%90%84%E9%83%A8%E5%88%86%E8%AF%A6%E8%A7%A3"><span class="toc-number">1.1.2.</span> <span class="toc-text">2. 各部分详解</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#a-my-project-%E9%A1%B9%E7%9B%AE%E6%A0%B9%E7%9B%AE%E5%BD%95"><span class="toc-number">1.1.2.1.</span> <span class="toc-text">a. my_project/ (项目根目录)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#b-my-package-%E4%B8%BB%E5%BA%94%E7%94%A8%E5%8C%85"><span class="toc-number">1.1.2.2.</span> <span class="toc-text">b. my_package/ (主应用包)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#c-tests-%E6%B5%8B%E8%AF%95%E7%9B%AE%E5%BD%95"><span class="toc-number">1.1.2.3.</span> <span class="toc-text">c. tests/ (测试目录)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#d-docs-%E6%96%87%E6%A1%A3%E7%9B%AE%E5%BD%95"><span class="toc-number">1.1.2.4.</span> <span class="toc-text">d. docs/ (文档目录)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#e-examples-%E7%A4%BA%E4%BE%8B%E4%BB%A3%E7%A0%81%E7%9B%AE%E5%BD%95"><span class="toc-number">1.1.2.5.</span> <span class="toc-text">e. examples/ (示例代码目录)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#f-gitignore"><span class="toc-number">1.1.2.6.</span> <span class="toc-text">f. .gitignore</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#g-LICENSE"><span class="toc-number">1.1.2.7.</span> <span class="toc-text">g. LICENSE</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#h-README-md"><span class="toc-number">1.1.2.8.</span> <span class="toc-text">h. README.md</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#i-requirements-txt"><span class="toc-number">1.1.2.9.</span> <span class="toc-text">i. requirements.txt</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#j-setup-py-%E6%88%96-pyproject-toml-setup-cfg"><span class="toc-number">1.1.2.10.</span> <span class="toc-text">j. setup.py (或 pyproject.toml + setup.cfg)</span></a></li></ol></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%BA%8C%E3%80%81%E4%BB%A3%E7%A0%81%E8%B4%A8%E9%87%8F%E4%B8%8E%E9%A3%8E%E6%A0%BC%E6%8C%87%E5%8D%97-PEP-8-%E6%A0%B8%E5%BF%83%E5%AE%9E%E8%B7%B5"><span class="toc-number">1.2.</span> <span class="toc-text">二、代码质量与风格指南 (PEP 8 核心实践)</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%B8%89%E3%80%81%E6%80%BB%E7%BB%93%E4%B8%8E%E5%B7%A5%E5%85%B7"><span class="toc-number">1.3.</span> <span class="toc-text">三、总结与工具</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>