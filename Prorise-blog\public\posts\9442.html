<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>SpringAI（十一）：11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="Java微服务篇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="SpringAI（十一）：11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑"><meta name="application-name" content="SpringAI（十一）：11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="SpringAI（十一）：11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑"><meta property="og:url" content="https://prorise666.site/posts/9442.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑 (v1.0+权威指南)在之前的章节里，我们已经让 AI 具备了强大的对话、理解和记忆能力。但它始终是一个“局外人”，被限制在自己的模型世界里，无法与我们应用程序的内部状态进行交互。它不知道当前用户的购物车里有什么，无法"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp"><meta name="description" content="11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑 (v1.0+权威指南)在之前的章节里，我们已经让 AI 具备了强大的对话、理解和记忆能力。但它始终是一个“局外人”，被限制在自己的模型世界里，无法与我们应用程序的内部状态进行交互。它不知道当前用户的购物车里有什么，无法"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/9442.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"SpringAI（十一）：11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑",postAI:"true",pageFillDescription:"11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑 (v1.0+权威指南), 11.1 Tool Calling 核心思想与流程, 11.2 核心API与定义工具的方式, 11.3 实战：构建一个数据库驱动的订单管理AI客服, 1. 第一步：环境准备 (MySQL amp MyBatis-Plus), 2. 第二步：定义数据访问层 (Entity amp Mapper), 3. 第三步：定义所有工具, 4. 第四步：构建统一的AI服务与API入口, 11.5 效果检验：测试统一的智能Agent, 测试场景一：AI智能选择OrderTools, 测试场景二：AI智能选择refundProcessor, 【关键】测试场景三：AI智能规划并连续调用多个工具函数调用让访问你的私有数据和内部逻辑权威指南在之前的章节里我们已经让具备了强大的对话理解和记忆能力但它始终是一个局外人被限制在自己的模型世界里无法与我们应用程序的内部状态进行交互它不知道当前用户的购物车里有什么无法查询一个特定订单的物流状态更不能帮用户执行一个取消订单的业务操作本章我们将解锁一项革命性的功能工具调用它为安装了连接应用内部世界的神经接口让它能够调用你编写的代码从而查询私有数据库执行复杂的业务逻辑核心思想与流程什么是工具调用工具调用是一种机制允许大语言模型在对话过程中智能地判断出用户的意图需要通过应用程序的内部功能来完成并生成一个结构化的对象来请求调用这个功能你的应用程序负责接收这个请求执行相应的本地方法然后将执行结果返回给再根据这个结果生成最终的自然语言答复必须理解的核心安全原则模型永远不会也永远无法直接执行您的代码它只是一个请求者您的应用程序是执行者整个流程是安全可控的核心与定义工具的方式提供了多种方式来定义一个工具本章我们将深入讲解两种最重要最常用的方式定义方式优点核心注解声明式方法最简单最直观代码可读性高适合将一个类中的多个相关方法打包成工具集函数式耦合度低更符合思想易于测试和替换适合定义单一独立的工具实战构建一个数据库驱动的订单管理客服我们将构建一个统一的服务它能够同时使用注解定义的订单管理工具和通过定义的退款工具并直接操作数据库第一步环境准备启动在项目根目录运行创建订单表插入一些测试数据实战指南编程思想高性能算法导论添加依赖配置第二步定义数据访问层第三步定义所有工具定义工具集用于查询和管理用户订单的数据库工具集根据订单查询订单状态要查询的订单从查询订单状态根据订单获取订单中的商品列表要查询的订单从查询订单商品取消一个用户的订单只有处理中的订单可以被取消要取消的订单尝试从取消订单订单未找到订单已成功取消订单无法取消因为它当前的状态是定义函数式工具为指定的订单和金额处理退款流程返回退款交易号和状态其中的代码与您之前的版本一致此处不再赘述第四步构建统一的服务与入口定义包创建统一的统一处理用户的自然语言请求并智能地调用所有可用的工具创建统一的统一的请求入口效果检验测试统一的智能现在我们所有的测试都将指向唯一的入口测试场景一智能选择请求我的订单里有什么东西后台日志从查询订单商品响应您的订单中包含的商品有实战指南编程思想测试场景二智能选择请求帮我给订单退款块钱后台日志正在为订单处理退款金额响应好的我已经为您处理了订单的元退款相关的交易号是关键测试场景三智能规划并连续调用多个工具请求我的订单是啥状态如果是处理中就帮我取消它然后给这个订单退款元后台日志将按顺序打印从查询订单状态尝试从取消订单正在为订单处理退款金额响应您好订单之前的状态是我已经成功为您取消了该订单并处理了一笔元的退款交易号是",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-08 13:53:33",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#11-Tool-Calling-%E5%87%BD%E6%95%B0%E8%B0%83%E7%94%A8-%EF%BC%9A%E8%AE%A9AI%E8%AE%BF%E9%97%AE%E4%BD%A0%E7%9A%84%E7%A7%81%E6%9C%89%E6%95%B0%E6%8D%AE%E5%92%8C%E5%86%85%E9%83%A8%E9%80%BB%E8%BE%91-v1-0-%E6%9D%83%E5%A8%81%E6%8C%87%E5%8D%97"><span class="toc-text">11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑 (v1.0+权威指南)</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-Tool-Calling-%E6%A0%B8%E5%BF%83%E6%80%9D%E6%83%B3%E4%B8%8E%E6%B5%81%E7%A8%8B"><span class="toc-text">11.1 Tool Calling 核心思想与流程</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-%E6%A0%B8%E5%BF%83API%E4%B8%8E%E5%AE%9A%E4%B9%89%E5%B7%A5%E5%85%B7%E7%9A%84%E6%96%B9%E5%BC%8F"><span class="toc-text">11.2 核心API与定义工具的方式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-%E5%AE%9E%E6%88%98%EF%BC%9A%E6%9E%84%E5%BB%BA%E4%B8%80%E4%B8%AA%E6%95%B0%E6%8D%AE%E5%BA%93%E9%A9%B1%E5%8A%A8%E7%9A%84%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86AI%E5%AE%A2%E6%9C%8D"><span class="toc-text">11.3 实战：构建一个数据库驱动的订单管理AI客服</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E7%8E%AF%E5%A2%83%E5%87%86%E5%A4%87-MySQL-MyBatis-Plus"><span class="toc-text">1. 第一步：环境准备 (MySQL &amp; MyBatis-Plus)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E5%AE%9A%E4%B9%89%E6%95%B0%E6%8D%AE%E8%AE%BF%E9%97%AE%E5%B1%82-Entity-Mapper"><span class="toc-text">2. 第二步：定义数据访问层 (Entity &amp; Mapper)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-%E7%AC%AC%E4%B8%89%E6%AD%A5%EF%BC%9A%E5%AE%9A%E4%B9%89%E6%89%80%E6%9C%89%E5%B7%A5%E5%85%B7"><span class="toc-text">3. 第三步：定义所有工具</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#4-%E7%AC%AC%E5%9B%9B%E6%AD%A5%EF%BC%9A%E6%9E%84%E5%BB%BA%E7%BB%9F%E4%B8%80%E7%9A%84AI%E6%9C%8D%E5%8A%A1%E4%B8%8EAPI%E5%85%A5%E5%8F%A3"><span class="toc-text">4. 第四步：构建统一的AI服务与API入口</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#11-5-%E6%95%88%E6%9E%9C%E6%A3%80%E9%AA%8C%EF%BC%9A%E6%B5%8B%E8%AF%95%E7%BB%9F%E4%B8%80%E7%9A%84%E6%99%BA%E8%83%BDAgent"><span class="toc-text">11.5 效果检验：测试统一的智能Agent</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%B5%8B%E8%AF%95%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9AAI%E6%99%BA%E8%83%BD%E9%80%89%E6%8B%A9OrderTools"><span class="toc-text">测试场景一：AI智能选择OrderTools</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%B5%8B%E8%AF%95%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9AAI%E6%99%BA%E8%83%BD%E9%80%89%E6%8B%A9refundProcessor"><span class="toc-text">测试场景二：AI智能选择refundProcessor</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E3%80%90%E5%85%B3%E9%94%AE%E3%80%91%E6%B5%8B%E8%AF%95%E5%9C%BA%E6%99%AF%E4%B8%89%EF%BC%9AAI%E6%99%BA%E8%83%BD%E8%A7%84%E5%88%92%E5%B9%B6%E8%BF%9E%E7%BB%AD%E8%B0%83%E7%94%A8%E5%A4%9A%E4%B8%AA%E5%B7%A5%E5%85%B7"><span class="toc-text">【关键】测试场景三：AI智能规划并连续调用多个工具</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Java微服务篇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">SpringAI（十一）：11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-03-21T14:13:45.000Z" title="发表于 2025-03-21 22:13:45">2025-03-21</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:33.802Z" title="更新于 2025-07-08 13:53:33">2025-07-08</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">2.2k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>9分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="SpringAI（十一）：11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/9442.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/9442.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/" itemprop="url">Java</a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" tabindex="-1" itemprop="url">Java微服务篇</a><h1 id="CrawlerTitle" itemprop="name headline">SpringAI（十一）：11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-03-21T14:13:45.000Z" title="发表于 2025-03-21 22:13:45">2025-03-21</time><time itemprop="dateCreated datePublished" datetime="2025-07-08T05:53:33.802Z" title="更新于 2025-07-08 13:53:33">2025-07-08</time></header><div id="postchat_postcontent"><h2 id="11-Tool-Calling-函数调用-：让AI访问你的私有数据和内部逻辑-v1-0-权威指南"><a href="#11-Tool-Calling-函数调用-：让AI访问你的私有数据和内部逻辑-v1-0-权威指南" class="headerlink" title="11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑 (v1.0+权威指南)"></a><strong>11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑 (v1.0+权威指南)</strong></h2><p>在之前的章节里，我们已经让 AI 具备了强大的对话、理解和记忆能力。但它始终是一个“局外人”，被限制在自己的模型世界里，无法与我们应用程序的<strong>内部状态</strong>进行交互。它不知道当前用户的购物车里有什么，无法查询一个特定订单的物流状态，更不能帮用户执行一个取消订单的业务操作。</p><p>本章，我们将解锁一项革命性的功能——<strong>工具调用（Tool Calling）</strong>，它为 AI 安装了连接应用内部世界的“神经接口”，让它能够调用你编写的 Java 代码，从而查询<strong>私有数据库</strong>、执行复杂的业务逻辑。</p><h3 id="11-1-Tool-Calling-核心思想与流程"><a href="#11-1-Tool-Calling-核心思想与流程" class="headerlink" title="11.1 Tool Calling 核心思想与流程"></a><strong>11.1 Tool Calling 核心思想与流程</strong></h3><blockquote><p><strong>什么是工具调用？</strong><br>工具调用是一种机制，允许大语言模型（LLM）在对话过程中，智能地判断出用户的意图需要通过<strong>应用程序的内部功能</strong>来完成，并生成一个结构化的 JSON 对象来请求调用这个功能。你的应用程序负责接收这个请求，执行相应的本地方法，然后将执行结果返回给 LLM，LLM 再根据这个结果，生成最终的自然语言答复。</p></blockquote><p><strong>必须理解的核心安全原则</strong>：模型永远不会、也永远无法直接执行您的代码。它只是一个<strong>请求者</strong>，您的应用程序是<strong>执行者</strong>。整个流程是安全可控的。</p><h3 id="11-2-核心API与定义工具的方式"><a href="#11-2-核心API与定义工具的方式" class="headerlink" title="11.2 核心API与定义工具的方式"></a><strong>11.2 核心API与定义工具的方式</strong></h3><p>Spring AI 1.0+ 提供了多种方式来定义一个工具，本章我们将深入讲解两种最重要、最常用的方式。</p><table><thead><tr><th align="left">定义方式</th><th align="left">优点</th><th align="left">核心API/注解</th></tr></thead><tbody><tr><td align="left"><strong>1. 声明式方法 (<code>@Tool</code>)</strong></td><td align="left"><strong>最简单、最直观</strong>，代码可读性高，适合将一个类中的多个相关方法打包成工具集。</td><td align="left"><code>@Tool</code>, <code>@ToolParam</code></td></tr><tr><td align="left"><strong>2. 函数式Bean (<code>@Bean</code>)</strong></td><td align="left"><strong>耦合度低，更符合Spring DI思想</strong>，易于测试和替换，适合定义单一、独立的工具。</td><td align="left"><code>@Bean</code>, <code>@Description</code></td></tr></tbody></table><h3 id="11-3-实战：构建一个数据库驱动的订单管理AI客服"><a href="#11-3-实战：构建一个数据库驱动的订单管理AI客服" class="headerlink" title="11.3 实战：构建一个数据库驱动的订单管理AI客服"></a><strong>11.3 实战：构建一个数据库驱动的订单管理AI客服</strong></h3><p>我们将构建一个统一的AI服务，它能够同时使用<code>@Tool</code>注解定义的<strong>订单管理工具 (<code>OrderTools</code>)和通过<code>@Bean</code>定义的退款工具 (<code>refundProcessor</code>)</strong>，并直接操作<strong>MySQL数据库</strong>。</p><h5 id="1-第一步：环境准备-MySQL-MyBatis-Plus"><a href="#1-第一步：环境准备-MySQL-MyBatis-Plus" class="headerlink" title="1. 第一步：环境准备 (MySQL &amp; MyBatis-Plus)"></a><strong>1. 第一步：环境准备 (MySQL &amp; MyBatis-Plus)</strong></h5><ul><li><p><strong>Docker启动MySQL</strong>:</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># docker-compose.yml</span></span><br><span class="line"><span class="attr">version:</span> <span class="string">'3.8'</span></span><br><span class="line"><span class="attr">services:</span></span><br><span class="line">  <span class="attr">mysql:</span></span><br><span class="line">    <span class="attr">image:</span> <span class="string">mysql:8.0</span></span><br><span class="line">    <span class="attr">container_name:</span> <span class="string">mysql-for-ai</span></span><br><span class="line">    <span class="attr">ports:</span></span><br><span class="line">      <span class="bullet">-</span> <span class="string">"3306:3306"</span></span><br><span class="line">    <span class="attr">environment:</span></span><br><span class="line">      <span class="bullet">-</span> <span class="string">MYSQL_ROOT_PASSWORD=root</span></span><br><span class="line">      <span class="bullet">-</span> <span class="string">MYSQL_DATABASE=spring_ai_db</span></span><br></pre></td></tr></tbody></table></figure><p>在项目根目录运行 <code>docker-compose up -d</code>。</p></li><li><p><strong>创建订单表</strong>:</p><figure class="highlight sql"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">CREATE TABLE</span> `orders` (</span><br><span class="line">  `id` <span class="type">varchar</span>(<span class="number">255</span>) <span class="keyword">NOT NULL</span>,</span><br><span class="line">  `status` <span class="type">varchar</span>(<span class="number">50</span>) <span class="keyword">DEFAULT</span> <span class="keyword">NULL</span>,</span><br><span class="line">  `products_json` json <span class="keyword">DEFAULT</span> <span class="keyword">NULL</span>,</span><br><span class="line">  <span class="keyword">PRIMARY KEY</span> (`id`)</span><br><span class="line">) ENGINE<span class="operator">=</span>InnoDB <span class="keyword">DEFAULT</span> CHARSET<span class="operator">=</span>utf8mb4;</span><br><span class="line"></span><br><span class="line"><span class="comment">-- 插入一些测试数据</span></span><br><span class="line"><span class="keyword">INSERT INTO</span> `orders` (id, status, products_json) <span class="keyword">VALUES</span></span><br><span class="line">(<span class="string">'ORD-001'</span>, <span class="string">'SHIPPED'</span>, <span class="string">'["Spring AI实战指南", "Java编程思想"]'</span>),</span><br><span class="line">(<span class="string">'ORD-002'</span>, <span class="string">'PROCESSING'</span>, <span class="string">'["高性能MySQL"]'</span>),</span><br><span class="line">(<span class="string">'ORD-003'</span>, <span class="string">'DELIVERED'</span>, <span class="string">'["算法导论"]'</span>);</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>添加Maven依赖</strong>:</p><figure class="highlight xml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">&lt;!-- MySQL Connector --&gt;</span></span><br><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>com.mysql<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>mysql-connector-j<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">scope</span>&gt;</span>runtime<span class="tag">&lt;/<span class="name">scope</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br><span class="line"><span class="comment">&lt;!-- MyBatis-Plus Starter --&gt;</span></span><br><span class="line"><span class="tag">&lt;<span class="name">dependency</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">groupId</span>&gt;</span>com.baomidou<span class="tag">&lt;/<span class="name">groupId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">artifactId</span>&gt;</span>mybatis-plus-spring-boot-starter<span class="tag">&lt;/<span class="name">artifactId</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">version</span>&gt;</span>3.5.7<span class="tag">&lt;/<span class="name">version</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">dependency</span>&gt;</span></span><br></pre></td></tr></tbody></table></figure></li><li><p><strong><code>application.yml</code>配置</strong>:</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">spring:</span></span><br><span class="line">  <span class="attr">datasource:</span></span><br><span class="line">    <span class="attr">url:</span> <span class="string">***********************************************************************************</span></span><br><span class="line">    <span class="attr">username:</span> <span class="string">root</span></span><br><span class="line">    <span class="attr">password:</span> <span class="string">root</span></span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="2-第二步：定义数据访问层-Entity-Mapper"><a href="#2-第二步：定义数据访问层-Entity-Mapper" class="headerlink" title="2. 第二步：定义数据访问层 (Entity &amp; Mapper)"></a><strong>2. 第二步：定义数据访问层 (Entity &amp; Mapper)</strong></h5><ul><li><p><strong><code>entity/Order.java</code></strong>:</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example.hellospringai.entity;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.baomidou.mybatisplus.annotation.IdType;</span><br><span class="line"><span class="keyword">import</span> com.baomidou.mybatisplus.annotation.TableField;</span><br><span class="line"><span class="keyword">import</span> com.baomidou.mybatisplus.annotation.TableId;</span><br><span class="line"><span class="keyword">import</span> com.baomidou.mybatisplus.annotation.TableName;</span><br><span class="line"><span class="keyword">import</span> com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;</span><br><span class="line"><span class="keyword">import</span> lombok.AllArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> lombok.Data;</span><br><span class="line"><span class="keyword">import</span> lombok.NoArgsConstructor;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Data</span></span><br><span class="line"><span class="meta">@NoArgsConstructor</span></span><br><span class="line"><span class="meta">@AllArgsConstructor</span></span><br><span class="line"><span class="meta">@TableName(value = "orders", autoResultMap = true)</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">Order</span> {</span><br><span class="line">    <span class="meta">@TableId(type = IdType.INPUT)</span></span><br><span class="line">    <span class="keyword">private</span> String id;</span><br><span class="line">    <span class="keyword">private</span> String status;</span><br><span class="line">    <span class="meta">@TableField(value = "products_json", typeHandler = JacksonTypeHandler.class)</span></span><br><span class="line">    <span class="keyword">private</span> List&lt;String&gt; products;</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong><code>mapper/OrderMapper.java</code></strong>:</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example.hellospringai.mapper;</span><br><span class="line"><span class="keyword">import</span> com.baomidou.mybatisplus.core.mapper.BaseMapper;</span><br><span class="line"><span class="keyword">import</span> com.example.hellospringai.entity.Order;</span><br><span class="line"><span class="keyword">import</span> org.apache.ibatis.annotations.Mapper;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Mapper</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">interface</span> <span class="title class_">OrderMapper</span> <span class="keyword">extends</span> <span class="title class_">BaseMapper</span>&lt;Order&gt; {}</span><br></pre></td></tr></tbody></table></figure></li></ul><h5 id="3-第三步：定义所有工具"><a href="#3-第三步：定义所有工具" class="headerlink" title="3. 第三步：定义所有工具"></a><strong>3. 第三步：定义所有工具</strong></h5><ul><li><p><strong>定义<code>@Tool</code>工具集 (<code>tool/OrderTools.java</code>)</strong>:</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example.hellospringai.tool;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.example.hellospringai.entity.Order;</span><br><span class="line"><span class="keyword">import</span> com.example.hellospringai.mapper.OrderMapper;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.tool.annotation.Tool;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.tool.annotation.ToolParam;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Description;</span><br><span class="line"><span class="keyword">import</span> org.springframework.stereotype.Component;</span><br><span class="line"><span class="keyword">import</span> java.util.List;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Component</span></span><br><span class="line"><span class="meta">@Description("用于查询和管理用户订单的数据库工具集")</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">OrderTools</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> OrderMapper orderMapper;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">OrderTools</span><span class="params">(OrderMapper orderMapper)</span> {</span><br><span class="line">        <span class="built_in">this</span>.orderMapper = orderMapper;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="keyword">record</span> <span class="title class_">CancelResponse</span><span class="params">(<span class="type">boolean</span> success, String message)</span> {}</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Tool(description = "根据订单ID查询订单状态")</span></span><br><span class="line">    <span class="keyword">public</span> String <span class="title function_">getOrderStatus</span><span class="params">(<span class="meta">@ToolParam(description = "要查询的订单ID")</span> String orderId)</span> {</span><br><span class="line">        System.out.printf(<span class="string">"&gt;&gt;&gt; [Tool Executing] 从MySQL查询订单状态，ID: %s%n"</span>, orderId);</span><br><span class="line">        <span class="type">Order</span> <span class="variable">order</span> <span class="operator">=</span> orderMapper.selectById(orderId);</span><br><span class="line">        <span class="keyword">return</span> (order != <span class="literal">null</span>) ? order.getStatus() : <span class="string">"NOT_FOUND"</span>;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Tool(description = "根据订单ID获取订单中的商品列表")</span></span><br><span class="line">    <span class="keyword">public</span> List&lt;String&gt; <span class="title function_">getOrderProducts</span><span class="params">(<span class="meta">@ToolParam(description = "要查询的订单ID")</span> String orderId)</span> {</span><br><span class="line">        System.out.printf(<span class="string">"&gt;&gt;&gt; [Tool Executing] 从MySQL查询订单商品，ID: %s%n"</span>, orderId);</span><br><span class="line">        <span class="type">Order</span> <span class="variable">order</span> <span class="operator">=</span> orderMapper.selectById(orderId);</span><br><span class="line">        <span class="keyword">return</span> (order != <span class="literal">null</span>) ? order.getProducts() : List.of();</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Tool(description = "取消一个用户的订单。只有处理中(PROCESSING)的订单可以被取消。")</span></span><br><span class="line">    <span class="keyword">public</span> CancelResponse <span class="title function_">cancelOrder</span><span class="params">(<span class="meta">@ToolParam(description = "要取消的订单ID")</span> String orderId)</span> {</span><br><span class="line">        System.out.printf(<span class="string">"&gt;&gt;&gt; [Tool Executing] 尝试从MySQL取消订单，ID: %s%n"</span>, orderId);</span><br><span class="line">        <span class="type">Order</span> <span class="variable">order</span> <span class="operator">=</span> orderMapper.selectById(orderId);</span><br><span class="line"></span><br><span class="line">        <span class="keyword">if</span> (order == <span class="literal">null</span>) {</span><br><span class="line">            <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">CancelResponse</span>(<span class="literal">false</span>, <span class="string">"订单未找到。"</span>);</span><br><span class="line">        }</span><br><span class="line">        <span class="keyword">if</span> (<span class="string">"PROCESSING"</span>.equals(order.getStatus())) {</span><br><span class="line">            order.setStatus(<span class="string">"CANCELLED"</span>);</span><br><span class="line">            orderMapper.updateById(order);</span><br><span class="line">            <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">CancelResponse</span>(<span class="literal">true</span>, <span class="string">"订单 "</span> + orderId + <span class="string">" 已成功取消。"</span>);</span><br><span class="line">        }</span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">CancelResponse</span>(<span class="literal">false</span>, <span class="string">"订单无法取消，因为它当前的状态是: "</span> + order.getStatus());</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>定义<code>@Bean</code>函数式工具 (<code>config/ToolConfiguration.java</code>)</strong>:</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example.hellospringai.config;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.example.hellospringai.service.PaymentService;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Bean;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Configuration;</span><br><span class="line"><span class="keyword">import</span> org.springframework.context.annotation.Description;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> java.util.function.Function;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Configuration</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">ToolConfiguration</span> {</span><br><span class="line"></span><br><span class="line">    <span class="meta">@Bean("refundProcessor")</span></span><br><span class="line">    <span class="meta">@Description("为指定的订单ID和金额处理退款流程，返回退款交易号和状态")</span> </span><br><span class="line">    <span class="keyword">public</span> Function&lt;PaymentService.RefundRequest, PaymentService.RefundResponse&gt; refundProcessor() {</span><br><span class="line">        <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">PaymentService</span>();</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><p>(其中 <code>service/PaymentService.java</code> 的代码与您之前的版本一致，此处不再赘述)</p></li></ul><h5 id="4-第四步：构建统一的AI服务与API入口"><a href="#4-第四步：构建统一的AI服务与API入口" class="headerlink" title="4. 第四步：构建统一的AI服务与API入口"></a><strong>4. 第四步：构建统一的AI服务与API入口</strong></h5><ul><li><p><strong>定义DTO (<code>dto</code>包)</strong>:</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example.hellospringai.dto;</span><br><span class="line"><span class="keyword">public</span> <span class="keyword">record</span> <span class="title class_">ToolRequest</span><span class="params">(String message)</span> {}</span><br><span class="line"></span><br><span class="line"><span class="keyword">package</span> com.example.hellospringai.dto;</span><br><span class="line"><span class="keyword">public</span> <span class="keyword">record</span> <span class="title class_">AiResponse</span><span class="params">(String message, Object data)</span> {}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>创建统一的Service (<code>service/UnifiedAiService.java</code>)</strong>:</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example.hellospringai.service;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.example.hellospringai.tool.OrderTools;</span><br><span class="line"><span class="keyword">import</span> org.springframework.ai.chat.client.ChatClient;</span><br><span class="line"><span class="keyword">import</span> org.springframework.stereotype.Service;</span><br><span class="line"></span><br><span class="line"><span class="meta">@Service</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">UnifiedAiService</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> ChatClient chatClient;</span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> OrderTools orderTools;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">UnifiedAiService</span><span class="params">(ChatClient.Builder chatClientBuilder, OrderTools orderTools)</span> {</span><br><span class="line">        <span class="built_in">this</span>.orderTools = orderTools;</span><br><span class="line">        <span class="built_in">this</span>.chatClient = chatClientBuilder.build();</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 统一处理用户的自然语言请求，并智能地调用所有可用的工具。</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="keyword">public</span> String <span class="title function_">processUserRequest</span><span class="params">(String message)</span> {</span><br><span class="line">        <span class="keyword">return</span> chatClient.prompt()</span><br><span class="line">                .user(message)</span><br><span class="line">                .tools(orderTools)</span><br><span class="line">                .call()</span><br><span class="line">                .content();</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li><li><p><strong>创建统一的Controller (<code>controller/UnifiedAiController.java</code>)</strong>:</p><figure class="highlight java"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">package</span> com.example.hellospringai.controller;</span><br><span class="line"></span><br><span class="line"><span class="keyword">import</span> com.example.hellospringai.dto.AiResponse;</span><br><span class="line"><span class="keyword">import</span> com.example.hellospringai.dto.ToolRequest;</span><br><span class="line"><span class="keyword">import</span> com.example.hellospringai.service.UnifiedAiService;</span><br><span class="line"><span class="keyword">import</span> org.springframework.http.ResponseEntity;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.PostMapping;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.RequestBody;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.RequestMapping;</span><br><span class="line"><span class="keyword">import</span> org.springframework.web.bind.annotation.RestController;</span><br><span class="line"></span><br><span class="line"><span class="meta">@RestController</span></span><br><span class="line"><span class="meta">@RequestMapping("/ai")</span></span><br><span class="line"><span class="keyword">public</span> <span class="keyword">class</span> <span class="title class_">UnifiedAiController</span> {</span><br><span class="line"></span><br><span class="line">    <span class="keyword">private</span> <span class="keyword">final</span> UnifiedAiService unifiedAiService;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">public</span> <span class="title function_">UnifiedAiController</span><span class="params">(UnifiedAiService unifiedAiService)</span> {</span><br><span class="line">        <span class="built_in">this</span>.unifiedAiService = unifiedAiService;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">/**</span></span><br><span class="line"><span class="comment">     * 统一的AI请求入口</span></span><br><span class="line"><span class="comment">     */</span></span><br><span class="line">    <span class="meta">@PostMapping("/request")</span></span><br><span class="line">    <span class="keyword">public</span> ResponseEntity&lt;AiResponse&gt; <span class="title function_">handleRequest</span><span class="params">(<span class="meta">@RequestBody</span> ToolRequest request)</span> {</span><br><span class="line">        <span class="type">String</span> <span class="variable">content</span> <span class="operator">=</span> unifiedAiService.processUserRequest(request.message());</span><br><span class="line">        <span class="keyword">return</span> ResponseEntity.ok(<span class="keyword">new</span> <span class="title class_">AiResponse</span>(content, <span class="literal">null</span>));</span><br><span class="line">    }</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure></li></ul><h4 id="11-5-效果检验：测试统一的智能Agent"><a href="#11-5-效果检验：测试统一的智能Agent" class="headerlink" title="11.5 效果检验：测试统一的智能Agent"></a><strong>11.5 效果检验：测试统一的智能Agent</strong></h4><p>现在，我们所有的测试都将指向唯一的入口 <code>POST /ai/request</code>。</p><h5 id="测试场景一：AI智能选择OrderTools"><a href="#测试场景一：AI智能选择OrderTools" class="headerlink" title="测试场景一：AI智能选择OrderTools"></a><strong>测试场景一：AI智能选择<code>OrderTools</code></strong></h5><ul><li><strong>请求</strong>: <code>POST http://localhost:8080/ai/request</code></li><li><strong>Body</strong>: <code>{"message": "我的订单ORD-001里有什么东西？"}</code></li><li><strong>后台日志</strong>: <code>&gt;&gt;&gt; [Tool Executing] 从MySQL查询订单商品，ID: ORD-001</code></li><li><strong>API响应</strong>: <code>"您的订单ORD-001中包含的商品有：[Spring AI实战指南, Java编程思想]。"</code></li></ul><h5 id="测试场景二：AI智能选择refundProcessor"><a href="#测试场景二：AI智能选择refundProcessor" class="headerlink" title="测试场景二：AI智能选择refundProcessor"></a><strong>测试场景二：AI智能选择<code>refundProcessor</code></strong></h5><ul><li><strong>请求</strong>: <code>POST http://localhost:8080/ai/request</code></li><li><strong>Body</strong>: <code>{"message": "帮我给订单ORD-003退款50块钱"}</code></li><li><strong>后台日志</strong>: <code>&gt;&gt;&gt; [Function Bean Executing] 正在为订单 ORD-003 处理退款，金额: 50.00</code></li><li><strong>API响应</strong>: <code>"好的，我已经为您处理了订单ORD-003的50.0元退款，相关的交易号是TXN-xxxxxxxxxxxxx。"</code></li></ul><h5 id="【关键】测试场景三：AI智能规划并连续调用多个工具"><a href="#【关键】测试场景三：AI智能规划并连续调用多个工具" class="headerlink" title="【关键】测试场景三：AI智能规划并连续调用多个工具"></a><strong>【关键】测试场景三：AI智能规划并连续调用多个工具</strong></h5><ul><li><strong>请求</strong>: <code>POST http://localhost:8080/ai/request</code></li><li><strong>Body</strong>: <code>{"message": "我的订单ORD-002是啥状态？如果是处理中，就帮我取消它，然后给这个订单退款100元。"}</code></li><li><strong>后台日志 (将按顺序打印)</strong>:<figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line">&gt;&gt;&gt; [Tool Executing] 从MySQL查询订单状态，ID: ORD-002</span><br><span class="line">&gt;&gt;&gt; [Tool Executing] 尝试从MySQL取消订单，ID: ORD-002</span><br><span class="line">&gt;&gt;&gt; [Function Bean Executing] 正在为订单 ORD-002 处理退款，金额: 100.00</span><br></pre></td></tr></tbody></table></figure></li><li><strong>API响应</strong>: <code>"您好，订单ORD-002之前的状态是PROCESSING。我已经成功为您取消了该订单，并处理了一笔100.0元的退款，交易号是TXN-xxxxxxxxxxxxx。"</code></li></ul></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/9442.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/9442.html&quot;)">SpringAI（十一）：11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/9442.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/9442.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Java/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Java<span class="categoryesPageCount">20</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Java微服务篇<span class="tagsPageCount">11</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/64777.html"><img class="prev-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">SpringAI（十）：10. ETL 框架与 Spring Batch：构建工业级数据管道</div></div></a></div><div class="next-post pull-right"><a href="/posts/17730.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Python（一）：Python 语言特性</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/5770.html" title="SpringAI（七）：7. Embedding Models：万物皆可向量化"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（七）：7. Embedding Models：万物皆可向量化</div></div></a></div><div><a href="/posts/59358.html" title="SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（一）：1. 序章：迎接 Java AI 开发新纪元</div></div></a></div><div><a href="/posts/52289.html" title="SpringAI（三）：3. 会话核心 API 深度解析"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（三）：3. 会话核心 API 深度解析</div></div></a></div><div><a href="/posts/18714.html" title="SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-20</div><div class="title">SpringAI（二）：2. 快速入门：构建你的第一个 AI 应用</div></div></a></div><div><a href="/posts/22322.html" title="SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（九）：9. RAG 检索增强生成：AI 的“开卷考试”</div></div></a></div><div><a href="/posts/60609.html" title="SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/07/947856.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-03-21</div><div class="title">SpringAI（五）：5. 连接AI大脑：Chat Models 对接指南</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"SpringAI（十一）：11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑",date:"2025-03-21 22:13:45",updated:"2025-07-08 13:53:33",tags:["Java微服务篇"],categories:["后端技术","Java"],content:'\n## **11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑 (v1.0+权威指南)**\n\n在之前的章节里，我们已经让 AI 具备了强大的对话、理解和记忆能力。但它始终是一个“局外人”，被限制在自己的模型世界里，无法与我们应用程序的**内部状态**进行交互。它不知道当前用户的购物车里有什么，无法查询一个特定订单的物流状态，更不能帮用户执行一个取消订单的业务操作。\n\n本章，我们将解锁一项革命性的功能——**工具调用（Tool Calling）**，它为 AI 安装了连接应用内部世界的“神经接口”，让它能够调用你编写的 Java 代码，从而查询**私有数据库**、执行复杂的业务逻辑。\n\n### **11.1 Tool Calling 核心思想与流程**\n\n> **什么是工具调用？**\n> 工具调用是一种机制，允许大语言模型（LLM）在对话过程中，智能地判断出用户的意图需要通过**应用程序的内部功能**来完成，并生成一个结构化的 JSON 对象来请求调用这个功能。你的应用程序负责接收这个请求，执行相应的本地方法，然后将执行结果返回给 LLM，LLM 再根据这个结果，生成最终的自然语言答复。\n\n**必须理解的核心安全原则**：模型永远不会、也永远无法直接执行您的代码。它只是一个**请求者**，您的应用程序是**执行者**。整个流程是安全可控的。\n\n### **11.2 核心API与定义工具的方式**\n\nSpring AI 1.0+ 提供了多种方式来定义一个工具，本章我们将深入讲解两种最重要、最常用的方式。\n\n| 定义方式 | 优点 | 核心API/注解 |\n| :--- | :--- | :--- |\n| **1. 声明式方法 (`@Tool`)** | **最简单、最直观**，代码可读性高，适合将一个类中的多个相关方法打包成工具集。 | `@Tool`, `@ToolParam` |\n| **2. 函数式Bean (`@Bean`)** | **耦合度低，更符合Spring DI思想**，易于测试和替换，适合定义单一、独立的工具。 | `@Bean`, `@Description` |\n\n### **11.3 实战：构建一个数据库驱动的订单管理AI客服**\n\n我们将构建一个统一的AI服务，它能够同时使用`@Tool`注解定义的**订单管理工具 (`OrderTools`)和通过`@Bean`定义的退款工具 (`refundProcessor`)**，并直接操作**MySQL数据库**。\n\n##### **1. 第一步：环境准备 (MySQL & MyBatis-Plus)**\n\n  * **Docker启动MySQL**:\n\n    ```yaml\n    # docker-compose.yml\n    version: \'3.8\'\n    services:\n      mysql:\n        image: mysql:8.0\n        container_name: mysql-for-ai\n        ports:\n          - "3306:3306"\n        environment:\n          - MYSQL_ROOT_PASSWORD=root\n          - MYSQL_DATABASE=spring_ai_db\n    ```\n\n    在项目根目录运行 `docker-compose up -d`。\n\n  * **创建订单表**:\n\n    ```sql\n    CREATE TABLE `orders` (\n      `id` varchar(255) NOT NULL,\n      `status` varchar(50) DEFAULT NULL,\n      `products_json` json DEFAULT NULL,\n      PRIMARY KEY (`id`)\n    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;\n\n    -- 插入一些测试数据\n    INSERT INTO `orders` (id, status, products_json) VALUES\n    (\'ORD-001\', \'SHIPPED\', \'["Spring AI实战指南", "Java编程思想"]\'),\n    (\'ORD-002\', \'PROCESSING\', \'["高性能MySQL"]\'),\n    (\'ORD-003\', \'DELIVERED\', \'["算法导论"]\');\n    ```\n\n  * **添加Maven依赖**:\n\n    ```xml\n    \x3c!-- MySQL Connector --\x3e\n    <dependency>\n        <groupId>com.mysql</groupId>\n        <artifactId>mysql-connector-j</artifactId>\n        <scope>runtime</scope>\n    </dependency>\n    \x3c!-- MyBatis-Plus Starter --\x3e\n    <dependency>\n        <groupId>com.baomidou</groupId>\n        <artifactId>mybatis-plus-spring-boot-starter</artifactId>\n        <version>3.5.7</version>\n    </dependency>\n    ```\n\n  * **`application.yml`配置**:\n\n    ```yaml\n    spring:\n      datasource:\n        url: *********************************************************************************        username: root\n        password: root\n    ```\n\n##### **2. 第二步：定义数据访问层 (Entity & Mapper)**\n\n  * **`entity/Order.java`**:\n\n    ```java\n    package com.example.hellospringai.entity;\n\n    import com.baomidou.mybatisplus.annotation.IdType;\n    import com.baomidou.mybatisplus.annotation.TableField;\n    import com.baomidou.mybatisplus.annotation.TableId;\n    import com.baomidou.mybatisplus.annotation.TableName;\n    import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;\n    import lombok.AllArgsConstructor;\n    import lombok.Data;\n    import lombok.NoArgsConstructor;\n    import java.util.List;\n\n    @Data\n    @NoArgsConstructor\n    @AllArgsConstructor\n    @TableName(value = "orders", autoResultMap = true)\n    public class Order {\n        @TableId(type = IdType.INPUT)\n        private String id;\n        private String status;\n        @TableField(value = "products_json", typeHandler = JacksonTypeHandler.class)\n        private List<String> products;\n    }\n    ```\n\n  * **`mapper/OrderMapper.java`**:\n\n    ```java\n    package com.example.hellospringai.mapper;\n    import com.baomidou.mybatisplus.core.mapper.BaseMapper;\n    import com.example.hellospringai.entity.Order;\n    import org.apache.ibatis.annotations.Mapper;\n\n    @Mapper\n    public interface OrderMapper extends BaseMapper<Order> {}\n    ```\n\n##### **3. 第三步：定义所有工具**\n\n  * **定义`@Tool`工具集 (`tool/OrderTools.java`)**:\n\n    ```java\n    package com.example.hellospringai.tool;\n\n    import com.example.hellospringai.entity.Order;\n    import com.example.hellospringai.mapper.OrderMapper;\n    import org.springframework.ai.tool.annotation.Tool;\n    import org.springframework.ai.tool.annotation.ToolParam;\n    import org.springframework.context.annotation.Description;\n    import org.springframework.stereotype.Component;\n    import java.util.List;\n\n    @Component\n    @Description("用于查询和管理用户订单的数据库工具集")\n    public class OrderTools {\n\n        private final OrderMapper orderMapper;\n\n        public OrderTools(OrderMapper orderMapper) {\n            this.orderMapper = orderMapper;\n        }\n\n        public record CancelResponse(boolean success, String message) {}\n\n        @Tool(description = "根据订单ID查询订单状态")\n        public String getOrderStatus(@ToolParam(description = "要查询的订单ID") String orderId) {\n            System.out.printf(">>> [Tool Executing] 从MySQL查询订单状态，ID: %s%n", orderId);\n            Order order = orderMapper.selectById(orderId);\n            return (order != null) ? order.getStatus() : "NOT_FOUND";\n        }\n\n        @Tool(description = "根据订单ID获取订单中的商品列表")\n        public List<String> getOrderProducts(@ToolParam(description = "要查询的订单ID") String orderId) {\n            System.out.printf(">>> [Tool Executing] 从MySQL查询订单商品，ID: %s%n", orderId);\n            Order order = orderMapper.selectById(orderId);\n            return (order != null) ? order.getProducts() : List.of();\n        }\n\n        @Tool(description = "取消一个用户的订单。只有处理中(PROCESSING)的订单可以被取消。")\n        public CancelResponse cancelOrder(@ToolParam(description = "要取消的订单ID") String orderId) {\n            System.out.printf(">>> [Tool Executing] 尝试从MySQL取消订单，ID: %s%n", orderId);\n            Order order = orderMapper.selectById(orderId);\n\n            if (order == null) {\n                return new CancelResponse(false, "订单未找到。");\n            }\n            if ("PROCESSING".equals(order.getStatus())) {\n                order.setStatus("CANCELLED");\n                orderMapper.updateById(order);\n                return new CancelResponse(true, "订单 " + orderId + " 已成功取消。");\n            }\n            return new CancelResponse(false, "订单无法取消，因为它当前的状态是: " + order.getStatus());\n        }\n    }\n    ```\n\n  * **定义`@Bean`函数式工具 (`config/ToolConfiguration.java`)**:\n\n    ```java\n    package com.example.hellospringai.config;\n\n    import com.example.hellospringai.service.PaymentService;\n    import org.springframework.context.annotation.Bean;\n    import org.springframework.context.annotation.Configuration;\n    import org.springframework.context.annotation.Description;\n\n    import java.util.function.Function;\n\n    @Configuration\n    public class ToolConfiguration {\n\n        @Bean("refundProcessor")\n        @Description("为指定的订单ID和金额处理退款流程，返回退款交易号和状态") \n        public Function<PaymentService.RefundRequest, PaymentService.RefundResponse> refundProcessor() {\n            return new PaymentService();\n        }\n    }\n    ```\n\n    (其中 `service/PaymentService.java` 的代码与您之前的版本一致，此处不再赘述)\n\n##### **4. 第四步：构建统一的AI服务与API入口**\n\n  * **定义DTO (`dto`包)**:\n\n    ```java\n    package com.example.hellospringai.dto;\n    public record ToolRequest(String message) {}\n\n    package com.example.hellospringai.dto;\n    public record AiResponse(String message, Object data) {}\n    ```\n\n  * **创建统一的Service (`service/UnifiedAiService.java`)**:\n\n    ```java\n    package com.example.hellospringai.service;\n\n    import com.example.hellospringai.tool.OrderTools;\n    import org.springframework.ai.chat.client.ChatClient;\n    import org.springframework.stereotype.Service;\n\n    @Service\n    public class UnifiedAiService {\n\n        private final ChatClient chatClient;\n        private final OrderTools orderTools;\n\n        public UnifiedAiService(ChatClient.Builder chatClientBuilder, OrderTools orderTools) {\n            this.orderTools = orderTools;\n            this.chatClient = chatClientBuilder.build();\n        }\n\n        /**\n         * 统一处理用户的自然语言请求，并智能地调用所有可用的工具。\n         */\n        public String processUserRequest(String message) {\n            return chatClient.prompt()\n                    .user(message)\n                    .tools(orderTools)\n                    .call()\n                    .content();\n        }\n    }\n    ```\n\n  * **创建统一的Controller (`controller/UnifiedAiController.java`)**:\n\n    ```java\n    package com.example.hellospringai.controller;\n\n    import com.example.hellospringai.dto.AiResponse;\n    import com.example.hellospringai.dto.ToolRequest;\n    import com.example.hellospringai.service.UnifiedAiService;\n    import org.springframework.http.ResponseEntity;\n    import org.springframework.web.bind.annotation.PostMapping;\n    import org.springframework.web.bind.annotation.RequestBody;\n    import org.springframework.web.bind.annotation.RequestMapping;\n    import org.springframework.web.bind.annotation.RestController;\n\n    @RestController\n    @RequestMapping("/ai")\n    public class UnifiedAiController {\n\n        private final UnifiedAiService unifiedAiService;\n\n        public UnifiedAiController(UnifiedAiService unifiedAiService) {\n            this.unifiedAiService = unifiedAiService;\n        }\n\n        /**\n         * 统一的AI请求入口\n         */\n        @PostMapping("/request")\n        public ResponseEntity<AiResponse> handleRequest(@RequestBody ToolRequest request) {\n            String content = unifiedAiService.processUserRequest(request.message());\n            return ResponseEntity.ok(new AiResponse(content, null));\n        }\n    }\n    ```\n\n#### **11.5 效果检验：测试统一的智能Agent**\n\n现在，我们所有的测试都将指向唯一的入口 `POST /ai/request`。\n\n##### **测试场景一：AI智能选择`OrderTools`**\n\n  * **请求**: `POST http://localhost:8080/ai/request`\n  * **Body**: `{"message": "我的订单ORD-001里有什么东西？"}`\n  * **后台日志**: `>>> [Tool Executing] 从MySQL查询订单商品，ID: ORD-001`\n  * **API响应**: `"您的订单ORD-001中包含的商品有：[Spring AI实战指南, Java编程思想]。"`\n\n##### **测试场景二：AI智能选择`refundProcessor`**\n\n  * **请求**: `POST http://localhost:8080/ai/request`\n  * **Body**: `{"message": "帮我给订单ORD-003退款50块钱"}`\n  * **后台日志**: `>>> [Function Bean Executing] 正在为订单 ORD-003 处理退款，金额: 50.00`\n  * **API响应**: `"好的，我已经为您处理了订单ORD-003的50.0元退款，相关的交易号是TXN-xxxxxxxxxxxxx。"`\n\n##### **【关键】测试场景三：AI智能规划并连续调用多个工具**\n\n  * **请求**: `POST http://localhost:8080/ai/request`\n  * **Body**: `{"message": "我的订单ORD-002是啥状态？如果是处理中，就帮我取消它，然后给这个订单退款100元。"}`\n  * **后台日志 (将按顺序打印)**:\n    ```\n    >>> [Tool Executing] 从MySQL查询订单状态，ID: ORD-002\n    >>> [Tool Executing] 尝试从MySQL取消订单，ID: ORD-002\n    >>> [Function Bean Executing] 正在为订单 ORD-002 处理退款，金额: 100.00\n    ```\n  * **API响应**: `"您好，订单ORD-002之前的状态是PROCESSING。我已经成功为您取消了该订单，并处理了一笔100.0元的退款，交易号是TXN-xxxxxxxxxxxxx。"`'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#11-Tool-Calling-%E5%87%BD%E6%95%B0%E8%B0%83%E7%94%A8-%EF%BC%9A%E8%AE%A9AI%E8%AE%BF%E9%97%AE%E4%BD%A0%E7%9A%84%E7%A7%81%E6%9C%89%E6%95%B0%E6%8D%AE%E5%92%8C%E5%86%85%E9%83%A8%E9%80%BB%E8%BE%91-v1-0-%E6%9D%83%E5%A8%81%E6%8C%87%E5%8D%97"><span class="toc-number">1.</span> <span class="toc-text">11. Tool Calling (函数调用)：让AI访问你的私有数据和内部逻辑 (v1.0+权威指南)</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#11-1-Tool-Calling-%E6%A0%B8%E5%BF%83%E6%80%9D%E6%83%B3%E4%B8%8E%E6%B5%81%E7%A8%8B"><span class="toc-number">1.1.</span> <span class="toc-text">11.1 Tool Calling 核心思想与流程</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-2-%E6%A0%B8%E5%BF%83API%E4%B8%8E%E5%AE%9A%E4%B9%89%E5%B7%A5%E5%85%B7%E7%9A%84%E6%96%B9%E5%BC%8F"><span class="toc-number">1.2.</span> <span class="toc-text">11.2 核心API与定义工具的方式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#11-3-%E5%AE%9E%E6%88%98%EF%BC%9A%E6%9E%84%E5%BB%BA%E4%B8%80%E4%B8%AA%E6%95%B0%E6%8D%AE%E5%BA%93%E9%A9%B1%E5%8A%A8%E7%9A%84%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86AI%E5%AE%A2%E6%9C%8D"><span class="toc-number">1.3.</span> <span class="toc-text">11.3 实战：构建一个数据库驱动的订单管理AI客服</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#1-%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9A%E7%8E%AF%E5%A2%83%E5%87%86%E5%A4%87-MySQL-MyBatis-Plus"><span class="toc-number">1.3.0.1.</span> <span class="toc-text">1. 第一步：环境准备 (MySQL &amp; MyBatis-Plus)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#2-%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9A%E5%AE%9A%E4%B9%89%E6%95%B0%E6%8D%AE%E8%AE%BF%E9%97%AE%E5%B1%82-Entity-Mapper"><span class="toc-number">1.3.0.2.</span> <span class="toc-text">2. 第二步：定义数据访问层 (Entity &amp; Mapper)</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#3-%E7%AC%AC%E4%B8%89%E6%AD%A5%EF%BC%9A%E5%AE%9A%E4%B9%89%E6%89%80%E6%9C%89%E5%B7%A5%E5%85%B7"><span class="toc-number">1.3.0.3.</span> <span class="toc-text">3. 第三步：定义所有工具</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#4-%E7%AC%AC%E5%9B%9B%E6%AD%A5%EF%BC%9A%E6%9E%84%E5%BB%BA%E7%BB%9F%E4%B8%80%E7%9A%84AI%E6%9C%8D%E5%8A%A1%E4%B8%8EAPI%E5%85%A5%E5%8F%A3"><span class="toc-number">1.3.0.4.</span> <span class="toc-text">4. 第四步：构建统一的AI服务与API入口</span></a></li></ol></li><li class="toc-item toc-level-4"><a class="toc-link" href="#11-5-%E6%95%88%E6%9E%9C%E6%A3%80%E9%AA%8C%EF%BC%9A%E6%B5%8B%E8%AF%95%E7%BB%9F%E4%B8%80%E7%9A%84%E6%99%BA%E8%83%BDAgent"><span class="toc-number">1.3.1.</span> <span class="toc-text">11.5 效果检验：测试统一的智能Agent</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%B5%8B%E8%AF%95%E5%9C%BA%E6%99%AF%E4%B8%80%EF%BC%9AAI%E6%99%BA%E8%83%BD%E9%80%89%E6%8B%A9OrderTools"><span class="toc-number">1.3.1.1.</span> <span class="toc-text">测试场景一：AI智能选择OrderTools</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E6%B5%8B%E8%AF%95%E5%9C%BA%E6%99%AF%E4%BA%8C%EF%BC%9AAI%E6%99%BA%E8%83%BD%E9%80%89%E6%8B%A9refundProcessor"><span class="toc-number">1.3.1.2.</span> <span class="toc-text">测试场景二：AI智能选择refundProcessor</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#%E3%80%90%E5%85%B3%E9%94%AE%E3%80%91%E6%B5%8B%E8%AF%95%E5%9C%BA%E6%99%AF%E4%B8%89%EF%BC%9AAI%E6%99%BA%E8%83%BD%E8%A7%84%E5%88%92%E5%B9%B6%E8%BF%9E%E7%BB%AD%E8%B0%83%E7%94%A8%E5%A4%9A%E4%B8%AA%E5%B7%A5%E5%85%B7"><span class="toc-number">1.3.1.3.</span> <span class="toc-text">【关键】测试场景三：AI智能规划并连续调用多个工具</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>