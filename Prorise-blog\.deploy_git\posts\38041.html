<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理入门（九）：第九章：平台端设计（用户-内容-运营） | Prorise - 分享技术与实战经验</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><meta name="application-name" content="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><meta property="og:url" content="https://prorise666.site/posts/38041.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="第九章：平台端设计（用户-内容-运营） 在前面两个章节，我们已经为产品的“前台”——用户端和自媒体端，设计好了舞台和化妆间。现在，我们需要开始搭建整个剧院的“后台”——平台端（Admin Backend）。 这是我们作为平台运营和管理人员，进行日常工作的“驾驶舱”，是整个产品生态能够有序、健康运转的"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta name="description" content="第九章：平台端设计（用户-内容-运营） 在前面两个章节，我们已经为产品的“前台”——用户端和自媒体端，设计好了舞台和化妆间。现在，我们需要开始搭建整个剧院的“后台”——平台端（Admin Backend）。 这是我们作为平台运营和管理人员，进行日常工作的“驾驶舱”，是整个产品生态能够有序、健康运转的"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/38041.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"产品经理入门（九）：第九章：平台端设计（用户-内容-运营）",postAI:"true",pageFillDescription:"第九章：平台端设计（用户-内容-运营）, 9.1 用户管理, 9.1.1 学习目标, 9.1.2 普通用户管理, 1. 用户列表与查询, 2. 用户详情查看, 3. 用户状态管理（封禁x2F解封）, 9.1.3 自媒体用户管理, 1. 创作者列表与查询, 2. 自媒体审核, 9.1.4 马甲管理, 1. 马甲管理的价值与鲶鱼效应, 2. 添加马甲, 3. 马甲列表, 9.2 内容管理, 9.2.1 学习目标, 9.2.2 内容列表, 1. 内容查询与筛选, 2. 内容审核（通过x2F驳回x2F下架）, 3. 内容推荐与加权, 9.2.3 敏感词管理, 1. 敏感词库维护（增x2F删x2F改x2F查）, 2. 敏感词分类与处理规则, 9.2.4 分类管理, 1. 频道x2F分类管理, 2. 标签管理, 9.3 运营管理, 9.3.1 学习目标, 9.3.2 消息推送, 1. 推送任务列表与数据统计, 2. 新建推送配置, 9.3.3 账号与权限管理, 1. 核心思想：RBAC 模型, 2. 角色管理, 3. 账号管理（后台用户）, 9.3.4 日志管理, 1. 操作日志的价值, 2. 操作日志设计要点第九章平台端设计用户内容运营在前面两个章节我们已经为产品的前台用户端和自媒体端设计好了舞台和化妆间现在我们需要开始搭建整个剧院的后台平台端这是我们作为平台运营和管理人员进行日常工作的驾驶舱是整个产品生态能够有序健康运转的中枢神经我们将从这个后台最基础也是最重要的模块开始用户管理用户管理我设计用户管理模块的核心思路是分类与控制正如思考题所提示的管理一名普通的内容消费者和管理一名专业的内容创作者其关注点和所需要的工具是截然不同的因此我的后台设计必须清晰地将他们分类并提供差异化的管理能力学习目标在本节中我的目标是带大家掌握一套专业的后台用户管理系统的设计方法我们将学习如何分别为普通用户和自媒体用户设计管理列表并重点拆解自媒体的审核流程最后还会介绍一个平台运营中非常实用的高级功能马甲管理普通用户管理这是我们用户管理的基础模块用于管理平台上所有的内容消费者用户列表与查询一个合格的用户列表必须具备强大的查询和筛选能力我需要为运营同事提供多维度的查询字段比如昵称手机号码用户状态如正常已封禁用户性别列表的表头则需要清晰地展示用户的核心信息如昵称头像性别地区手机号用户状态等用户详情查看在操作列点击查看运营同事可以进入用户的详情页看到该用户更完整的资料行为日志消费记录等用户状态管理封禁解封这是最重要的管理权限在操作列我必须提供封禁功能当一个用户出现违规行为时运营同事可以将其账号封禁当然也必须提供对应的解封功能自媒体用户管理管理创作者我们需要关注比普通用户更多的信息创作者列表与查询创作者列表的设计在普通用户列表的基础上我会额外增加几个关键的展示字段和筛选条件认证类型清晰地标识出该创作者是个人认证还是企业认证认证时间记录其通过审核正式成为创作者的时间自媒体审核这是自媒体用户管理中一个独立且至关重要的工作流我通常会把它设计成一个单独的页面待审核列表这个页面的默认视图就是所有提交了入驻申请正在等待审核的用户列表审核详情页通过驳回运营同事点击审核后可以查看该用户提交的所有资质信息在这个页面必须有两个明确的操作按钮通过和驳回如果选择驳回还需要提供一个填写驳回理由的输入框审核历史记录系统需要记录所有的审核操作便于未来追溯马甲管理现在我们来探讨一个平台运营中非常实用甚至可以说是必不可少的高阶功能马甲管理马甲管理的价值与鲶鱼效应马甲指的就是由我们平台内部运营人员在后台创建和控制的虚拟用户我设计这个功能主要是为了在社区运营中起到鲶鱼效应即通过引入一些活跃的鲶鱼马甲来激活整个鱼塘真实用户生态的活力它的核心价值主要体现在两方面制造争议热点话题带节奏在社区冷启动或需要引导舆论时我的运营同事可以使用马甲发布一些具有话题性的内容引发用户讨论掌握社区的话题走向灌水活跃社区氛围在社区初期用户较少时通过马甲发布一些日常内容进行评论和互动可以快速地让社区看起来有人气打破无人区的尴尬从而吸引真实用户加入和发言添加马甲为了让运营同事能方便地创建这些虚拟用户我需要设计一个新增马甲的功能它通常是一个后台的弹窗表单核心字段表单中需要包含创建逼真用户所需的核心字段比如马甲昵称头像性别地区等具体需要哪些字段由我们产品的实际业务决定归属管理员这是我设计中非常关键的一环为了分工明确责任到人每一个马甲都必须可以分配给一个指定的管理员这样我们就能清晰地知道哪个马甲由哪位运营同事负责使用和维护马甲列表所有创建的马甲都需要在一个单独的马甲管理列表中进行统一的查看和维护这个列表只有特定的高权限的管理员才能看到列表设计要点查询与筛选列表必须提供强大的查询功能特别是要支持按归属管理员进行筛选方便运营主管查看自己团队成员名下的马甲信息展示列表中需要清晰地展示马甲昵称归属管理员状态等核心信息技术实现一个设计要点是马甲可以不需要像真实用户一样拥有完整的账号密码体系从技术实现上它可以是一个仅有昵称头像等信息的虚拟身份能通过后台直接进行内容发布和评论即可内容管理在第八章我们为创作者自媒体设计了他们的创作车间但这引出了一个问题作为平台我们难道只需要提供工具然后对海量的内容放任不管吗答案显然是否定的一个健康的内容生态平台绝不能只当一个被动的房东而必须是一个积极的秩序维护者和价值发现者内容管理后台就是我们履行这个职责的核心工具学习目标在本节中我的目标是带大家设计一个专业高效的内容管理后台我们将重点学习内容列表的设计掌握如何实现强大的查询与筛选严谨的内容审核流程以及精细化的内容推荐与加权等高级运营功能内容列表内容管理模块的核心就是这张包罗万象的内容列表这是我们的运营和审核同学日常工作中停留时间最长的页面我设计的核心目标是效率和掌控力内容查询与筛选一个内容平台文章动辄成千上万如果不能让运营同学在秒内找到他想要的内容那这个后台就是失败的因此我必须设计一套强大的查询与筛选系统基础筛选字段根据图例至少要包含文章标题作者昵称发表时间支持按时间范围筛选文章分类我的拓展设计为了让管理更精细我还会额外增加几个关键的筛选条件内容用于研发同学进行精准的问题定位来源这是图例中提到的一个设计要点我必须让运营可以筛选出自媒体发布或普通用户发布如果我们的产品支持的内容因为这两者的审核标准和权重可能不同审核状态这是审核人员最高频使用的筛选条件他们可以通过筛选待审核状态快速进入自己的工作队列内容审核通过驳回下架这是内容列表的控制核心体现在操作这一列一个严谨的后台其可执行的操作必须与内容的审核状态严格绑定我会将它设计成一个状态机当内容状态为待审核时操作列应提供查看通过驳回设计要点点击驳回时必须弹出一个输入框让审核人员填写驳回理由这个理由会反馈给创作者当内容状态为已通过即已上线时操作列应变为查看下架删除设计要点下架是一个软删除内容仅对用户不可见作者后台依然可见而删除则是一个硬删除会彻底清除内容我必须为删除操作设计一个二次确认的弹窗防止误操作当内容状态为已驳回或已下架时操作列可以简化为查看删除内容推荐与加权除了基础的审核一个优秀的后台还要能让运营同事对优质内容进行助推我会增加几个高级运营功能置顶如图例所示这是最常见的运营手段提供一个置顶按钮可以将优质内容在前端的某个列表如分类列表频道首页顶部固定显示我还会设计一个取消置顶的功能加权这是一个更精细化的运营工具我会在后台为每篇文章增加一个权重值输入框比如我们前端的算法推荐或热度排序公式就可以把这个人工权重作为一个重要的计算因子这样我就实现了人工编辑意志与算法推荐的完美结合推送对于级的顶级内容我还会设计一个推送按钮运营同事点击后可以将这篇文章通过推送通知的形式直接触达我们的用户实现最大化的曝光敏感词管理在我们的内容列表中审核人员需要对待审核的内容进行人工处理但面对平台海量的内容生产如果完全依赖人工审核团队会被瞬间淹没因此我必须设计一套自动化的初审过滤系统来分担团队的压力并将最明显的违规内容拦截在摇篮里这套系统的核心就是敏感词管理敏感词库维护增删改查我需要为我的运营同事提供一个简单易用的后台界面来持续地维护我们平台的敏感词词库这个词库就是我们自动化审核的规则库这个后台界面必须具备以下核心功能添加敏感词提供一个添加敏感词的入口让运营可以随时将新发现的违规词语录入系统查询与筛选当词库变得庞大时必须提供按敏感词本身进行搜索或按状态进行筛选的功能编辑与状态管理这是我设计中的一个要点除了编辑和删除我必须为每个敏感词增加一个上线下线的状态只有处于上线状态的词才会被系统用于前端的匹配过滤这个设计能让运营同事在不删除历史词语的情况下灵活地启用或禁用某些词的过滤规则特别是在应对一些突发的热点事件时非常有用敏感词分类与处理规则仅仅有一个词库列表在我看来还是一个非常初级的设计一个专业的敏感词系统必须具备更聪明的差异化的处理能力为了实现这一点我会在我的设计中再增加两个维度敏感词分类和处理规则第一步为敏感词分类在添加敏感词时我会要求运营同事必须为这个词选择一个预设的分类我会将词库至少分为以下几类涉政类暴恐类色情类广告营销类辱骂攻击类第二步设定差异化的处理规则完成了分类我就可以为不同类别的敏感词设定不同的自动化处理规则这才是这个系统智能化的体现敏感词分类示例我设定的处理规则对用户的反馈辱骂攻击类傻垃圾等替换系统自动将傻替换为内容依然可以发布广告营销类加信拦截系统直接阻止该内容的发布并向用户提示内容包含违规广告信息请修改涉政暴恐等高危类一些高度敏感的词语转人工审核内容发布后用户自己可见但其他用户不可见同时该内容自动进入我们后台的待审核列表由人工进行最终判定通过这套分类规则的组合设计我们的敏感词管理系统就从一个简单的过滤器升级为了一个具备初步智能的能分级处理风险的自动化审核引擎分类管理一个很核心的问题是当创作者发布内容时他可以选择的那些分类是从哪里来的呢答案就是由我们平台的运营人员在这个分类管理后台中进行统一的创建和维护在我看来分类管理是为我们的内容产品搭建一个清晰有序的图书馆目录它将直接决定我们产品前台的频道划分和用户浏览结构我通常将这个目录体系分为两个层级宏观的频道分类和微观的标签频道分类管理频道分类是内容最高层级的划分它通常直接对应着我们首页的导航栏比如科技财经体育娱乐等这个后台管理界面我的设计要点如下分类的增删改查这是最基础的功能运营人员必须可以方便地新增分类并对已有的分类进行编辑和删除我的拓展思考在设计删除功能时我必须考虑一个边界情况如果某个分类下已经存在大量内容那么直接删除这个分类会导致这些内容成为孤儿因此一个严谨的删除逻辑应该是当分类下存在内容时禁止删除并提示运营人员先将该分类下的内容迁移至其他分类状态管理和我们之前设计的其他模块一样我必须为每个分类提供上线下线状态这能让运营同事从容地去筹备一个新频道在内容和运营活动都准备好之后再一键上线呈现给所有用户排序功能我认为极其重要的一个功能运营同事必须可以手动调整分类的显示顺序这个后台的排序将直接决定前端导航栏的频道顺序我通常会设计一个支持上移下移或拖拽排序的功能标签管理如果说分类是图书馆的区域如区文学那么标签就是贴在每一本书上更精细的关键词如科幻刘慈欣三体我们为内容打上精准的标签核心目的就是为了喂给我们的算法推荐引擎让它能实现更精准的人内容匹配这个后台的设计除了基础的增删改查和状态管理外我还会重点考虑以下两点标签分类与层级当标签数量达到成千上万时一个扁平的列表是无法管理的因此我必须设计一个支持层级的标签体系如图例所示一个产品经理的标签它的上级分类可能是互联网这种树状的层级结构有两大好处便于管理运营同事可以像操作文件夹一样高效地管理和查找标签便于算法能让我们的推荐算法更聪明算法会知道喜欢产品经理标签内容的用户可能也会对互联网这个更大范畴下的其他内容感兴趣从而扩大推荐的相关性标签的创建与合并创建权限我需要做一个重要的产品决策新标签是由谁来创建的是只能由运营在后台创建还是允许创作者在发布内容时自由地创建新标签前者能保证标签库的规范和整洁后者则能让标签库的内容更丰富更接地气在产品初期我通常会采用运营后台创建为主创作者可申请为辅的策略合并功能当标签库由多人维护或允许用户创建时不可避免地会出现语义相同但文字不同的标签如产品经理产品策划因此我必须设计一个标签合并功能让运营可以将这几个重复的标签合并为一个标准标签并自动更新所有关联了这些标签的内容运营管理我们已经设计了用户和内容的管理后台但这还不够一个好的产品不能只依赖用户主动地回来使用在如今这个信息爆炸泛滥的时代酒香也怕巷子深我们必须建立一套属于自己的广播系统能够主动地在合适的时机去触达我们的用户提醒他们吸引他们回来这就是运营管理模块的核心价值学习目标在本节中我的目标是带大家设计一个专业的运营管理后台我们将重点学习消息推送后台账号与权限和日志管理这三大系统的设计消息推送消息推送是我作为平台运营方唯一能免费且主动触达已安装但未打开用户的渠道它是拉动用户活跃进行活动通知实现用户召回的最强武器推送任务列表与数据统计这是我运营团队的发射控制中心它是一个记录了所有已发送和待发送推送任务的列表除了展示标题目标用户推送时间等基础信息外一个专业后台的核心在于提供推送效果的数据统计数据指标说明发送数本次任务我们总共向多少个设备发送了通知到达数其中成功送达到用户设备的数量有些可能因网络或卸载失败点击数最终有多少用户被我们的文案吸引点击了这条通知点击率最重要的评估指标点击数到达数直接反映了推送的质量新建推送配置这是我为运营同事设计的炮弹编辑器这个新建推送的表单必须严谨清晰我通常会把它设计为推送四要素的配置配置要素核心选项与说明推送对象全部用户用于平台级的重大公告用户分群按用户标签如地域兴趣活跃度等进行精准推送指定用户通过上传用户列表进行点对点推送推送时间立即推送用于突发新闻热点事件定时推送用于已规划好的运营活动可以提前设置推送内容通知标题吸引用户眼球的第一句话至关重要通知内容对标题的补充说明可支持插入用户昵称等个性化变量目标页配置打开应用首页打开应用内指定页面如某篇文章某个活动页打开一个链接跳转到外部网页我的拓展设计技术实现浅谈这个功能的技术实现我们通常不会自己从零搭建而是会依赖专业的第三方推送服务对于端我们后台需要接入苹果官方的对于国内的端我通常会选择集成一个像极光推送或个推这样的第三方聚合服务商我的需要明确技术方案因为不同服务商的能力会影响我的产品设计账号与权限管理这个模块管理的不是我们产品的用户而是我们公司内部使用这个后台系统的员工其设计的核心是确保后台系统的安全性和规范性核心思想模型我设计后台权限普遍采用的是基于角色的访问控制模型它的逻辑很简单我不直接给某个人分配权限而是先创建不同的角色如内容审核员高级运营为这些角色分配权限最后再把某个人归属到某个角色里去这样做的好处是当公司人员变动时我只需要调整这个人的角色而不需要重新为他配置一遍复杂的权限管理效率极高角色管理角色列表一个展示所有已创建角色的列表如超级管理员内容审核数据分析师等新建编辑角色提供创建新角色的功能角色权限配置这是核心我会以功能菜单树的形式列出后台的所有功能点让管理员可以通过勾选的方式为每个角色分配它能看到和操作的菜单权限账号管理后台用户这是具体管理员工账号的地方后台账号列表展示所有后台用户的列表新增编辑账号当我需要为一位新同事开通后台权限时我会在这里为他新建账号新增账号字段填写说明登录账号用于后台登录的唯一建议使用员工的企业邮箱员工姓名账号使用者的真实姓名用于日志记录和责任追溯所属角色从我们创建的角色列表中为该账号选择一个角色从而赋予他对应的权限账号状态正常冻结当员工离职时我可以将其账号冻结而不是直接删除日志管理日志管理是后台的黑匣子和监控录像它记录了所有管理员在后台的一举一动是进行安全审计和问题追溯的最后一道防线操作日志的价值它的价值在于安全和可追溯当后台出现误操作或恶意操作时我可以通过日志精准地定位到是谁在什么时间对什么东西做了什么操作操作日志设计要点一个合格的操作日志系统必须清晰地记录以下关键信息并提供强大的查询功能记录字段说明示例操作人执行该操作的后台账号时间戳操作发生的精确时间地址操作人当时使用的地址操作行为具体执行了什么动作内容管理下架文章操作对象该动作作用于哪个具体目标文章操作结果本次操作是成功还是失败成功",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-21 14:51:52",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B9%9D%E7%AB%A0%EF%BC%9A%E5%B9%B3%E5%8F%B0%E7%AB%AF%E8%AE%BE%E8%AE%A1%EF%BC%88%E7%94%A8%E6%88%B7-%E5%86%85%E5%AE%B9-%E8%BF%90%E8%90%A5%EF%BC%89"><span class="toc-text">第九章：平台端设计（用户-内容-运营）</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#9-1-%E7%94%A8%E6%88%B7%E7%AE%A1%E7%90%86"><span class="toc-text">9.1 用户管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#9-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">9.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-1-2-%E6%99%AE%E9%80%9A%E7%94%A8%E6%88%B7%E7%AE%A1%E7%90%86"><span class="toc-text">9.1.2 普通用户管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%94%A8%E6%88%B7%E5%88%97%E8%A1%A8%E4%B8%8E%E6%9F%A5%E8%AF%A2"><span class="toc-text">1. 用户列表与查询</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%94%A8%E6%88%B7%E8%AF%A6%E6%83%85%E6%9F%A5%E7%9C%8B"><span class="toc-text">2. 用户详情查看</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%94%A8%E6%88%B7%E7%8A%B6%E6%80%81%E7%AE%A1%E7%90%86%EF%BC%88%E5%B0%81%E7%A6%81-%E8%A7%A3%E5%B0%81%EF%BC%89"><span class="toc-text">3. 用户状态管理（封禁/解封）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-1-3-%E8%87%AA%E5%AA%92%E4%BD%93%E7%94%A8%E6%88%B7%E7%AE%A1%E7%90%86"><span class="toc-text">9.1.3 自媒体用户管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%88%9B%E4%BD%9C%E8%80%85%E5%88%97%E8%A1%A8%E4%B8%8E%E6%9F%A5%E8%AF%A2"><span class="toc-text">1. 创作者列表与查询</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%87%AA%E5%AA%92%E4%BD%93%E5%AE%A1%E6%A0%B8"><span class="toc-text">2. 自媒体审核</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-1-4-%E9%A9%AC%E7%94%B2%E7%AE%A1%E7%90%86"><span class="toc-text">9.1.4 马甲管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%A9%AC%E7%94%B2%E7%AE%A1%E7%90%86%E7%9A%84%E4%BB%B7%E5%80%BC%E4%B8%8E%E2%80%9C%E9%B2%B6%E9%B1%BC%E6%95%88%E5%BA%94%E2%80%9D"><span class="toc-text">1. 马甲管理的价值与“鲶鱼效应”</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%B7%BB%E5%8A%A0%E9%A9%AC%E7%94%B2"><span class="toc-text">2. 添加马甲</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E9%A9%AC%E7%94%B2%E5%88%97%E8%A1%A8"><span class="toc-text">3. 马甲列表</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#9-2-%E5%86%85%E5%AE%B9%E7%AE%A1%E7%90%86"><span class="toc-text">9.2 内容管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">9.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-2-%E5%86%85%E5%AE%B9%E5%88%97%E8%A1%A8"><span class="toc-text">9.2.2 内容列表</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%86%85%E5%AE%B9%E6%9F%A5%E8%AF%A2%E4%B8%8E%E7%AD%9B%E9%80%89"><span class="toc-text">1. 内容查询与筛选</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%86%85%E5%AE%B9%E5%AE%A1%E6%A0%B8%EF%BC%88%E9%80%9A%E8%BF%87-%E9%A9%B3%E5%9B%9E-%E4%B8%8B%E6%9E%B6%EF%BC%89"><span class="toc-text">2. 内容审核（通过/驳回/下架）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E5%86%85%E5%AE%B9%E6%8E%A8%E8%8D%90%E4%B8%8E%E5%8A%A0%E6%9D%83"><span class="toc-text">3. 内容推荐与加权</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-3-%E6%95%8F%E6%84%9F%E8%AF%8D%E7%AE%A1%E7%90%86"><span class="toc-text">9.2.3 敏感词管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%95%8F%E6%84%9F%E8%AF%8D%E5%BA%93%E7%BB%B4%E6%8A%A4%EF%BC%88%E5%A2%9E-%E5%88%A0-%E6%94%B9-%E6%9F%A5%EF%BC%89"><span class="toc-text">1. 敏感词库维护（增/删/改/查）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%95%8F%E6%84%9F%E8%AF%8D%E5%88%86%E7%B1%BB%E4%B8%8E%E5%A4%84%E7%90%86%E8%A7%84%E5%88%99"><span class="toc-text">2. 敏感词分类与处理规则</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-4-%E5%88%86%E7%B1%BB%E7%AE%A1%E7%90%86"><span class="toc-text">9.2.4 分类管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%A2%91%E9%81%93-%E5%88%86%E7%B1%BB%E7%AE%A1%E7%90%86"><span class="toc-text">1. 频道/分类管理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%A0%87%E7%AD%BE%E7%AE%A1%E7%90%86"><span class="toc-text">2. 标签管理</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#9-3-%E8%BF%90%E8%90%A5%E7%AE%A1%E7%90%86"><span class="toc-text">9.3 运营管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-text">9.3.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-2-%E6%B6%88%E6%81%AF%E6%8E%A8%E9%80%81"><span class="toc-text">9.3.2 消息推送</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%8E%A8%E9%80%81%E4%BB%BB%E5%8A%A1%E5%88%97%E8%A1%A8%E4%B8%8E%E6%95%B0%E6%8D%AE%E7%BB%9F%E8%AE%A1"><span class="toc-text">1. 推送任务列表与数据统计</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%96%B0%E5%BB%BA%E6%8E%A8%E9%80%81%E9%85%8D%E7%BD%AE"><span class="toc-text">2. 新建推送配置</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-3-%E8%B4%A6%E5%8F%B7%E4%B8%8E%E6%9D%83%E9%99%90%E7%AE%A1%E7%90%86"><span class="toc-text">9.3.3 账号与权限管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%A0%B8%E5%BF%83%E6%80%9D%E6%83%B3%EF%BC%9ARBAC-%E6%A8%A1%E5%9E%8B"><span class="toc-text">1. 核心思想：RBAC 模型</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%A7%92%E8%89%B2%E7%AE%A1%E7%90%86"><span class="toc-text">2. 角色管理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E8%B4%A6%E5%8F%B7%E7%AE%A1%E7%90%86%EF%BC%88%E5%90%8E%E5%8F%B0%E7%94%A8%E6%88%B7%EF%BC%89"><span class="toc-text">3. 账号管理（后台用户）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-4-%E6%97%A5%E5%BF%97%E7%AE%A1%E7%90%86"><span class="toc-text">9.3.4 日志管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%93%8D%E4%BD%9C%E6%97%A5%E5%BF%97%E7%9A%84%E4%BB%B7%E5%80%BC"><span class="toc-text">1. 操作日志的价值</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%93%8D%E4%BD%9C%E6%97%A5%E5%BF%97%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-text">2. 操作日志设计要点</span></a></li></ol></li></ol></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-20T16:13:45.000Z" title="发表于 2025-07-21 00:13:45">2025-07-21</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-21T06:51:52.679Z" title="更新于 2025-07-21 14:51:52">2025-07-21</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">6.4k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>18分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/38041.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/38041.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-20T16:13:45.000Z" title="发表于 2025-07-21 00:13:45">2025-07-21</time><time itemprop="dateCreated datePublished" datetime="2025-07-21T06:51:52.679Z" title="更新于 2025-07-21 14:51:52">2025-07-21</time></header><div id="postchat_postcontent"><h1 id="第九章：平台端设计（用户-内容-运营）"><a href="#第九章：平台端设计（用户-内容-运营）" class="headerlink" title="第九章：平台端设计（用户-内容-运营）"></a>第九章：平台端设计（用户-内容-运营）</h1><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721124904426.png" alt="image-20250721124904426"></p><p>在前面两个章节，我们已经为产品的“前台”——用户端和自媒体端，设计好了舞台和化妆间。现在，我们需要开始搭建整个剧院的“<strong>后台</strong>”——<strong>平台端（Admin Backend）</strong>。</p><p>这是我们作为平台运营和管理人员，进行日常工作的“驾驶舱”，是整个产品生态能够有序、健康运转的中枢神经。我们将从这个后台最基础，也是最重要的模块开始：<strong>用户管理</strong>。</p><h2 id="9-1-用户管理"><a href="#9-1-用户管理" class="headerlink" title="9.1 用户管理"></a>9.1 用户管理</h2><p>我设计用户管理模块的核心思路是“<strong>分类与控制</strong>”。正如思考题所提示的，管理一名普通的内容消费者，和管理一名专业的内容创作者，其关注点和所需要的工具是截然不同的。因此，我的后台设计，必须清晰地将他们分类，并提供差异化的管理能力。</p><h3 id="9-1-1-学习目标"><a href="#9-1-1-学习目标" class="headerlink" title="9.1.1 学习目标"></a>9.1.1 学习目标</h3><p>在本节中，我的目标是带大家掌握一套专业的后台用户管理系统的设计方法。我们将学习如何分别为 <strong>普通用户</strong> 和 <strong>自媒体用户</strong> 设计管理列表，并重点拆解 <strong>自媒体的审核流程</strong>，最后还会介绍一个平台运营中非常实用的高级功能——<strong>马甲管理</strong>。</p><h3 id="9-1-2-普通用户管理"><a href="#9-1-2-普通用户管理" class="headerlink" title="9.1.2 普通用户管理"></a>9.1.2 普通用户管理</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721124938842.png" alt="image-20250721124938842"></p><p>这是我们用户管理的基础模块，用于管理平台上所有的内容消费者。</p><h4 id="1-用户列表与查询"><a href="#1-用户列表与查询" class="headerlink" title="1. 用户列表与查询"></a>1. 用户列表与查询</h4><p>一个合格的用户列表，必须具备强大的查询和筛选能力。我需要为运营同事，提供多维度的查询字段，比如：</p><ul><li><strong>昵称</strong></li><li><strong>手机号码</strong></li><li><strong>用户状态</strong>（如：正常、已封禁）</li><li><strong>用户性别</strong></li></ul><p>列表的表头，则需要清晰地展示用户的核心信息，如 <code>昵称</code>、<code>头像</code>、<code>性别</code>、<code>地区</code>、<code>手机号</code>、<code>用户状态</code> 等。</p><h4 id="2-用户详情查看"><a href="#2-用户详情查看" class="headerlink" title="2. 用户详情查看"></a>2. 用户详情查看</h4><p>在操作列，点击“查看”，运营同事可以进入用户的详情页，看到该用户更完整的资料、行为日志、消费记录等。</p><h4 id="3-用户状态管理（封禁-解封）"><a href="#3-用户状态管理（封禁-解封）" class="headerlink" title="3. 用户状态管理（封禁/解封）"></a>3. 用户状态管理（封禁/解封）</h4><p>这是最重要的管理权限。在操作列，我必须提供“<strong>封禁</strong>”功能。当一个用户出现违规行为时，运营同事可以将其账号封禁。当然，也必须提供对应的“<strong>解封</strong>”功能。</p><h3 id="9-1-3-自媒体用户管理"><a href="#9-1-3-自媒体用户管理" class="headerlink" title="9.1.3 自媒体用户管理"></a>9.1.3 自媒体用户管理</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721125236680.png" alt="image-20250721125236680"></p><p>管理创作者，我们需要关注比普通用户更多的信息。</p><h4 id="1-创作者列表与查询"><a href="#1-创作者列表与查询" class="headerlink" title="1. 创作者列表与查询"></a>1. 创作者列表与查询</h4><p>创作者列表的设计，在普通用户列表的基础上，我会额外增加几个关键的展示字段和筛选条件：</p><ul><li><strong>认证类型</strong>：清晰地标识出该创作者是“个人认证”还是“企业认证”。</li><li><strong>认证时间</strong>：记录其通过审核、正式成为创作者的时间。</li></ul><h4 id="2-自媒体审核"><a href="#2-自媒体审核" class="headerlink" title="2. 自媒体审核"></a>2. 自媒体审核</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721125408677.png" alt="image-20250721125408677"></p><p>这是自媒体用户管理中，一个独立且至关重要的工作流，我通常会把它设计成一个单独的页面。</p><ul><li><strong>待审核列表</strong>：这个页面的默认视图，就是所有提交了入驻申请、正在等待审核的用户列表。</li><li><strong>审核详情页（通过/驳回）</strong>：运营同事点击“审核”后，可以查看该用户提交的所有资质信息。在这个页面，必须有两个明确的操作按钮：“<strong>通过</strong>”和“<strong>驳回</strong>”。如果选择“驳回”，还需要提供一个填写驳回理由的输入框。</li><li><strong>审核历史记录</strong>：系统需要记录所有的审核操作，便于未来追溯。</li></ul><hr><h3 id="9-1-4-马甲管理"><a href="#9-1-4-马甲管理" class="headerlink" title="9.1.4 马甲管理"></a>9.1.4 马甲管理</h3><p>现在，我们来探讨一个平台运营中，非常实用甚至可以说是必不可少的高阶功能——<strong>马甲管理</strong>。</p><h4 id="1-马甲管理的价值与“鲶鱼效应”"><a href="#1-马甲管理的价值与“鲶鱼效应”" class="headerlink" title="1. 马甲管理的价值与“鲶鱼效应”"></a>1. 马甲管理的价值与“鲶鱼效应”</h4><p>“马甲”，指的就是由我们平台内部运营人员，在后台创建和控制的“虚拟用户”。我设计这个功能，主要是为了在社区运营中，起到“<strong>鲶鱼效应</strong>”——即，通过引入一些活跃的“鲶鱼”（马甲），来激活整个“鱼塘”（真实用户生态）的活力。</p><p>它的核心价值主要体现在两方面：</p><ol><li><strong>制造争议、热点话题，带节奏</strong>：在社区冷启动或需要引导舆论时，我的运营同事可以使用马甲，发布一些具有话题性的内容，引发用户讨论，掌握社区的话题走向。</li><li><strong>灌水，活跃社区氛围</strong>：在社区初期用户较少时，通过马甲发布一些日常内容、进行评论和互动，可以快速地让社区看起来“有人气”，打破“无人区”的尴尬，从而吸引真实用户加入和发言。</li></ol><h4 id="2-添加马甲"><a href="#2-添加马甲" class="headerlink" title="2. 添加马甲"></a>2. 添加马甲</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721131112565.png" alt="image-20250721131112565"></p><p>为了让运营同事能方便地创建这些虚拟用户，我需要设计一个“<strong>新增马甲</strong>”的功能，它通常是一个后台的弹窗表单。</p><ul><li><strong>核心字段</strong>：表单中需要包含创建逼真用户所需的核心字段，比如 <code>马甲昵称</code>、<code>头像</code>、<code>性别</code>、<code>地区</code> 等。具体需要哪些字段，由我们产品的实际业务决定。</li><li><strong>归属管理员</strong>：这是我设计中非常关键的一环。为了分工明确、责任到人，<strong>每一个马甲，都必须可以分配给一个指定的管理员</strong>。这样，我们就能清晰地知道，哪个马甲由哪位运营同事负责使用和维护。</li></ul><h4 id="3-马甲列表"><a href="#3-马甲列表" class="headerlink" title="3. 马甲列表"></a>3. 马甲列表</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721131135140.png" alt="image-20250721131135140"></p><p>所有创建的马甲，都需要在一个单独的“<strong>马甲管理</strong>”列表中进行统一的查看和维护。这个列表，只有特定的、高权限的管理员才能看到。</p><ul><li><strong>列表设计要点</strong>：<ol><li><strong>查询与筛选</strong>：列表必须提供强大的查询功能，特别是要支持按“<strong>归属管理员</strong>”进行筛选，方便运营主管查看自己团队成员名下的马甲。</li><li><strong>信息展示</strong>：列表中需要清晰地展示 <code>马甲昵称</code>、<code>归属管理员</code>、<code>状态</code> 等核心信息。</li><li><strong>技术实现</strong>：一个设计要点是，马甲可以不需要像真实用户一样，拥有完整的账号密码体系。从技术实现上，它可以是一个仅有昵称、头像等信息的“虚拟身份”，能通过后台直接进行内容发布和评论即可。</li></ol></li></ul><hr><h2 id="9-2-内容管理"><a href="#9-2-内容管理" class="headerlink" title="9.2 内容管理"></a>9.2 内容管理</h2><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721131945141.png" alt="image-20250721131945141"></p><p>在第八章，我们为创作者（自媒体）设计了他们的“创作车间”。但这引出了一个问题：作为平台，我们难道只需要提供工具，然后对海量的内容放任不管吗？</p><p>答案显然是 <strong>否定</strong> 的。</p><p>一个健康的内容生态，平台绝不能只当一个被动的“房东”，而必须是一个积极的“<strong>秩序维护者</strong>”和“<strong>价值发现者</strong>”。<strong>内容管理</strong> 后台，就是我们履行这个职责的核心工具。</p><h3 id="9-2-1-学习目标"><a href="#9-2-1-学习目标" class="headerlink" title="9.2.1 学习目标"></a>9.2.1 学习目标</h3><p>在本节中，我的目标是带大家设计一个专业、高效的内容管理后台。我们将重点学习 <strong>内容列表</strong> 的设计，掌握如何实现强大的 <strong>查询与筛选</strong>、严谨的 <strong>内容审核</strong> 流程，以及精细化的 <strong>内容推荐与加权</strong> 等高级运营功能。</p><h3 id="9-2-2-内容列表"><a href="#9-2-2-内容列表" class="headerlink" title="9.2.2 内容列表"></a>9.2.2 内容列表</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721132036484.png" alt="image-20250721132036484"></p><p>内容管理模块的核心，就是这张包罗万象的 <strong>内容列表</strong>。这是我们的运营和审核同学，日常工作中停留时间最长的页面。我设计的核心目标是 <strong>效率</strong> 和 <strong>掌控力</strong>。</p><h4 id="1-内容查询与筛选"><a href="#1-内容查询与筛选" class="headerlink" title="1. 内容查询与筛选"></a>1. 内容查询与筛选</h4><p>一个内容平台，文章动辄成千上万。如果不能让运营同学在 3 秒内找到他想要的内容，那这个后台就是失败的。因此，我必须设计一套强大的查询与筛选系统。</p><ul><li><p><strong>基础筛选字段</strong>：根据图例，至少要包含：</p><ul><li><code>文章标题</code></li><li><code>作者昵称</code></li><li><code>发表时间</code>（支持按时间范围筛选）</li><li><code>文章分类</code></li></ul></li><li><p><strong>我的拓展设计</strong>：为了让管理更精细，我还会额外增加几个关键的筛选条件：</p><ul><li><code>内容ID</code>：用于研发同学进行精准的问题定位。</li><li><code>来源</code>：这是图例中提到的一个设计要点。我必须让运营可以筛选出 <strong>“自媒体发布”</strong> 或 <strong>“普通用户发布”</strong>（如果我们的产品支持）的内容，因为这两者的审核标准和权重可能不同。</li><li><code>审核状态</code>：这是审核人员最高频使用的筛选条件。他们可以通过筛选“<strong>待审核</strong>”状态，快速进入自己的工作队列。</li></ul></li></ul><h4 id="2-内容审核（通过-驳回-下架）"><a href="#2-内容审核（通过-驳回-下架）" class="headerlink" title="2. 内容审核（通过/驳回/下架）"></a>2. 内容审核（通过/驳回/下架）</h4><p>这是内容列表的“控制核心”，体现在“<strong>操作</strong>”这一列。一个严谨的后台，其可执行的操作，必须与内容的“<strong>审核状态</strong>”严格绑定。我会将它设计成一个状态机：</p><ul><li><p><strong>当内容状态为“待审核”时</strong>：<br>操作列应提供：<code>查看</code>、<code>通过</code>、<code>驳回</code>。</p><ul><li><strong>设计要点</strong>：点击“驳回”时，必须弹出一个输入框，让审核人员填写驳回理由，这个理由会反馈给创作者。</li></ul></li><li><p><strong>当内容状态为“已通过”（即已上线）时</strong>：<br>操作列应变为：<code>查看</code>、<code>下架</code>、<code>删除</code>。</p><ul><li><strong>设计要点</strong>：“下架”是一个软删除，内容仅对用户不可见，作者后台依然可见；而“删除”则是一个硬删除，会彻底清除内容。我必须为“删除”操作，设计一个“二次确认”的弹窗，防止误操作。</li></ul></li><li><p><strong>当内容状态为“已驳回”或“已下架”时</strong>：<br>操作列可以简化为：<code>查看</code>、<code>删除</code>。</p></li></ul><h4 id="3-内容推荐与加权"><a href="#3-内容推荐与加权" class="headerlink" title="3. 内容推荐与加权"></a>3. 内容推荐与加权</h4><p>除了基础的审核，一个优秀的后台，还要能让运营同事对优质内容进行“助推”。我会增加几个高级运营功能：</p><ul><li><strong>置顶</strong>：如图例所示，这是最常见的运营手段。提供一个“置顶”按钮，可以将优质内容在前端的某个列表（如分类列表、频道首页）顶部固定显示。我还会设计一个“取消置顶”的功能。</li><li><strong>加权</strong>：这是一个更精细化的运营工具。我会在后台为每篇文章增加一个“<strong>权重值</strong>”输入框（比如 1-100）。我们前端的算法推荐或热度排序公式，就可以把这个“人工权重”作为一个重要的计算因子。这样，我就实现了“<strong>人工编辑意志</strong>”与“<strong>算法推荐</strong>”的完美结合。</li><li><strong>推送</strong>：对于 S 级的顶级内容，我还会设计一个“<strong>推送</strong>”按钮。运营同事点击后，可以将这篇文章通过 Push（推送通知）的形式，直接触达我们的用户，实现最大化的曝光。</li></ul><hr><h3 id="9-2-3-敏感词管理"><a href="#9-2-3-敏感词管理" class="headerlink" title="9.2.3 敏感词管理"></a>9.2.3 敏感词管理</h3><p>在我们的内容列表中，审核人员需要对“待审核”的内容进行人工处理。但面对 UGC 平台海量的内容生产，如果完全依赖人工，审核团队会被瞬间淹没。</p><p>因此，我必须设计一套 <strong>自动化的初审过滤系统</strong>，来分担团队的压力，并将最明显的违规内容，拦截在“摇篮”里。这套系统的核心，就是 <strong>敏感词管理</strong>。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721132535224.png" alt="image-20250721132535224"></p><h4 id="1-敏感词库维护（增-删-改-查）"><a href="#1-敏感词库维护（增-删-改-查）" class="headerlink" title="1. 敏感词库维护（增/删/改/查）"></a>1. 敏感词库维护（增/删/改/查）</h4><p>我需要为我的运营同事，提供一个简单、易用的后台界面，来持续地维护我们平台的“<strong>敏感词词库</strong>”。这个词库，就是我们自动化审核的“规则库”。</p><p>这个后台界面，必须具备以下核心功能：</p><ul><li><strong>添加敏感词</strong>：提供一个“+ 添加敏感词”的入口，让运营可以随时将新发现的违规词语录入系统。</li><li><strong>查询与筛选</strong>：当词库变得庞大时，必须提供按“<strong>敏感词</strong>”本身进行搜索，或按“<strong>状态</strong>”进行筛选的功能。</li><li><strong>编辑与状态管理</strong>：这是我设计中的一个要点。除了编辑和删除，我必须为每个敏感词，增加一个“<strong>上线/下线</strong>”的状态。只有处于“<strong>上线</strong>”状态的词，才会被系统用于前端的匹配过滤。这个设计，能让运营同事在不删除历史词语的情况下，灵活地启用或禁用某些词的过滤规则，特别是在应对一些突发的热点事件时，非常有用。</li></ul><h4 id="2-敏感词分类与处理规则"><a href="#2-敏感词分类与处理规则" class="headerlink" title="2. 敏感词分类与处理规则"></a>2. 敏感词分类与处理规则</h4><p>仅仅有一个词库列表，在我看来，还是一个非常初级的设计。一个专业的敏感词系统，必须具备更“聪明”的、差异化的处理能力。为了实现这一点，我会在我的设计中，再增加两个维度：<strong>敏感词分类</strong> 和 <strong>处理规则</strong>。</p><ul><li><p><strong>第一步：为敏感词分类</strong><br>在“添加敏感词”时，我会要求运营同事，必须为这个词选择一个预设的“<strong>分类</strong>”。我会将词库至少分为以下几类：</p><ul><li><code>涉政类</code></li><li><code>暴恐类</code></li><li><code>色情类</code></li><li><code>广告营销类</code></li></ul></li><li><p><code>辱骂攻击类</code></p></li><li><p><strong>第二步：设定差异化的处理规则</strong><br>完成了分类，我就可以为 <strong>不同类别</strong> 的敏感词，设定 <strong>不同的自动化处理规则</strong>。这才是这个系统智能化的体现。</p><table><thead><tr><th align="left"><strong>敏感词分类</strong></th><th align="left"><strong>示例</strong></th><th align="left"><strong>我设定的处理规则</strong></th><th align="left"><strong>对用户的反馈</strong></th></tr></thead><tbody><tr><td align="left"><strong>辱骂攻击类</strong></td><td align="left">“傻 X”、“垃圾”等</td><td align="left"><strong>替换（Masking）</strong></td><td align="left">系统自动将“傻 X”替换为“**”，内容依然可以发布。</td></tr><tr><td align="left"><strong>广告营销类</strong></td><td align="left">“加 V 信”、“www. … .com”</td><td align="left"><strong>拦截（Block）</strong></td><td align="left">系统直接阻止该内容的发布，并向用户提示“内容包含违规广告信息，请修改”。</td></tr><tr><td align="left"><strong>涉政/暴恐等高危类</strong></td><td align="left">（一些高度敏感的词语）</td><td align="left"><strong>转人工审核（Manual Review）</strong></td><td align="left">内容发布后，用户自己可见，但其他用户不可见，同时该内容自动进入我们后台的“待审核”列表，由人工进行最终判定。</td></tr></tbody></table></li></ul><p>通过这套“<strong>分类+规则</strong>”的组合设计，我们的敏感词管理系统，就从一个简单的“过滤器”，升级为了一个具备初步智能的、能分级处理风险的“<strong>自动化审核引擎</strong>”。</p><hr><h3 id="9-2-4-分类管理"><a href="#9-2-4-分类管理" class="headerlink" title="9.2.4 分类管理"></a>9.2.4 分类管理</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133028297.png" alt="image-20250721133028297"></p><p>一个很核心的问题是：当创作者发布内容时，他可以选择的那些“分类”，是从哪里来的呢？答案就是由我们平台的运营人员，在这个“<strong>分类管理</strong>”后台中，进行统一的创建和维护。</p><p>在我看来，分类管理是为我们的内容产品，搭建一个清晰、有序的“<strong>图书馆目录</strong>”。它将直接决定我们产品前台的 <strong>频道划分</strong> 和 <strong>用户浏览结构</strong>。</p><p>我通常将这个“目录”体系，分为两个层级：宏观的“<strong>频道/分类</strong>”和微观的“<strong>标签</strong>”。</p><h4 id="1-频道-分类管理"><a href="#1-频道-分类管理" class="headerlink" title="1. 频道/分类管理"></a>1. 频道/分类管理</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133051445.png" alt="image-20250721133051445"></p><p>“频道/分类”是内容最高层级的划分，它通常直接对应着我们 App 首页的 <strong>导航栏</strong>，比如“科技”、“财经”、“体育”、“娱乐”等。</p><p>这个后台管理界面，我的设计要点如下：</p><ul><li><strong>分类的增删改查</strong>：这是最基础的功能。运营人员必须可以方便地 <code>新增分类</code>，并对已有的分类进行 <code>编辑</code> 和 <code>删除</code>。<ul><li><strong>我的拓展思考</strong>：在设计“删除”功能时，我必须考虑一个边界情况：如果某个分类下已经存在大量内容，那么直接删除这个分类，会导致这些内容成为“孤儿”。因此，一个严谨的删除逻辑应该是：<strong>当分类下存在内容时，禁止删除，并提示运营人员先将该分类下的内容，迁移至其他分类</strong>。</li></ul></li><li><strong>状态管理</strong>：和我们之前设计的其他模块一样，我必须为每个分类，提供“<strong>上线/下线</strong>”状态。这能让运营同事从容地去筹备一个新频道，在内容和运营活动都准备好之后，再一键“上线”，呈现给所有用户。</li><li><strong>排序功能</strong>：我认为 <strong>极其重要</strong> 的一个功能。运营同事必须可以 <strong>手动调整分类的显示顺序</strong>。这个后台的排序，将直接决定前端 App 导航栏的频道顺序。我通常会设计一个支持“上移/下移”或“拖拽排序”的功能。</li></ul><h4 id="2-标签管理"><a href="#2-标签管理" class="headerlink" title="2. 标签管理"></a>2. 标签管理</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133343391.png" alt="image-20250721133343391"></p><p>如果说“分类”是图书馆的“区域”（如：A 区-文学），那么“<strong>标签</strong>”就是贴在每一本书上，更精细的“<strong>关键词</strong>”（如：科幻、刘慈欣、三体）。我们为内容打上精准的标签，核心目的就是为了 <strong>喂给我们的算法推荐引擎</strong>，让它能实现更精准的“人-内容”匹配。</p><p>这个后台的设计，除了基础的增删改查和状态管理外，我还会重点考虑以下两点：</p><ul><li><strong>标签分类与层级</strong>：当标签数量达到成千上万时，一个扁平的列表是无法管理的。因此，我必须设计一个 <strong>支持层级</strong> 的标签体系。<ul><li>如图例所示，一个“<code>产品经理</code>”的标签，它的上级分类可能是“<code>互联网</code>”。这种树状的层级结构，有两大好处：<br>1. <strong>便于管理</strong>：运营同事可以像操作文件夹一样，高效地管理和查找标签。<br>2. <strong>便于算法</strong>：能让我们的推荐算法更“聪明”。算法会知道，喜欢“<code>产品经理</code>”标签内容的用户，可能也会对“<code>互联网</code>”这个更大范畴下的其他内容感兴趣，从而扩大推荐的相关性。</li></ul></li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133429415.png" alt="image-20250721133429415"></p><ul><li><strong>标签的创建与合并</strong>：<ul><li><strong>创建权限</strong>：我需要做一个重要的产品决策：<code>新标签是由谁来创建的？</code> 是只能由运营在后台创建？还是允许创作者在发布内容时，自由地创建新标签？前者能保证标签库的规范和整洁，后者则能让标签库的内容更丰富、更接地气。在产品初期，我通常会采用“<strong>运营后台创建为主，创作者可申请为辅</strong>”的策略。</li><li><strong>合并功能</strong>：当标签库由多人维护或允许用户创建时，不可避免地会出现语义相同但文字不同的标签（如：“产品经理”、“PM”、“产品策划”）。因此，我必须设计一个“<strong>标签合并</strong>”功能，让运营可以将这几个重复的标签，合并为一个标准标签，并自动更新所有关联了这些标签的内容。</li></ul></li></ul><hr><h2 id="9-3-运营管理"><a href="#9-3-运营管理" class="headerlink" title="9.3 运营管理"></a>9.3 运营管理</h2><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721134507525.png" alt="image-20250721134507525"></p><p>我们已经设计了用户和内容的管理后台，但这还不够。一个好的产品，不能只依赖用户“<strong>主动</strong>”地回来使用。在如今这个信息爆炸、App 泛滥的时代，“酒香也怕巷子深”。</p><p>我们必须建立一套属于自己的“<strong>广播系统</strong>”，能够主动地、在合适的时机，去触达我们的用户，提醒他们、吸引他们回来。这就是 <strong>运营管理</strong> 模块的核心价值。</p><h3 id="9-3-1-学习目标"><a href="#9-3-1-学习目标" class="headerlink" title="9.3.1 学习目标"></a>9.3.1 学习目标</h3><p>在本节中，我的目标是带大家设计一个专业的运营管理后台。我们将重点学习 <strong>消息推送</strong>、<strong>后台账号与权限</strong> 和 <strong>日志管理</strong> 这三大系统的设计。</p><h3 id="9-3-2-消息推送"><a href="#9-3-2-消息推送" class="headerlink" title="9.3.2 消息推送"></a>9.3.2 消息推送</h3><p>消息推送（Push Notification）是我作为平台运营方，唯一能“<strong>免费</strong>”且“<strong>主动</strong>”触达已安装 App 但未打开用户的渠道。它是拉动用户活跃、进行活动通知、实现用户召回的最强武器。</p><h4 id="1-推送任务列表与数据统计"><a href="#1-推送任务列表与数据统计" class="headerlink" title="1. 推送任务列表与数据统计"></a>1. 推送任务列表与数据统计</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721134611755.png" alt="image-20250721134611755"></p><p>这是我运营团队的“<strong>发射控制中心</strong>”。它是一个记录了所有已发送和待发送推送任务的列表。除了展示 <code>标题</code>、<code>目标用户</code>、<code>推送时间</code> 等基础信息外，一个专业后台的核心，在于提供推送效果的数据统计。</p><table><thead><tr><th align="left">数据指标</th><th align="left">说明</th></tr></thead><tbody><tr><td align="left"><strong>发送数 (Sent)</strong></td><td align="left">本次任务，我们总共向多少个设备发送了通知。</td></tr><tr><td align="left"><strong>到达数 (Delivered)</strong></td><td align="left">其中，成功送达到用户设备的数量。（有些可能因网络或卸载失败）</td></tr><tr><td align="left"><strong>点击数 (Clicks/Opens)</strong></td><td align="left">最终，有多少用户被我们的文案吸引，点击了这条通知。</td></tr><tr><td align="left"><strong>点击率 (CTR)</strong></td><td align="left"><strong>最重要的评估指标（CTR = 点击数 / 到达数）</strong>，直接反映了推送的质量。</td></tr></tbody></table><h4 id="2-新建推送配置"><a href="#2-新建推送配置" class="headerlink" title="2. 新建推送配置"></a>2. 新建推送配置</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721134721627.png" alt="image-20250721134721627"></p><p>这是我为运营同事设计的“<strong>炮弹编辑器</strong>”。这个“新建推送”的表单，必须严谨、清晰，我通常会把它设计为“<strong>推送四要素</strong>”的配置。</p><table><thead><tr><th align="left">配置要素</th><th align="left">核心选项与说明</th></tr></thead><tbody><tr><td align="left"><strong>推送对象 (Who)</strong></td><td align="left"><strong>全部用户</strong>：用于平台级的重大公告。<br><strong>用户分群</strong>：按用户标签（如地域、兴趣）、活跃度等进行精准推送。<br><strong>指定用户</strong>：通过上传用户 ID 列表，进行点对点推送。</td></tr><tr><td align="left"><strong>推送时间 (When)</strong></td><td align="left"><strong>立即推送</strong>：用于突发新闻、热点事件。<br><strong>定时推送</strong>：用于已规划好的运营活动，可以提前设置。</td></tr><tr><td align="left"><strong>推送内容 (What)</strong></td><td align="left"><strong>通知标题</strong>：吸引用户眼球的第一句话，至关重要。<br><strong>通知内容</strong>：对标题的补充说明，可支持插入用户昵称等个性化变量。</td></tr><tr><td align="left"><strong>目标页配置 (Where To)</strong></td><td align="left"><strong>打开应用首页</strong><br><strong>打开应用内指定页面</strong>：如某篇文章、某个活动页。<br><strong>打开一个 H5 链接</strong>：跳转到外部网页。</td></tr></tbody></table><p><strong>我的拓展设计（技术实现浅谈）</strong>：<br>这个功能的技术实现，我们通常不会自己从零搭建，而是会依赖专业的 <strong>第三方推送服务</strong>。对于 iOS 端，我们后台需要接入苹果官方的 <strong>APNs</strong>；对于国内的 Android 端，我通常会选择集成一个像 <strong>极光推送（JPush）或个推</strong> 这样的第三方聚合服务商。我的 PRD 需要明确技术方案，因为不同服务商的能力会影响我的产品设计。</p><h3 id="9-3-3-账号与权限管理"><a href="#9-3-3-账号与权限管理" class="headerlink" title="9.3.3 账号与权限管理"></a>9.3.3 账号与权限管理</h3><p>这个模块，管理的不是我们产品的“用户”，而是我们公司内部使用这个后台系统的“<strong>员工</strong>”。其设计的核心，是确保后台系统的 <strong>安全性</strong> 和 <strong>规范性</strong>。</p><h4 id="1-核心思想：RBAC-模型"><a href="#1-核心思想：RBAC-模型" class="headerlink" title="1. 核心思想：RBAC 模型"></a>1. 核心思想：RBAC 模型</h4><p>我设计后台权限，普遍采用的是 <strong>RBAC（Role-Based Access Control，基于角色的访问控制）</strong> 模型。</p><p>它的逻辑很简单：我不直接给“某个人”分配权限，而是先创建不同的“<strong>角色</strong>”（如：内容审核员、高级运营），为这些“角色”分配权限，最后再把“某个人”归属到某个“角色”里去。</p><p>这样做的好处是，当公司人员变动时，我只需要调整这个人的角色，而不需要重新为他配置一遍复杂的权限，管理效率极高。</p><h4 id="2-角色管理"><a href="#2-角色管理" class="headerlink" title="2. 角色管理"></a>2. 角色管理</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721135012847.png" alt="image-20250721135012847"></p><ul><li><strong>角色列表</strong>：一个展示所有已创建角色的列表，如“超级管理员”、“内容审核”、“数据分析师”等。</li><li><strong>新建/编辑角色</strong>：提供创建新角色的功能。</li><li><strong>角色权限配置</strong>：这是核心。我会以“功能菜单树”的形式，列出后台的所有功能点，让管理员可以通过勾选的方式，为每个角色分配它能看到和操作的菜单权限。</li></ul><h4 id="3-账号管理（后台用户）"><a href="#3-账号管理（后台用户）" class="headerlink" title="3. 账号管理（后台用户）"></a>3. 账号管理（后台用户）</h4><p>这是具体管理员工账号的地方。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721135036854.png" alt="image-20250721135036854"></p><ul><li><strong>后台账号列表</strong>：展示所有后台用户的列表。</li><li><strong>新增/编辑账号</strong>：当我需要为一位新同事开通后台权限时，我会在这里为他新建账号。</li></ul><table><thead><tr><th align="left">新增账号字段</th><th align="left">填写说明</th></tr></thead><tbody><tr><td align="left"><strong>登录账号</strong></td><td align="left">用于后台登录的唯一 ID，建议使用员工的企业邮箱。</td></tr><tr><td align="left"><strong>员工姓名</strong></td><td align="left">账号使用者的真实姓名，用于日志记录和责任追溯。</td></tr><tr><td align="left"><strong>所属角色</strong></td><td align="left">从我们创建的“角色列表”中，为该账号选择一个角色，从而赋予他对应的权限。</td></tr><tr><td align="left"><strong>账号状态</strong></td><td align="left">正常/冻结。当员工离职时，我可以将其账号冻结，而不是直接删除。</td></tr></tbody></table><h3 id="9-3-4-日志管理"><a href="#9-3-4-日志管理" class="headerlink" title="9.3.4 日志管理"></a>9.3.4 日志管理</h3><p>日志管理是后台的“<strong>黑匣子</strong>”和“<strong>监控录像</strong>”。它记录了所有管理员在后台的一举一动，是进行安全审计和问题追溯的最后一道防线。</p><h4 id="1-操作日志的价值"><a href="#1-操作日志的价值" class="headerlink" title="1. 操作日志的价值"></a>1. 操作日志的价值</h4><p>它的价值在于 <strong>安全</strong> 和 <strong>可追溯</strong>。当后台出现误操作或恶意操作时，我可以通过日志，精准地定位到是“谁”，在“什么时间”，对“什么东西”，做了“什么操作”。</p><h4 id="2-操作日志设计要点"><a href="#2-操作日志设计要点" class="headerlink" title="2. 操作日志设计要点"></a>2. 操作日志设计要点</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721135143985.png" alt="image-20250721135143985"></p><p>一个合格的操作日志系统，必须清晰地记录以下关键信息，并提供强大的查询功能。</p><table><thead><tr><th align="left">记录字段</th><th align="left">说明</th><th align="left">示例</th></tr></thead><tbody><tr><td align="left"><strong>操作人</strong></td><td align="left">执行该操作的后台账号。</td><td align="left"><code>yunying_xiaowang</code></td></tr><tr><td align="left"><strong>时间戳</strong></td><td align="left">操作发生的精确时间。</td><td align="left"><code>2025-07-21 14:42:00</code></td></tr><tr><td align="left"><strong>IP 地址</strong></td><td align="left">操作人当时使用的 IP 地址。</td><td align="left"><code>************</code></td></tr><tr><td align="left"><strong>操作行为</strong></td><td align="left">具体执行了什么动作。</td><td align="left"><code>内容管理 - 下架文章</code></td></tr><tr><td align="left"><strong>操作对象</strong></td><td align="left">该动作作用于哪个具体目标。</td><td align="left"><code>文章ID: 88012</code></td></tr><tr><td align="left"><strong>操作结果</strong></td><td align="left">本次操作是成功还是失败。</td><td align="left"><code>成功</code></td></tr></tbody></table><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/38041.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/38041.html&quot;)">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/38041.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/38041.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/11780.html"><img class="prev-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div><div class="next-post pull-right"><a href="/posts/7673.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理入门（十）：第十章：产品研发全流程管理</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/59297.html" title="产品经理入门（三）：第三章：需求分析"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（三）：第三章：需求分析</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div><div><a href="/posts/11780.html" title="产品经理入门（八）：第八章：内容产品自媒体端设计"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理入门（九）：第九章：平台端设计（用户-内容-运营）",date:"2025-07-21 00:13:45",updated:"2025-07-21 14:51:52",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第九章：平台端设计（用户-内容-运营）\n\n![image-20250721124904426](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721124904426.png)\n\n在前面两个章节，我们已经为产品的“前台”——用户端和自媒体端，设计好了舞台和化妆间。现在，我们需要开始搭建整个剧院的“**后台**”——**平台端（Admin Backend）**。\n\n这是我们作为平台运营和管理人员，进行日常工作的“驾驶舱”，是整个产品生态能够有序、健康运转的中枢神经。我们将从这个后台最基础，也是最重要的模块开始：**用户管理**。\n\n## 9.1 用户管理\n\n我设计用户管理模块的核心思路是“**分类与控制**”。正如思考题所提示的，管理一名普通的内容消费者，和管理一名专业的内容创作者，其关注点和所需要的工具是截然不同的。因此，我的后台设计，必须清晰地将他们分类，并提供差异化的管理能力。\n\n### 9.1.1 学习目标\n\n在本节中，我的目标是带大家掌握一套专业的后台用户管理系统的设计方法。我们将学习如何分别为 **普通用户** 和 **自媒体用户** 设计管理列表，并重点拆解 **自媒体的审核流程**，最后还会介绍一个平台运营中非常实用的高级功能——**马甲管理**。\n\n### 9.1.2 普通用户管理\n\n![image-20250721124938842](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721124938842.png)\n\n这是我们用户管理的基础模块，用于管理平台上所有的内容消费者。\n\n#### 1. 用户列表与查询\n\n一个合格的用户列表，必须具备强大的查询和筛选能力。我需要为运营同事，提供多维度的查询字段，比如：\n\n* **昵称**\n* **手机号码**\n* **用户状态**（如：正常、已封禁）\n* **用户性别**\n\n列表的表头，则需要清晰地展示用户的核心信息，如 `昵称`、`头像`、`性别`、`地区`、`手机号`、`用户状态` 等。\n\n#### 2. 用户详情查看\n\n在操作列，点击“查看”，运营同事可以进入用户的详情页，看到该用户更完整的资料、行为日志、消费记录等。\n\n#### 3. 用户状态管理（封禁/解封）\n\n这是最重要的管理权限。在操作列，我必须提供“**封禁**”功能。当一个用户出现违规行为时，运营同事可以将其账号封禁。当然，也必须提供对应的“**解封**”功能。\n\n### 9.1.3 自媒体用户管理\n\n![image-20250721125236680](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721125236680.png)\n\n管理创作者，我们需要关注比普通用户更多的信息。\n\n#### 1. 创作者列表与查询\n\n创作者列表的设计，在普通用户列表的基础上，我会额外增加几个关键的展示字段和筛选条件：\n\n* **认证类型**：清晰地标识出该创作者是“个人认证”还是“企业认证”。\n* **认证时间**：记录其通过审核、正式成为创作者的时间。\n\n#### 2. 自媒体审核\n\n![image-20250721125408677](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721125408677.png)\n\n这是自媒体用户管理中，一个独立且至关重要的工作流，我通常会把它设计成一个单独的页面。\n\n* **待审核列表**：这个页面的默认视图，就是所有提交了入驻申请、正在等待审核的用户列表。\n* **审核详情页（通过/驳回）**：运营同事点击“审核”后，可以查看该用户提交的所有资质信息。在这个页面，必须有两个明确的操作按钮：“**通过**”和“**驳回**”。如果选择“驳回”，还需要提供一个填写驳回理由的输入框。\n* **审核历史记录**：系统需要记录所有的审核操作，便于未来追溯。\n\n\n\n---\n\n### 9.1.4 马甲管理\n\n现在，我们来探讨一个平台运营中，非常实用甚至可以说是必不可少的高阶功能——**马甲管理**。\n\n#### 1. 马甲管理的价值与“鲶鱼效应”\n\n“马甲”，指的就是由我们平台内部运营人员，在后台创建和控制的“虚拟用户”。我设计这个功能，主要是为了在社区运营中，起到“**鲶鱼效应**”——即，通过引入一些活跃的“鲶鱼”（马甲），来激活整个“鱼塘”（真实用户生态）的活力。\n\n它的核心价值主要体现在两方面：\n\n1. **制造争议、热点话题，带节奏**：在社区冷启动或需要引导舆论时，我的运营同事可以使用马甲，发布一些具有话题性的内容，引发用户讨论，掌握社区的话题走向。\n2. **灌水，活跃社区氛围**：在社区初期用户较少时，通过马甲发布一些日常内容、进行评论和互动，可以快速地让社区看起来“有人气”，打破“无人区”的尴尬，从而吸引真实用户加入和发言。\n\n#### 2. 添加马甲\n\n![image-20250721131112565](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721131112565.png)\n\n为了让运营同事能方便地创建这些虚拟用户，我需要设计一个“**新增马甲**”的功能，它通常是一个后台的弹窗表单。\n\n* **核心字段**：表单中需要包含创建逼真用户所需的核心字段，比如 `马甲昵称`、`头像`、`性别`、`地区` 等。具体需要哪些字段，由我们产品的实际业务决定。\n* **归属管理员**：这是我设计中非常关键的一环。为了分工明确、责任到人，**每一个马甲，都必须可以分配给一个指定的管理员**。这样，我们就能清晰地知道，哪个马甲由哪位运营同事负责使用和维护。\n\n#### 3. 马甲列表\n\n![image-20250721131135140](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721131135140.png)\n\n所有创建的马甲，都需要在一个单独的“**马甲管理**”列表中进行统一的查看和维护。这个列表，只有特定的、高权限的管理员才能看到。\n\n* **列表设计要点**：\n    1. **查询与筛选**：列表必须提供强大的查询功能，特别是要支持按“**归属管理员**”进行筛选，方便运营主管查看自己团队成员名下的马甲。\n    2. **信息展示**：列表中需要清晰地展示 `马甲昵称`、`归属管理员`、`状态` 等核心信息。\n    3. **技术实现**：一个设计要点是，马甲可以不需要像真实用户一样，拥有完整的账号密码体系。从技术实现上，它可以是一个仅有昵称、头像等信息的“虚拟身份”，能通过后台直接进行内容发布和评论即可。\n\n\n\n---\n\n## 9.2 内容管理\n\n![image-20250721131945141](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721131945141.png)\n\n在第八章，我们为创作者（自媒体）设计了他们的“创作车间”。但这引出了一个问题：作为平台，我们难道只需要提供工具，然后对海量的内容放任不管吗？\n\n答案显然是 **否定** 的。\n\n一个健康的内容生态，平台绝不能只当一个被动的“房东”，而必须是一个积极的“**秩序维护者**”和“**价值发现者**”。**内容管理** 后台，就是我们履行这个职责的核心工具。\n\n### 9.2.1 学习目标\n\n在本节中，我的目标是带大家设计一个专业、高效的内容管理后台。我们将重点学习 **内容列表** 的设计，掌握如何实现强大的 **查询与筛选**、严谨的 **内容审核** 流程，以及精细化的 **内容推荐与加权** 等高级运营功能。\n\n### 9.2.2 内容列表\n\n![image-20250721132036484](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721132036484.png)\n\n内容管理模块的核心，就是这张包罗万象的 **内容列表**。这是我们的运营和审核同学，日常工作中停留时间最长的页面。我设计的核心目标是 **效率** 和 **掌控力**。\n\n#### 1. 内容查询与筛选\n\n一个内容平台，文章动辄成千上万。如果不能让运营同学在 3 秒内找到他想要的内容，那这个后台就是失败的。因此，我必须设计一套强大的查询与筛选系统。\n\n* **基础筛选字段**：根据图例，至少要包含：\n  * `文章标题`\n  * `作者昵称`\n  * `发表时间`（支持按时间范围筛选）\n  * `文章分类`\n\n* **我的拓展设计**：为了让管理更精细，我还会额外增加几个关键的筛选条件：\n  * `内容ID`：用于研发同学进行精准的问题定位。\n  * `来源`：这是图例中提到的一个设计要点。我必须让运营可以筛选出 **“自媒体发布”** 或 **“普通用户发布”**（如果我们的产品支持）的内容，因为这两者的审核标准和权重可能不同。\n  * `审核状态`：这是审核人员最高频使用的筛选条件。他们可以通过筛选“**待审核**”状态，快速进入自己的工作队列。\n\n#### 2. 内容审核（通过/驳回/下架）\n\n这是内容列表的“控制核心”，体现在“**操作**”这一列。一个严谨的后台，其可执行的操作，必须与内容的“**审核状态**”严格绑定。我会将它设计成一个状态机：\n\n* **当内容状态为“待审核”时**：\n    操作列应提供：`查看`、`通过`、`驳回`。\n  * **设计要点**：点击“驳回”时，必须弹出一个输入框，让审核人员填写驳回理由，这个理由会反馈给创作者。\n\n* **当内容状态为“已通过”（即已上线）时**：\n    操作列应变为：`查看`、`下架`、`删除`。\n  * **设计要点**：“下架”是一个软删除，内容仅对用户不可见，作者后台依然可见；而“删除”则是一个硬删除，会彻底清除内容。我必须为“删除”操作，设计一个“二次确认”的弹窗，防止误操作。\n\n* **当内容状态为“已驳回”或“已下架”时**：\n    操作列可以简化为：`查看`、`删除`。\n\n#### 3. 内容推荐与加权\n\n除了基础的审核，一个优秀的后台，还要能让运营同事对优质内容进行“助推”。我会增加几个高级运营功能：\n\n* **置顶**：如图例所示，这是最常见的运营手段。提供一个“置顶”按钮，可以将优质内容在前端的某个列表（如分类列表、频道首页）顶部固定显示。我还会设计一个“取消置顶”的功能。\n* **加权**：这是一个更精细化的运营工具。我会在后台为每篇文章增加一个“**权重值**”输入框（比如 1-100）。我们前端的算法推荐或热度排序公式，就可以把这个“人工权重”作为一个重要的计算因子。这样，我就实现了“**人工编辑意志**”与“**算法推荐**”的完美结合。\n* **推送**：对于 S 级的顶级内容，我还会设计一个“**推送**”按钮。运营同事点击后，可以将这篇文章通过 Push（推送通知）的形式，直接触达我们的用户，实现最大化的曝光。\n\n\n---\n\n### 9.2.3 敏感词管理\n\n在我们的内容列表中，审核人员需要对“待审核”的内容进行人工处理。但面对 UGC 平台海量的内容生产，如果完全依赖人工，审核团队会被瞬间淹没。\n\n因此，我必须设计一套 **自动化的初审过滤系统**，来分担团队的压力，并将最明显的违规内容，拦截在“摇篮”里。这套系统的核心，就是 **敏感词管理**。\n\n![image-20250721132535224](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721132535224.png)\n\n#### 1. 敏感词库维护（增/删/改/查）\n\n我需要为我的运营同事，提供一个简单、易用的后台界面，来持续地维护我们平台的“**敏感词词库**”。这个词库，就是我们自动化审核的“规则库”。\n\n这个后台界面，必须具备以下核心功能：\n\n* **添加敏感词**：提供一个“+ 添加敏感词”的入口，让运营可以随时将新发现的违规词语录入系统。\n* **查询与筛选**：当词库变得庞大时，必须提供按“**敏感词**”本身进行搜索，或按“**状态**”进行筛选的功能。\n* **编辑与状态管理**：这是我设计中的一个要点。除了编辑和删除，我必须为每个敏感词，增加一个“**上线/下线**”的状态。只有处于“**上线**”状态的词，才会被系统用于前端的匹配过滤。这个设计，能让运营同事在不删除历史词语的情况下，灵活地启用或禁用某些词的过滤规则，特别是在应对一些突发的热点事件时，非常有用。\n\n#### 2. 敏感词分类与处理规则\n\n仅仅有一个词库列表，在我看来，还是一个非常初级的设计。一个专业的敏感词系统，必须具备更“聪明”的、差异化的处理能力。为了实现这一点，我会在我的设计中，再增加两个维度：**敏感词分类** 和 **处理规则**。\n\n* **第一步：为敏感词分类**\n    在“添加敏感词”时，我会要求运营同事，必须为这个词选择一个预设的“**分类**”。我会将词库至少分为以下几类：\n\n  * `涉政类`\n  * `暴恐类`\n  * `色情类`\n  * `广告营销类`\n* `辱骂攻击类`\n  \n* **第二步：设定差异化的处理规则**\n    完成了分类，我就可以为 **不同类别** 的敏感词，设定 **不同的自动化处理规则**。这才是这个系统智能化的体现。\n\n    | **敏感词分类** | **示例** | **我设定的处理规则** | **对用户的反馈** |\n    | :--- | :--- | :--- | :--- |\n    | **辱骂攻击类** | “傻 X”、“垃圾”等 | **替换（Masking）** | 系统自动将“傻 X”替换为“**”，内容依然可以发布。 |\n    | **广告营销类** | “加 V 信”、“www. ... .com” | **拦截（Block）** | 系统直接阻止该内容的发布，并向用户提示“内容包含违规广告信息，请修改”。 |\n    | **涉政/暴恐等高危类**| （一些高度敏感的词语） | **转人工审核（Manual Review）** | 内容发布后，用户自己可见，但其他用户不可见，同时该内容自动进入我们后台的“待审核”列表，由人工进行最终判定。 |\n\n通过这套“**分类+规则**”的组合设计，我们的敏感词管理系统，就从一个简单的“过滤器”，升级为了一个具备初步智能的、能分级处理风险的“**自动化审核引擎**”。\n\n\n\n---\n\n### 9.2.4 分类管理\n\n![image-20250721133028297](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133028297.png)\n\n一个很核心的问题是：当创作者发布内容时，他可以选择的那些“分类”，是从哪里来的呢？答案就是由我们平台的运营人员，在这个“**分类管理**”后台中，进行统一的创建和维护。\n\n在我看来，分类管理是为我们的内容产品，搭建一个清晰、有序的“**图书馆目录**”。它将直接决定我们产品前台的 **频道划分** 和 **用户浏览结构**。\n\n我通常将这个“目录”体系，分为两个层级：宏观的“**频道/分类**”和微观的“**标签**”。\n\n#### 1. 频道/分类管理\n\n![image-20250721133051445](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133051445.png)\n\n“频道/分类”是内容最高层级的划分，它通常直接对应着我们 App 首页的 **导航栏**，比如“科技”、“财经”、“体育”、“娱乐”等。\n\n这个后台管理界面，我的设计要点如下：\n\n* **分类的增删改查**：这是最基础的功能。运营人员必须可以方便地 `新增分类`，并对已有的分类进行 `编辑` 和 `删除`。\n  * **我的拓展思考**：在设计“删除”功能时，我必须考虑一个边界情况：如果某个分类下已经存在大量内容，那么直接删除这个分类，会导致这些内容成为“孤儿”。因此，一个严谨的删除逻辑应该是：**当分类下存在内容时，禁止删除，并提示运营人员先将该分类下的内容，迁移至其他分类**。\n* **状态管理**：和我们之前设计的其他模块一样，我必须为每个分类，提供“**上线/下线**”状态。这能让运营同事从容地去筹备一个新频道，在内容和运营活动都准备好之后，再一键“上线”，呈现给所有用户。\n* **排序功能**：我认为 **极其重要** 的一个功能。运营同事必须可以 **手动调整分类的显示顺序**。这个后台的排序，将直接决定前端 App 导航栏的频道顺序。我通常会设计一个支持“上移/下移”或“拖拽排序”的功能。\n\n#### 2. 标签管理\n\n![image-20250721133343391](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133343391.png)\n\n如果说“分类”是图书馆的“区域”（如：A 区-文学），那么“**标签**”就是贴在每一本书上，更精细的“**关键词**”（如：科幻、刘慈欣、三体）。我们为内容打上精准的标签，核心目的就是为了 **喂给我们的算法推荐引擎**，让它能实现更精准的“人-内容”匹配。\n\n这个后台的设计，除了基础的增删改查和状态管理外，我还会重点考虑以下两点：\n\n* **标签分类与层级**：当标签数量达到成千上万时，一个扁平的列表是无法管理的。因此，我必须设计一个 **支持层级** 的标签体系。\n  * 如图例所示，一个“`产品经理`”的标签，它的上级分类可能是“`互联网`”。这种树状的层级结构，有两大好处：\n        1. **便于管理**：运营同事可以像操作文件夹一样，高效地管理和查找标签。\n        2. **便于算法**：能让我们的推荐算法更“聪明”。算法会知道，喜欢“`产品经理`”标签内容的用户，可能也会对“`互联网`”这个更大范畴下的其他内容感兴趣，从而扩大推荐的相关性。\n\n\n![image-20250721133429415](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721133429415.png)\n\n* **标签的创建与合并**：\n  * **创建权限**：我需要做一个重要的产品决策：`新标签是由谁来创建的？` 是只能由运营在后台创建？还是允许创作者在发布内容时，自由地创建新标签？前者能保证标签库的规范和整洁，后者则能让标签库的内容更丰富、更接地气。在产品初期，我通常会采用“**运营后台创建为主，创作者可申请为辅**”的策略。\n  * **合并功能**：当标签库由多人维护或允许用户创建时，不可避免地会出现语义相同但文字不同的标签（如：“产品经理”、“PM”、“产品策划”）。因此，我必须设计一个“**标签合并**”功能，让运营可以将这几个重复的标签，合并为一个标准标签，并自动更新所有关联了这些标签的内容。\n\n\n\n-----\n\n## 9.3 运营管理\n\n![image-20250721134507525](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721134507525.png)\n\n我们已经设计了用户和内容的管理后台，但这还不够。一个好的产品，不能只依赖用户“**主动**”地回来使用。在如今这个信息爆炸、App 泛滥的时代，“酒香也怕巷子深”。\n\n我们必须建立一套属于自己的“**广播系统**”，能够主动地、在合适的时机，去触达我们的用户，提醒他们、吸引他们回来。这就是 **运营管理** 模块的核心价值。\n\n### 9.3.1 学习目标\n\n在本节中，我的目标是带大家设计一个专业的运营管理后台。我们将重点学习 **消息推送**、**后台账号与权限** 和 **日志管理** 这三大系统的设计。\n\n### 9.3.2 消息推送\n\n消息推送（Push Notification）是我作为平台运营方，唯一能“**免费**”且“**主动**”触达已安装 App 但未打开用户的渠道。它是拉动用户活跃、进行活动通知、实现用户召回的最强武器。\n\n#### 1\\. 推送任务列表与数据统计\n\n![image-20250721134611755](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721134611755.png)\n\n这是我运营团队的“**发射控制中心**”。它是一个记录了所有已发送和待发送推送任务的列表。除了展示 `标题`、`目标用户`、`推送时间` 等基础信息外，一个专业后台的核心，在于提供推送效果的数据统计。\n\n| 数据指标 | 说明 |\n| :--- | :--- |\n| **发送数 (Sent)** | 本次任务，我们总共向多少个设备发送了通知。 |\n| **到达数 (Delivered)**| 其中，成功送达到用户设备的数量。（有些可能因网络或卸载失败） |\n| **点击数 (Clicks/Opens)**| 最终，有多少用户被我们的文案吸引，点击了这条通知。 |\n| **点击率 (CTR)**| **最重要的评估指标（CTR = 点击数 / 到达数）**，直接反映了推送的质量。 |\n\n#### 2\\. 新建推送配置\n\n![image-20250721134721627](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721134721627.png)\n\n这是我为运营同事设计的“**炮弹编辑器**”。这个“新建推送”的表单，必须严谨、清晰，我通常会把它设计为“**推送四要素**”的配置。\n\n| 配置要素 | 核心选项与说明 |\n| :--- | :--- |\n| **推送对象 (Who)** | **全部用户**：用于平台级的重大公告。<br>**用户分群**：按用户标签（如地域、兴趣）、活跃度等进行精准推送。<br>**指定用户**：通过上传用户 ID 列表，进行点对点推送。 |\n| **推送时间 (When)**| **立即推送**：用于突发新闻、热点事件。<br>**定时推送**：用于已规划好的运营活动，可以提前设置。 |\n| **推送内容 (What)**| **通知标题**：吸引用户眼球的第一句话，至关重要。<br>**通知内容**：对标题的补充说明，可支持插入用户昵称等个性化变量。 |\n| **目标页配置 (Where To)**| **打开应用首页**<br>**打开应用内指定页面**：如某篇文章、某个活动页。<br>**打开一个 H5 链接**：跳转到外部网页。 |\n\n**我的拓展设计（技术实现浅谈）**：\n这个功能的技术实现，我们通常不会自己从零搭建，而是会依赖专业的 **第三方推送服务**。对于 iOS 端，我们后台需要接入苹果官方的 **APNs**；对于国内的 Android 端，我通常会选择集成一个像 **极光推送（JPush）或个推** 这样的第三方聚合服务商。我的 PRD 需要明确技术方案，因为不同服务商的能力会影响我的产品设计。\n\n### 9.3.3 账号与权限管理\n\n这个模块，管理的不是我们产品的“用户”，而是我们公司内部使用这个后台系统的“**员工**”。其设计的核心，是确保后台系统的 **安全性** 和 **规范性**。\n\n#### 1\\. 核心思想：RBAC 模型\n\n我设计后台权限，普遍采用的是 **RBAC（Role-Based Access Control，基于角色的访问控制）** 模型。\n\n它的逻辑很简单：我不直接给“某个人”分配权限，而是先创建不同的“**角色**”（如：内容审核员、高级运营），为这些“角色”分配权限，最后再把“某个人”归属到某个“角色”里去。\n\n这样做的好处是，当公司人员变动时，我只需要调整这个人的角色，而不需要重新为他配置一遍复杂的权限，管理效率极高。\n\n#### 2\\. 角色管理\n\n![image-20250721135012847](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721135012847.png)\n\n* **角色列表**：一个展示所有已创建角色的列表，如“超级管理员”、“内容审核”、“数据分析师”等。\n* **新建/编辑角色**：提供创建新角色的功能。\n* **角色权限配置**：这是核心。我会以“功能菜单树”的形式，列出后台的所有功能点，让管理员可以通过勾选的方式，为每个角色分配它能看到和操作的菜单权限。\n\n#### 3\\. 账号管理（后台用户）\n\n这是具体管理员工账号的地方。\n\n![image-20250721135036854](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721135036854.png)\n\n* **后台账号列表**：展示所有后台用户的列表。\n* **新增/编辑账号**：当我需要为一位新同事开通后台权限时，我会在这里为他新建账号。\n\n| 新增账号字段 | 填写说明 |\n| :--- | :--- |\n| **登录账号** | 用于后台登录的唯一 ID，建议使用员工的企业邮箱。 |\n| **员工姓名** | 账号使用者的真实姓名，用于日志记录和责任追溯。 |\n| **所属角色** | 从我们创建的“角色列表”中，为该账号选择一个角色，从而赋予他对应的权限。 |\n| **账号状态** | 正常/冻结。当员工离职时，我可以将其账号冻结，而不是直接删除。 |\n\n### 9.3.4 日志管理\n\n日志管理是后台的“**黑匣子**”和“**监控录像**”。它记录了所有管理员在后台的一举一动，是进行安全审计和问题追溯的最后一道防线。\n\n#### 1\\. 操作日志的价值\n\n它的价值在于 **安全** 和 **可追溯**。当后台出现误操作或恶意操作时，我可以通过日志，精准地定位到是“谁”，在“什么时间”，对“什么东西”，做了“什么操作”。\n\n#### 2\\. 操作日志设计要点\n\n![image-20250721135143985](https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/image-20250721135143985.png)\n\n一个合格的操作日志系统，必须清晰地记录以下关键信息，并提供强大的查询功能。\n\n| 记录字段 | 说明 | 示例 |\n| :--- | :--- | :--- |\n| **操作人** | 执行该操作的后台账号。 | `yunying_xiaowang` |\n| **时间戳** | 操作发生的精确时间。 | `2025-07-21 14:42:00` |\n| **IP 地址** | 操作人当时使用的 IP 地址。 | `************` |\n| **操作行为**| 具体执行了什么动作。 | `内容管理 - 下架文章` |\n| **操作对象**| 该动作作用于哪个具体目标。 | `文章ID: 88012` |\n| **操作结果**| 本次操作是成功还是失败。 | `成功` |\n\n\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B9%9D%E7%AB%A0%EF%BC%9A%E5%B9%B3%E5%8F%B0%E7%AB%AF%E8%AE%BE%E8%AE%A1%EF%BC%88%E7%94%A8%E6%88%B7-%E5%86%85%E5%AE%B9-%E8%BF%90%E8%90%A5%EF%BC%89"><span class="toc-number">1.</span> <span class="toc-text">第九章：平台端设计（用户-内容-运营）</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#9-1-%E7%94%A8%E6%88%B7%E7%AE%A1%E7%90%86"><span class="toc-number">1.1.</span> <span class="toc-text">9.1 用户管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#9-1-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.1.1.</span> <span class="toc-text">9.1.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-1-2-%E6%99%AE%E9%80%9A%E7%94%A8%E6%88%B7%E7%AE%A1%E7%90%86"><span class="toc-number">1.1.2.</span> <span class="toc-text">9.1.2 普通用户管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%94%A8%E6%88%B7%E5%88%97%E8%A1%A8%E4%B8%8E%E6%9F%A5%E8%AF%A2"><span class="toc-number">1.1.2.1.</span> <span class="toc-text">1. 用户列表与查询</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E7%94%A8%E6%88%B7%E8%AF%A6%E6%83%85%E6%9F%A5%E7%9C%8B"><span class="toc-number">1.1.2.2.</span> <span class="toc-text">2. 用户详情查看</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%94%A8%E6%88%B7%E7%8A%B6%E6%80%81%E7%AE%A1%E7%90%86%EF%BC%88%E5%B0%81%E7%A6%81-%E8%A7%A3%E5%B0%81%EF%BC%89"><span class="toc-number">1.1.2.3.</span> <span class="toc-text">3. 用户状态管理（封禁/解封）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-1-3-%E8%87%AA%E5%AA%92%E4%BD%93%E7%94%A8%E6%88%B7%E7%AE%A1%E7%90%86"><span class="toc-number">1.1.3.</span> <span class="toc-text">9.1.3 自媒体用户管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%88%9B%E4%BD%9C%E8%80%85%E5%88%97%E8%A1%A8%E4%B8%8E%E6%9F%A5%E8%AF%A2"><span class="toc-number">1.1.3.1.</span> <span class="toc-text">1. 创作者列表与查询</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%87%AA%E5%AA%92%E4%BD%93%E5%AE%A1%E6%A0%B8"><span class="toc-number">1.1.3.2.</span> <span class="toc-text">2. 自媒体审核</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-1-4-%E9%A9%AC%E7%94%B2%E7%AE%A1%E7%90%86"><span class="toc-number">1.1.4.</span> <span class="toc-text">9.1.4 马甲管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%A9%AC%E7%94%B2%E7%AE%A1%E7%90%86%E7%9A%84%E4%BB%B7%E5%80%BC%E4%B8%8E%E2%80%9C%E9%B2%B6%E9%B1%BC%E6%95%88%E5%BA%94%E2%80%9D"><span class="toc-number">1.1.4.1.</span> <span class="toc-text">1. 马甲管理的价值与“鲶鱼效应”</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%B7%BB%E5%8A%A0%E9%A9%AC%E7%94%B2"><span class="toc-number">1.1.4.2.</span> <span class="toc-text">2. 添加马甲</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E9%A9%AC%E7%94%B2%E5%88%97%E8%A1%A8"><span class="toc-number">1.1.4.3.</span> <span class="toc-text">3. 马甲列表</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#9-2-%E5%86%85%E5%AE%B9%E7%AE%A1%E7%90%86"><span class="toc-number">1.2.</span> <span class="toc-text">9.2 内容管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.2.1.</span> <span class="toc-text">9.2.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-2-%E5%86%85%E5%AE%B9%E5%88%97%E8%A1%A8"><span class="toc-number">1.2.2.</span> <span class="toc-text">9.2.2 内容列表</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%86%85%E5%AE%B9%E6%9F%A5%E8%AF%A2%E4%B8%8E%E7%AD%9B%E9%80%89"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">1. 内容查询与筛选</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%86%85%E5%AE%B9%E5%AE%A1%E6%A0%B8%EF%BC%88%E9%80%9A%E8%BF%87-%E9%A9%B3%E5%9B%9E-%E4%B8%8B%E6%9E%B6%EF%BC%89"><span class="toc-number">1.2.2.2.</span> <span class="toc-text">2. 内容审核（通过/驳回/下架）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E5%86%85%E5%AE%B9%E6%8E%A8%E8%8D%90%E4%B8%8E%E5%8A%A0%E6%9D%83"><span class="toc-number">1.2.2.3.</span> <span class="toc-text">3. 内容推荐与加权</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-3-%E6%95%8F%E6%84%9F%E8%AF%8D%E7%AE%A1%E7%90%86"><span class="toc-number">1.2.3.</span> <span class="toc-text">9.2.3 敏感词管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%95%8F%E6%84%9F%E8%AF%8D%E5%BA%93%E7%BB%B4%E6%8A%A4%EF%BC%88%E5%A2%9E-%E5%88%A0-%E6%94%B9-%E6%9F%A5%EF%BC%89"><span class="toc-number">1.2.3.1.</span> <span class="toc-text">1. 敏感词库维护（增/删/改/查）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%95%8F%E6%84%9F%E8%AF%8D%E5%88%86%E7%B1%BB%E4%B8%8E%E5%A4%84%E7%90%86%E8%A7%84%E5%88%99"><span class="toc-number">1.2.3.2.</span> <span class="toc-text">2. 敏感词分类与处理规则</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-2-4-%E5%88%86%E7%B1%BB%E7%AE%A1%E7%90%86"><span class="toc-number">1.2.4.</span> <span class="toc-text">9.2.4 分类管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%A2%91%E9%81%93-%E5%88%86%E7%B1%BB%E7%AE%A1%E7%90%86"><span class="toc-number">1.2.4.1.</span> <span class="toc-text">1. 频道/分类管理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%A0%87%E7%AD%BE%E7%AE%A1%E7%90%86"><span class="toc-number">1.2.4.2.</span> <span class="toc-text">2. 标签管理</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#9-3-%E8%BF%90%E8%90%A5%E7%AE%A1%E7%90%86"><span class="toc-number">1.3.</span> <span class="toc-text">9.3 运营管理</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-1-%E5%AD%A6%E4%B9%A0%E7%9B%AE%E6%A0%87"><span class="toc-number">1.3.1.</span> <span class="toc-text">9.3.1 学习目标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-2-%E6%B6%88%E6%81%AF%E6%8E%A8%E9%80%81"><span class="toc-number">1.3.2.</span> <span class="toc-text">9.3.2 消息推送</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%8E%A8%E9%80%81%E4%BB%BB%E5%8A%A1%E5%88%97%E8%A1%A8%E4%B8%8E%E6%95%B0%E6%8D%AE%E7%BB%9F%E8%AE%A1"><span class="toc-number">1.3.2.1.</span> <span class="toc-text">1. 推送任务列表与数据统计</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%96%B0%E5%BB%BA%E6%8E%A8%E9%80%81%E9%85%8D%E7%BD%AE"><span class="toc-number">1.3.2.2.</span> <span class="toc-text">2. 新建推送配置</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-3-%E8%B4%A6%E5%8F%B7%E4%B8%8E%E6%9D%83%E9%99%90%E7%AE%A1%E7%90%86"><span class="toc-number">1.3.3.</span> <span class="toc-text">9.3.3 账号与权限管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%A0%B8%E5%BF%83%E6%80%9D%E6%83%B3%EF%BC%9ARBAC-%E6%A8%A1%E5%9E%8B"><span class="toc-number">1.3.3.1.</span> <span class="toc-text">1. 核心思想：RBAC 模型</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E8%A7%92%E8%89%B2%E7%AE%A1%E7%90%86"><span class="toc-number">1.3.3.2.</span> <span class="toc-text">2. 角色管理</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E8%B4%A6%E5%8F%B7%E7%AE%A1%E7%90%86%EF%BC%88%E5%90%8E%E5%8F%B0%E7%94%A8%E6%88%B7%EF%BC%89"><span class="toc-number">1.3.3.3.</span> <span class="toc-text">3. 账号管理（后台用户）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#9-3-4-%E6%97%A5%E5%BF%97%E7%AE%A1%E7%90%86"><span class="toc-number">1.3.4.</span> <span class="toc-text">9.3.4 日志管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E6%93%8D%E4%BD%9C%E6%97%A5%E5%BF%97%E7%9A%84%E4%BB%B7%E5%80%BC"><span class="toc-number">1.3.4.1.</span> <span class="toc-text">1. 操作日志的价值</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%93%8D%E4%BD%9C%E6%97%A5%E5%BF%97%E8%AE%BE%E8%AE%A1%E8%A6%81%E7%82%B9"><span class="toc-number">1.3.4.2.</span> <span class="toc-text">2. 操作日志设计要点</span></a></li></ol></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>