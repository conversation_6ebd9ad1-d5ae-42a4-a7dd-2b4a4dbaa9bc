<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>Python（八）：第七章： 文件操作 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="Python基础知识总汇"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="Python（八）：第七章： 文件操作"><meta name="application-name" content="Python（八）：第七章： 文件操作"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="Python（八）：第七章： 文件操作"><meta property="og:url" content="https://prorise666.site/posts/37372.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="第七章： 文件操作Python 提供了强大而灵活的文件操作接口，从基础的读写能力到高级的路径操作和目录管理。本章将由浅入深地介绍 Python 文件操作的全面知识。 7.1 文件打开模式文件操作的第一步是打开文件，Python 提供了多种打开模式以满足不同需求。    模式 描述 文件不存在时 文件"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp"><meta name="description" content="第七章： 文件操作Python 提供了强大而灵活的文件操作接口，从基础的读写能力到高级的路径操作和目录管理。本章将由浅入深地介绍 Python 文件操作的全面知识。 7.1 文件打开模式文件操作的第一步是打开文件，Python 提供了多种打开模式以满足不同需求。    模式 描述 文件不存在时 文件"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/37372.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"Python（八）：第七章： 文件操作",postAI:"true",pageFillDescription:"第七章： 文件操作, 7.1 文件打开模式, 7.2 基本文件操作, 7.2.1 文件读取操作, 7.2.2 文件写入操作, 7.2.3 多文件操作, 7.2.4 文件修改示例, 7.3 文件指针控制, 7.3.1 seek() 和 tell() 函数, 7.3.2 实际应用场景, 7.4 缓冲区管理, 7.4.1 缓冲设置与刷新, 7.4.2 缓冲区触发条件, 7.5 文件路径操作, 7.5.1 使用 os.path 模块, 7.5.2 使用 pathlib 模块 (Python 3.4+), 7.6 高级文件操作, 7.6.1 二进制文件操作, 7.6.2 临时文件操作, 7.7 目录操作, 7.7.1 基本目录操作, 7.7.2 使用 pathlib 进行目录操作, 7.7.3 递归遍历目录树, 7.7.4 文件复制、移动和删除操作, 7.8 文件监控与变更检测, 7.8.1 基础文件监控, 7.8.2 使用 watchdog 库进行高级文件监控, 7.9 实际应用场景示例, 7.9.1 文件备份工具, 7.9.2 日志分析工具, 7.9.3 文件同步工具第七章文件操作提供了强大而灵活的文件操作接口从基础的读写能力到高级的路径操作和目录管理本章将由浅入深地介绍文件操作的全面知识文件打开模式文件操作的第一步是打开文件提供了多种打开模式以满足不同需求模式描述文件不存在时文件存在时常见应用场景只读模式报错从头读取读取配置文件日志文件只写模式创建新文件清空内容生成报告写入日志追加模式创建新文件在末尾追加日志记录数据收集读写模式报错可读可写需要同时读写的场景读写模式创建新文件清空内容需要先写后读的场景追加读写创建新文件追加且可读数据分析日志分析二进制模式与其他模式组合处理二进制图片视频压缩文件文本模式默认与其他模式组合处理文本文本文件处理实用提示选择合适的文件模式可以避免数据丢失例如使用模式时要格外小心因为它会清空现有文件当不确定时使用模式追加内容会更安全基本文件操作文件读取操作使用语句自动处理文件关闭推荐方式方法一次性读取整个文件读取全部内容到内存注意后文件指针已经到达文件末尾需要重新打开文件或使用重置指针重置文件指针到文件开头方法读取一行读取第一行包含换行符方法读取所有行到列表重置文件指针返回包含所有行的列表第一行第二行方法逐行读取内存效率最高推荐用于大文件重置文件指针文件对象本身是可迭代的包含换行符通常需要移除去除行首行尾的空白字符文件写入操作写入文件覆盖模式方法写入字符串第一行内容注意需手动添加换行符第二行内容方法写入多行第三行内容第四行内容注意不会自动添加换行符追加内容到文件追加的内容读写模式示例读取前个字符插入的内容在当前位置第个字符后写入多文件操作同时操作多个文件例如文件复制按块读取和写入适用于大文件的块大小到达文件末尾或者简单地复制所有内容先重置到文件开头或者逐行复制适合文本处理可以在此添加行处理逻辑文件修改示例在实际应用中我们经常需要读取修改并写回文件示例为文件的所有标题增加一个符号读取并修改处理每一行如果行以开头表示标题则增加一个将修改后的内容写入新文件文件已修改并保存为最佳实践对于文件修改操作始终先写入临时文件然后在确认写入成功后才替换原文件这样可以防止文件损坏文件指针控制文件指针或文件位置决定了读写操作的起始位置掌握指针控制对于高级文件操作至关重要和函数写入一些测试内容你好世界英文部分占用个字符中文部分你好世界占用个字符剩余的空格符号占据了个字符总共个字符获取当前文件指针位置写入内容后的位置写入内容后的位置将指针移回文件开头回到文件开头位置回到文件开头位置读取全部内容文件全部内容文件全部内容你好世界读取后的位置读取后的位置再次回到文件开头读取前个字符前个字符前个字符读取个字符后的位置读取个字符后的位置回到文件开头一次性定位到第个字符位置从文件开头算起直接定位到第个位置从第个位置开始读取的内容从第个位置开始读取的内容你好世界注意在文本模式下由于字符编码原因可能无法精确定位到任意字节位置对于需要精确控制的场景应使用二进制模式等实际应用场景场景在大日志文件中读取最后行读取文件最后行类似于的命令移动到文件末尾文件总大小初始化变量从文件末尾向前读取移动到倒数第个块读取数据块处理可能被截断的行丢弃第一行不完整数据计算行数合并行返回最后行使用示例缓冲区管理理解缓冲区对于优化文件操作性能至关重要特别是在处理大量小块写入时缓冲设置与刷新设置不同的缓冲策略无缓冲仅在二进制模式可用行缓冲仅在文本模式可用指定缓冲区大小字节使用默认缓冲区大小无缓冲每次写入都直接写入磁盘行缓冲遇到换行符时刷新遇到会刷新保留在缓冲区指定缓冲区大小缓冲区适合频繁小写入数据会积累到才写入磁盘手动刷新缓冲区立即刷新缓冲区确保数据写入磁盘进一步确保数据写入物理存储设备缓冲区触发条件缓冲区会在以下条件下自动刷新缓冲区满时文件关闭时如块结束调用方法时程序正常退出时行缓冲模式下遇到换行符时性能提示对于大量小写操作使用适当的缓冲区大小可以显著提高性能但对于关键数据应及时调用确保数据安全文件路径操作有效管理文件路径是文件操作的基础提供了两种主要方式传统的模块和现代的库使用模块方法属性描述获取当前工作目录改变当前工作目录创建目录递归创建多级目录删除空目录删除文件列出指定目录的文件和目录重命名文件或目录执行系统命令环境变量字典检查路径是否存在检查路径是否为文件检查路径是否为目录连接路径根据最后一个路径分隔符分割路径为目录文件名根据最后一个点号分割路径为文件名拓展名获取路径的目录部分获取路径的文件名部分获取文件大小字节获取当前脚本所在目录在当前目录下创建首先创建一个测试文件这是测试内容文件路径处理演示文件路径信息目录文件名纯名称扩展名路径检查文件是否存在是否是文件是否是目录文件信息获取修改时间获取创建时间获取访问时间转换时间戳为可读时间文件大小字节创建时间修改时间访问时间文件内容文件内容获取文件信息时发生错误使用模块获取当前脚本所在目录并创建的路径创建并写入测试文件这是测试内容基本路径信息完整路径父目录文件名纯名称扩展名所有路径组件路径解析绝对路径解析路径相对路径无法计算相对路径文件可能在不同驱动器或目录文件状态检查文件是否存在是否是文件是否是目录是否是符号链接文件信息文件大小字节创建时间修改时间访问时间文件内容文件内容路径修改示例修改整个文件名修改扩展名仅修改文件名不含扩展名功能方式方式推荐路径连接更直观获取目录属性访问更清晰获取文件名属性访问更清晰分离扩展名更简洁检查存在两者类似面向对象获取绝对路径两者相当最佳实践在新项目中优先使用它提供了更现代更直观的面向对象接口在维护旧代码时可能需要继续使用高级文件操作二进制文件操作读取整个二进制文件读取并返回整个二进制文件的内容分块读取大型二进制文件分块处理大型二进制文件避免内存溢出每次读取到达文件末尾处理当前数据块假设的处理函数处理数据块的函数处理二进制数据块的函数这里可以根据需要实现具体的数据处理逻辑例如计算校验和搜索特定字节模式转换数据格式等处理数据块字节示例计算数据块的哈希值数据块哈希值示例检查数据块中的特定字节序列在数据块中发现特定字节序列返回处理结果可选读取文件特定部分读取文件中从位置开始的个字节移动文件指针到指定位置读取指定长度字节检测文件类型通过文件头部特征识别文件类型常见文件格式的魔数读取文件前个字节用于检测未知文件类型实际演示处理图像文件演示二进制文件操作的实际应用定义两个图形的基础文件路径检测图像类型检测文件类型是检测文件类型是检测文件类型是检测文件类型是读取文件头部信息文件头部字节文件头部字节获取文件大小小技巧在函数中使用即可以将数字以千分位分隔符展示文件大小字节文件大小字节文件大小字节文件大小字节处理大型二进制文件临时文件操作临时文件对于需要中间处理结果但不想留下永久文件的操作非常有用示例使用临时目录处理多个文件在临时目录中创建多个文件并进行批处理创建临时工作区创建多个数据文件获取到临时目录下的文件路径处理所有的文件模拟处理计算字符数并添加到结果集中文件包含个字符列出处理的文件处理文件为退出块后会自动删除临时目录暂停秒等待用户查看结果处理开始大约需要秒请稍候处理完成演示临时目录的批处理使用这是第一个文件的测试内容这是第二个文件包含更多的信息以及携带数字这是第三个文件包含中文你好世界处理结果为目录操作目录操作是文件系统操作的重要组成部分提供了多种目录操作方法基本目录操作获取当前工作目录当前工作目录更改当前工作目录更改后的工作目录列出目录内容目录内容过滤目录内容文件目录创建目录切换回原目录创建单个目录创建多级目录忽略已存在的目录删除目录只能删除空目录删除目录以及所有内容谨慎使用使用进行目录操作创建目录创建多层目录递归创建父目录列出目录内容文件大小目录过滤特定类型的文件列出当前目录下所有文件递归搜索所有子目录当前目录下文件递归搜索所有文件所有文件删除目录只能删除空目录删除目录及其所有内容操作方法方法优势比较获取当前目录返回对象便于后续操作切换工作目录无直接等效方法更适合改变全局工作目录列出目录内容直接返回对象无需再拼接路径创建单个目录功能相同更面向对象创建多级目录语义更明确参数名更具描述性删除空目录功能相同更面向对象递归删除目录无直接等效方法需循环删除提供单一高效操作文件路径拼接使用运算符更直观简洁检查路径是否存在功能相同更面向对象检查是否为文件功能相同更面向对象检查是否为目录功能相同更面向对象递归遍历目录树递归遍历是处理目录层次结构的强大工具在很多需要列举文件目录树的场景都可以采用该思路去打印输出使用遍历目录树示例输出学习用电子商务系统实现笔记目录大小递归扫描目录显示结构和文件大小其中当前目录路径子目录列表文件列表计算当前的目录深度用于缩进计算目录深度将当前路径中的起始目录部分替换为空字符串得到相对路径统计相对路径中目录分隔符如或的数量每一个分隔符代表一层目录深度因此分隔符的数量就等于目录的嵌套层级打印当前目录缩进子文件统计当前目录下的文件大小累加总大小目录大小返回总大小单位学习用总大小文件复制移动和删除操作文件复制操作复制文件到目标路径不保留元数据如文件的创建时间修改时间等参数说明源文件路径目标路径可以是目录或文件名返回值目标文件路径复制文件到目标路径保留所有元数据如修改时间访问时间权限等参数说明同函数返回值目标文件路径目录复制操作递归复制整个目录树目标目录不能已存在参数说明源目录目标目录必须不存在返回值目标目录路径高级用法使用参数忽略特定文件创建一个忽略函数用于过滤不需要复制的文件参数说明可变参数接受多个风格的模式字符串文件移动操作移动文件或目录到目标路径参数说明源路径目标路径返回值目标路径用法重命名文件用法移动文件到其他目录文件删除操作删除指定路径的文件不能删除目录参数说明要删除的文件路径注意如果文件不存在会抛出异常文件监控与变更检测在某些应用场景中需要监控文件变化并作出响应基础文件监控监控文件变化并输出新增内容文件不存在获取初始状态文件大小最后修改时间戳开始监控文件文件大小最后修改时间检查文件是否被修改文件在被修改如果文件增大了读取新增内容尝试不同的编码方式读取文件移动文件指针到上次读取位置新增内容更新当前使用的编码如果所有编码都失败尝试以二进制方式读取并显示无法解码文件内容显示二进制内容读取文件失败文件缩小了可能是被清空了文件大小减小了可能被截断或重写更新状态休眠一段时间再检查文件监控已停止使用库进行高级文件监控对于更复杂的文件系统监控需求的第三方库提供了更强大的功能文件被创建文件被删除文件被修改文件被移动确保监控的是目录而不是文件如果是文件则监控其所在的目录如果是当前目录下的文件开始监控目录按停止监控监控当前目录或者指定一个确实存在的目录路径实际应用场景示例文件备份工具创建目录的备份参数要备份的源目录备份文件存放目录默认在源目录的父目录是否创建压缩备份返回备份文件的路径确保源目录存在源目录不存在或不是一个目录设置默认备份目录创建备份文件名包含时间戳创建备份创建备份遍历源目录中的所有文件计算文件在中的相对路径添加创建目录备份复制创建目录备份使用示例备份已创建日志分析工具分析日志文件并生成报告参数日志文件路径用于匹配日志行的正则表达式模式默认为表示所有行返回包含分析结果的字典日志文件不存在初始化结果编译正则表达式地址模式状态码模式时间戳模式假设格式为模式错误和警告模式读取和分析日志文件应用模式匹配过滤如果提供提取地址提取状态码提取提取时间并按小时汇总检查错误和警告生成日志分析报告文本和图表创建文本报告日志分析报告总行数匹配行数错误数警告数按小时分布时行前个地址次状态码统计次前个次生成图表报告按小时分布图小时日志条目数日志按小时分布状态码分布饼图状态码分布前个地址条形图请求次数地址前个地址使用示例报告已生成文件同步工具配置日志计算文件的哈希值同步两个目录的内容参数源目录目标目录是否删除目标目录中源目录没有的文件要排除的文件目录列表返回操作统计信息确保目录存在源目录不存在创建目标目录初始化统计获取源文件清单从中移除要排除的目录修改原地同步文件到目标目录确保目标目录存在检查文件是否需要更新复制新文件比较修改时间和哈希值秒容差进一步比较内容哈希更新文件处理需要删除的文件删除多余文件删除空目录从下向上遍历检查目录是否为空删除空目录定期同步功能定期同步两个目录参数源目录目标目录同步间隔秒是否删除目标目录中多余文件要排除的文件目录列表启动定期同步从到间隔秒开始同步同步完成复制更新删除跳过同步出错计算实际等待时间等待秒后进行下次同步同步服务已停止使用示例定期同步每小时",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-13 22:13:01",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%B8%83%E7%AB%A0%EF%BC%9A-%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-text">第七章： 文件操作</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-%E6%96%87%E4%BB%B6%E6%89%93%E5%BC%80%E6%A8%A1%E5%BC%8F"><span class="toc-text">7.1 文件打开模式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-%E5%9F%BA%E6%9C%AC%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-text">7.2 基本文件操作</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-2-1-%E6%96%87%E4%BB%B6%E8%AF%BB%E5%8F%96%E6%93%8D%E4%BD%9C"><span class="toc-text">7.2.1 文件读取操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-2-2-%E6%96%87%E4%BB%B6%E5%86%99%E5%85%A5%E6%93%8D%E4%BD%9C"><span class="toc-text">7.2.2 文件写入操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-2-3-%E5%A4%9A%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-text">7.2.3 多文件操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-2-4-%E6%96%87%E4%BB%B6%E4%BF%AE%E6%94%B9%E7%A4%BA%E4%BE%8B"><span class="toc-text">7.2.4 文件修改示例</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-%E6%96%87%E4%BB%B6%E6%8C%87%E9%92%88%E6%8E%A7%E5%88%B6"><span class="toc-text">7.3 文件指针控制</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-3-1-seek-%E5%92%8C-tell-%E5%87%BD%E6%95%B0"><span class="toc-text">7.3.1 seek() 和 tell() 函数</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-3-2-%E5%AE%9E%E9%99%85%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-text">7.3.2 实际应用场景</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-4-%E7%BC%93%E5%86%B2%E5%8C%BA%E7%AE%A1%E7%90%86"><span class="toc-text">7.4 缓冲区管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-4-1-%E7%BC%93%E5%86%B2%E8%AE%BE%E7%BD%AE%E4%B8%8E%E5%88%B7%E6%96%B0"><span class="toc-text">7.4.1 缓冲设置与刷新</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-4-2-%E7%BC%93%E5%86%B2%E5%8C%BA%E8%A7%A6%E5%8F%91%E6%9D%A1%E4%BB%B6"><span class="toc-text">7.4.2 缓冲区触发条件</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-5-%E6%96%87%E4%BB%B6%E8%B7%AF%E5%BE%84%E6%93%8D%E4%BD%9C"><span class="toc-text">7.5 文件路径操作</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-5-1-%E4%BD%BF%E7%94%A8-os-path-%E6%A8%A1%E5%9D%97"><span class="toc-text">7.5.1 使用 os.path 模块</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-5-2-%E4%BD%BF%E7%94%A8-pathlib-%E6%A8%A1%E5%9D%97-Python-3-4"><span class="toc-text">7.5.2 使用 pathlib 模块 (Python 3.4+)</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-6-%E9%AB%98%E7%BA%A7%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-text">7.6 高级文件操作</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-6-1-%E4%BA%8C%E8%BF%9B%E5%88%B6%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-text">7.6.1 二进制文件操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-6-2-%E4%B8%B4%E6%97%B6%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-text">7.6.2 临时文件操作</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-7-%E7%9B%AE%E5%BD%95%E6%93%8D%E4%BD%9C"><span class="toc-text">7.7 目录操作</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-7-1-%E5%9F%BA%E6%9C%AC%E7%9B%AE%E5%BD%95%E6%93%8D%E4%BD%9C"><span class="toc-text">7.7.1 基本目录操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-7-2-%E4%BD%BF%E7%94%A8-pathlib-%E8%BF%9B%E8%A1%8C%E7%9B%AE%E5%BD%95%E6%93%8D%E4%BD%9C"><span class="toc-text">7.7.2 使用 pathlib 进行目录操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-7-3-%E9%80%92%E5%BD%92%E9%81%8D%E5%8E%86%E7%9B%AE%E5%BD%95%E6%A0%91"><span class="toc-text">7.7.3 递归遍历目录树</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-7-4-%E6%96%87%E4%BB%B6%E5%A4%8D%E5%88%B6%E3%80%81%E7%A7%BB%E5%8A%A8%E5%92%8C%E5%88%A0%E9%99%A4%E6%93%8D%E4%BD%9C"><span class="toc-text">7.7.4 文件复制、移动和删除操作</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-8-%E6%96%87%E4%BB%B6%E7%9B%91%E6%8E%A7%E4%B8%8E%E5%8F%98%E6%9B%B4%E6%A3%80%E6%B5%8B"><span class="toc-text">7.8 文件监控与变更检测</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-8-1-%E5%9F%BA%E7%A1%80%E6%96%87%E4%BB%B6%E7%9B%91%E6%8E%A7"><span class="toc-text">7.8.1 基础文件监控</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-8-2-%E4%BD%BF%E7%94%A8-watchdog-%E5%BA%93%E8%BF%9B%E8%A1%8C%E9%AB%98%E7%BA%A7%E6%96%87%E4%BB%B6%E7%9B%91%E6%8E%A7"><span class="toc-text">7.8.2 使用 watchdog 库进行高级文件监控</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-9-%E5%AE%9E%E9%99%85%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E7%A4%BA%E4%BE%8B"><span class="toc-text">7.9 实际应用场景示例</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-9-1-%E6%96%87%E4%BB%B6%E5%A4%87%E4%BB%BD%E5%B7%A5%E5%85%B7"><span class="toc-text">7.9.1 文件备份工具</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-9-2-%E6%97%A5%E5%BF%97%E5%88%86%E6%9E%90%E5%B7%A5%E5%85%B7"><span class="toc-text">7.9.2 日志分析工具</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-9-3-%E6%96%87%E4%BB%B6%E5%90%8C%E6%AD%A5%E5%B7%A5%E5%85%B7"><span class="toc-text">7.9.3 文件同步工具</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>Python基础知识总汇</span></a></span></div></div><h1 class="post-title" itemprop="name headline">Python（八）：第七章： 文件操作</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-04-18T15:13:45.000Z" title="发表于 2025-04-18 23:13:45">2025-04-18</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.524Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">9.5k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>42分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="Python（八）：第七章： 文件操作"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/37372.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/37372.html"><header><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/" itemprop="url">后端技术</a><a class="post-meta-categories" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/" itemprop="url">Python</a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" tabindex="-1" itemprop="url">Python基础知识总汇</a><h1 id="CrawlerTitle" itemprop="name headline">Python（八）：第七章： 文件操作</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-04-18T15:13:45.000Z" title="发表于 2025-04-18 23:13:45">2025-04-18</time><time itemprop="dateCreated datePublished" datetime="2025-07-13T14:13:01.524Z" title="更新于 2025-07-13 22:13:01">2025-07-13</time></header><div id="postchat_postcontent"><h2 id="第七章：-文件操作"><a href="#第七章：-文件操作" class="headerlink" title="第七章： 文件操作"></a>第七章： 文件操作</h2><p>Python 提供了强大而灵活的文件操作接口，从基础的读写能力到高级的路径操作和目录管理。本章将由浅入深地介绍 Python 文件操作的全面知识。</p><h3 id="7-1-文件打开模式"><a href="#7-1-文件打开模式" class="headerlink" title="7.1 文件打开模式"></a>7.1 文件打开模式</h3><p>文件操作的第一步是打开文件，Python 提供了多种打开模式以满足不同需求。</p><table><thead><tr><th>模式</th><th>描述</th><th>文件不存在时</th><th>文件存在时</th><th>常见应用场景</th></tr></thead><tbody><tr><td><code>r</code></td><td>只读模式</td><td>报错</td><td>从头读取</td><td>读取配置文件、日志文件</td></tr><tr><td><code>w</code></td><td>只写模式</td><td>创建新文件</td><td>清空内容</td><td>生成报告、写入日志</td></tr><tr><td><code>a</code></td><td>追加模式</td><td>创建新文件</td><td>在末尾追加</td><td>日志记录、数据收集</td></tr><tr><td><code>r+</code></td><td>读写模式</td><td>报错</td><td>可读可写</td><td>需要同时读写的场景</td></tr><tr><td><code>w+</code></td><td>读写模式</td><td>创建新文件</td><td>清空内容</td><td>需要先写后读的场景</td></tr><tr><td><code>a+</code></td><td>追加读写</td><td>创建新文件</td><td>追加且可读</td><td>数据分析、日志分析</td></tr><tr><td><code>b</code></td><td>二进制模式</td><td>与其他模式组合</td><td>处理二进制</td><td>图片、视频、压缩文件</td></tr><tr><td><code>t</code></td><td>文本模式(默认)</td><td>与其他模式组合</td><td>处理文本</td><td>文本文件处理</td></tr></tbody></table><blockquote><p><strong>实用提示</strong>：选择合适的文件模式可以避免数据丢失。例如，使用 <code>w</code> 模式时要格外小心，因为它会清空现有文件。当不确定时，使用 <code>a</code> 模式追加内容会更安全。</p></blockquote><h3 id="7-2-基本文件操作"><a href="#7-2-基本文件操作" class="headerlink" title="7.2 基本文件操作"></a>7.2 基本文件操作</h3><h4 id="7-2-1-文件读取操作"><a href="#7-2-1-文件读取操作" class="headerlink" title="7.2.1 文件读取操作"></a>7.2.1 文件读取操作</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 使用 with 语句自动处理文件关闭（推荐方式）</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">'example.txt'</span>, mode=<span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:</span><br><span class="line">    <span class="comment"># 方法1：一次性读取整个文件</span></span><br><span class="line">    content = file.read()  <span class="comment"># 读取全部内容到内存</span></span><br><span class="line">    <span class="built_in">print</span>(content)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 注意：read()后文件指针已经到达文件末尾</span></span><br><span class="line">    <span class="comment"># 需要重新打开文件或使用seek(0)重置指针</span></span><br><span class="line">    file.seek(<span class="number">0</span>)  <span class="comment"># 重置文件指针到文件开头</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 方法2：读取一行</span></span><br><span class="line">    line = file.readline()  <span class="comment"># 读取第一行（包含换行符）</span></span><br><span class="line">    <span class="built_in">print</span>(line)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 方法3：读取所有行到列表</span></span><br><span class="line">    file.seek(<span class="number">0</span>)  <span class="comment"># 重置文件指针</span></span><br><span class="line">    lines = file.readlines()  <span class="comment"># 返回包含所有行的列表</span></span><br><span class="line">    <span class="built_in">print</span>(lines)  <span class="comment"># ['第一行\n', '第二行\n', ...]</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 方法4：逐行读取（内存效率最高，推荐用于大文件）</span></span><br><span class="line">    file.seek(<span class="number">0</span>)  <span class="comment"># 重置文件指针</span></span><br><span class="line">    <span class="keyword">for</span> line <span class="keyword">in</span> file:  <span class="comment"># 文件对象本身是可迭代的</span></span><br><span class="line">        <span class="comment"># line包含换行符，通常需要移除</span></span><br><span class="line">        <span class="built_in">print</span>(line.strip())  <span class="comment"># 去除行首行尾的空白字符</span></span><br></pre></td></tr></tbody></table></figure><h4 id="7-2-2-文件写入操作"><a href="#7-2-2-文件写入操作" class="headerlink" title="7.2.2 文件写入操作"></a>7.2.2 文件写入操作</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 写入文件（覆盖模式）</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">'example.txt'</span>, mode=<span class="string">'w'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:</span><br><span class="line">    <span class="comment"># 方法1：写入字符串</span></span><br><span class="line">    file.write(<span class="string">'第一行内容\n'</span>)  <span class="comment"># 注意需手动添加换行符</span></span><br><span class="line">    file.write(<span class="string">'第二行内容\n'</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 方法2：写入多行</span></span><br><span class="line">    lines = [<span class="string">'第三行内容\n'</span>, <span class="string">'第四行内容\n'</span>]</span><br><span class="line">    file.writelines(lines)  <span class="comment"># 注意writelines不会自动添加换行符</span></span><br><span class="line">    </span><br><span class="line"><span class="comment"># 追加内容到文件</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">'example.txt'</span>, mode=<span class="string">'a'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:</span><br><span class="line">    file.write(<span class="string">'追加的内容\n'</span>)</span><br><span class="line">    </span><br><span class="line"><span class="comment"># 读写模式示例</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">'example.txt'</span>, mode=<span class="string">'r+'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:</span><br><span class="line">    content = file.read(<span class="number">10</span>)  <span class="comment"># 读取前10个字符</span></span><br><span class="line">    file.write(<span class="string">'插入的内容'</span>)  <span class="comment"># 在当前位置（第10个字符后）写入</span></span><br></pre></td></tr></tbody></table></figure><h4 id="7-2-3-多文件操作"><a href="#7-2-3-多文件操作" class="headerlink" title="7.2.3 多文件操作"></a>7.2.3 多文件操作</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 同时操作多个文件（例如：文件复制）</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">'source.txt'</span>, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> source, \</span><br><span class="line">     <span class="built_in">open</span>(<span class="string">'destination.txt'</span>, <span class="string">'w'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> destination:</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 按块读取和写入（适用于大文件）</span></span><br><span class="line">    chunk_size = <span class="number">4096</span>  <span class="comment"># 4KB 的块大小</span></span><br><span class="line">    <span class="keyword">while</span> <span class="literal">True</span>:</span><br><span class="line">        chunk = source.read(chunk_size)</span><br><span class="line">        <span class="keyword">if</span> <span class="keyword">not</span> chunk:  <span class="comment"># 到达文件末尾</span></span><br><span class="line">            <span class="keyword">break</span></span><br><span class="line">        destination.write(chunk)</span><br><span class="line">        </span><br><span class="line">    <span class="comment"># 或者简单地复制所有内容</span></span><br><span class="line">    <span class="comment"># source.seek(0)  # 先重置到文件开头</span></span><br><span class="line">    <span class="comment"># destination.write(source.read())</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 或者逐行复制（适合文本处理）</span></span><br><span class="line">    <span class="comment"># source.seek(0)</span></span><br><span class="line">    <span class="comment"># for line in source:</span></span><br><span class="line">    <span class="comment">#     # 可以在此添加行处理逻辑</span></span><br><span class="line">    <span class="comment">#     destination.write(line)</span></span><br></pre></td></tr></tbody></table></figure><h4 id="7-2-4-文件修改示例"><a href="#7-2-4-文件修改示例" class="headerlink" title="7.2.4 文件修改示例"></a>7.2.4 文件修改示例</h4><p>在实际应用中，我们经常需要读取、修改并写回文件：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 示例：为Markdown文件的所有标题增加一个#符号</span></span><br><span class="line">file_name = <span class="string">'document.md'</span></span><br><span class="line">output_file = <span class="string">'document_modified.md'</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 读取并修改</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(file_name, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> input_file:</span><br><span class="line">    lines = input_file.readlines()</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 处理每一行</span></span><br><span class="line">    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="built_in">range</span>(<span class="built_in">len</span>(lines)):</span><br><span class="line">        <span class="comment"># 如果行以#开头（表示标题），则增加一个#</span></span><br><span class="line">        <span class="keyword">if</span> lines[i].strip().startswith(<span class="string">'#'</span>):</span><br><span class="line">            lines[i] = <span class="string">'#'</span> + lines[i]</span><br><span class="line"></span><br><span class="line"><span class="comment"># 将修改后的内容写入新文件</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(output_file, <span class="string">'w'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> output_file:</span><br><span class="line">    output_file.writelines(lines)</span><br><span class="line">    </span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"文件已修改并保存为 <span class="subst">{output_file}</span>"</span>)</span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>最佳实践</strong>：对于文件修改操作，始终先写入临时文件，然后在确认写入成功后才替换原文件，这样可以防止文件损坏。</p></blockquote><h3 id="7-3-文件指针控制"><a href="#7-3-文件指针控制" class="headerlink" title="7.3 文件指针控制"></a>7.3 文件指针控制</h3><p>文件指针（或文件位置）决定了读写操作的起始位置。掌握指针控制对于高级文件操作至关重要。</p><h4 id="7-3-1-seek-和-tell-函数"><a href="#7-3-1-seek-和-tell-函数" class="headerlink" title="7.3.1 seek() 和 tell() 函数"></a>7.3.1 seek() 和 tell() 函数</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">'example.txt'</span>, <span class="string">'w+'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:</span><br><span class="line">    <span class="comment"># 写入一些测试内容</span></span><br><span class="line">    file.write(<span class="string">"Hello World! 你好世界！"</span>) <span class="comment"># 英文部分 "Hello World " 占用 12 个字符，中文部分 "你好世界！" 占用 12 个字符，剩余的空格+符号占据了 4 个字符 总共 28 个字符</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># tell() 获取当前文件指针位置</span></span><br><span class="line">    position = file.tell()</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"写入内容后的位置: <span class="subst">{position}</span>"</span>)  <span class="comment"># 写入内容后的位置: 28</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 将指针移回文件开头</span></span><br><span class="line">    file.seek(<span class="number">0</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"回到文件开头位置: <span class="subst">{file.tell()}</span>"</span>)   <span class="comment"># 回到文件开头位置: 0</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 读取全部内容</span></span><br><span class="line">    content = file.read()</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"文件全部内容: <span class="subst">{content}</span>"</span>) <span class="comment"># 文件全部内容: Hello World! 你好世界！</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"读取后的位置: <span class="subst">{file.tell()}</span>"</span>)  <span class="comment"># 读取后的位置: 28</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 再次回到文件开头</span></span><br><span class="line">    file.seek(<span class="number">0</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 读取前5个字符</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"前5个字符: <span class="subst">{file.read(<span class="number">5</span>)}</span>"</span>) <span class="comment"># 前5个字符: Hello</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"读取5个字符后的位置: <span class="subst">{file.tell()}</span>"</span>) <span class="comment"># 读取5个字符后的位置: 5</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 回到文件开头</span></span><br><span class="line">    file.seek(<span class="number">0</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 一次性定位到第13个字符位置（从文件开头算起）</span></span><br><span class="line">    file.seek(<span class="number">13</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"直接定位到第10个位置: <span class="subst">{file.tell()}</span>"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"从第13个位置开始读取的内容: <span class="subst">{file.read()}</span>"</span>) <span class="comment"># 从第13个位置开始读取的内容: 你好世界！</span></span><br></pre></td></tr></tbody></table></figure><blockquote><p><strong>注意</strong>：在文本模式下，由于字符编码原因，seek() 可能无法精确定位到任意字节位置。对于需要精确控制的场景，应使用二进制模式 (‘rb’, ‘wb’ 等)。</p></blockquote><h4 id="7-3-2-实际应用场景"><a href="#7-3-2-实际应用场景" class="headerlink" title="7.3.2 实际应用场景"></a>7.3.2 实际应用场景</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 场景：在大日志文件中读取最后100行</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">tail</span>(<span class="params">file_path, n=<span class="number">10</span></span>):</span><br><span class="line">    <span class="string">"""读取文件最后n行，类似于Unix的tail命令"""</span></span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(file_path, <span class="string">'rb'</span>) <span class="keyword">as</span> f:</span><br><span class="line">        <span class="comment"># 移动到文件末尾</span></span><br><span class="line">        f.seek(<span class="number">0</span>, <span class="number">2</span>)</span><br><span class="line">        <span class="comment"># 文件总大小</span></span><br><span class="line">        total_size = f.tell()</span><br><span class="line">        </span><br><span class="line">        <span class="comment"># 初始化变量</span></span><br><span class="line">        block_size = <span class="number">1024</span></span><br><span class="line">        block = -<span class="number">1</span></span><br><span class="line">        lines = []</span><br><span class="line">        </span><br><span class="line">        <span class="comment"># 从文件末尾向前读取</span></span><br><span class="line">        <span class="keyword">while</span> <span class="built_in">len</span>(lines) &lt; n <span class="keyword">and</span> -block * block_size &lt; total_size:</span><br><span class="line">            <span class="comment"># 移动到倒数第block个块</span></span><br><span class="line">            position = <span class="built_in">max</span>(<span class="number">0</span>, total_size + block * block_size)</span><br><span class="line">            f.seek(position)</span><br><span class="line">            </span><br><span class="line">            <span class="comment"># 读取数据块</span></span><br><span class="line">            data = f.read(<span class="built_in">min</span>(block_size, total_size - position))</span><br><span class="line">            </span><br><span class="line">            <span class="comment"># 处理可能被截断的行</span></span><br><span class="line">            <span class="keyword">if</span> position &gt; <span class="number">0</span>:</span><br><span class="line">                <span class="comment"># 丢弃第一行不完整数据</span></span><br><span class="line">                data = data.split(<span class="string">b'\n'</span>, <span class="number">1</span>)[<span class="number">1</span>] <span class="keyword">if</span> <span class="string">b'\n'</span> <span class="keyword">in</span> data <span class="keyword">else</span> <span class="string">b''</span></span><br><span class="line">            </span><br><span class="line">            <span class="comment"># 计算行数</span></span><br><span class="line">            lines_in_block = data.split(<span class="string">b'\n'</span>)</span><br><span class="line">            </span><br><span class="line">            <span class="comment"># 合并行</span></span><br><span class="line">            lines = lines_in_block + lines</span><br><span class="line">            block -= <span class="number">1</span></span><br><span class="line">        </span><br><span class="line">        <span class="comment"># 返回最后n行</span></span><br><span class="line">        <span class="keyword">return</span> [line.decode(<span class="string">'utf-8'</span>) <span class="keyword">for</span> line <span class="keyword">in</span> lines[-n:]]</span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用示例</span></span><br><span class="line">last_lines = tail(<span class="string">'large_log_file.txt'</span>, <span class="number">100</span>)</span><br><span class="line"><span class="keyword">for</span> line <span class="keyword">in</span> last_lines:</span><br><span class="line">    <span class="built_in">print</span>(line)</span><br></pre></td></tr></tbody></table></figure><h3 id="7-4-缓冲区管理"><a href="#7-4-缓冲区管理" class="headerlink" title="7.4 缓冲区管理"></a>7.4 缓冲区管理</h3><p>理解缓冲区对于优化文件操作性能至关重要，特别是在处理大量小块写入时。</p><h4 id="7-4-1-缓冲设置与刷新"><a href="#7-4-1-缓冲设置与刷新" class="headerlink" title="7.4.1 缓冲设置与刷新"></a>7.4.1 缓冲设置与刷新</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 设置不同的缓冲策略</span></span><br><span class="line"><span class="comment"># buffering=0: 无缓冲 (仅在二进制模式可用)</span></span><br><span class="line"><span class="comment"># buffering=1: 行缓冲 (仅在文本模式可用)</span></span><br><span class="line"><span class="comment"># buffering&gt;1: 指定缓冲区大小(字节)</span></span><br><span class="line"><span class="comment"># buffering=-1: 使用默认缓冲区大小</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 无缓冲 (每次写入都直接写入磁盘)</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">'binary_file.bin'</span>, <span class="string">'wb'</span>, buffering=<span class="number">0</span>) <span class="keyword">as</span> f:</span><br><span class="line">    f.write(<span class="string">b'Data will be written immediately'</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 行缓冲 (遇到换行符时刷新)</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">'log.txt'</span>, <span class="string">'w'</span>, buffering=<span class="number">1</span>) <span class="keyword">as</span> f:</span><br><span class="line">    f.write(<span class="string">'This line will be buffered\n'</span>)  <span class="comment"># 遇到\n会刷新</span></span><br><span class="line">    f.write(<span class="string">'Until a newline is encountered'</span>)  <span class="comment"># 保留在缓冲区</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 指定缓冲区大小</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">'large_file.txt'</span>, <span class="string">'w'</span>, buffering=<span class="number">4096</span>) <span class="keyword">as</span> f:</span><br><span class="line">    <span class="comment"># 4KB缓冲区，适合频繁小写入</span></span><br><span class="line">    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="built_in">range</span>(<span class="number">10000</span>):</span><br><span class="line">        f.write(<span class="string">f'Line <span class="subst">{i}</span>\n'</span>)  <span class="comment"># 数据会积累到4KB才写入磁盘</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 手动刷新缓冲区</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(<span class="string">'important.txt'</span>, <span class="string">'w'</span>) <span class="keyword">as</span> f:</span><br><span class="line">    f.write(<span class="string">'Critical data'</span>)</span><br><span class="line">    f.flush()  <span class="comment"># 立即刷新缓冲区，确保数据写入磁盘</span></span><br><span class="line">    os.fsync(f.fileno())  <span class="comment"># 进一步确保数据写入物理存储设备</span></span><br></pre></td></tr></tbody></table></figure><h4 id="7-4-2-缓冲区触发条件"><a href="#7-4-2-缓冲区触发条件" class="headerlink" title="7.4.2 缓冲区触发条件"></a>7.4.2 缓冲区触发条件</h4><p>缓冲区会在以下条件下自动刷新：</p><ol><li>缓冲区满时</li><li>文件关闭时（如 with 块结束）</li><li>调用 flush() 方法时</li><li>程序正常退出时</li><li>行缓冲模式下遇到换行符时</li></ol><blockquote><p><strong>性能提示</strong>：对于大量小写操作，使用适当的缓冲区大小可以显著提高性能。但对于关键数据，应及时调用 flush() 确保数据安全。</p></blockquote><h3 id="7-5-文件路径操作"><a href="#7-5-文件路径操作" class="headerlink" title="7.5 文件路径操作"></a>7.5 文件路径操作</h3><p>有效管理文件路径是文件操作的基础，Python 提供了两种主要方式：传统的 <code>os.path</code> 模块和现代的 <code>pathlib</code> 库。</p><h4 id="7-5-1-使用-os-path-模块"><a href="#7-5-1-使用-os-path-模块" class="headerlink" title="7.5.1 使用 os.path 模块"></a>7.5.1 使用 os.path 模块</h4><table><thead><tr><th>方法/属性</th><th>描述</th></tr></thead><tbody><tr><td><code>os.getcwd()</code></td><td>获取当前工作目录</td></tr><tr><td><code>os.chdir(path)</code></td><td>改变当前工作目录</td></tr><tr><td><code>os.mkdir(name)</code></td><td>创建目录</td></tr><tr><td><code>os.makedirs(path)</code></td><td>递归创建多级目录</td></tr><tr><td><code>os.rmdir(name)</code></td><td>删除空目录</td></tr><tr><td><code>os.remove(path)</code></td><td>删除文件</td></tr><tr><td><code>os.listdir(path)</code></td><td>列出指定目录的文件和目录</td></tr><tr><td><code>os.rename(src, dst)</code></td><td>重命名文件或目录</td></tr><tr><td><code>os.system(command)</code></td><td>执行系统命令</td></tr><tr><td><code>os.environ</code></td><td>环境变量字典</td></tr><tr><td><code>os.path.exists(path)</code></td><td>检查路径是否存在</td></tr><tr><td><code>os.path.isfile(path)</code></td><td>检查路径是否为文件</td></tr><tr><td><code>os.path.isdir(path)</code></td><td>检查路径是否为目录</td></tr><tr><td><code>os.path.join(path1, path2)</code></td><td>连接路径</td></tr><tr><td><code>os.path.split(path)</code></td><td>根据最后一个路径分隔符分割路径为(目录, 文件名)</td></tr><tr><td><code>os.path.splitext(path)</code></td><td>根据最后一个点号分割路径为(文件名，拓展名)</td></tr><tr><td><code>os.path.dirname(path)</code></td><td>获取路径的目录部分</td></tr><tr><td><code>os.path.basename(path)</code></td><td>获取路径的文件名部分</td></tr><tr><td><code>os.path.getsize(path)</code></td><td>获取文件大小(字节)</td></tr></tbody></table><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> os.path</span><br><span class="line"><span class="keyword">import</span> datetime</span><br><span class="line"></span><br><span class="line"><span class="comment"># 获取当前脚本所在目录</span></span><br><span class="line">current_dir = os.path.dirname(os.path.abspath(__file__))</span><br><span class="line"></span><br><span class="line"><span class="comment"># 在当前目录下创建data.txt</span></span><br><span class="line">data_path = os.path.join(current_dir, <span class="string">"data.txt"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 首先创建一个测试文件</span></span><br><span class="line"><span class="keyword">with</span> <span class="built_in">open</span>(data_path, <span class="string">'w'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:</span><br><span class="line">    file.write(<span class="string">"这是测试内容！\nHello World!"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 文件路径处理演示</span></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"=== 文件路径信息 ==="</span>)</span><br><span class="line">directory = os.path.dirname(data_path)</span><br><span class="line">filename = os.path.basename(data_path)</span><br><span class="line">name, extension = os.path.splitext(filename)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"目录: <span class="subst">{directory}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"文件名: <span class="subst">{filename}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"纯名称: <span class="subst">{name}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"扩展名: <span class="subst">{extension}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"\n=== 路径检查 ==="</span>)</span><br><span class="line">exists = os.path.exists(data_path)</span><br><span class="line">is_file = os.path.isfile(data_path)</span><br><span class="line">is_dir = os.path.isdir(data_path)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"文件是否存在: <span class="subst">{exists}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"是否是文件: <span class="subst">{is_file}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"是否是目录: <span class="subst">{is_dir}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"\n=== 文件信息 ==="</span>)</span><br><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    size = os.path.getsize(data_path)</span><br><span class="line">    mod_time = os.path.getmtime(data_path) <span class="comment"># 获取修改时间</span></span><br><span class="line">    create_time = os.path.getctime(data_path) <span class="comment"># 获取创建时间</span></span><br><span class="line">    access_time = os.path.getatime(data_path) <span class="comment"># 获取访问时间</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 转换时间戳为可读时间</span></span><br><span class="line">    mod_datetime = datetime.datetime.fromtimestamp(mod_time)</span><br><span class="line">    create_datetime = datetime.datetime.fromtimestamp(create_time)</span><br><span class="line">    access_datetime = datetime.datetime.fromtimestamp(access_time)</span><br><span class="line">    </span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"文件大小: <span class="subst">{size}</span> 字节"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"创建时间: <span class="subst">{create_datetime}</span>"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"修改时间: <span class="subst">{mod_datetime}</span>"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"访问时间: <span class="subst">{access_datetime}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"\n=== 文件内容 ==="</span>)</span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(data_path, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:</span><br><span class="line">        content = file.read()</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"文件内容:\n<span class="subst">{content}</span>"</span>)</span><br><span class="line">        </span><br><span class="line"><span class="keyword">except</span> OSError <span class="keyword">as</span> e:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"获取文件信息时发生错误: <span class="subst">{e}</span>"</span>)</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><h4 id="7-5-2-使用-pathlib-模块-Python-3-4"><a href="#7-5-2-使用-pathlib-模块-Python-3-4" class="headerlink" title="7.5.2 使用 pathlib 模块 (Python 3.4+)"></a>7.5.2 使用 pathlib 模块 (Python 3.4+)</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> pathlib <span class="keyword">import</span> Path</span><br><span class="line"><span class="keyword">import</span> datetime</span><br><span class="line"></span><br><span class="line"><span class="comment"># 获取当前脚本所在目录并创建data.txt的路径</span></span><br><span class="line">current_file = Path(__file__)</span><br><span class="line">data_path = current_file.parent / <span class="string">"data.txt"</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 创建并写入测试文件</span></span><br><span class="line">data_path.write_text(<span class="string">"这是测试内容！\nHello World!"</span>, encoding=<span class="string">'utf-8'</span>)</span><br><span class="line"></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"=== 基本路径信息 ==="</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"完整路径: <span class="subst">{data_path}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"父目录: <span class="subst">{data_path.parent}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"文件名: <span class="subst">{data_path.name}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"纯名称: <span class="subst">{data_path.stem}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"扩展名: <span class="subst">{data_path.suffix}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"所有路径组件: <span class="subst">{data_path.parts}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"\n=== 路径解析 ==="</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"绝对路径: <span class="subst">{data_path.absolute()}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"解析路径: <span class="subst">{data_path.resolve()}</span>"</span>)</span><br><span class="line"><span class="keyword">try</span>:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"相对路径: <span class="subst">{data_path.relative_to(Path.cwd())}</span>"</span>)</span><br><span class="line"><span class="keyword">except</span> ValueError:</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">"无法计算相对路径（文件可能在不同驱动器或目录）"</span>)</span><br><span class="line"></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"\n=== 文件状态检查 ==="</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"文件是否存在: <span class="subst">{data_path.exists()}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"是否是文件: <span class="subst">{data_path.is_file()}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"是否是目录: <span class="subst">{data_path.is_dir()}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"是否是符号链接: <span class="subst">{data_path.is_symlink()}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"\n=== 文件信息 ==="</span>)</span><br><span class="line"><span class="keyword">if</span> data_path.exists() <span class="keyword">and</span> data_path.is_file():</span><br><span class="line">    stat = data_path.stat()</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"文件大小: <span class="subst">{stat.st_size}</span> 字节"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"创建时间: <span class="subst">{datetime.datetime.fromtimestamp(stat.st_ctime)}</span>"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"修改时间: <span class="subst">{datetime.datetime.fromtimestamp(stat.st_mtime)}</span>"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"访问时间: <span class="subst">{datetime.datetime.fromtimestamp(stat.st_atime)}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"\n=== 文件内容 ==="</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"文件内容:\n<span class="subst">{data_path.read_text(encoding=<span class="string">'utf-8'</span>)}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="built_in">print</span>(<span class="string">"\n=== 路径修改示例 ==="</span>)</span><br><span class="line">new_name = data_path.with_name(<span class="string">"newdata.txt"</span>)</span><br><span class="line">new_ext = data_path.with_suffix(<span class="string">".csv"</span>)</span><br><span class="line">new_stem = data_path.with_stem(<span class="string">"newdata"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"修改整个文件名: <span class="subst">{new_name}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"修改扩展名: <span class="subst">{new_ext}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"仅修改文件名(不含扩展名): <span class="subst">{new_stem}</span>"</span>)</span><br></pre></td></tr></tbody></table></figure><table><thead><tr><th>功能</th><th>os.path 方式</th><th>pathlib 方式</th><th>推荐</th></tr></thead><tbody><tr><td>路径连接</td><td><code>os.path.join(dir, file)</code></td><td><code>Path(dir) / file</code></td><td>pathlib 更直观</td></tr><tr><td>获取目录</td><td><code>os.path.dirname(path)</code></td><td><code>path.parent</code></td><td>pathlib 属性访问更清晰</td></tr><tr><td>获取文件名</td><td><code>os.path.basename(path)</code></td><td><code>path.name</code></td><td>pathlib 属性访问更清晰</td></tr><tr><td>分离扩展名</td><td><code>os.path.splitext(path)[1]</code></td><td><code>path.suffix</code></td><td>pathlib 更简洁</td></tr><tr><td>检查存在</td><td><code>os.path.exists(path)</code></td><td><code>path.exists()</code></td><td>两者类似，pathlib 面向对象</td></tr><tr><td>获取绝对路径</td><td><code>os.path.abspath(path)</code></td><td><code>path.absolute()</code></td><td>两者相当</td></tr></tbody></table><blockquote><p><strong>最佳实践</strong>：在新项目中优先使用 pathlib，它提供了更现代、更直观的面向对象接口。在维护旧代码时可能需要继续使用 os.path。</p></blockquote><h3 id="7-6-高级文件操作"><a href="#7-6-高级文件操作" class="headerlink" title="7.6 高级文件操作"></a>7.6 高级文件操作</h3><h4 id="7-6-1-二进制文件操作"><a href="#7-6-1-二进制文件操作" class="headerlink" title="7.6.1 二进制文件操作"></a>7.6.1 二进制文件操作</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 读取整个二进制文件</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">read_binary_file</span>(<span class="params">file_path</span>):</span><br><span class="line">    <span class="string">"""读取并返回整个二进制文件的内容"""</span></span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(file_path, <span class="string">"rb"</span>) <span class="keyword">as</span> f:</span><br><span class="line">        <span class="keyword">return</span> f.read()</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 分块读取大型二进制文件</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">process_large_binary_file</span>(<span class="params">file_path, chunk_size=<span class="number">1024</span> * <span class="number">1024</span></span>):</span><br><span class="line">    <span class="string">"""分块处理大型二进制文件，避免内存溢出"""</span></span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(file_path, <span class="string">"rb"</span>) <span class="keyword">as</span> f:</span><br><span class="line">        <span class="keyword">while</span> <span class="literal">True</span>:</span><br><span class="line">            chunk = f.read(chunk_size)  <span class="comment"># 每次读取1MB</span></span><br><span class="line">            <span class="keyword">if</span> <span class="keyword">not</span> chunk:  <span class="comment"># 到达文件末尾</span></span><br><span class="line">                <span class="keyword">break</span></span><br><span class="line"></span><br><span class="line">            <span class="comment"># 处理当前数据块</span></span><br><span class="line">            process_chunk(chunk)  <span class="comment"># 假设的处理函数</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 处理数据块的函数</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">process_chunk</span>(<span class="params">chunk</span>):</span><br><span class="line">    <span class="string">"""处理二进制数据块的函数"""</span></span><br><span class="line">    <span class="comment"># 这里可以根据需要实现具体的数据处理逻辑</span></span><br><span class="line">    <span class="comment"># 例如：计算校验和、搜索特定字节模式、转换数据格式等</span></span><br><span class="line">    chunk_size = <span class="built_in">len</span>(chunk)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"处理数据块: <span class="subst">{chunk_size}</span> 字节"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="comment"># 示例：计算数据块的哈希值</span></span><br><span class="line">    <span class="keyword">import</span> hashlib</span><br><span class="line">    chunk_hash = hashlib.md5(chunk).hexdigest()</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"数据块MD5哈希值: <span class="subst">{chunk_hash}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="comment"># 示例：检查数据块中的特定字节序列</span></span><br><span class="line">    <span class="keyword">if</span> <span class="string">b'\x00\x00\xff\xff'</span> <span class="keyword">in</span> chunk:</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">"在数据块中发现特定字节序列"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="comment"># 返回处理结果（可选）</span></span><br><span class="line">    <span class="keyword">return</span> {</span><br><span class="line">        <span class="string">'size'</span>: chunk_size,</span><br><span class="line">        <span class="string">'hash'</span>: chunk_hash</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 读取文件特定部分</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">read_file_section</span>(<span class="params">file_path, start, length</span>):</span><br><span class="line">    <span class="string">"""读取文件中从start位置开始的length个字节"""</span></span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(file_path, <span class="string">"rb"</span>) <span class="keyword">as</span> f:</span><br><span class="line">        f.seek(start)  <span class="comment"># 移动文件指针到指定位置</span></span><br><span class="line">        <span class="keyword">return</span> f.read(length)  <span class="comment"># 读取指定长度字节</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 检测文件类型</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">detect_file_type</span>(<span class="params">file_path</span>):</span><br><span class="line">    <span class="string">"""通过文件头部特征识别文件类型"""</span></span><br><span class="line">    <span class="comment"># 常见文件格式的魔数（Magic Numbers）</span></span><br><span class="line">    file_signatures = {</span><br><span class="line">        <span class="string">b'\x89PNG\r\n\x1a\n'</span>: <span class="string">'PNG image'</span>,</span><br><span class="line">        <span class="string">b'\xff\xd8\xff'</span>: <span class="string">'JPEG image'</span>,</span><br><span class="line">        <span class="string">b'GIF87a'</span>: <span class="string">'GIF image (87a)'</span>,</span><br><span class="line">        <span class="string">b'GIF89a'</span>: <span class="string">'GIF image (89a)'</span>,</span><br><span class="line">        <span class="string">b'%PDF'</span>: <span class="string">'PDF document'</span>,</span><br><span class="line">        <span class="string">b'PK\x03\x04'</span>: <span class="string">'ZIP archive'</span>,</span><br><span class="line">        <span class="string">b'\x50\x4b\x03\x04'</span>: <span class="string">'ZIP archive'</span>,  <span class="comment"># PK..</span></span><br><span class="line">        <span class="string">b'\x1f\x8b\x08'</span>: <span class="string">'GZIP compressed file'</span>,</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(file_path, <span class="string">"rb"</span>) <span class="keyword">as</span> f:</span><br><span class="line">        <span class="comment"># 读取文件前16个字节用于检测</span></span><br><span class="line">        header = f.read(<span class="number">16</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">for</span> signature, file_type <span class="keyword">in</span> file_signatures.items():</span><br><span class="line">        <span class="keyword">if</span> header.startswith(signature):</span><br><span class="line">            <span class="keyword">return</span> file_type</span><br><span class="line"></span><br><span class="line">    <span class="keyword">return</span> <span class="string">"未知文件类型"</span></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 实际演示：处理图像文件</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">image_file_demo</span>():</span><br><span class="line">    <span class="string">"""演示二进制文件操作的实际应用"""</span></span><br><span class="line">    <span class="comment"># 定义两个图形的基础文件路径</span></span><br><span class="line">    png_path = <span class="string">"example.png"</span></span><br><span class="line">    jpg_path = <span class="string">"example.jpg"</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 检测图像类型</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"检测文件类型: <span class="subst">{png_path}</span> 是 <span class="subst">{detect_file_type(png_path)}</span>"</span>) <span class="comment"># 检测文件类型: example.png 是 JPEG image</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"检测文件类型: <span class="subst">{jpg_path}</span> 是 <span class="subst">{detect_file_type(jpg_path)}</span>"</span>) <span class="comment"># 检测文件类型: example.jpg 是 JPEG image</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 读取PNG文件头部信息</span></span><br><span class="line">    png_header = read_file_section(png_path, <span class="number">0</span>, <span class="number">24</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"\nPNG文件头部字节: <span class="subst">{png_header.<span class="built_in">hex</span>(<span class="string">' '</span>, <span class="number">4</span>)}</span>"</span>)  <span class="comment"># PNG文件头部字节: ffd8ffe0 00104a46 49460001 01000001 00010000 ffdb0043</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 获取文件大小</span></span><br><span class="line">    png_data = read_binary_file(png_path)</span><br><span class="line">    jpg_data = read_binary_file(jpg_path)</span><br><span class="line"></span><br><span class="line">    <span class="comment"># 小技巧：在len函数中使用:, 即可以将数字以千分位分隔符展示</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"\n<span class="subst">{png_path}</span> 文件大小: <span class="subst">{<span class="built_in">len</span>(png_data):,}</span> 字节"</span>) <span class="comment"># example.png 文件大小: 7,189 字节</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"<span class="subst">{jpg_path}</span> 文件大小: <span class="subst">{<span class="built_in">len</span>(jpg_data):,}</span> 字节"</span>) <span class="comment"># example.jpg 文件大小: 7,189 字节</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 处理大型二进制文件</span></span><br><span class="line">    process_large_binary_file(png_path)</span><br><span class="line">    process_large_binary_file(jpg_path)</span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    image_file_demo()</span><br></pre></td></tr></tbody></table></figure><h4 id="7-6-2-临时文件操作"><a href="#7-6-2-临时文件操作" class="headerlink" title="7.6.2 临时文件操作"></a>7.6.2 临时文件操作</h4><p>临时文件对于需要中间处理结果但不想留下永久文件的操作非常有用。</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> tempfile</span><br><span class="line"><span class="keyword">import</span> os</span><br><span class="line"><span class="keyword">import</span> time</span><br><span class="line"><span class="keyword">import</span> random</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 示例2: 使用临时目录处理多个文件</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">process_batch_files</span>(<span class="params">data_items</span>):</span><br><span class="line">    <span class="string">"""在临时目录中创建多个文件并进行批处理"""</span></span><br><span class="line">    results = []</span><br><span class="line"></span><br><span class="line">    <span class="keyword">with</span> tempfile.TemporaryDirectory(prefix=<span class="string">"batch_"</span>) <span class="keyword">as</span> temp_dir:</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"创建临时工作区 <span class="subst">{temp_dir}</span>"</span>)</span><br><span class="line"></span><br><span class="line">        <span class="comment"># 创建多个数据文件</span></span><br><span class="line">        file_paths = []</span><br><span class="line">        <span class="keyword">for</span> i, data <span class="keyword">in</span> <span class="built_in">enumerate</span>(data_items):</span><br><span class="line">            <span class="comment"># 获取到临时目录下的文件路径</span></span><br><span class="line">            file_path = os.path.join(temp_dir, <span class="string">f"data_<span class="subst">{i}</span>.txt"</span>)</span><br><span class="line">            <span class="keyword">with</span> <span class="built_in">open</span>(file_path, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">                f.write(data)</span><br><span class="line">            file_paths.append(file_path)</span><br><span class="line"></span><br><span class="line">        <span class="comment"># 处理所有的文件</span></span><br><span class="line">        <span class="keyword">for</span> file_path <span class="keyword">in</span> file_paths:</span><br><span class="line">            <span class="keyword">with</span> <span class="built_in">open</span>(file_path, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:</span><br><span class="line">                content = f.read()</span><br><span class="line">                <span class="comment"># 模拟处理：计算字符数并添加到结果集中</span></span><br><span class="line">                results.append(<span class="string">f"文件<span class="subst">{os.path.basename(file_path)}</span> 包含 <span class="subst">{<span class="built_in">len</span>(content)}</span> 个字符"</span>)</span><br><span class="line">        <span class="comment"># 列出处理的文件</span></span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"处理文件为<span class="subst">{<span class="string">','</span>.join(os.path.basename(p) <span class="keyword">for</span> p <span class="keyword">in</span> file_paths)}</span>"</span>)</span><br><span class="line"></span><br><span class="line">        <span class="comment"># 退出with块后，tempfile会自动删除临时目录</span></span><br><span class="line">        <span class="comment"># 暂停30秒，等待用户查看结果</span></span><br><span class="line">        <span class="built_in">print</span>(<span class="string">"处理开始，大约需要30秒，请稍候..."</span>)</span><br><span class="line">        time.sleep(random.randint(<span class="number">10</span>, <span class="number">30</span>))</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">"处理完成"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">return</span> results</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 演示临时目录的批处理使用</span></span><br><span class="line">data_to_process = [</span><br><span class="line">    <span class="string">"这是第一个文件的测试内容！"</span>,</span><br><span class="line">    <span class="string">"这是第二个文件包含更多的信息以及携带数字13123123123132"</span>,</span><br><span class="line">    <span class="string">"这是第三个文件包含中文，你好，世界！"</span>]</span><br><span class="line"></span><br><span class="line">results = process_batch_files(data_to_process)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">"\n处理结果为:"</span>)</span><br><span class="line"><span class="keyword">for</span> r <span class="keyword">in</span> results:</span><br><span class="line">    <span class="built_in">print</span>(results)</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><h3 id="7-7-目录操作"><a href="#7-7-目录操作" class="headerlink" title="7.7 目录操作"></a>7.7 目录操作</h3><p>目录操作是文件系统操作的重要组成部分，Python 提供了多种目录操作方法。</p><h4 id="7-7-1-基本目录操作"><a href="#7-7-1-基本目录操作" class="headerlink" title="7.7.1 基本目录操作"></a>7.7.1 基本目录操作</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> os</span><br><span class="line"><span class="keyword">import</span> shutil</span><br><span class="line"></span><br><span class="line"><span class="comment"># 获取当前工作目录</span></span><br><span class="line">current_dir = os.getcwd()</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"当前工作目录: <span class="subst">{current_dir}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 更改当前工作目录</span></span><br><span class="line">os.chdir(<span class="string">"../"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"更改后的工作目录: <span class="subst">{os.getcwd()}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 列出目录内容</span></span><br><span class="line">entries = os.listdir(<span class="string">"."</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"目录内容: <span class="subst">{entries}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 过滤目录内容</span></span><br><span class="line">files_only = [f <span class="keyword">for</span> f <span class="keyword">in</span> entries <span class="keyword">if</span> os.path.isfile(f)]</span><br><span class="line">dirs_only = [d <span class="keyword">for</span> d <span class="keyword">in</span> entries <span class="keyword">if</span> os.path.isdir(d)]</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"文件: <span class="subst">{files_only}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"目录: <span class="subst">{dirs_only}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 创建目录</span></span><br><span class="line">os.chdir(current_dir) <span class="comment"># 切换回原目录</span></span><br><span class="line">os.mkdir(<span class="string">"new_dir"</span>) <span class="comment"># 创建单个目录</span></span><br><span class="line">os.makedirs(<span class="string">"new_dir2/sub_dir/sub_sub_dir"</span>,exist_ok=<span class="literal">True</span>) <span class="comment"># 创建多级目录 (exist_ok=True 忽略已存在的目录)</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 删除目录</span></span><br><span class="line">os.rmdir(<span class="string">"new_dir"</span>) <span class="comment"># 只能删除空目录</span></span><br><span class="line">shutil.rmtree(<span class="string">"new_dir2"</span>) <span class="comment"># 删除目录以及所有内容(谨慎使用！！！)</span></span><br></pre></td></tr></tbody></table></figure><h4 id="7-7-2-使用-pathlib-进行目录操作"><a href="#7-7-2-使用-pathlib-进行目录操作" class="headerlink" title="7.7.2 使用 pathlib 进行目录操作"></a>7.7.2 使用 pathlib 进行目录操作</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> pathlib <span class="keyword">import</span> Path</span><br><span class="line"><span class="keyword">import</span> shutil</span><br><span class="line"></span><br><span class="line"><span class="comment"># 创建目录</span></span><br><span class="line">Path(<span class="string">"new_dir"</span>).mkdir(exist_ok=<span class="literal">True</span>)</span><br><span class="line">Path(<span class="string">"parent/child/grandchild"</span>).mkdir(parents=<span class="literal">True</span>, exist_ok=<span class="literal">True</span>) <span class="comment"># 创建多层目录 parents=True 递归创建父目录</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># 列出目录内容</span></span><br><span class="line">p = Path(<span class="string">"."</span>)</span><br><span class="line"><span class="keyword">for</span> item <span class="keyword">in</span> p.iterdir():</span><br><span class="line">    <span class="keyword">if</span> item.is_file():</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"文件: <span class="subst">{item}</span>，大小: <span class="subst">{item.stat().st_size}</span> bytes"</span>)</span><br><span class="line">    <span class="keyword">elif</span> item.is_dir():</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"目录: <span class="subst">{item}</span>"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 过滤特定类型的文件</span></span><br><span class="line">python_files = <span class="built_in">list</span>(p.glob(<span class="string">"*.py"</span>)) <span class="comment"># 列出当前目录下所有.py 文件</span></span><br><span class="line">all_python_files = <span class="built_in">list</span>(p.rglob(<span class="string">"*.py"</span>)) <span class="comment"># 递归搜索所有子目录</span></span><br><span class="line"></span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"当前目录下Python文件: <span class="subst">{[f.name <span class="keyword">for</span> f <span class="keyword">in</span> python_files]}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"递归搜索所有Python文件: <span class="subst">{[f.name <span class="keyword">for</span> f <span class="keyword">in</span> all_python_files]}</span>"</span>)</span><br><span class="line"><span class="built_in">print</span>(<span class="string">f"所有Python文件: <span class="subst">{<span class="built_in">len</span>(all_python_files)}</span>"</span>)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="comment"># 删除目录</span></span><br><span class="line">Path(<span class="string">'new_dir'</span>).rmdir()  <span class="comment"># 只能删除空目录</span></span><br><span class="line">shutil.rmtree(<span class="string">'parent'</span>) <span class="comment"># 删除目录及其所有内容</span></span><br></pre></td></tr></tbody></table></figure><table><thead><tr><th align="left">操作</th><th align="left">os/shutil 方法</th><th align="left">pathlib 方法</th><th align="left">优势比较</th></tr></thead><tbody><tr><td align="left"><strong>获取当前目录</strong></td><td align="left"><code>os.getcwd()</code></td><td align="left"><code>Path.cwd()</code></td><td align="left">pathlib 返回 Path 对象，便于后续操作</td></tr><tr><td align="left"><strong>切换工作目录</strong></td><td align="left"><code>os.chdir(path)</code></td><td align="left"><em>(无直接等效方法)</em></td><td align="left">os 更适合改变全局工作目录</td></tr><tr><td align="left"><strong>列出目录内容</strong></td><td align="left"><code>os.listdir(path)</code></td><td align="left"><code>Path(path).iterdir()</code></td><td align="left">pathlib 直接返回 Path 对象，无需再拼接路径</td></tr><tr><td align="left"><strong>创建单个目录</strong></td><td align="left"><code>os.mkdir(path)</code></td><td align="left"><code>Path(path).mkdir()</code></td><td align="left">功能相同，pathlib 更面向对象</td></tr><tr><td align="left"><strong>创建多级目录</strong></td><td align="left"><code>os.makedirs(path, exist_ok=True)</code></td><td align="left"><code>Path(path).mkdir(parents=True, exist_ok=True)</code></td><td align="left">语义更明确，参数名更具描述性</td></tr><tr><td align="left"><strong>删除空目录</strong></td><td align="left"><code>os.rmdir(path)</code></td><td align="left"><code>Path(path).rmdir()</code></td><td align="left">功能相同，pathlib 更面向对象</td></tr><tr><td align="left"><strong>递归删除目录</strong></td><td align="left"><code>shutil.rmtree(path)</code></td><td align="left"><em>(无直接等效方法，需循环删除)</em></td><td align="left">os/shutil 提供单一高效操作</td></tr><tr><td align="left"><strong>文件路径拼接</strong></td><td align="left"><code>os.path.join(dir, file)</code></td><td align="left"><code>Path(dir) / file</code></td><td align="left">pathlib 使用/运算符更直观简洁</td></tr><tr><td align="left"><strong>检查路径是否存在</strong></td><td align="left"><code>os.path.exists(path)</code></td><td align="left"><code>Path(path).exists()</code></td><td align="left">功能相同，pathlib 更面向对象</td></tr><tr><td align="left"><strong>检查是否为文件</strong></td><td align="left"><code>os.path.isfile(path)</code></td><td align="left"><code>Path(path).is_file()</code></td><td align="left">功能相同，pathlib 更面向对象</td></tr><tr><td align="left"><strong>检查是否为目录</strong></td><td align="left"><code>os.path.isdir(path)</code></td><td align="left"><code>Path(path).is_dir()</code></td><td align="left">功能相同，pathlib 更面向对象</td></tr></tbody></table><h4 id="7-7-3-递归遍历目录树"><a href="#7-7-3-递归遍历目录树" class="headerlink" title="7.7.3 递归遍历目录树"></a>7.7.3 递归遍历目录树</h4><p>递归遍历是处理目录层次结构的强大工具，在很多需要列举文件目录树的场景都可以采用该思路去打印输出</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 使用os.walk遍历目录树</span></span><br><span class="line"><span class="keyword">import</span> os</span><br><span class="line"><span class="comment">#示例输出：</span></span><br><span class="line"><span class="comment"># 学习用/</span></span><br><span class="line"><span class="comment">#     data.txt - 0.04 KB</span></span><br><span class="line"><span class="comment">#     example.jpg - 7.02 KB</span></span><br><span class="line"><span class="comment">#     example.png - 7.02 KB</span></span><br><span class="line"><span class="comment">#     example.py - 4.34 KB</span></span><br><span class="line"><span class="comment">#     example.txt - 0.03 KB</span></span><br><span class="line"><span class="comment">#     main.py - 4.54 KB</span></span><br><span class="line"><span class="comment">#     requirements.txt - 0.01 KB</span></span><br><span class="line"><span class="comment">#     study.py - 1.40 KB</span></span><br><span class="line"><span class="comment">#     电子商务系统实现笔记.md - 24.60 KB</span></span><br><span class="line"><span class="comment">#     (目录大小: 49.00 KB)</span></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">scan_directory</span>(<span class="params">directory</span>):</span><br><span class="line">    <span class="string">"""递归扫描目录，显示结构和文件大小"""</span></span><br><span class="line">    total_size = <span class="number">0</span></span><br><span class="line">    <span class="keyword">for</span> root, dirs, files <span class="keyword">in</span> os.walk(directory):  <span class="comment"># 其中root：当前目录路径，dirs：子目录列表，files：文件列表</span></span><br><span class="line">        <span class="comment"># 计算当前的目录深度(用于缩进)</span></span><br><span class="line">        <span class="comment"># 计算目录深度：</span></span><br><span class="line">        <span class="comment"># 1. root.replace(directory, "") - 将当前路径中的起始目录部分替换为空字符串，得到相对路径</span></span><br><span class="line">        <span class="comment"># 2. .count(os.sep) - 统计相对路径中目录分隔符(如'/'或'\')的数量</span></span><br><span class="line">        <span class="comment"># 每一个分隔符代表一层目录深度，因此分隔符的数量就等于目录的嵌套层级</span></span><br><span class="line">        level = root.replace(directory, <span class="string">""</span>).count(os.sep)</span><br><span class="line">        indent = <span class="string">' '</span> * <span class="number">4</span> * level</span><br><span class="line">        <span class="comment"># 打印当前目录</span></span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"<span class="subst">{indent}</span><span class="subst">{os.path.basename(root)}</span>/"</span>)</span><br><span class="line"></span><br><span class="line">        <span class="comment"># 缩进子文件</span></span><br><span class="line">        sub_indent = <span class="string">' '</span> * <span class="number">4</span> * (level + <span class="number">1</span>)</span><br><span class="line"></span><br><span class="line">        <span class="comment"># 统计当前目录下的文件大小</span></span><br><span class="line">        dir_size = <span class="number">0</span></span><br><span class="line">        <span class="keyword">for</span> file <span class="keyword">in</span> files:</span><br><span class="line">            file_path = os.path.join(root, file)</span><br><span class="line">            file_size = os.path.getsize(file_path)</span><br><span class="line">            dir_size += file_size</span><br><span class="line">            <span class="built_in">print</span>(<span class="string">f"<span class="subst">{sub_indent}</span><span class="subst">{file}</span> - <span class="subst">{file_size / <span class="number">1024</span>:<span class="number">.2</span>f}</span> KB"</span>)</span><br><span class="line"></span><br><span class="line">        <span class="comment"># 累加总大小</span></span><br><span class="line">        total_size += dir_size</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"<span class="subst">{sub_indent}</span>(目录大小: <span class="subst">{dir_size / <span class="number">1024</span>:<span class="number">.2</span>f}</span> KB)"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">return</span> total_size / <span class="number">1024</span>  <span class="comment"># 返回总大小(单位：KB)</span></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    total_size = scan_directory(<span class="string">'D:/python/学习用'</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"总大小: <span class="subst">{total_size:<span class="number">.2</span>f}</span> KB"</span>)</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><h4 id="7-7-4-文件复制、移动和删除操作"><a href="#7-7-4-文件复制、移动和删除操作" class="headerlink" title="7.7.4 文件复制、移动和删除操作"></a>7.7.4 文件复制、移动和删除操作</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> os</span><br><span class="line"><span class="keyword">import</span> shutil</span><br><span class="line"></span><br><span class="line"><span class="comment"># ========== 文件复制操作 ==========</span></span><br><span class="line"><span class="comment"># shutil.copy(src, dst): 复制文件到目标路径，不保留元数据（如文件的创建时间、修改时间等）</span></span><br><span class="line"><span class="comment"># 参数说明：src - 源文件路径，dst - 目标路径（可以是目录或文件名）</span></span><br><span class="line"><span class="comment"># 返回值：目标文件路径</span></span><br><span class="line">shutil.copy(<span class="string">'source.txt'</span>, <span class="string">'destination.txt'</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># shutil.copy2(src, dst): 复制文件到目标路径，保留所有元数据（如修改时间、访问时间、权限等）</span></span><br><span class="line"><span class="comment"># 参数说明：同copy函数</span></span><br><span class="line"><span class="comment"># 返回值：目标文件路径</span></span><br><span class="line">shutil.copy2(<span class="string">'source.txt'</span>, <span class="string">'destination.txt'</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># ========== 目录复制操作 ==========</span></span><br><span class="line"><span class="comment"># shutil.copytree(src, dst): 递归复制整个目录树，目标目录不能已存在</span></span><br><span class="line"><span class="comment"># 参数说明：src - 源目录，dst - 目标目录（必须不存在）</span></span><br><span class="line"><span class="comment"># 返回值：目标目录路径</span></span><br><span class="line">shutil.copytree(<span class="string">'source_dir'</span>, <span class="string">'destination_dir'</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># shutil.copytree 高级用法：使用ignore参数忽略特定文件</span></span><br><span class="line"><span class="comment"># shutil.ignore_patterns(): 创建一个忽略函数，用于过滤不需要复制的文件</span></span><br><span class="line"><span class="comment"># 参数说明：可变参数，接受多个glob风格的模式字符串</span></span><br><span class="line">shutil.copytree(<span class="string">'source_dir'</span>, <span class="string">'destination_dir'</span>,</span><br><span class="line">                ignore=shutil.ignore_patterns(<span class="string">'*.pyc'</span>, <span class="string">'*.git'</span>))</span><br><span class="line"></span><br><span class="line"><span class="comment"># ========== 文件移动操作 ==========</span></span><br><span class="line"><span class="comment"># shutil.move(src, dst): 移动文件或目录到目标路径</span></span><br><span class="line"><span class="comment"># 参数说明：src - 源路径，dst - 目标路径</span></span><br><span class="line"><span class="comment"># 返回值：目标路径</span></span><br><span class="line"><span class="comment"># 用法1：重命名文件</span></span><br><span class="line">shutil.move(<span class="string">'old_name.txt'</span>, <span class="string">'new_name.txt'</span>)</span><br><span class="line"><span class="comment"># 用法2：移动文件到其他目录</span></span><br><span class="line">shutil.move(<span class="string">'file.txt'</span>, <span class="string">'directory/'</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># ========== 文件删除操作 ==========</span></span><br><span class="line"><span class="comment"># os.remove(path): 删除指定路径的文件（不能删除目录）</span></span><br><span class="line"><span class="comment"># 参数说明：path - 要删除的文件路径</span></span><br><span class="line"><span class="comment"># 注意：如果文件不存在会抛出FileNotFoundError异常</span></span><br><span class="line">os.remove(<span class="string">'file.txt'</span>)</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><h3 id="7-8-文件监控与变更检测"><a href="#7-8-文件监控与变更检测" class="headerlink" title="7.8 文件监控与变更检测"></a>7.8 文件监控与变更检测</h3><p>在某些应用场景中，需要监控文件变化并作出响应。</p><h4 id="7-8-1-基础文件监控"><a href="#7-8-1-基础文件监控" class="headerlink" title="7.8.1 基础文件监控"></a>7.8.1 基础文件监控</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> os</span><br><span class="line"><span class="keyword">import</span> time</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">monitor_file</span>(<span class="params">file_path, interval=<span class="number">1</span>, encoding=<span class="string">'utf-8'</span></span>):</span><br><span class="line">    <span class="string">"""监控文件变化，并输出新增内容"""</span></span><br><span class="line">    <span class="keyword">if</span> <span class="keyword">not</span> os.path.exists(file_path):</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"文件 <span class="subst">{file_path}</span> 不存在"</span>)</span><br><span class="line">        <span class="keyword">return</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 获取初始状态</span></span><br><span class="line">    last_size = os.path.getsize(file_path) <span class="comment"># 文件大小</span></span><br><span class="line">    last_modified = os.path.getmtime(file_path) <span class="comment"># 最后修改时间戳</span></span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"开始监控文件:<span class="subst">{file_path}</span>"</span>)</span><br><span class="line">    <span class="built_in">print</span>(<span class="string">f"文件大小:<span class="subst">{last_size}</span> 最后修改时间:<span class="subst">{time.ctime(last_modified)}</span>"</span>)</span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="keyword">while</span> <span class="literal">True</span>:</span><br><span class="line">            <span class="comment"># 检查文件是否被修改</span></span><br><span class="line">            current_modified = os.path.getmtime(file_path)</span><br><span class="line">            current_size = os.path.getsize(file_path)</span><br><span class="line">            <span class="keyword">if</span> current_modified != last_modified:</span><br><span class="line">                <span class="built_in">print</span>(<span class="string">f"文件在<span class="subst">{time.ctime(current_modified)}</span>被修改"</span>)</span><br><span class="line">            <span class="comment"># 如果文件增大了，读取新增内容</span></span><br><span class="line">            <span class="keyword">if</span> current_size &gt; last_size:</span><br><span class="line">                <span class="comment"># 尝试不同的编码方式读取文件</span></span><br><span class="line">                encodings_to_try = [<span class="string">'utf-8'</span>, <span class="string">'gbk'</span>, <span class="string">'gb2312'</span>, <span class="string">'gb18030'</span>]</span><br><span class="line">                content_read = <span class="literal">False</span></span><br><span class="line">                </span><br><span class="line">                <span class="keyword">for</span> enc <span class="keyword">in</span> encodings_to_try:</span><br><span class="line">                    <span class="keyword">try</span>:</span><br><span class="line">                        <span class="keyword">with</span> <span class="built_in">open</span>(file_path, <span class="string">"r"</span>, encoding=enc) <span class="keyword">as</span> f:</span><br><span class="line">                            f.seek(last_size) <span class="comment"># 移动文件指针到上次读取位置</span></span><br><span class="line">                            new_content = f.read()</span><br><span class="line">                            <span class="built_in">print</span>(<span class="string">f"新增内容:\n<span class="subst">{new_content}</span>"</span>)</span><br><span class="line">                            <span class="comment"># 更新当前使用的编码</span></span><br><span class="line">                            encoding = enc</span><br><span class="line">                            content_read = <span class="literal">True</span></span><br><span class="line">                            <span class="keyword">break</span></span><br><span class="line">                    <span class="keyword">except</span> UnicodeDecodeError:</span><br><span class="line">                        <span class="keyword">continue</span></span><br><span class="line">                </span><br><span class="line">                <span class="keyword">if</span> <span class="keyword">not</span> content_read:</span><br><span class="line">                    <span class="comment"># 如果所有编码都失败，尝试以二进制方式读取并显示</span></span><br><span class="line">                    <span class="keyword">try</span>:</span><br><span class="line">                        <span class="keyword">with</span> <span class="built_in">open</span>(file_path, <span class="string">"rb"</span>) <span class="keyword">as</span> f:</span><br><span class="line">                            f.seek(last_size)</span><br><span class="line">                            binary_content = f.read()</span><br><span class="line">                            <span class="built_in">print</span>(<span class="string">f"无法解码文件内容，显示二进制内容: <span class="subst">{binary_content}</span>"</span>)</span><br><span class="line">                    <span class="keyword">except</span> Exception <span class="keyword">as</span> e:</span><br><span class="line">                        <span class="built_in">print</span>(<span class="string">f"读取文件失败: <span class="subst">{e}</span>"</span>)</span><br><span class="line">                        </span><br><span class="line">            <span class="keyword">elif</span> current_size &lt; last_size: <span class="comment"># 文件缩小了，可能是被清空了</span></span><br><span class="line">                <span class="built_in">print</span>(<span class="string">"文件大小减小了，可能被截断或重写"</span>)</span><br><span class="line"></span><br><span class="line">            <span class="comment"># 更新状态</span></span><br><span class="line">            last_modified = current_modified</span><br><span class="line">            last_size = current_size</span><br><span class="line"></span><br><span class="line">            time.sleep(interval) <span class="comment"># 休眠一段时间再检查文件</span></span><br><span class="line">    <span class="keyword">except</span> KeyboardInterrupt:</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">"监控已停止"</span>)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    monitor_file(<span class="string">"destination.txt"</span>, interval=<span class="number">1</span>)</span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><h4 id="7-8-2-使用-watchdog-库进行高级文件监控"><a href="#7-8-2-使用-watchdog-库进行高级文件监控" class="headerlink" title="7.8.2 使用 watchdog 库进行高级文件监控"></a>7.8.2 使用 watchdog 库进行高级文件监控</h4><p>对于更复杂的文件系统监控需求，Python 的第三方库 <code>watchdog</code> 提供了更强大的功能：</p><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> watchdog.observers <span class="keyword">import</span> Observer</span><br><span class="line"><span class="keyword">from</span> watchdog.events <span class="keyword">import</span> FileSystemEventHandler, DirCreatedEvent, FileCreatedEvent, DirDeletedEvent, \</span><br><span class="line">    FileDeletedEvent, DirModifiedEvent, FileModifiedEvent, DirMovedEvent, FileMovedEvent</span><br><span class="line"><span class="keyword">import</span> time</span><br><span class="line"><span class="keyword">import</span> os</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">class</span> <span class="title class_">MyHandler</span>(<span class="title class_ inherited__">FileSystemEventHandler</span>):</span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">on_created</span>(<span class="params">self, event: DirCreatedEvent | FileCreatedEvent</span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">        <span class="keyword">if</span> <span class="keyword">not</span> event.is_directory:</span><br><span class="line">            <span class="built_in">print</span>(<span class="string">f"文件被创建: <span class="subst">{event.src_path}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">on_deleted</span>(<span class="params">self, event: DirDeletedEvent | FileDeletedEvent</span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">        <span class="keyword">if</span> <span class="keyword">not</span> event.is_directory:</span><br><span class="line">            <span class="built_in">print</span>(<span class="string">f"文件被删除: <span class="subst">{event.src_path}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">on_modified</span>(<span class="params">self, event: DirModifiedEvent | FileModifiedEvent</span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">        <span class="keyword">if</span> <span class="keyword">not</span> event.is_directory:</span><br><span class="line">            <span class="built_in">print</span>(<span class="string">f"文件被修改: <span class="subst">{event.src_path}</span>"</span>)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">def</span> <span class="title function_">on_moved</span>(<span class="params">self, event: DirMovedEvent | FileMovedEvent</span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">        <span class="keyword">if</span> <span class="keyword">not</span> event.is_directory:</span><br><span class="line">            <span class="built_in">print</span>(<span class="string">f"文件被移动: <span class="subst">{event.src_path}</span> -&gt; <span class="subst">{event.dest_path}</span>"</span>)</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">watch_directory</span>(<span class="params">path: <span class="built_in">str</span></span>) -&gt; <span class="literal">None</span>:</span><br><span class="line">    <span class="comment"># 确保监控的是目录而不是文件</span></span><br><span class="line">    <span class="keyword">if</span> os.path.isfile(path):</span><br><span class="line">        <span class="comment"># 如果是文件，则监控其所在的目录</span></span><br><span class="line">        directory = os.path.dirname(path)</span><br><span class="line">        <span class="keyword">if</span> <span class="keyword">not</span> directory:  <span class="comment"># 如果是当前目录下的文件</span></span><br><span class="line">            directory = <span class="string">'.'</span></span><br><span class="line">    <span class="keyword">else</span>:</span><br><span class="line">        directory = path</span><br><span class="line">    </span><br><span class="line">    event_handler = MyHandler()</span><br><span class="line">    observer = Observer()</span><br><span class="line">    observer.schedule(event_handler, directory, recursive=<span class="literal">True</span>)</span><br><span class="line">    observer.start()</span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"开始监控目录: <span class="subst">{directory}</span>"</span>)</span><br><span class="line">        <span class="built_in">print</span>(<span class="string">"按Ctrl+C停止监控..."</span>)</span><br><span class="line">        <span class="keyword">while</span> <span class="literal">True</span>:</span><br><span class="line">            time.sleep(<span class="number">1</span>)</span><br><span class="line">    <span class="keyword">except</span> KeyboardInterrupt:</span><br><span class="line">        observer.stop()</span><br><span class="line">    observer.join()</span><br><span class="line"></span><br><span class="line"></span><br><span class="line"><span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:</span><br><span class="line">    watch_directory(<span class="string">"."</span>)  <span class="comment"># 监控当前目录，或者指定一个确实存在的目录路径</span></span><br><span class="line"></span><br></pre></td></tr></tbody></table></figure><h3 id="7-9-实际应用场景示例"><a href="#7-9-实际应用场景示例" class="headerlink" title="7.9 实际应用场景示例"></a>7.9 实际应用场景示例</h3><h4 id="7-9-1-文件备份工具"><a href="#7-9-1-文件备份工具" class="headerlink" title="7.9.1 文件备份工具"></a>7.9.1 文件备份工具</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> os</span><br><span class="line"><span class="keyword">import</span> shutil</span><br><span class="line"><span class="keyword">import</span> datetime</span><br><span class="line"><span class="keyword">from</span> pathlib <span class="keyword">import</span> Path</span><br><span class="line"><span class="keyword">import</span> zipfile</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">backup_directory</span>(<span class="params">source_dir, backup_dir=<span class="literal">None</span>, zip_backup=<span class="literal">True</span></span>):</span><br><span class="line">    <span class="string">"""</span></span><br><span class="line"><span class="string">    创建目录的备份</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    参数:</span></span><br><span class="line"><span class="string">        source_dir: 要备份的源目录</span></span><br><span class="line"><span class="string">        backup_dir: 备份文件存放目录(默认在源目录的父目录)</span></span><br><span class="line"><span class="string">        zip_backup: 是否创建zip压缩备份</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    返回:</span></span><br><span class="line"><span class="string">        备份文件的路径</span></span><br><span class="line"><span class="string">    """</span></span><br><span class="line">    <span class="comment"># 确保源目录存在</span></span><br><span class="line">    source_path = Path(source_dir)</span><br><span class="line">    <span class="keyword">if</span> <span class="keyword">not</span> source_path.exists() <span class="keyword">or</span> <span class="keyword">not</span> source_path.is_dir():</span><br><span class="line">        <span class="keyword">raise</span> ValueError(<span class="string">f"源目录 '<span class="subst">{source_dir}</span>' 不存在或不是一个目录"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 设置默认备份目录</span></span><br><span class="line">    <span class="keyword">if</span> backup_dir <span class="keyword">is</span> <span class="literal">None</span>:</span><br><span class="line">        backup_dir = source_path.parent</span><br><span class="line">    <span class="keyword">else</span>:</span><br><span class="line">        backup_dir = Path(backup_dir)</span><br><span class="line">        <span class="keyword">if</span> <span class="keyword">not</span> backup_dir.exists():</span><br><span class="line">            backup_dir.mkdir(parents=<span class="literal">True</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 创建备份文件名(包含时间戳)</span></span><br><span class="line">    timestamp = datetime.datetime.now().strftime(<span class="string">"%Y%m%d_%H%M%S"</span>)</span><br><span class="line">    backup_name = <span class="string">f"<span class="subst">{source_path.name}</span>_backup_<span class="subst">{timestamp}</span>"</span></span><br><span class="line">    backup_path = backup_dir / backup_name</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">if</span> zip_backup:</span><br><span class="line">        <span class="comment"># 创建ZIP备份</span></span><br><span class="line">        zip_path = <span class="built_in">str</span>(backup_path) + <span class="string">'.zip'</span></span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"创建ZIP备份: <span class="subst">{zip_path}</span>"</span>)</span><br><span class="line">        </span><br><span class="line">        <span class="keyword">with</span> zipfile.ZipFile(zip_path, <span class="string">'w'</span>, zipfile.ZIP_DEFLATED) <span class="keyword">as</span> zipf:</span><br><span class="line">            <span class="comment"># 遍历源目录中的所有文件</span></span><br><span class="line">            <span class="keyword">for</span> root, _, files <span class="keyword">in</span> os.walk(source_dir):</span><br><span class="line">                <span class="keyword">for</span> file <span class="keyword">in</span> files:</span><br><span class="line">                    file_path = os.path.join(root, file)</span><br><span class="line">                    <span class="comment"># 计算文件在ZIP中的相对路径</span></span><br><span class="line">                    rel_path = os.path.relpath(file_path, source_dir)</span><br><span class="line">                    <span class="built_in">print</span>(<span class="string">f"添加: <span class="subst">{rel_path}</span>"</span>)</span><br><span class="line">                    zipf.write(file_path, rel_path)</span><br><span class="line">        </span><br><span class="line">        <span class="keyword">return</span> zip_path</span><br><span class="line">    <span class="keyword">else</span>:</span><br><span class="line">        <span class="comment"># 创建目录备份(复制)</span></span><br><span class="line">        <span class="built_in">print</span>(<span class="string">f"创建目录备份: <span class="subst">{backup_path}</span>"</span>)</span><br><span class="line">        shutil.copytree(source_path, backup_path)</span><br><span class="line">        <span class="keyword">return</span> <span class="built_in">str</span>(backup_path)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用示例</span></span><br><span class="line"><span class="comment"># source = "/path/to/important_data"</span></span><br><span class="line"><span class="comment"># backup = backup_directory(source, zip_backup=True)</span></span><br><span class="line"><span class="comment"># print(f"备份已创建: {backup}")</span></span><br></pre></td></tr></tbody></table></figure><h4 id="7-9-2-日志分析工具"><a href="#7-9-2-日志分析工具" class="headerlink" title="7.9.2 日志分析工具"></a>7.9.2 日志分析工具</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br><span class="line">142</span><br><span class="line">143</span><br><span class="line">144</span><br><span class="line">145</span><br><span class="line">146</span><br><span class="line">147</span><br><span class="line">148</span><br><span class="line">149</span><br><span class="line">150</span><br><span class="line">151</span><br><span class="line">152</span><br><span class="line">153</span><br><span class="line">154</span><br><span class="line">155</span><br><span class="line">156</span><br><span class="line">157</span><br><span class="line">158</span><br><span class="line">159</span><br><span class="line">160</span><br><span class="line">161</span><br><span class="line">162</span><br><span class="line">163</span><br><span class="line">164</span><br><span class="line">165</span><br><span class="line">166</span><br><span class="line">167</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">from</span> collections <span class="keyword">import</span> Counter</span><br><span class="line"><span class="keyword">import</span> re</span><br><span class="line"><span class="keyword">from</span> datetime <span class="keyword">import</span> datetime</span><br><span class="line"><span class="keyword">import</span> matplotlib.pyplot <span class="keyword">as</span> plt</span><br><span class="line"><span class="keyword">from</span> pathlib <span class="keyword">import</span> Path</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">analyze_log_file</span>(<span class="params">log_path, pattern=<span class="literal">None</span></span>):</span><br><span class="line">    <span class="string">"""</span></span><br><span class="line"><span class="string">    分析日志文件并生成报告</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    参数:</span></span><br><span class="line"><span class="string">        log_path: 日志文件路径</span></span><br><span class="line"><span class="string">        pattern: 用于匹配日志行的正则表达式模式(默认为None，表示所有行)</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    返回:</span></span><br><span class="line"><span class="string">        包含分析结果的字典</span></span><br><span class="line"><span class="string">    """</span></span><br><span class="line">    log_path = Path(log_path)</span><br><span class="line">    <span class="keyword">if</span> <span class="keyword">not</span> log_path.exists():</span><br><span class="line">        <span class="keyword">raise</span> FileNotFoundError(<span class="string">f"日志文件不存在: <span class="subst">{log_path}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 初始化结果</span></span><br><span class="line">    results = {</span><br><span class="line">        <span class="string">'total_lines'</span>: <span class="number">0</span>,</span><br><span class="line">        <span class="string">'matched_lines'</span>: <span class="number">0</span>,</span><br><span class="line">        <span class="string">'errors'</span>: <span class="number">0</span>,</span><br><span class="line">        <span class="string">'warnings'</span>: <span class="number">0</span>,</span><br><span class="line">        <span class="string">'by_hour'</span>: Counter(),</span><br><span class="line">        <span class="string">'ip_addresses'</span>: Counter(),</span><br><span class="line">        <span class="string">'status_codes'</span>: Counter(),</span><br><span class="line">        <span class="string">'top_urls'</span>: Counter()</span><br><span class="line">    }</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 编译正则表达式</span></span><br><span class="line">    <span class="keyword">if</span> pattern:</span><br><span class="line">        regex = re.<span class="built_in">compile</span>(pattern)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># IP地址模式</span></span><br><span class="line">    ip_pattern = re.<span class="built_in">compile</span>(<span class="string">r'\b(?:\d{1,3}\.){3}\d{1,3}\b'</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># HTTP状态码模式</span></span><br><span class="line">    status_pattern = re.<span class="built_in">compile</span>(<span class="string">r'\s(\d{3})\s'</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 时间戳模式(假设格式为[DD/Mon/YYYY:HH:MM:SS +ZZZZ])</span></span><br><span class="line">    timestamp_pattern = re.<span class="built_in">compile</span>(<span class="string">r'\[(\d{2}/\w{3}/\d{4}):(\d{2}):\d{2}:\d{2}\s[+\-]\d{4}\]'</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># URL模式</span></span><br><span class="line">    url_pattern = re.<span class="built_in">compile</span>(<span class="string">r'"(?:GET|POST|PUT|DELETE)\s+([^\s"]+)'</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 错误和警告模式</span></span><br><span class="line">    error_pattern = re.<span class="built_in">compile</span>(<span class="string">r'ERROR|CRITICAL|FATAL'</span>, re.IGNORECASE)</span><br><span class="line">    warning_pattern = re.<span class="built_in">compile</span>(<span class="string">r'WARNING|WARN'</span>, re.IGNORECASE)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 读取和分析日志文件</span></span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(log_path, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>, errors=<span class="string">'ignore'</span>) <span class="keyword">as</span> f:</span><br><span class="line">        <span class="keyword">for</span> line <span class="keyword">in</span> f:</span><br><span class="line">            results[<span class="string">'total_lines'</span>] += <span class="number">1</span></span><br><span class="line">            </span><br><span class="line">            <span class="comment"># 应用模式匹配过滤(如果提供)</span></span><br><span class="line">            <span class="keyword">if</span> pattern <span class="keyword">and</span> <span class="keyword">not</span> regex.search(line):</span><br><span class="line">                <span class="keyword">continue</span></span><br><span class="line">            </span><br><span class="line">            results[<span class="string">'matched_lines'</span>] += <span class="number">1</span></span><br><span class="line">            </span><br><span class="line">            <span class="comment"># 提取IP地址</span></span><br><span class="line">            ip_matches = ip_pattern.findall(line)</span><br><span class="line">            <span class="keyword">if</span> ip_matches:</span><br><span class="line">                results[<span class="string">'ip_addresses'</span>].update([ip_matches[<span class="number">0</span>]])</span><br><span class="line">            </span><br><span class="line">            <span class="comment"># 提取HTTP状态码</span></span><br><span class="line">            status_match = status_pattern.search(line)</span><br><span class="line">            <span class="keyword">if</span> status_match:</span><br><span class="line">                results[<span class="string">'status_codes'</span>].update([status_match.group(<span class="number">1</span>)])</span><br><span class="line">            </span><br><span class="line">            <span class="comment"># 提取URL</span></span><br><span class="line">            url_match = url_pattern.search(line)</span><br><span class="line">            <span class="keyword">if</span> url_match:</span><br><span class="line">                results[<span class="string">'top_urls'</span>].update([url_match.group(<span class="number">1</span>)])</span><br><span class="line">            </span><br><span class="line">            <span class="comment"># 提取时间并按小时汇总</span></span><br><span class="line">            time_match = timestamp_pattern.search(line)</span><br><span class="line">            <span class="keyword">if</span> time_match:</span><br><span class="line">                date_str, hour = time_match.groups()</span><br><span class="line">                results[<span class="string">'by_hour'</span>].update([<span class="built_in">int</span>(hour)])</span><br><span class="line">            </span><br><span class="line">            <span class="comment"># 检查错误和警告</span></span><br><span class="line">            <span class="keyword">if</span> error_pattern.search(line):</span><br><span class="line">                results[<span class="string">'errors'</span>] += <span class="number">1</span></span><br><span class="line">            <span class="keyword">elif</span> warning_pattern.search(line):</span><br><span class="line">                results[<span class="string">'warnings'</span>] += <span class="number">1</span></span><br><span class="line">    </span><br><span class="line">    <span class="keyword">return</span> results</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">generate_log_report</span>(<span class="params">results, output_dir=<span class="literal">None</span></span>):</span><br><span class="line">    <span class="string">"""生成日志分析报告(文本和图表)"""</span></span><br><span class="line">    output_dir = Path(output_dir) <span class="keyword">if</span> output_dir <span class="keyword">else</span> Path.cwd()</span><br><span class="line">    <span class="keyword">if</span> <span class="keyword">not</span> output_dir.exists():</span><br><span class="line">        output_dir.mkdir(parents=<span class="literal">True</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 创建文本报告</span></span><br><span class="line">    report_path = output_dir / <span class="string">"log_analysis_report.txt"</span></span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(report_path, <span class="string">'w'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> f:</span><br><span class="line">        f.write(<span class="string">"=== 日志分析报告 ===\n"</span>)</span><br><span class="line">        f.write(<span class="string">f"总行数: <span class="subst">{results[<span class="string">'total_lines'</span>]}</span>\n"</span>)</span><br><span class="line">        f.write(<span class="string">f"匹配行数: <span class="subst">{results[<span class="string">'matched_lines'</span>]}</span>\n"</span>)</span><br><span class="line">        f.write(<span class="string">f"错误数: <span class="subst">{results[<span class="string">'errors'</span>]}</span>\n"</span>)</span><br><span class="line">        f.write(<span class="string">f"警告数: <span class="subst">{results[<span class="string">'warnings'</span>]}</span>\n\n"</span>)</span><br><span class="line">        </span><br><span class="line">        f.write(<span class="string">"=== 按小时分布 ===\n"</span>)</span><br><span class="line">        <span class="keyword">for</span> hour <span class="keyword">in</span> <span class="built_in">sorted</span>(results[<span class="string">'by_hour'</span>]):</span><br><span class="line">            f.write(<span class="string">f"<span class="subst">{hour}</span>时: <span class="subst">{results[<span class="string">'by_hour'</span>][hour]}</span>行\n"</span>)</span><br><span class="line">        </span><br><span class="line">        f.write(<span class="string">"\n=== 前10个IP地址 ===\n"</span>)</span><br><span class="line">        <span class="keyword">for</span> ip, count <span class="keyword">in</span> results[<span class="string">'ip_addresses'</span>].most_common(<span class="number">10</span>):</span><br><span class="line">            f.write(<span class="string">f"<span class="subst">{ip}</span>: <span class="subst">{count}</span>次\n"</span>)</span><br><span class="line">        </span><br><span class="line">        f.write(<span class="string">"\n=== HTTP状态码统计 ===\n"</span>)</span><br><span class="line">        <span class="keyword">for</span> status, count <span class="keyword">in</span> results[<span class="string">'status_codes'</span>].most_common():</span><br><span class="line">            f.write(<span class="string">f"<span class="subst">{status}</span>: <span class="subst">{count}</span>次\n"</span>)</span><br><span class="line">        </span><br><span class="line">        f.write(<span class="string">"\n=== 前10个URL ===\n"</span>)</span><br><span class="line">        <span class="keyword">for</span> url, count <span class="keyword">in</span> results[<span class="string">'top_urls'</span>].most_common(<span class="number">10</span>):</span><br><span class="line">            f.write(<span class="string">f"<span class="subst">{url}</span>: <span class="subst">{count}</span>次\n"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 生成图表报告</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 1. 按小时分布图</span></span><br><span class="line">    plt.figure(figsize=(<span class="number">10</span>, <span class="number">6</span>))</span><br><span class="line">    hours = <span class="built_in">range</span>(<span class="number">24</span>)</span><br><span class="line">    counts = [results[<span class="string">'by_hour'</span>].get(hour, <span class="number">0</span>) <span class="keyword">for</span> hour <span class="keyword">in</span> hours]</span><br><span class="line">    plt.bar(hours, counts)</span><br><span class="line">    plt.xlabel(<span class="string">'小时'</span>)</span><br><span class="line">    plt.ylabel(<span class="string">'日志条目数'</span>)</span><br><span class="line">    plt.title(<span class="string">'日志按小时分布'</span>)</span><br><span class="line">    plt.xticks(hours)</span><br><span class="line">    plt.grid(<span class="literal">True</span>, axis=<span class="string">'y'</span>, alpha=<span class="number">0.3</span>)</span><br><span class="line">    plt.savefig(output_dir / <span class="string">'hourly_distribution.png'</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 2. HTTP状态码分布饼图</span></span><br><span class="line">    plt.figure(figsize=(<span class="number">8</span>, <span class="number">8</span>))</span><br><span class="line">    status_codes = <span class="built_in">list</span>(results[<span class="string">'status_codes'</span>].keys())</span><br><span class="line">    counts = <span class="built_in">list</span>(results[<span class="string">'status_codes'</span>].values())</span><br><span class="line">    plt.pie(counts, labels=status_codes, autopct=<span class="string">'%1.1f%%'</span>, startangle=<span class="number">140</span>)</span><br><span class="line">    plt.axis(<span class="string">'equal'</span>)</span><br><span class="line">    plt.title(<span class="string">'HTTP状态码分布'</span>)</span><br><span class="line">    plt.savefig(output_dir / <span class="string">'status_codes_pie.png'</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 3. 前5个IP地址条形图</span></span><br><span class="line">    plt.figure(figsize=(<span class="number">10</span>, <span class="number">6</span>))</span><br><span class="line">    top_ips = results[<span class="string">'ip_addresses'</span>].most_common(<span class="number">5</span>)</span><br><span class="line">    ips = [ip <span class="keyword">for</span> ip, _ <span class="keyword">in</span> top_ips]</span><br><span class="line">    counts = [count <span class="keyword">for</span> _, count <span class="keyword">in</span> top_ips]</span><br><span class="line">    plt.barh(ips, counts)</span><br><span class="line">    plt.xlabel(<span class="string">'请求次数'</span>)</span><br><span class="line">    plt.ylabel(<span class="string">'IP地址'</span>)</span><br><span class="line">    plt.title(<span class="string">'前5个IP地址'</span>)</span><br><span class="line">    plt.grid(<span class="literal">True</span>, axis=<span class="string">'x'</span>, alpha=<span class="number">0.3</span>)</span><br><span class="line">    plt.tight_layout()</span><br><span class="line">    plt.savefig(output_dir / <span class="string">'top_ips.png'</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">return</span> report_path</span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用示例</span></span><br><span class="line"><span class="comment"># log_file = "access.log"</span></span><br><span class="line"><span class="comment"># results = analyze_log_file(log_file)</span></span><br><span class="line"><span class="comment"># report_path = generate_log_report(results, "reports")</span></span><br><span class="line"><span class="comment"># print(f"报告已生成: {report_path}")</span></span><br></pre></td></tr></tbody></table></figure><h4 id="7-9-3-文件同步工具"><a href="#7-9-3-文件同步工具" class="headerlink" title="7.9.3 文件同步工具"></a>7.9.3 文件同步工具</h4><figure class="highlight python"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br><span class="line">142</span><br><span class="line">143</span><br><span class="line">144</span><br><span class="line">145</span><br><span class="line">146</span><br><span class="line">147</span><br><span class="line">148</span><br><span class="line">149</span><br><span class="line">150</span><br><span class="line">151</span><br><span class="line">152</span><br><span class="line">153</span><br><span class="line">154</span><br><span class="line">155</span><br><span class="line">156</span><br><span class="line">157</span><br><span class="line">158</span><br><span class="line">159</span><br><span class="line">160</span><br><span class="line">161</span><br><span class="line">162</span><br><span class="line">163</span><br><span class="line">164</span><br><span class="line">165</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> os</span><br><span class="line"><span class="keyword">import</span> shutil</span><br><span class="line"><span class="keyword">import</span> hashlib</span><br><span class="line"><span class="keyword">from</span> pathlib <span class="keyword">import</span> Path</span><br><span class="line"><span class="keyword">import</span> time</span><br><span class="line"><span class="keyword">import</span> logging</span><br><span class="line"></span><br><span class="line"><span class="comment"># 配置日志</span></span><br><span class="line">logging.basicConfig(</span><br><span class="line">    level=logging.INFO,</span><br><span class="line">    <span class="built_in">format</span>=<span class="string">'%(asctime)s - %(levelname)s - %(message)s'</span>,</span><br><span class="line">    handlers=[</span><br><span class="line">        logging.FileHandler(<span class="string">"file_sync.log"</span>),</span><br><span class="line">        logging.StreamHandler()</span><br><span class="line">    ]</span><br><span class="line">)</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">calculate_file_hash</span>(<span class="params">filepath</span>):</span><br><span class="line">    <span class="string">"""计算文件的MD5哈希值"""</span></span><br><span class="line">    hash_md5 = hashlib.md5()</span><br><span class="line">    <span class="keyword">with</span> <span class="built_in">open</span>(filepath, <span class="string">"rb"</span>) <span class="keyword">as</span> f:</span><br><span class="line">        <span class="keyword">for</span> chunk <span class="keyword">in</span> <span class="built_in">iter</span>(<span class="keyword">lambda</span>: f.read(<span class="number">4096</span>), <span class="string">b""</span>):</span><br><span class="line">            hash_md5.update(chunk)</span><br><span class="line">    <span class="keyword">return</span> hash_md5.hexdigest()</span><br><span class="line"></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">sync_directories</span>(<span class="params">source_dir, target_dir, delete=<span class="literal">False</span>, exclude=<span class="literal">None</span></span>):</span><br><span class="line">    <span class="string">"""</span></span><br><span class="line"><span class="string">    同步两个目录的内容</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    参数:</span></span><br><span class="line"><span class="string">        source_dir: 源目录</span></span><br><span class="line"><span class="string">        target_dir: 目标目录</span></span><br><span class="line"><span class="string">        delete: 是否删除目标目录中源目录没有的文件</span></span><br><span class="line"><span class="string">        exclude: 要排除的文件/目录列表</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    返回:</span></span><br><span class="line"><span class="string">        操作统计信息</span></span><br><span class="line"><span class="string">    """</span></span><br><span class="line">    source_dir = Path(source_dir)</span><br><span class="line">    target_dir = Path(target_dir)</span><br><span class="line">    exclude = exclude <span class="keyword">or</span> []</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 确保目录存在</span></span><br><span class="line">    <span class="keyword">if</span> <span class="keyword">not</span> source_dir.exists():</span><br><span class="line">        <span class="keyword">raise</span> ValueError(<span class="string">f"源目录不存在: <span class="subst">{source_dir}</span>"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">if</span> <span class="keyword">not</span> target_dir.exists():</span><br><span class="line">        logging.info(<span class="string">f"创建目标目录: <span class="subst">{target_dir}</span>"</span>)</span><br><span class="line">        target_dir.mkdir(parents=<span class="literal">True</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 初始化统计</span></span><br><span class="line">    stats = {</span><br><span class="line">        <span class="string">"copied"</span>: <span class="number">0</span>,</span><br><span class="line">        <span class="string">"updated"</span>: <span class="number">0</span>,</span><br><span class="line">        <span class="string">"deleted"</span>: <span class="number">0</span>,</span><br><span class="line">        <span class="string">"skipped"</span>: <span class="number">0</span></span><br><span class="line">    }</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 获取源文件清单</span></span><br><span class="line">    source_files = {}</span><br><span class="line">    <span class="keyword">for</span> root, dirs, files <span class="keyword">in</span> os.walk(source_dir):</span><br><span class="line">        <span class="comment"># 从dirs中移除要排除的目录(修改原地)</span></span><br><span class="line">        dirs[:] = [d <span class="keyword">for</span> d <span class="keyword">in</span> dirs <span class="keyword">if</span> d <span class="keyword">not</span> <span class="keyword">in</span> exclude]</span><br><span class="line">        </span><br><span class="line">        <span class="keyword">for</span> file <span class="keyword">in</span> files:</span><br><span class="line">            <span class="keyword">if</span> file <span class="keyword">in</span> exclude:</span><br><span class="line">                <span class="keyword">continue</span></span><br><span class="line">                </span><br><span class="line">            file_path = Path(root) / file</span><br><span class="line">            rel_path = file_path.relative_to(source_dir)</span><br><span class="line">            source_files[rel_path] = file_path</span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 同步文件到目标目录</span></span><br><span class="line">    <span class="keyword">for</span> rel_path, source_path <span class="keyword">in</span> source_files.items():</span><br><span class="line">        target_path = target_dir / rel_path</span><br><span class="line">        </span><br><span class="line">        <span class="comment"># 确保目标目录存在</span></span><br><span class="line">        target_path.parent.mkdir(parents=<span class="literal">True</span>, exist_ok=<span class="literal">True</span>)</span><br><span class="line">        </span><br><span class="line">        <span class="comment"># 检查文件是否需要更新</span></span><br><span class="line">        <span class="keyword">if</span> <span class="keyword">not</span> target_path.exists():</span><br><span class="line">            logging.info(<span class="string">f"复制新文件: <span class="subst">{rel_path}</span>"</span>)</span><br><span class="line">            shutil.copy2(source_path, target_path)</span><br><span class="line">            stats[<span class="string">"copied"</span>] += <span class="number">1</span></span><br><span class="line">        <span class="keyword">else</span>:</span><br><span class="line">            <span class="comment"># 比较修改时间和哈希值</span></span><br><span class="line">            source_mtime = os.path.getmtime(source_path)</span><br><span class="line">            target_mtime = os.path.getmtime(target_path)</span><br><span class="line">            </span><br><span class="line">            <span class="keyword">if</span> <span class="built_in">abs</span>(source_mtime - target_mtime) &gt; <span class="number">1</span>:  <span class="comment"># 1秒容差</span></span><br><span class="line">                <span class="comment"># 进一步比较内容哈希</span></span><br><span class="line">                source_hash = calculate_file_hash(source_path)</span><br><span class="line">                target_hash = calculate_file_hash(target_path)</span><br><span class="line">                </span><br><span class="line">                <span class="keyword">if</span> source_hash != target_hash:</span><br><span class="line">                    logging.info(<span class="string">f"更新文件: <span class="subst">{rel_path}</span>"</span>)</span><br><span class="line">                    shutil.copy2(source_path, target_path)</span><br><span class="line">                    stats[<span class="string">"updated"</span>] += <span class="number">1</span></span><br><span class="line">                <span class="keyword">else</span>:</span><br><span class="line">                    stats[<span class="string">"skipped"</span>] += <span class="number">1</span></span><br><span class="line">            <span class="keyword">else</span>:</span><br><span class="line">                stats[<span class="string">"skipped"</span>] += <span class="number">1</span></span><br><span class="line">    </span><br><span class="line">    <span class="comment"># 处理需要删除的文件</span></span><br><span class="line">    <span class="keyword">if</span> delete:</span><br><span class="line">        <span class="keyword">for</span> root, dirs, files <span class="keyword">in</span> os.walk(target_dir):</span><br><span class="line">            <span class="keyword">for</span> file <span class="keyword">in</span> files:</span><br><span class="line">                file_path = Path(root) / file</span><br><span class="line">                rel_path = file_path.relative_to(target_dir)</span><br><span class="line">                </span><br><span class="line">                <span class="keyword">if</span> rel_path <span class="keyword">not</span> <span class="keyword">in</span> source_files <span class="keyword">and</span> file <span class="keyword">not</span> <span class="keyword">in</span> exclude:</span><br><span class="line">                    logging.info(<span class="string">f"删除多余文件: <span class="subst">{rel_path}</span>"</span>)</span><br><span class="line">                    file_path.unlink()</span><br><span class="line">                    stats[<span class="string">"deleted"</span>] += <span class="number">1</span></span><br><span class="line">        </span><br><span class="line">        <span class="comment"># 删除空目录(从下向上遍历)</span></span><br><span class="line">        <span class="keyword">for</span> root, dirs, files <span class="keyword">in</span> os.walk(target_dir, topdown=<span class="literal">False</span>):</span><br><span class="line">            <span class="keyword">for</span> dir_name <span class="keyword">in</span> dirs:</span><br><span class="line">                dir_path = Path(root) / dir_name</span><br><span class="line">                <span class="keyword">if</span> <span class="keyword">not</span> <span class="built_in">any</span>(dir_path.iterdir()):  <span class="comment"># 检查目录是否为空</span></span><br><span class="line">                    logging.info(<span class="string">f"删除空目录: <span class="subst">{dir_path.relative_to(target_dir)}</span>"</span>)</span><br><span class="line">                    dir_path.rmdir()</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">return</span> stats</span><br><span class="line"></span><br><span class="line"><span class="comment"># 定期同步功能</span></span><br><span class="line"><span class="keyword">def</span> <span class="title function_">periodic_sync</span>(<span class="params">source_dir, target_dir, interval=<span class="number">3600</span>, delete=<span class="literal">False</span>, exclude=<span class="literal">None</span></span>):</span><br><span class="line">    <span class="string">"""</span></span><br><span class="line"><span class="string">    定期同步两个目录</span></span><br><span class="line"><span class="string">    </span></span><br><span class="line"><span class="string">    参数:</span></span><br><span class="line"><span class="string">        source_dir: 源目录</span></span><br><span class="line"><span class="string">        target_dir: 目标目录</span></span><br><span class="line"><span class="string">        interval: 同步间隔(秒)</span></span><br><span class="line"><span class="string">        delete: 是否删除目标目录中多余文件</span></span><br><span class="line"><span class="string">        exclude: 要排除的文件/目录列表</span></span><br><span class="line"><span class="string">    """</span></span><br><span class="line">    logging.info(<span class="string">f"启动定期同步: 从 <span class="subst">{source_dir}</span> 到 <span class="subst">{target_dir}</span>, 间隔 <span class="subst">{interval}</span>秒"</span>)</span><br><span class="line">    </span><br><span class="line">    <span class="keyword">try</span>:</span><br><span class="line">        <span class="keyword">while</span> <span class="literal">True</span>:</span><br><span class="line">            logging.info(<span class="string">"开始同步..."</span>)</span><br><span class="line">            start_time = time.time()</span><br><span class="line">            </span><br><span class="line">            <span class="keyword">try</span>:</span><br><span class="line">                stats = sync_directories(source_dir, target_dir, delete, exclude)</span><br><span class="line">                logging.info(<span class="string">f"同步完成: 复制=<span class="subst">{stats[<span class="string">'copied'</span>]}</span>, 更新=<span class="subst">{stats[<span class="string">'updated'</span>]}</span>, "</span></span><br><span class="line">                            <span class="string">f"删除=<span class="subst">{stats[<span class="string">'deleted'</span>]}</span>, 跳过=<span class="subst">{stats[<span class="string">'skipped'</span>]}</span>"</span>)</span><br><span class="line">            <span class="keyword">except</span> Exception <span class="keyword">as</span> e:</span><br><span class="line">                logging.error(<span class="string">f"同步出错: <span class="subst">{<span class="built_in">str</span>(e)}</span>"</span>)</span><br><span class="line">            </span><br><span class="line">            <span class="comment"># 计算实际等待时间</span></span><br><span class="line">            elapsed = time.time() - start_time</span><br><span class="line">            wait_time = <span class="built_in">max</span>(<span class="number">0</span>, interval - elapsed)</span><br><span class="line">            </span><br><span class="line">            logging.info(<span class="string">f"等待<span class="subst">{wait_time:<span class="number">.1</span>f}</span>秒后进行下次同步..."</span>)</span><br><span class="line">            time.sleep(wait_time)</span><br><span class="line">            </span><br><span class="line">    <span class="keyword">except</span> KeyboardInterrupt:</span><br><span class="line">        logging.info(<span class="string">"同步服务已停止"</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment"># 使用示例</span></span><br><span class="line"><span class="comment"># sync_directories("/path/to/source", "/path/to/backup", delete=True, exclude=[".git", "node_modules"])</span></span><br><span class="line"><span class="comment"># 定期同步(每小时)</span></span><br><span class="line"><span class="comment"># periodic_sync("/path/to/source", "/path/to/backup", interval=3600, delete=True)</span></span><br></pre></td></tr></tbody></table></figure></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/37372.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/37372.html&quot;)">Python（八）：第七章： 文件操作</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/37372.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/37372.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>后端技术<span class="categoryesPageCount">42</span></a><a class="post-meta__box__categoryes" href="/categories/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF/Python/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Python<span class="categoryesPageCount">22</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>Python基础知识总汇<span class="tagsPageCount">22</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/45310.html"><img class="prev-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Python（七）：第六章：条件循环分支</div></div></a></div><div class="next-post pull-right"><a href="/posts/56572.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Python（九）：第八章： 函数知识总结</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/45310.html" title="Python（七）：第六章：条件循环分支"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（七）：第六章：条件循环分支</div></div></a></div><div><a href="/posts/8019.html" title="Python（三）：第二章：转义字符"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（三）：第二章：转义字符</div></div></a></div><div><a href="/posts/56572.html" title="Python（九）：第八章： 函数知识总结"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（九）：第八章： 函数知识总结</div></div></a></div><div><a href="/posts/55902.html" title="Python（二十一）：第二十章：Python 语法新特性总结"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（二十一）：第二十章：Python 语法新特性总结</div></div></a></div><div><a href="/posts/2501.html" title="Python（二）：第一章：字符串打印格式化与PyCharm模板变量"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-18</div><div class="title">Python（二）：第一章：字符串打印格式化与PyCharm模板变量</div></div></a></div><div><a href="/posts/43091.html" title="Python（二十二）：第二十一章：项目结构规范与最佳实践"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/13/546590.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-04-19</div><div class="title">Python（二十二）：第二十一章：项目结构规范与最佳实践</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"Python（八）：第七章： 文件操作",date:"2025-04-18 23:13:45",updated:"2025-07-13 22:13:01",tags:["Python基础知识总汇"],categories:["后端技术","Python"],content:'\n## 第七章： 文件操作\n\nPython 提供了强大而灵活的文件操作接口，从基础的读写能力到高级的路径操作和目录管理。本章将由浅入深地介绍 Python 文件操作的全面知识。\n\n### 7.1 文件打开模式\n\n文件操作的第一步是打开文件，Python 提供了多种打开模式以满足不同需求。\n\n| 模式 | 描述           | 文件不存在时   | 文件存在时 | 常见应用场景           |\n| ---- | -------------- | -------------- | ---------- | ---------------------- |\n| `r`  | 只读模式       | 报错           | 从头读取   | 读取配置文件、日志文件 |\n| `w`  | 只写模式       | 创建新文件     | 清空内容   | 生成报告、写入日志     |\n| `a`  | 追加模式       | 创建新文件     | 在末尾追加 | 日志记录、数据收集     |\n| `r+` | 读写模式       | 报错           | 可读可写   | 需要同时读写的场景     |\n| `w+` | 读写模式       | 创建新文件     | 清空内容   | 需要先写后读的场景     |\n| `a+` | 追加读写       | 创建新文件     | 追加且可读 | 数据分析、日志分析     |\n| `b`  | 二进制模式     | 与其他模式组合 | 处理二进制 | 图片、视频、压缩文件   |\n| `t`  | 文本模式(默认) | 与其他模式组合 | 处理文本   | 文本文件处理           |\n\n> **实用提示**：选择合适的文件模式可以避免数据丢失。例如，使用 `w` 模式时要格外小心，因为它会清空现有文件。当不确定时，使用 `a` 模式追加内容会更安全。\n\n### 7.2 基本文件操作\n\n#### 7.2.1 文件读取操作\n\n```python\n# 使用 with 语句自动处理文件关闭（推荐方式）\nwith open(\'example.txt\', mode=\'r\', encoding=\'utf-8\') as file:\n    # 方法1：一次性读取整个文件\n    content = file.read()  # 读取全部内容到内存\n    print(content)\n    \n    # 注意：read()后文件指针已经到达文件末尾\n    # 需要重新打开文件或使用seek(0)重置指针\n    file.seek(0)  # 重置文件指针到文件开头\n    \n    # 方法2：读取一行\n    line = file.readline()  # 读取第一行（包含换行符）\n    print(line)\n    \n    # 方法3：读取所有行到列表\n    file.seek(0)  # 重置文件指针\n    lines = file.readlines()  # 返回包含所有行的列表\n    print(lines)  # [\'第一行\\n\', \'第二行\\n\', ...]\n    \n    # 方法4：逐行读取（内存效率最高，推荐用于大文件）\n    file.seek(0)  # 重置文件指针\n    for line in file:  # 文件对象本身是可迭代的\n        # line包含换行符，通常需要移除\n        print(line.strip())  # 去除行首行尾的空白字符\n```\n\n#### 7.2.2 文件写入操作\n\n```python\n# 写入文件（覆盖模式）\nwith open(\'example.txt\', mode=\'w\', encoding=\'utf-8\') as file:\n    # 方法1：写入字符串\n    file.write(\'第一行内容\\n\')  # 注意需手动添加换行符\n    file.write(\'第二行内容\\n\')\n    \n    # 方法2：写入多行\n    lines = [\'第三行内容\\n\', \'第四行内容\\n\']\n    file.writelines(lines)  # 注意writelines不会自动添加换行符\n    \n# 追加内容到文件\nwith open(\'example.txt\', mode=\'a\', encoding=\'utf-8\') as file:\n    file.write(\'追加的内容\\n\')\n    \n# 读写模式示例\nwith open(\'example.txt\', mode=\'r+\', encoding=\'utf-8\') as file:\n    content = file.read(10)  # 读取前10个字符\n    file.write(\'插入的内容\')  # 在当前位置（第10个字符后）写入\n```\n\n#### 7.2.3 多文件操作\n\n```python\n# 同时操作多个文件（例如：文件复制）\nwith open(\'source.txt\', \'r\', encoding=\'utf-8\') as source, \\\n     open(\'destination.txt\', \'w\', encoding=\'utf-8\') as destination:\n    \n    # 按块读取和写入（适用于大文件）\n    chunk_size = 4096  # 4KB 的块大小\n    while True:\n        chunk = source.read(chunk_size)\n        if not chunk:  # 到达文件末尾\n            break\n        destination.write(chunk)\n        \n    # 或者简单地复制所有内容\n    # source.seek(0)  # 先重置到文件开头\n    # destination.write(source.read())\n    \n    # 或者逐行复制（适合文本处理）\n    # source.seek(0)\n    # for line in source:\n    #     # 可以在此添加行处理逻辑\n    #     destination.write(line)\n```\n\n#### 7.2.4 文件修改示例\n\n在实际应用中，我们经常需要读取、修改并写回文件：\n\n```python\n# 示例：为Markdown文件的所有标题增加一个#符号\nfile_name = \'document.md\'\noutput_file = \'document_modified.md\'\n\n# 读取并修改\nwith open(file_name, \'r\', encoding=\'utf-8\') as input_file:\n    lines = input_file.readlines()\n    \n    # 处理每一行\n    for i in range(len(lines)):\n        # 如果行以#开头（表示标题），则增加一个#\n        if lines[i].strip().startswith(\'#\'):\n            lines[i] = \'#\' + lines[i]\n\n# 将修改后的内容写入新文件\nwith open(output_file, \'w\', encoding=\'utf-8\') as output_file:\n    output_file.writelines(lines)\n    \nprint(f"文件已修改并保存为 {output_file}")\n```\n\n> **最佳实践**：对于文件修改操作，始终先写入临时文件，然后在确认写入成功后才替换原文件，这样可以防止文件损坏。\n\n### 7.3 文件指针控制\n\n文件指针（或文件位置）决定了读写操作的起始位置。掌握指针控制对于高级文件操作至关重要。\n\n#### 7.3.1 seek() 和 tell() 函数\n\n```python\nwith open(\'example.txt\', \'w+\', encoding=\'utf-8\') as file:\n    # 写入一些测试内容\n    file.write("Hello World! 你好世界！") # 英文部分 "Hello World " 占用 12 个字符，中文部分 "你好世界！" 占用 12 个字符，剩余的空格+符号占据了 4 个字符 总共 28 个字符\n    \n    # tell() 获取当前文件指针位置\n    position = file.tell()\n    print(f"写入内容后的位置: {position}")  # 写入内容后的位置: 28\n\n    # 将指针移回文件开头\n    file.seek(0)\n    print(f"回到文件开头位置: {file.tell()}")   # 回到文件开头位置: 0\n\n    # 读取全部内容\n    content = file.read()\n    print(f"文件全部内容: {content}") # 文件全部内容: Hello World! 你好世界！\n    print(f"读取后的位置: {file.tell()}")  # 读取后的位置: 28\n\n    # 再次回到文件开头\n    file.seek(0)\n    \n    # 读取前5个字符\n    print(f"前5个字符: {file.read(5)}") # 前5个字符: Hello\n    print(f"读取5个字符后的位置: {file.tell()}") # 读取5个字符后的位置: 5\n\n    # 回到文件开头\n    file.seek(0)\n    \n    # 一次性定位到第13个字符位置（从文件开头算起）\n    file.seek(13)\n    print(f"直接定位到第10个位置: {file.tell()}")\n    print(f"从第13个位置开始读取的内容: {file.read()}") # 从第13个位置开始读取的内容: 你好世界！\n```\n\n> **注意**：在文本模式下，由于字符编码原因，seek() 可能无法精确定位到任意字节位置。对于需要精确控制的场景，应使用二进制模式 (\'rb\', \'wb\' 等)。\n\n#### 7.3.2 实际应用场景\n\n```python\n# 场景：在大日志文件中读取最后100行\ndef tail(file_path, n=10):\n    """读取文件最后n行，类似于Unix的tail命令"""\n    with open(file_path, \'rb\') as f:\n        # 移动到文件末尾\n        f.seek(0, 2)\n        # 文件总大小\n        total_size = f.tell()\n        \n        # 初始化变量\n        block_size = 1024\n        block = -1\n        lines = []\n        \n        # 从文件末尾向前读取\n        while len(lines) < n and -block * block_size < total_size:\n            # 移动到倒数第block个块\n            position = max(0, total_size + block * block_size)\n            f.seek(position)\n            \n            # 读取数据块\n            data = f.read(min(block_size, total_size - position))\n            \n            # 处理可能被截断的行\n            if position > 0:\n                # 丢弃第一行不完整数据\n                data = data.split(b\'\\n\', 1)[1] if b\'\\n\' in data else b\'\'\n            \n            # 计算行数\n            lines_in_block = data.split(b\'\\n\')\n            \n            # 合并行\n            lines = lines_in_block + lines\n            block -= 1\n        \n        # 返回最后n行\n        return [line.decode(\'utf-8\') for line in lines[-n:]]\n\n# 使用示例\nlast_lines = tail(\'large_log_file.txt\', 100)\nfor line in last_lines:\n    print(line)\n```\n\n### 7.4 缓冲区管理\n\n理解缓冲区对于优化文件操作性能至关重要，特别是在处理大量小块写入时。\n\n#### 7.4.1 缓冲设置与刷新\n\n```python\n# 设置不同的缓冲策略\n# buffering=0: 无缓冲 (仅在二进制模式可用)\n# buffering=1: 行缓冲 (仅在文本模式可用)\n# buffering>1: 指定缓冲区大小(字节)\n# buffering=-1: 使用默认缓冲区大小\n\n# 无缓冲 (每次写入都直接写入磁盘)\nwith open(\'binary_file.bin\', \'wb\', buffering=0) as f:\n    f.write(b\'Data will be written immediately\')\n\n# 行缓冲 (遇到换行符时刷新)\nwith open(\'log.txt\', \'w\', buffering=1) as f:\n    f.write(\'This line will be buffered\\n\')  # 遇到\\n会刷新\n    f.write(\'Until a newline is encountered\')  # 保留在缓冲区\n\n# 指定缓冲区大小\nwith open(\'large_file.txt\', \'w\', buffering=4096) as f:\n    # 4KB缓冲区，适合频繁小写入\n    for i in range(10000):\n        f.write(f\'Line {i}\\n\')  # 数据会积累到4KB才写入磁盘\n\n# 手动刷新缓冲区\nwith open(\'important.txt\', \'w\') as f:\n    f.write(\'Critical data\')\n    f.flush()  # 立即刷新缓冲区，确保数据写入磁盘\n    os.fsync(f.fileno())  # 进一步确保数据写入物理存储设备\n```\n\n#### 7.4.2 缓冲区触发条件\n\n缓冲区会在以下条件下自动刷新：\n\n1. 缓冲区满时\n2. 文件关闭时（如 with 块结束）\n3. 调用 flush() 方法时\n4. 程序正常退出时\n5. 行缓冲模式下遇到换行符时\n\n> **性能提示**：对于大量小写操作，使用适当的缓冲区大小可以显著提高性能。但对于关键数据，应及时调用 flush() 确保数据安全。\n\n### 7.5 文件路径操作\n\n有效管理文件路径是文件操作的基础，Python 提供了两种主要方式：传统的 `os.path` 模块和现代的 `pathlib` 库。\n\n#### 7.5.1 使用 os.path 模块\n\n| 方法/属性                    | 描述                                           |\n| ---------------------------- | ---------------------------------------------- |\n| `os.getcwd()`                | 获取当前工作目录                               |\n| `os.chdir(path)`             | 改变当前工作目录                               |\n| `os.mkdir(name)`             | 创建目录                                       |\n| `os.makedirs(path)`          | 递归创建多级目录                               |\n| `os.rmdir(name)`             | 删除空目录                                     |\n| `os.remove(path)`            | 删除文件                                       |\n| `os.listdir(path)`           | 列出指定目录的文件和目录                       |\n| `os.rename(src, dst)`        | 重命名文件或目录                               |\n| `os.system(command)`         | 执行系统命令                                   |\n| `os.environ`                 | 环境变量字典                                   |\n| `os.path.exists(path)`       | 检查路径是否存在                               |\n| `os.path.isfile(path)`       | 检查路径是否为文件                             |\n| `os.path.isdir(path)`        | 检查路径是否为目录                             |\n| `os.path.join(path1, path2)` | 连接路径                                       |\n| `os.path.split(path)`        | 根据最后一个路径分隔符分割路径为(目录, 文件名) |\n| `os.path.splitext(path)`     | 根据最后一个点号分割路径为(文件名，拓展名)     |\n| `os.path.dirname(path)`      | 获取路径的目录部分                             |\n| `os.path.basename(path)`     | 获取路径的文件名部分                           |\n| `os.path.getsize(path)`      | 获取文件大小(字节)                             |\n\n```python\nimport os.path\nimport datetime\n\n# 获取当前脚本所在目录\ncurrent_dir = os.path.dirname(os.path.abspath(__file__))\n\n# 在当前目录下创建data.txt\ndata_path = os.path.join(current_dir, "data.txt")\n\n# 首先创建一个测试文件\nwith open(data_path, \'w\', encoding=\'utf-8\') as file:\n    file.write("这是测试内容！\\nHello World!")\n\n# 文件路径处理演示\nprint(f"=== 文件路径信息 ===")\ndirectory = os.path.dirname(data_path)\nfilename = os.path.basename(data_path)\nname, extension = os.path.splitext(filename)\nprint(f"目录: {directory}")\nprint(f"文件名: {filename}")\nprint(f"纯名称: {name}")\nprint(f"扩展名: {extension}")\n\nprint(f"\\n=== 路径检查 ===")\nexists = os.path.exists(data_path)\nis_file = os.path.isfile(data_path)\nis_dir = os.path.isdir(data_path)\nprint(f"文件是否存在: {exists}")\nprint(f"是否是文件: {is_file}")\nprint(f"是否是目录: {is_dir}")\n\nprint(f"\\n=== 文件信息 ===")\ntry:\n    size = os.path.getsize(data_path)\n    mod_time = os.path.getmtime(data_path) # 获取修改时间\n    create_time = os.path.getctime(data_path) # 获取创建时间\n    access_time = os.path.getatime(data_path) # 获取访问时间\n    \n    # 转换时间戳为可读时间\n    mod_datetime = datetime.datetime.fromtimestamp(mod_time)\n    create_datetime = datetime.datetime.fromtimestamp(create_time)\n    access_datetime = datetime.datetime.fromtimestamp(access_time)\n    \n    print(f"文件大小: {size} 字节")\n    print(f"创建时间: {create_datetime}")\n    print(f"修改时间: {mod_datetime}")\n    print(f"访问时间: {access_datetime}")\n    \n    print(f"\\n=== 文件内容 ===")\n    with open(data_path, \'r\', encoding=\'utf-8\') as file:\n        content = file.read()\n        print(f"文件内容:\\n{content}")\n        \nexcept OSError as e:\n    print(f"获取文件信息时发生错误: {e}")\n\n```\n\n#### 7.5.2 使用 pathlib 模块 (Python 3.4+)\n\n\n\n```python\nfrom pathlib import Path\nimport datetime\n\n# 获取当前脚本所在目录并创建data.txt的路径\ncurrent_file = Path(__file__)\ndata_path = current_file.parent / "data.txt"\n\n# 创建并写入测试文件\ndata_path.write_text("这是测试内容！\\nHello World!", encoding=\'utf-8\')\n\nprint("=== 基本路径信息 ===")\nprint(f"完整路径: {data_path}")\nprint(f"父目录: {data_path.parent}")\nprint(f"文件名: {data_path.name}")\nprint(f"纯名称: {data_path.stem}")\nprint(f"扩展名: {data_path.suffix}")\nprint(f"所有路径组件: {data_path.parts}")\n\nprint("\\n=== 路径解析 ===")\nprint(f"绝对路径: {data_path.absolute()}")\nprint(f"解析路径: {data_path.resolve()}")\ntry:\n    print(f"相对路径: {data_path.relative_to(Path.cwd())}")\nexcept ValueError:\n    print("无法计算相对路径（文件可能在不同驱动器或目录）")\n\nprint("\\n=== 文件状态检查 ===")\nprint(f"文件是否存在: {data_path.exists()}")\nprint(f"是否是文件: {data_path.is_file()}")\nprint(f"是否是目录: {data_path.is_dir()}")\nprint(f"是否是符号链接: {data_path.is_symlink()}")\n\nprint("\\n=== 文件信息 ===")\nif data_path.exists() and data_path.is_file():\n    stat = data_path.stat()\n    print(f"文件大小: {stat.st_size} 字节")\n    print(f"创建时间: {datetime.datetime.fromtimestamp(stat.st_ctime)}")\n    print(f"修改时间: {datetime.datetime.fromtimestamp(stat.st_mtime)}")\n    print(f"访问时间: {datetime.datetime.fromtimestamp(stat.st_atime)}")\n\nprint("\\n=== 文件内容 ===")\nprint(f"文件内容:\\n{data_path.read_text(encoding=\'utf-8\')}")\n\nprint("\\n=== 路径修改示例 ===")\nnew_name = data_path.with_name("newdata.txt")\nnew_ext = data_path.with_suffix(".csv")\nnew_stem = data_path.with_stem("newdata")\nprint(f"修改整个文件名: {new_name}")\nprint(f"修改扩展名: {new_ext}")\nprint(f"仅修改文件名(不含扩展名): {new_stem}")\n```\n\n| 功能         | os.path 方式                | pathlib 方式       | 推荐                       |\n| ------------ | --------------------------- | ------------------ | -------------------------- |\n| 路径连接     | `os.path.join(dir, file)`   | `Path(dir) / file` | pathlib 更直观             |\n| 获取目录     | `os.path.dirname(path)`     | `path.parent`      | pathlib 属性访问更清晰     |\n| 获取文件名   | `os.path.basename(path)`    | `path.name`        | pathlib 属性访问更清晰     |\n| 分离扩展名   | `os.path.splitext(path)[1]` | `path.suffix`      | pathlib 更简洁             |\n| 检查存在     | `os.path.exists(path)`      | `path.exists()`    | 两者类似，pathlib 面向对象 |\n| 获取绝对路径 | `os.path.abspath(path)`     | `path.absolute()`  | 两者相当                   |\n\n> **最佳实践**：在新项目中优先使用 pathlib，它提供了更现代、更直观的面向对象接口。在维护旧代码时可能需要继续使用 os.path。\n\n### 7.6 高级文件操作\n\n#### 7.6.1 二进制文件操作\n\n```python\n# 读取整个二进制文件\ndef read_binary_file(file_path):\n    """读取并返回整个二进制文件的内容"""\n    with open(file_path, "rb") as f:\n        return f.read()\n\n\n# 分块读取大型二进制文件\ndef process_large_binary_file(file_path, chunk_size=1024 * 1024):\n    """分块处理大型二进制文件，避免内存溢出"""\n    with open(file_path, "rb") as f:\n        while True:\n            chunk = f.read(chunk_size)  # 每次读取1MB\n            if not chunk:  # 到达文件末尾\n                break\n\n            # 处理当前数据块\n            process_chunk(chunk)  # 假设的处理函数\n\n\n# 处理数据块的函数\ndef process_chunk(chunk):\n    """处理二进制数据块的函数"""\n    # 这里可以根据需要实现具体的数据处理逻辑\n    # 例如：计算校验和、搜索特定字节模式、转换数据格式等\n    chunk_size = len(chunk)\n    print(f"处理数据块: {chunk_size} 字节")\n\n    # 示例：计算数据块的哈希值\n    import hashlib\n    chunk_hash = hashlib.md5(chunk).hexdigest()\n    print(f"数据块MD5哈希值: {chunk_hash}")\n\n    # 示例：检查数据块中的特定字节序列\n    if b\'\\x00\\x00\\xff\\xff\' in chunk:\n        print("在数据块中发现特定字节序列")\n\n    # 返回处理结果（可选）\n    return {\n        \'size\': chunk_size,\n        \'hash\': chunk_hash\n    }\n\n\n# 读取文件特定部分\ndef read_file_section(file_path, start, length):\n    """读取文件中从start位置开始的length个字节"""\n    with open(file_path, "rb") as f:\n        f.seek(start)  # 移动文件指针到指定位置\n        return f.read(length)  # 读取指定长度字节\n\n\n# 检测文件类型\ndef detect_file_type(file_path):\n    """通过文件头部特征识别文件类型"""\n    # 常见文件格式的魔数（Magic Numbers）\n    file_signatures = {\n        b\'\\x89PNG\\r\\n\\x1a\\n\': \'PNG image\',\n        b\'\\xff\\xd8\\xff\': \'JPEG image\',\n        b\'GIF87a\': \'GIF image (87a)\',\n        b\'GIF89a\': \'GIF image (89a)\',\n        b\'%PDF\': \'PDF document\',\n        b\'PK\\x03\\x04\': \'ZIP archive\',\n        b\'\\x50\\x4b\\x03\\x04\': \'ZIP archive\',  # PK..\n        b\'\\x1f\\x8b\\x08\': \'GZIP compressed file\',\n    }\n\n    with open(file_path, "rb") as f:\n        # 读取文件前16个字节用于检测\n        header = f.read(16)\n\n    for signature, file_type in file_signatures.items():\n        if header.startswith(signature):\n            return file_type\n\n    return "未知文件类型"\n\n\n# 实际演示：处理图像文件\ndef image_file_demo():\n    """演示二进制文件操作的实际应用"""\n    # 定义两个图形的基础文件路径\n    png_path = "example.png"\n    jpg_path = "example.jpg"\n\n    # 检测图像类型\n    print(f"检测文件类型: {png_path} 是 {detect_file_type(png_path)}") # 检测文件类型: example.png 是 JPEG image\n    print(f"检测文件类型: {jpg_path} 是 {detect_file_type(jpg_path)}") # 检测文件类型: example.jpg 是 JPEG image\n\n    # 读取PNG文件头部信息\n    png_header = read_file_section(png_path, 0, 24)\n    print(f"\\nPNG文件头部字节: {png_header.hex(\' \', 4)}")  # PNG文件头部字节: ffd8ffe0 00104a46 49460001 01000001 00010000 ffdb0043\n\n    # 获取文件大小\n    png_data = read_binary_file(png_path)\n    jpg_data = read_binary_file(jpg_path)\n\n    # 小技巧：在len函数中使用:, 即可以将数字以千分位分隔符展示\n    print(f"\\n{png_path} 文件大小: {len(png_data):,} 字节") # example.png 文件大小: 7,189 字节\n    print(f"{jpg_path} 文件大小: {len(jpg_data):,} 字节") # example.jpg 文件大小: 7,189 字节\n\n    # 处理大型二进制文件\n    process_large_binary_file(png_path)\n    process_large_binary_file(jpg_path)\n\nif __name__ == \'__main__\':\n    image_file_demo()\n```\n\n#### 7.6.2 临时文件操作\n\n临时文件对于需要中间处理结果但不想留下永久文件的操作非常有用。\n\n```python\nimport tempfile\nimport os\nimport time\nimport random\n\n\n# 示例2: 使用临时目录处理多个文件\ndef process_batch_files(data_items):\n    """在临时目录中创建多个文件并进行批处理"""\n    results = []\n\n    with tempfile.TemporaryDirectory(prefix="batch_") as temp_dir:\n        print(f"创建临时工作区 {temp_dir}")\n\n        # 创建多个数据文件\n        file_paths = []\n        for i, data in enumerate(data_items):\n            # 获取到临时目录下的文件路径\n            file_path = os.path.join(temp_dir, f"data_{i}.txt")\n            with open(file_path, "w", encoding="utf-8") as f:\n                f.write(data)\n            file_paths.append(file_path)\n\n        # 处理所有的文件\n        for file_path in file_paths:\n            with open(file_path, "r", encoding="utf-8") as f:\n                content = f.read()\n                # 模拟处理：计算字符数并添加到结果集中\n                results.append(f"文件{os.path.basename(file_path)} 包含 {len(content)} 个字符")\n        # 列出处理的文件\n        print(f"处理文件为{\',\'.join(os.path.basename(p) for p in file_paths)}")\n\n        # 退出with块后，tempfile会自动删除临时目录\n        # 暂停30秒，等待用户查看结果\n        print("处理开始，大约需要30秒，请稍候...")\n        time.sleep(random.randint(10, 30))\n        print("处理完成")\n\n    return results\n\n\n\n# 演示临时目录的批处理使用\ndata_to_process = [\n    "这是第一个文件的测试内容！",\n    "这是第二个文件包含更多的信息以及携带数字13123123123132",\n    "这是第三个文件包含中文，你好，世界！"]\n\nresults = process_batch_files(data_to_process)\nprint("\\n处理结果为:")\nfor r in results:\n    print(results)\n\n```\n\n### 7.7 目录操作\n\n目录操作是文件系统操作的重要组成部分，Python 提供了多种目录操作方法。\n\n#### 7.7.1 基本目录操作\n\n```python\nimport os\nimport shutil\n\n# 获取当前工作目录\ncurrent_dir = os.getcwd()\nprint(f"当前工作目录: {current_dir}")\n\n# 更改当前工作目录\nos.chdir("../")\nprint(f"更改后的工作目录: {os.getcwd()}")\n\n# 列出目录内容\nentries = os.listdir(".")\nprint(f"目录内容: {entries}")\n\n# 过滤目录内容\nfiles_only = [f for f in entries if os.path.isfile(f)]\ndirs_only = [d for d in entries if os.path.isdir(d)]\nprint(f"文件: {files_only}")\nprint(f"目录: {dirs_only}")\n\n# 创建目录\nos.chdir(current_dir) # 切换回原目录\nos.mkdir("new_dir") # 创建单个目录\nos.makedirs("new_dir2/sub_dir/sub_sub_dir",exist_ok=True) # 创建多级目录 (exist_ok=True 忽略已存在的目录)\n\n# 删除目录\nos.rmdir("new_dir") # 只能删除空目录\nshutil.rmtree("new_dir2") # 删除目录以及所有内容(谨慎使用！！！)\n```\n\n#### 7.7.2 使用 pathlib 进行目录操作\n\n```python\nfrom pathlib import Path\nimport shutil\n\n# 创建目录\nPath("new_dir").mkdir(exist_ok=True)\nPath("parent/child/grandchild").mkdir(parents=True, exist_ok=True) # 创建多层目录 parents=True 递归创建父目录\n\n# 列出目录内容\np = Path(".")\nfor item in p.iterdir():\n    if item.is_file():\n        print(f"文件: {item}，大小: {item.stat().st_size} bytes")\n    elif item.is_dir():\n        print(f"目录: {item}")\n\n# 过滤特定类型的文件\npython_files = list(p.glob("*.py")) # 列出当前目录下所有.py 文件\nall_python_files = list(p.rglob("*.py")) # 递归搜索所有子目录\n\nprint(f"当前目录下Python文件: {[f.name for f in python_files]}")\nprint(f"递归搜索所有Python文件: {[f.name for f in all_python_files]}")\nprint(f"所有Python文件: {len(all_python_files)}")\n\n\n# 删除目录\nPath(\'new_dir\').rmdir()  # 只能删除空目录\nshutil.rmtree(\'parent\') # 删除目录及其所有内容\n```\n\n| 操作                 | os/shutil 方法                     | pathlib 方法                                    | 优势比较                                   |\n| :------------------- | :--------------------------------- | :---------------------------------------------- | :----------------------------------------- |\n| **获取当前目录**     | `os.getcwd()`                      | `Path.cwd()`                                    | pathlib 返回 Path 对象，便于后续操作       |\n| **切换工作目录**     | `os.chdir(path)`                   | *(无直接等效方法)*                              | os 更适合改变全局工作目录                  |\n| **列出目录内容**     | `os.listdir(path)`                 | `Path(path).iterdir()`                          | pathlib 直接返回 Path 对象，无需再拼接路径 |\n| **创建单个目录**     | `os.mkdir(path)`                   | `Path(path).mkdir()`                            | 功能相同，pathlib 更面向对象               |\n| **创建多级目录**     | `os.makedirs(path, exist_ok=True)` | `Path(path).mkdir(parents=True, exist_ok=True)` | 语义更明确，参数名更具描述性               |\n| **删除空目录**       | `os.rmdir(path)`                   | `Path(path).rmdir()`                            | 功能相同，pathlib 更面向对象               |\n| **递归删除目录**     | `shutil.rmtree(path)`              | *(无直接等效方法，需循环删除)*                  | os/shutil 提供单一高效操作                 |\n| **文件路径拼接**     | `os.path.join(dir, file)`          | `Path(dir) / file`                              | pathlib 使用/运算符更直观简洁              |\n| **检查路径是否存在** | `os.path.exists(path)`             | `Path(path).exists()`                           | 功能相同，pathlib 更面向对象               |\n| **检查是否为文件**   | `os.path.isfile(path)`             | `Path(path).is_file()`                          | 功能相同，pathlib 更面向对象               |\n| **检查是否为目录**   | `os.path.isdir(path)`              | `Path(path).is_dir()`                           | 功能相同，pathlib 更面向对象               |\n\n#### 7.7.3 递归遍历目录树\n\n递归遍历是处理目录层次结构的强大工具，在很多需要列举文件目录树的场景都可以采用该思路去打印输出\n\n```python\n# 使用os.walk遍历目录树\nimport os\n#示例输出：\n# 学习用/\n#     data.txt - 0.04 KB\n#     example.jpg - 7.02 KB\n#     example.png - 7.02 KB\n#     example.py - 4.34 KB\n#     example.txt - 0.03 KB\n#     main.py - 4.54 KB\n#     requirements.txt - 0.01 KB\n#     study.py - 1.40 KB\n#     电子商务系统实现笔记.md - 24.60 KB\n#     (目录大小: 49.00 KB)\n\ndef scan_directory(directory):\n    """递归扫描目录，显示结构和文件大小"""\n    total_size = 0\n    for root, dirs, files in os.walk(directory):  # 其中root：当前目录路径，dirs：子目录列表，files：文件列表\n        # 计算当前的目录深度(用于缩进)\n        # 计算目录深度：\n        # 1. root.replace(directory, "") - 将当前路径中的起始目录部分替换为空字符串，得到相对路径\n        # 2. .count(os.sep) - 统计相对路径中目录分隔符(如\'/\'或\'\\\')的数量\n        # 每一个分隔符代表一层目录深度，因此分隔符的数量就等于目录的嵌套层级\n        level = root.replace(directory, "").count(os.sep)\n        indent = \' \' * 4 * level\n        # 打印当前目录\n        print(f"{indent}{os.path.basename(root)}/")\n\n        # 缩进子文件\n        sub_indent = \' \' * 4 * (level + 1)\n\n        # 统计当前目录下的文件大小\n        dir_size = 0\n        for file in files:\n            file_path = os.path.join(root, file)\n            file_size = os.path.getsize(file_path)\n            dir_size += file_size\n            print(f"{sub_indent}{file} - {file_size / 1024:.2f} KB")\n\n        # 累加总大小\n        total_size += dir_size\n        print(f"{sub_indent}(目录大小: {dir_size / 1024:.2f} KB)")\n\n    return total_size / 1024  # 返回总大小(单位：KB)\nif __name__ == \'__main__\':\n    total_size = scan_directory(\'D:/python/学习用\')\n    print(f"总大小: {total_size:.2f} KB")\n\n```\n\n#### 7.7.4 文件复制、移动和删除操作\n\n```python\nimport os\nimport shutil\n\n# ========== 文件复制操作 ==========\n# shutil.copy(src, dst): 复制文件到目标路径，不保留元数据（如文件的创建时间、修改时间等）\n# 参数说明：src - 源文件路径，dst - 目标路径（可以是目录或文件名）\n# 返回值：目标文件路径\nshutil.copy(\'source.txt\', \'destination.txt\')\n\n# shutil.copy2(src, dst): 复制文件到目标路径，保留所有元数据（如修改时间、访问时间、权限等）\n# 参数说明：同copy函数\n# 返回值：目标文件路径\nshutil.copy2(\'source.txt\', \'destination.txt\')\n\n# ========== 目录复制操作 ==========\n# shutil.copytree(src, dst): 递归复制整个目录树，目标目录不能已存在\n# 参数说明：src - 源目录，dst - 目标目录（必须不存在）\n# 返回值：目标目录路径\nshutil.copytree(\'source_dir\', \'destination_dir\')\n\n# shutil.copytree 高级用法：使用ignore参数忽略特定文件\n# shutil.ignore_patterns(): 创建一个忽略函数，用于过滤不需要复制的文件\n# 参数说明：可变参数，接受多个glob风格的模式字符串\nshutil.copytree(\'source_dir\', \'destination_dir\',\n                ignore=shutil.ignore_patterns(\'*.pyc\', \'*.git\'))\n\n# ========== 文件移动操作 ==========\n# shutil.move(src, dst): 移动文件或目录到目标路径\n# 参数说明：src - 源路径，dst - 目标路径\n# 返回值：目标路径\n# 用法1：重命名文件\nshutil.move(\'old_name.txt\', \'new_name.txt\')\n# 用法2：移动文件到其他目录\nshutil.move(\'file.txt\', \'directory/\')\n\n# ========== 文件删除操作 ==========\n# os.remove(path): 删除指定路径的文件（不能删除目录）\n# 参数说明：path - 要删除的文件路径\n# 注意：如果文件不存在会抛出FileNotFoundError异常\nos.remove(\'file.txt\')\n\n```\n\n### 7.8 文件监控与变更检测\n\n在某些应用场景中，需要监控文件变化并作出响应。\n\n#### 7.8.1 基础文件监控\n\n```python\nimport os\nimport time\n\n\ndef monitor_file(file_path, interval=1, encoding=\'utf-8\'):\n    """监控文件变化，并输出新增内容"""\n    if not os.path.exists(file_path):\n        print(f"文件 {file_path} 不存在")\n        return\n\n    # 获取初始状态\n    last_size = os.path.getsize(file_path) # 文件大小\n    last_modified = os.path.getmtime(file_path) # 最后修改时间戳\n    print(f"开始监控文件:{file_path}")\n    print(f"文件大小:{last_size} 最后修改时间:{time.ctime(last_modified)}")\n    try:\n        while True:\n            # 检查文件是否被修改\n            current_modified = os.path.getmtime(file_path)\n            current_size = os.path.getsize(file_path)\n            if current_modified != last_modified:\n                print(f"文件在{time.ctime(current_modified)}被修改")\n            # 如果文件增大了，读取新增内容\n            if current_size > last_size:\n                # 尝试不同的编码方式读取文件\n                encodings_to_try = [\'utf-8\', \'gbk\', \'gb2312\', \'gb18030\']\n                content_read = False\n                \n                for enc in encodings_to_try:\n                    try:\n                        with open(file_path, "r", encoding=enc) as f:\n                            f.seek(last_size) # 移动文件指针到上次读取位置\n                            new_content = f.read()\n                            print(f"新增内容:\\n{new_content}")\n                            # 更新当前使用的编码\n                            encoding = enc\n                            content_read = True\n                            break\n                    except UnicodeDecodeError:\n                        continue\n                \n                if not content_read:\n                    # 如果所有编码都失败，尝试以二进制方式读取并显示\n                    try:\n                        with open(file_path, "rb") as f:\n                            f.seek(last_size)\n                            binary_content = f.read()\n                            print(f"无法解码文件内容，显示二进制内容: {binary_content}")\n                    except Exception as e:\n                        print(f"读取文件失败: {e}")\n                        \n            elif current_size < last_size: # 文件缩小了，可能是被清空了\n                print("文件大小减小了，可能被截断或重写")\n\n            # 更新状态\n            last_modified = current_modified\n            last_size = current_size\n\n            time.sleep(interval) # 休眠一段时间再检查文件\n    except KeyboardInterrupt:\n        print("监控已停止")\n\n\n\nif __name__ == \'__main__\':\n    monitor_file("destination.txt", interval=1)\n\n```\n\n#### 7.8.2 使用 watchdog 库进行高级文件监控\n\n对于更复杂的文件系统监控需求，Python 的第三方库 `watchdog` 提供了更强大的功能：\n\n```python\nfrom watchdog.observers import Observer\nfrom watchdog.events import FileSystemEventHandler, DirCreatedEvent, FileCreatedEvent, DirDeletedEvent, \\\n    FileDeletedEvent, DirModifiedEvent, FileModifiedEvent, DirMovedEvent, FileMovedEvent\nimport time\nimport os\n\n\nclass MyHandler(FileSystemEventHandler):\n    def on_created(self, event: DirCreatedEvent | FileCreatedEvent) -> None:\n        if not event.is_directory:\n            print(f"文件被创建: {event.src_path}")\n\n    def on_deleted(self, event: DirDeletedEvent | FileDeletedEvent) -> None:\n        if not event.is_directory:\n            print(f"文件被删除: {event.src_path}")\n\n    def on_modified(self, event: DirModifiedEvent | FileModifiedEvent) -> None:\n        if not event.is_directory:\n            print(f"文件被修改: {event.src_path}")\n\n    def on_moved(self, event: DirMovedEvent | FileMovedEvent) -> None:\n        if not event.is_directory:\n            print(f"文件被移动: {event.src_path} -> {event.dest_path}")\n\n\ndef watch_directory(path: str) -> None:\n    # 确保监控的是目录而不是文件\n    if os.path.isfile(path):\n        # 如果是文件，则监控其所在的目录\n        directory = os.path.dirname(path)\n        if not directory:  # 如果是当前目录下的文件\n            directory = \'.\'\n    else:\n        directory = path\n    \n    event_handler = MyHandler()\n    observer = Observer()\n    observer.schedule(event_handler, directory, recursive=True)\n    observer.start()\n    try:\n        print(f"开始监控目录: {directory}")\n        print("按Ctrl+C停止监控...")\n        while True:\n            time.sleep(1)\n    except KeyboardInterrupt:\n        observer.stop()\n    observer.join()\n\n\nif __name__ == \'__main__\':\n    watch_directory(".")  # 监控当前目录，或者指定一个确实存在的目录路径\n\n```\n\n\n\n\n\n\n\n\n\n\n\n### 7.9 实际应用场景示例\n\n#### 7.9.1 文件备份工具\n\n```python\nimport os\nimport shutil\nimport datetime\nfrom pathlib import Path\nimport zipfile\n\ndef backup_directory(source_dir, backup_dir=None, zip_backup=True):\n    """\n    创建目录的备份\n    \n    参数:\n        source_dir: 要备份的源目录\n        backup_dir: 备份文件存放目录(默认在源目录的父目录)\n        zip_backup: 是否创建zip压缩备份\n    \n    返回:\n        备份文件的路径\n    """\n    # 确保源目录存在\n    source_path = Path(source_dir)\n    if not source_path.exists() or not source_path.is_dir():\n        raise ValueError(f"源目录 \'{source_dir}\' 不存在或不是一个目录")\n    \n    # 设置默认备份目录\n    if backup_dir is None:\n        backup_dir = source_path.parent\n    else:\n        backup_dir = Path(backup_dir)\n        if not backup_dir.exists():\n            backup_dir.mkdir(parents=True)\n    \n    # 创建备份文件名(包含时间戳)\n    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")\n    backup_name = f"{source_path.name}_backup_{timestamp}"\n    backup_path = backup_dir / backup_name\n    \n    if zip_backup:\n        # 创建ZIP备份\n        zip_path = str(backup_path) + \'.zip\'\n        print(f"创建ZIP备份: {zip_path}")\n        \n        with zipfile.ZipFile(zip_path, \'w\', zipfile.ZIP_DEFLATED) as zipf:\n            # 遍历源目录中的所有文件\n            for root, _, files in os.walk(source_dir):\n                for file in files:\n                    file_path = os.path.join(root, file)\n                    # 计算文件在ZIP中的相对路径\n                    rel_path = os.path.relpath(file_path, source_dir)\n                    print(f"添加: {rel_path}")\n                    zipf.write(file_path, rel_path)\n        \n        return zip_path\n    else:\n        # 创建目录备份(复制)\n        print(f"创建目录备份: {backup_path}")\n        shutil.copytree(source_path, backup_path)\n        return str(backup_path)\n\n# 使用示例\n# source = "/path/to/important_data"\n# backup = backup_directory(source, zip_backup=True)\n# print(f"备份已创建: {backup}")\n```\n\n#### 7.9.2 日志分析工具\n\n```python\nfrom collections import Counter\nimport re\nfrom datetime import datetime\nimport matplotlib.pyplot as plt\nfrom pathlib import Path\n\ndef analyze_log_file(log_path, pattern=None):\n    """\n    分析日志文件并生成报告\n    \n    参数:\n        log_path: 日志文件路径\n        pattern: 用于匹配日志行的正则表达式模式(默认为None，表示所有行)\n    \n    返回:\n        包含分析结果的字典\n    """\n    log_path = Path(log_path)\n    if not log_path.exists():\n        raise FileNotFoundError(f"日志文件不存在: {log_path}")\n    \n    # 初始化结果\n    results = {\n        \'total_lines\': 0,\n        \'matched_lines\': 0,\n        \'errors\': 0,\n        \'warnings\': 0,\n        \'by_hour\': Counter(),\n        \'ip_addresses\': Counter(),\n        \'status_codes\': Counter(),\n        \'top_urls\': Counter()\n    }\n    \n    # 编译正则表达式\n    if pattern:\n        regex = re.compile(pattern)\n    \n    # IP地址模式\n    ip_pattern = re.compile(r\'\\b(?:\\d{1,3}\\.){3}\\d{1,3}\\b\')\n    \n    # HTTP状态码模式\n    status_pattern = re.compile(r\'\\s(\\d{3})\\s\')\n    \n    # 时间戳模式(假设格式为[DD/Mon/YYYY:HH:MM:SS +ZZZZ])\n    timestamp_pattern = re.compile(r\'\\[(\\d{2}/\\w{3}/\\d{4}):(\\d{2}):\\d{2}:\\d{2}\\s[+\\-]\\d{4}\\]\')\n    \n    # URL模式\n    url_pattern = re.compile(r\'"(?:GET|POST|PUT|DELETE)\\s+([^\\s"]+)\')\n    \n    # 错误和警告模式\n    error_pattern = re.compile(r\'ERROR|CRITICAL|FATAL\', re.IGNORECASE)\n    warning_pattern = re.compile(r\'WARNING|WARN\', re.IGNORECASE)\n    \n    # 读取和分析日志文件\n    with open(log_path, \'r\', encoding=\'utf-8\', errors=\'ignore\') as f:\n        for line in f:\n            results[\'total_lines\'] += 1\n            \n            # 应用模式匹配过滤(如果提供)\n            if pattern and not regex.search(line):\n                continue\n            \n            results[\'matched_lines\'] += 1\n            \n            # 提取IP地址\n            ip_matches = ip_pattern.findall(line)\n            if ip_matches:\n                results[\'ip_addresses\'].update([ip_matches[0]])\n            \n            # 提取HTTP状态码\n            status_match = status_pattern.search(line)\n            if status_match:\n                results[\'status_codes\'].update([status_match.group(1)])\n            \n            # 提取URL\n            url_match = url_pattern.search(line)\n            if url_match:\n                results[\'top_urls\'].update([url_match.group(1)])\n            \n            # 提取时间并按小时汇总\n            time_match = timestamp_pattern.search(line)\n            if time_match:\n                date_str, hour = time_match.groups()\n                results[\'by_hour\'].update([int(hour)])\n            \n            # 检查错误和警告\n            if error_pattern.search(line):\n                results[\'errors\'] += 1\n            elif warning_pattern.search(line):\n                results[\'warnings\'] += 1\n    \n    return results\n\ndef generate_log_report(results, output_dir=None):\n    """生成日志分析报告(文本和图表)"""\n    output_dir = Path(output_dir) if output_dir else Path.cwd()\n    if not output_dir.exists():\n        output_dir.mkdir(parents=True)\n    \n    # 创建文本报告\n    report_path = output_dir / "log_analysis_report.txt"\n    with open(report_path, \'w\', encoding=\'utf-8\') as f:\n        f.write("=== 日志分析报告 ===\\n")\n        f.write(f"总行数: {results[\'total_lines\']}\\n")\n        f.write(f"匹配行数: {results[\'matched_lines\']}\\n")\n        f.write(f"错误数: {results[\'errors\']}\\n")\n        f.write(f"警告数: {results[\'warnings\']}\\n\\n")\n        \n        f.write("=== 按小时分布 ===\\n")\n        for hour in sorted(results[\'by_hour\']):\n            f.write(f"{hour}时: {results[\'by_hour\'][hour]}行\\n")\n        \n        f.write("\\n=== 前10个IP地址 ===\\n")\n        for ip, count in results[\'ip_addresses\'].most_common(10):\n            f.write(f"{ip}: {count}次\\n")\n        \n        f.write("\\n=== HTTP状态码统计 ===\\n")\n        for status, count in results[\'status_codes\'].most_common():\n            f.write(f"{status}: {count}次\\n")\n        \n        f.write("\\n=== 前10个URL ===\\n")\n        for url, count in results[\'top_urls\'].most_common(10):\n            f.write(f"{url}: {count}次\\n")\n    \n    # 生成图表报告\n    \n    # 1. 按小时分布图\n    plt.figure(figsize=(10, 6))\n    hours = range(24)\n    counts = [results[\'by_hour\'].get(hour, 0) for hour in hours]\n    plt.bar(hours, counts)\n    plt.xlabel(\'小时\')\n    plt.ylabel(\'日志条目数\')\n    plt.title(\'日志按小时分布\')\n    plt.xticks(hours)\n    plt.grid(True, axis=\'y\', alpha=0.3)\n    plt.savefig(output_dir / \'hourly_distribution.png\')\n    \n    # 2. HTTP状态码分布饼图\n    plt.figure(figsize=(8, 8))\n    status_codes = list(results[\'status_codes\'].keys())\n    counts = list(results[\'status_codes\'].values())\n    plt.pie(counts, labels=status_codes, autopct=\'%1.1f%%\', startangle=140)\n    plt.axis(\'equal\')\n    plt.title(\'HTTP状态码分布\')\n    plt.savefig(output_dir / \'status_codes_pie.png\')\n    \n    # 3. 前5个IP地址条形图\n    plt.figure(figsize=(10, 6))\n    top_ips = results[\'ip_addresses\'].most_common(5)\n    ips = [ip for ip, _ in top_ips]\n    counts = [count for _, count in top_ips]\n    plt.barh(ips, counts)\n    plt.xlabel(\'请求次数\')\n    plt.ylabel(\'IP地址\')\n    plt.title(\'前5个IP地址\')\n    plt.grid(True, axis=\'x\', alpha=0.3)\n    plt.tight_layout()\n    plt.savefig(output_dir / \'top_ips.png\')\n    \n    return report_path\n\n# 使用示例\n# log_file = "access.log"\n# results = analyze_log_file(log_file)\n# report_path = generate_log_report(results, "reports")\n# print(f"报告已生成: {report_path}")\n```\n\n#### 7.9.3 文件同步工具\n\n```python\nimport os\nimport shutil\nimport hashlib\nfrom pathlib import Path\nimport time\nimport logging\n\n# 配置日志\nlogging.basicConfig(\n    level=logging.INFO,\n    format=\'%(asctime)s - %(levelname)s - %(message)s\',\n    handlers=[\n        logging.FileHandler("file_sync.log"),\n        logging.StreamHandler()\n    ]\n)\n\ndef calculate_file_hash(filepath):\n    """计算文件的MD5哈希值"""\n    hash_md5 = hashlib.md5()\n    with open(filepath, "rb") as f:\n        for chunk in iter(lambda: f.read(4096), b""):\n            hash_md5.update(chunk)\n    return hash_md5.hexdigest()\n\ndef sync_directories(source_dir, target_dir, delete=False, exclude=None):\n    """\n    同步两个目录的内容\n    \n    参数:\n        source_dir: 源目录\n        target_dir: 目标目录\n        delete: 是否删除目标目录中源目录没有的文件\n        exclude: 要排除的文件/目录列表\n    \n    返回:\n        操作统计信息\n    """\n    source_dir = Path(source_dir)\n    target_dir = Path(target_dir)\n    exclude = exclude or []\n    \n    # 确保目录存在\n    if not source_dir.exists():\n        raise ValueError(f"源目录不存在: {source_dir}")\n    \n    if not target_dir.exists():\n        logging.info(f"创建目标目录: {target_dir}")\n        target_dir.mkdir(parents=True)\n    \n    # 初始化统计\n    stats = {\n        "copied": 0,\n        "updated": 0,\n        "deleted": 0,\n        "skipped": 0\n    }\n    \n    # 获取源文件清单\n    source_files = {}\n    for root, dirs, files in os.walk(source_dir):\n        # 从dirs中移除要排除的目录(修改原地)\n        dirs[:] = [d for d in dirs if d not in exclude]\n        \n        for file in files:\n            if file in exclude:\n                continue\n                \n            file_path = Path(root) / file\n            rel_path = file_path.relative_to(source_dir)\n            source_files[rel_path] = file_path\n    \n    # 同步文件到目标目录\n    for rel_path, source_path in source_files.items():\n        target_path = target_dir / rel_path\n        \n        # 确保目标目录存在\n        target_path.parent.mkdir(parents=True, exist_ok=True)\n        \n        # 检查文件是否需要更新\n        if not target_path.exists():\n            logging.info(f"复制新文件: {rel_path}")\n            shutil.copy2(source_path, target_path)\n            stats["copied"] += 1\n        else:\n            # 比较修改时间和哈希值\n            source_mtime = os.path.getmtime(source_path)\n            target_mtime = os.path.getmtime(target_path)\n            \n            if abs(source_mtime - target_mtime) > 1:  # 1秒容差\n                # 进一步比较内容哈希\n                source_hash = calculate_file_hash(source_path)\n                target_hash = calculate_file_hash(target_path)\n                \n                if source_hash != target_hash:\n                    logging.info(f"更新文件: {rel_path}")\n                    shutil.copy2(source_path, target_path)\n                    stats["updated"] += 1\n                else:\n                    stats["skipped"] += 1\n            else:\n                stats["skipped"] += 1\n    \n    # 处理需要删除的文件\n    if delete:\n        for root, dirs, files in os.walk(target_dir):\n            for file in files:\n                file_path = Path(root) / file\n                rel_path = file_path.relative_to(target_dir)\n                \n                if rel_path not in source_files and file not in exclude:\n                    logging.info(f"删除多余文件: {rel_path}")\n                    file_path.unlink()\n                    stats["deleted"] += 1\n        \n        # 删除空目录(从下向上遍历)\n        for root, dirs, files in os.walk(target_dir, topdown=False):\n            for dir_name in dirs:\n                dir_path = Path(root) / dir_name\n                if not any(dir_path.iterdir()):  # 检查目录是否为空\n                    logging.info(f"删除空目录: {dir_path.relative_to(target_dir)}")\n                    dir_path.rmdir()\n    \n    return stats\n\n# 定期同步功能\ndef periodic_sync(source_dir, target_dir, interval=3600, delete=False, exclude=None):\n    """\n    定期同步两个目录\n    \n    参数:\n        source_dir: 源目录\n        target_dir: 目标目录\n        interval: 同步间隔(秒)\n        delete: 是否删除目标目录中多余文件\n        exclude: 要排除的文件/目录列表\n    """\n    logging.info(f"启动定期同步: 从 {source_dir} 到 {target_dir}, 间隔 {interval}秒")\n    \n    try:\n        while True:\n            logging.info("开始同步...")\n            start_time = time.time()\n            \n            try:\n                stats = sync_directories(source_dir, target_dir, delete, exclude)\n                logging.info(f"同步完成: 复制={stats[\'copied\']}, 更新={stats[\'updated\']}, "\n                            f"删除={stats[\'deleted\']}, 跳过={stats[\'skipped\']}")\n            except Exception as e:\n                logging.error(f"同步出错: {str(e)}")\n            \n            # 计算实际等待时间\n            elapsed = time.time() - start_time\n            wait_time = max(0, interval - elapsed)\n            \n            logging.info(f"等待{wait_time:.1f}秒后进行下次同步...")\n            time.sleep(wait_time)\n            \n    except KeyboardInterrupt:\n        logging.info("同步服务已停止")\n\n# 使用示例\n# sync_directories("/path/to/source", "/path/to/backup", delete=True, exclude=[".git", "node_modules"])\n# 定期同步(每小时)\n# periodic_sync("/path/to/source", "/path/to/backup", interval=3600, delete=True)\n```'}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%B8%83%E7%AB%A0%EF%BC%9A-%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-number">1.</span> <span class="toc-text">第七章： 文件操作</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#7-1-%E6%96%87%E4%BB%B6%E6%89%93%E5%BC%80%E6%A8%A1%E5%BC%8F"><span class="toc-number">1.1.</span> <span class="toc-text">7.1 文件打开模式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-2-%E5%9F%BA%E6%9C%AC%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-number">1.2.</span> <span class="toc-text">7.2 基本文件操作</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-2-1-%E6%96%87%E4%BB%B6%E8%AF%BB%E5%8F%96%E6%93%8D%E4%BD%9C"><span class="toc-number">1.2.1.</span> <span class="toc-text">7.2.1 文件读取操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-2-2-%E6%96%87%E4%BB%B6%E5%86%99%E5%85%A5%E6%93%8D%E4%BD%9C"><span class="toc-number">1.2.2.</span> <span class="toc-text">7.2.2 文件写入操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-2-3-%E5%A4%9A%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-number">1.2.3.</span> <span class="toc-text">7.2.3 多文件操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-2-4-%E6%96%87%E4%BB%B6%E4%BF%AE%E6%94%B9%E7%A4%BA%E4%BE%8B"><span class="toc-number">1.2.4.</span> <span class="toc-text">7.2.4 文件修改示例</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-3-%E6%96%87%E4%BB%B6%E6%8C%87%E9%92%88%E6%8E%A7%E5%88%B6"><span class="toc-number">1.3.</span> <span class="toc-text">7.3 文件指针控制</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-3-1-seek-%E5%92%8C-tell-%E5%87%BD%E6%95%B0"><span class="toc-number">1.3.1.</span> <span class="toc-text">7.3.1 seek() 和 tell() 函数</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-3-2-%E5%AE%9E%E9%99%85%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF"><span class="toc-number">1.3.2.</span> <span class="toc-text">7.3.2 实际应用场景</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-4-%E7%BC%93%E5%86%B2%E5%8C%BA%E7%AE%A1%E7%90%86"><span class="toc-number">1.4.</span> <span class="toc-text">7.4 缓冲区管理</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-4-1-%E7%BC%93%E5%86%B2%E8%AE%BE%E7%BD%AE%E4%B8%8E%E5%88%B7%E6%96%B0"><span class="toc-number">1.4.1.</span> <span class="toc-text">7.4.1 缓冲设置与刷新</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-4-2-%E7%BC%93%E5%86%B2%E5%8C%BA%E8%A7%A6%E5%8F%91%E6%9D%A1%E4%BB%B6"><span class="toc-number">1.4.2.</span> <span class="toc-text">7.4.2 缓冲区触发条件</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-5-%E6%96%87%E4%BB%B6%E8%B7%AF%E5%BE%84%E6%93%8D%E4%BD%9C"><span class="toc-number">1.5.</span> <span class="toc-text">7.5 文件路径操作</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-5-1-%E4%BD%BF%E7%94%A8-os-path-%E6%A8%A1%E5%9D%97"><span class="toc-number">1.5.1.</span> <span class="toc-text">7.5.1 使用 os.path 模块</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-5-2-%E4%BD%BF%E7%94%A8-pathlib-%E6%A8%A1%E5%9D%97-Python-3-4"><span class="toc-number">1.5.2.</span> <span class="toc-text">7.5.2 使用 pathlib 模块 (Python 3.4+)</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-6-%E9%AB%98%E7%BA%A7%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-number">1.6.</span> <span class="toc-text">7.6 高级文件操作</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-6-1-%E4%BA%8C%E8%BF%9B%E5%88%B6%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-number">1.6.1.</span> <span class="toc-text">7.6.1 二进制文件操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-6-2-%E4%B8%B4%E6%97%B6%E6%96%87%E4%BB%B6%E6%93%8D%E4%BD%9C"><span class="toc-number">1.6.2.</span> <span class="toc-text">7.6.2 临时文件操作</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-7-%E7%9B%AE%E5%BD%95%E6%93%8D%E4%BD%9C"><span class="toc-number">1.7.</span> <span class="toc-text">7.7 目录操作</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-7-1-%E5%9F%BA%E6%9C%AC%E7%9B%AE%E5%BD%95%E6%93%8D%E4%BD%9C"><span class="toc-number">1.7.1.</span> <span class="toc-text">7.7.1 基本目录操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-7-2-%E4%BD%BF%E7%94%A8-pathlib-%E8%BF%9B%E8%A1%8C%E7%9B%AE%E5%BD%95%E6%93%8D%E4%BD%9C"><span class="toc-number">1.7.2.</span> <span class="toc-text">7.7.2 使用 pathlib 进行目录操作</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-7-3-%E9%80%92%E5%BD%92%E9%81%8D%E5%8E%86%E7%9B%AE%E5%BD%95%E6%A0%91"><span class="toc-number">1.7.3.</span> <span class="toc-text">7.7.3 递归遍历目录树</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-7-4-%E6%96%87%E4%BB%B6%E5%A4%8D%E5%88%B6%E3%80%81%E7%A7%BB%E5%8A%A8%E5%92%8C%E5%88%A0%E9%99%A4%E6%93%8D%E4%BD%9C"><span class="toc-number">1.7.4.</span> <span class="toc-text">7.7.4 文件复制、移动和删除操作</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-8-%E6%96%87%E4%BB%B6%E7%9B%91%E6%8E%A7%E4%B8%8E%E5%8F%98%E6%9B%B4%E6%A3%80%E6%B5%8B"><span class="toc-number">1.8.</span> <span class="toc-text">7.8 文件监控与变更检测</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-8-1-%E5%9F%BA%E7%A1%80%E6%96%87%E4%BB%B6%E7%9B%91%E6%8E%A7"><span class="toc-number">1.8.1.</span> <span class="toc-text">7.8.1 基础文件监控</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-8-2-%E4%BD%BF%E7%94%A8-watchdog-%E5%BA%93%E8%BF%9B%E8%A1%8C%E9%AB%98%E7%BA%A7%E6%96%87%E4%BB%B6%E7%9B%91%E6%8E%A7"><span class="toc-number">1.8.2.</span> <span class="toc-text">7.8.2 使用 watchdog 库进行高级文件监控</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#7-9-%E5%AE%9E%E9%99%85%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E7%A4%BA%E4%BE%8B"><span class="toc-number">1.9.</span> <span class="toc-text">7.9 实际应用场景示例</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#7-9-1-%E6%96%87%E4%BB%B6%E5%A4%87%E4%BB%BD%E5%B7%A5%E5%85%B7"><span class="toc-number">1.9.1.</span> <span class="toc-text">7.9.1 文件备份工具</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-9-2-%E6%97%A5%E5%BF%97%E5%88%86%E6%9E%90%E5%B7%A5%E5%85%B7"><span class="toc-number">1.9.2.</span> <span class="toc-text">7.9.2 日志分析工具</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#7-9-3-%E6%96%87%E4%BB%B6%E5%90%8C%E6%AD%A5%E5%B7%A5%E5%85%B7"><span class="toc-number">1.9.3.</span> <span class="toc-text">7.9.3 文件同步工具</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>