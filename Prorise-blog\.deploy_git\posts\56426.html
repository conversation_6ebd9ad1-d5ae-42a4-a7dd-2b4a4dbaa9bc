<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>23.内容拓展：代码运行器功能实现指南 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="博客搭建教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="23.内容拓展：代码运行器功能实现指南"><meta name="application-name" content="23.内容拓展：代码运行器功能实现指南"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="23.内容拓展：代码运行器功能实现指南"><meta property="og:url" content="https://prorise666.site/posts/56426.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="23.内容拓展：代码运行器功能实现指南本指南提供了在AnZhiYu主题中集成代码运行器功能的完整实现步骤，包括两级导航菜单、多服务商支持、响应式设计等功能。 步骤1：修改主题配置文件文件路径： _config.anzhiyu.yml 在配置文件中添加以下内容： 1234567891011121314"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp"><meta name="description" content="23.内容拓展：代码运行器功能实现指南本指南提供了在AnZhiYu主题中集成代码运行器功能的完整实现步骤，包括两级导航菜单、多服务商支持、响应式设计等功能。 步骤1：修改主题配置文件文件路径： _config.anzhiyu.yml 在配置文件中添加以下内容： 1234567891011121314"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/56426.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"23.内容拓展：代码运行器功能实现指南",postAI:"true",pageFillDescription:"23.内容拓展：代码运行器功能实现指南, 步骤1：修改主题配置文件, 步骤2：修改rightside.pug文件, 步骤3：修改layout.pug文件, 步骤4：修改config.pug文件, 步骤5：创建CSS样式文件, 步骤6：创建JavaScript功能文件, 完整代码文件, CSS样式文件内容, JavaScript功能文件内容内容拓展代码运行器功能实现指南本指南提供了在主题中集成代码运行器功能的完整实现步骤包括两级导航菜单多服务商支持响应式设计等功能步骤修改主题配置文件文件路径在配置文件中添加以下内容代码运行器配置是否启用代码运行器功能代码运行器面板标题代码运行器按钮提示文字面板宽度是否自动加载第一个实例是否支持键关闭是否记住用户选择服务商分类配置第一个分类适合等基础代码运行界面简洁易用在线编程环境前端三件套在线编辑器在线编程环境第二个分类支持种编程语言功能强大的在线编译器在线编译器在线编译器在线编程语言在线编程第三个分类前端开发者的在线代码编辑器和社区在线编辑器在线开发环境修改按钮配置右下角按钮顺序和显示控制是否启用自定义右下角按钮顺序要隐藏的按钮列表要显示的按钮列表添加修改配置其他现有配置代码运行器样式其他现有配置代码运行器功能脚本步骤修改文件文件路径在文件的语句中添加分支代码运行器步骤修改文件文件路径在之后添加面板结构代码运行器面板代码运行器关闭左侧导航菜单右侧内容区欢迎使用代码运行器请从左侧菜单选择一个编程环境开始编码正在加载编程环境步骤修改文件文件路径在对象中添加配置将此行添加到对象的其他配置项中步骤创建样式文件文件路径创建完整的文件内容较长见下一部分步骤创建功能文件文件路径创建完整的文件内容较长见下一部分完整代码文件样式文件内容文件代码运行器面板样式面板头部面板主体左侧导航实例列表右侧内容区全局状态响应式设计暗色模式适配按钮激活状态样式功能文件内容文件代码运行器功能基于的实现模式初始化函数支持初始化代码运行器功能设置初始状态设置按钮功能设置面板内部交互恢复用户选择设置初始状态确保面板初始状态为隐藏设置和加载指示器初始状态设置切换按钮功能打开面板延迟自动加载第一个实例如果配置允许延迟加载避免切换时立即加载导致失败延迟秒关闭面板设置面板内部交互关闭按钮分类展开收缩事件实例选择事件键关闭点击面板外部关闭切换分类展开收缩收缩所有其他分类切换当前分类选择实例更新选中状态加载加载显示加载指示器清除之前的事件监听器设置加载超时增加到秒超时加载完成处理加载错误处理如果重试次数少于次则重试延迟秒重试重试失败显示错误信息加载失败无法加载编程环境请检查网络连接或尝试其他选项刷新页面重试绑定事件延迟设置源避免过快加载自动加载第一个实例展开第一个分类选择第一个实例等待展开动画完成恢复用户选择查找并恢复分类查找并恢复实例防止重复初始化的标志页面加载完成时初始化为提供支持重置初始化标志因为可能已经改变延迟执行以确保完全加载增加延迟时间",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-19 19:25:44",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-3"><a class="toc-link" href="#23-%E5%86%85%E5%AE%B9%E6%8B%93%E5%B1%95%EF%BC%9A%E4%BB%A3%E7%A0%81%E8%BF%90%E8%A1%8C%E5%99%A8%E5%8A%9F%E8%83%BD%E5%AE%9E%E7%8E%B0%E6%8C%87%E5%8D%97"><span class="toc-text">23.内容拓展：代码运行器功能实现指南</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A41%EF%BC%9A%E4%BF%AE%E6%94%B9%E4%B8%BB%E9%A2%98%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6"><span class="toc-text">步骤1：修改主题配置文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A42%EF%BC%9A%E4%BF%AE%E6%94%B9rightside-pug%E6%96%87%E4%BB%B6"><span class="toc-text">步骤2：修改rightside.pug文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A43%EF%BC%9A%E4%BF%AE%E6%94%B9layout-pug%E6%96%87%E4%BB%B6"><span class="toc-text">步骤3：修改layout.pug文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A44%EF%BC%9A%E4%BF%AE%E6%94%B9config-pug%E6%96%87%E4%BB%B6"><span class="toc-text">步骤4：修改config.pug文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A45%EF%BC%9A%E5%88%9B%E5%BB%BACSS%E6%A0%B7%E5%BC%8F%E6%96%87%E4%BB%B6"><span class="toc-text">步骤5：创建CSS样式文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A46%EF%BC%9A%E5%88%9B%E5%BB%BAJavaScript%E5%8A%9F%E8%83%BD%E6%96%87%E4%BB%B6"><span class="toc-text">步骤6：创建JavaScript功能文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%AE%8C%E6%95%B4%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6"><span class="toc-text">完整代码文件</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#CSS%E6%A0%B7%E5%BC%8F%E6%96%87%E4%BB%B6%E5%86%85%E5%AE%B9"><span class="toc-text">CSS样式文件内容</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#JavaScript%E5%8A%9F%E8%83%BD%E6%96%87%E4%BB%B6%E5%86%85%E5%AE%B9"><span class="toc-text">JavaScript功能文件内容</span></a></li></ol></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a><i class="anzhiyufont anzhiyu-icon-angle-right post-meta-separator"></i><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/%E9%AD%94%E6%94%B9/" itemprop="url">魔改</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>博客搭建教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">23.内容拓展：代码运行器功能实现指南</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-11T07:13:45.000Z" title="发表于 2025-07-11 15:13:45">2025-07-11</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-19T11:25:44.300Z" title="更新于 2025-07-19 19:25:44">2025-07-19</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">3.5k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>18分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="23.内容拓展：代码运行器功能实现指南"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/56426.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/56426.html"><header><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/" itemprop="url">框架技术</a><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/" itemprop="url">Hexo</a><a class="post-meta-categories" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/%E9%AD%94%E6%94%B9/" itemprop="url">魔改</a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">博客搭建教程</a><h1 id="CrawlerTitle" itemprop="name headline">23.内容拓展：代码运行器功能实现指南</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-11T07:13:45.000Z" title="发表于 2025-07-11 15:13:45">2025-07-11</time><time itemprop="dateCreated datePublished" datetime="2025-07-19T11:25:44.300Z" title="更新于 2025-07-19 19:25:44">2025-07-19</time></header><div id="postchat_postcontent"><h3 id="23-内容拓展：代码运行器功能实现指南"><a href="#23-内容拓展：代码运行器功能实现指南" class="headerlink" title="23.内容拓展：代码运行器功能实现指南"></a>23.内容拓展：代码运行器功能实现指南</h3><p>本指南提供了在AnZhiYu主题中集成代码运行器功能的完整实现步骤，包括两级导航菜单、多服务商支持、响应式设计等功能。</p><h4 id="步骤1：修改主题配置文件"><a href="#步骤1：修改主题配置文件" class="headerlink" title="步骤1：修改主题配置文件"></a>步骤1：修改主题配置文件</h4><p><strong>文件路径：</strong> <code>_config.anzhiyu.yml</code></p><p>在配置文件中添加以下内容：</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 代码运行器配置</span></span><br><span class="line"><span class="attr">code_runner:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span>                    <span class="comment"># 是否启用代码运行器功能</span></span><br><span class="line">  <span class="attr">title:</span> <span class="string">"代码运行器"</span>             <span class="comment"># 面板标题</span></span><br><span class="line">  <span class="attr">button_title:</span> <span class="string">"代码运行器"</span>      <span class="comment"># 按钮提示文字</span></span><br><span class="line">  <span class="attr">panel_width:</span> <span class="string">"600px"</span>            <span class="comment"># 面板宽度</span></span><br><span class="line">  <span class="attr">auto_load_first:</span> <span class="literal">false</span>          <span class="comment"># 是否自动加载第一个实例</span></span><br><span class="line">  <span class="attr">close_on_escape:</span> <span class="literal">true</span>           <span class="comment"># 是否支持ESC键关闭</span></span><br><span class="line">  <span class="attr">remember_selection:</span> <span class="literal">true</span>        <span class="comment"># 是否记住用户选择</span></span><br><span class="line"></span><br><span class="line">  <span class="comment"># 服务商分类配置</span></span><br><span class="line">  <span class="attr">categories:</span></span><br><span class="line">    <span class="comment"># 第一个分类：Trinket</span></span><br><span class="line">    <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"Trinket"</span></span><br><span class="line">      <span class="attr">icon:</span> <span class="string">"fas fa-leaf"</span></span><br><span class="line">      <span class="attr">description:</span> <span class="string">"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"</span></span><br><span class="line">      <span class="attr">instances:</span></span><br><span class="line">        <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"Python 3"</span></span><br><span class="line">          <span class="attr">url:</span> <span class="string">"https://trinket.io/embed/python3/f417f7026885"</span></span><br><span class="line">          <span class="attr">description:</span> <span class="string">"Python 3 在线编程环境"</span></span><br><span class="line">        <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"HTML/CSS/JS"</span></span><br><span class="line">          <span class="attr">url:</span> <span class="string">"https://trinket.io/embed/html/1aac0e8640a7"</span></span><br><span class="line">          <span class="attr">description:</span> <span class="string">"前端三件套在线编辑器"</span></span><br><span class="line">        <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"Java"</span></span><br><span class="line">          <span class="attr">url:</span> <span class="string">"https://trinket.io/embed/java/33cfa8ec292c"</span></span><br><span class="line">          <span class="attr">description:</span> <span class="string">"Java 在线编程环境"</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 第二个分类：JDoodle</span></span><br><span class="line">    <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"JDoodle"</span></span><br><span class="line">      <span class="attr">icon:</span> <span class="string">"fas fa-terminal"</span></span><br><span class="line">      <span class="attr">description:</span> <span class="string">"支持70+种编程语言，功能强大的在线编译器"</span></span><br><span class="line">      <span class="attr">instances:</span></span><br><span class="line">        <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"C++ Compiler"</span></span><br><span class="line">          <span class="attr">url:</span> <span class="string">"https://www.jdoodle.com/online-compiler-c++/"</span></span><br><span class="line">          <span class="attr">description:</span> <span class="string">"C++ 在线编译器"</span></span><br><span class="line">        <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"Java Compiler"</span></span><br><span class="line">          <span class="attr">url:</span> <span class="string">"https://www.jdoodle.com/online-java-compiler/"</span></span><br><span class="line">          <span class="attr">description:</span> <span class="string">"Java 在线编译器"</span></span><br><span class="line">        <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"Python 3"</span></span><br><span class="line">          <span class="attr">url:</span> <span class="string">"https://www.jdoodle.com/python3-programming-online/"</span></span><br><span class="line">          <span class="attr">description:</span> <span class="string">"Python 3 在线编程"</span></span><br><span class="line">        <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"Go Playground"</span></span><br><span class="line">          <span class="attr">url:</span> <span class="string">"https://www.jdoodle.com/compile-go-online/"</span></span><br><span class="line">          <span class="attr">description:</span> <span class="string">"Go 语言在线编程"</span></span><br><span class="line"></span><br><span class="line">    <span class="comment"># 第三个分类：CodePen</span></span><br><span class="line">    <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"CodePen"</span></span><br><span class="line">      <span class="attr">icon:</span> <span class="string">"fab fa-codepen"</span></span><br><span class="line">      <span class="attr">description:</span> <span class="string">"前端开发者的在线代码编辑器和社区"</span></span><br><span class="line">      <span class="attr">instances:</span></span><br><span class="line">        <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"HTML/CSS/JS"</span></span><br><span class="line">          <span class="attr">url:</span> <span class="string">"https://codepen.io/pen/"</span></span><br><span class="line">          <span class="attr">description:</span> <span class="string">"CodePen 在线编辑器"</span></span><br><span class="line">        <span class="bullet">-</span> <span class="attr">name:</span> <span class="string">"React Playground"</span></span><br><span class="line">          <span class="attr">url:</span> <span class="string">"https://codepen.io/pen/?template=react"</span></span><br><span class="line">          <span class="attr">description:</span> <span class="string">"React 在线开发环境"</span></span><br></pre></td></tr></tbody></table></figure><p>修改rightside按钮配置：</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">rightside_item_order:</span> <span class="comment"># 右下角按钮顺序和显示控制</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span> <span class="comment"># 是否启用自定义右下角按钮顺序</span></span><br><span class="line">  <span class="attr">hide:</span> <span class="string">readmode,translate,darkmode,hideAside</span> <span class="comment"># 要隐藏的按钮列表</span></span><br><span class="line">  <span class="attr">show:</span> <span class="string">toc,chat,comment,downloadMd,docToc,codeRunner</span> <span class="comment"># 要显示的按钮列表 (添加codeRunner)</span></span><br></pre></td></tr></tbody></table></figure><p>修改inject配置：</p><figure class="highlight yaml"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">inject:</span></span><br><span class="line">  <span class="attr">head:</span></span><br><span class="line">    <span class="comment"># 其他现有配置...</span></span><br><span class="line">    <span class="comment"># 代码运行器样式</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">'&lt;link rel="stylesheet" href="/css/code-runner.css"&gt;'</span></span><br><span class="line"></span><br><span class="line">  <span class="attr">bottom:</span></span><br><span class="line">    <span class="comment"># 其他现有配置...</span></span><br><span class="line">    <span class="comment"># 代码运行器功能脚本</span></span><br><span class="line">    <span class="bullet">-</span> <span class="string">'&lt;script src="/js/code-runner.js"&gt;&lt;/script&gt;'</span></span><br></pre></td></tr></tbody></table></figure><h4 id="步骤2：修改rightside-pug文件"><a href="#步骤2：修改rightside-pug文件" class="headerlink" title="步骤2：修改rightside.pug文件"></a>步骤2：修改rightside.pug文件</h4><p><strong>文件路径：</strong> <code>themes/anzhiyu/layout/includes/rightside.pug</code></p><p>在rightside.pug文件的case语句中添加codeRunner分支：</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line">when 'codeRunner'</span><br><span class="line">  if theme.code_runner &amp;&amp; theme.code_runner.enable</span><br><span class="line">    button#code-runner-btn(type="button" title=theme.code_runner.button_title || "代码运行器")</span><br><span class="line">      i.fas.fa-code</span><br></pre></td></tr></tbody></table></figure><h4 id="步骤3：修改layout-pug文件"><a href="#步骤3：修改layout-pug文件" class="headerlink" title="步骤3：修改layout.pug文件"></a>步骤3：修改layout.pug文件</h4><p><strong>文件路径：</strong> <code>themes/anzhiyu/layout/includes/layout.pug</code></p><p>在rightside.pug之后添加面板HTML结构：</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br></pre></td><td class="code"><pre><span class="line">//- 代码运行器面板</span><br><span class="line">if theme.code_runner &amp;&amp; theme.code_runner.enable</span><br><span class="line">  #code-runner-panel.code-runner-panel</span><br><span class="line">    .panel-header</span><br><span class="line">      .panel-title= theme.code_runner.title || "代码运行器"</span><br><span class="line">      button.panel-close-btn(type="button" title="关闭")</span><br><span class="line">        i.fas.fa-times</span><br><span class="line">    </span><br><span class="line">    .panel-body</span><br><span class="line">      //- 左侧导航菜单</span><br><span class="line">      nav.panel-nav</span><br><span class="line">        each category in theme.code_runner.categories</span><br><span class="line">          .nav-category(data-category=category.name)</span><br><span class="line">            .category-header(</span><br><span class="line">              data-description=category.description</span><br><span class="line">              title=category.description</span><br><span class="line">            )</span><br><span class="line">              if category.icon</span><br><span class="line">                i(class=category.icon)</span><br><span class="line">              span.category-name= category.name</span><br><span class="line">              i.expand-icon.fas.fa-chevron-down</span><br><span class="line">            </span><br><span class="line">            ul.instance-list</span><br><span class="line">              each instance in category.instances</span><br><span class="line">                li.instance-item</span><br><span class="line">                  a.instance-link(</span><br><span class="line">                    href="javascript:void(0);"</span><br><span class="line">                    data-url=instance.url</span><br><span class="line">                    data-name=instance.name</span><br><span class="line">                    title=instance.description || instance.name</span><br><span class="line">                  )= instance.name</span><br><span class="line">      </span><br><span class="line">      //- 右侧内容区</span><br><span class="line">      .panel-content</span><br><span class="line">        .welcome-message</span><br><span class="line">          .welcome-icon</span><br><span class="line">            i.fas.fa-code</span><br><span class="line">          .welcome-text</span><br><span class="line">            h3 欢迎使用代码运行器</span><br><span class="line">            p 请从左侧菜单选择一个编程环境开始编码</span><br><span class="line">        </span><br><span class="line">        .iframe-container</span><br><span class="line">          iframe#code-runner-iframe(</span><br><span class="line">            frameborder="0"</span><br><span class="line">            width="100%"</span><br><span class="line">            height="100%"</span><br><span class="line">            sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"</span><br><span class="line">          )</span><br><span class="line">        </span><br><span class="line">        .loading-indicator</span><br><span class="line">          .loading-spinner</span><br><span class="line">            i.fas.fa-spinner.fa-spin</span><br><span class="line">          .loading-text 正在加载编程环境...</span><br></pre></td></tr></tbody></table></figure><h4 id="步骤4：修改config-pug文件"><a href="#步骤4：修改config-pug文件" class="headerlink" title="步骤4：修改config.pug文件"></a>步骤4：修改config.pug文件</h4><p><strong>文件路径：</strong> <code>themes/anzhiyu/layout/includes/head/config.pug</code></p><p>在GLOBAL_CONFIG对象中添加code_runner配置：</p><figure class="highlight plaintext"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">code_runner: !{theme.code_runner ? JSON.stringify(theme.code_runner) : 'null'}</span><br></pre></td></tr></tbody></table></figure><p>将此行添加到GLOBAL_CONFIG对象的其他配置项中。</p><h4 id="步骤5：创建CSS样式文件"><a href="#步骤5：创建CSS样式文件" class="headerlink" title="步骤5：创建CSS样式文件"></a>步骤5：创建CSS样式文件</h4><p><strong>文件路径：</strong> <code>themes/anzhiyu/source/css/code-runner.css</code></p><p>创建完整的CSS文件（内容较长，见下一部分）。</p><h4 id="步骤6：创建JavaScript功能文件"><a href="#步骤6：创建JavaScript功能文件" class="headerlink" title="步骤6：创建JavaScript功能文件"></a>步骤6：创建JavaScript功能文件</h4><p><strong>文件路径：</strong> <code>themes/anzhiyu/source/js/code-runner.js</code></p><p>创建完整的JavaScript文件（内容较长，见下一部分）。</p><h4 id="完整代码文件"><a href="#完整代码文件" class="headerlink" title="完整代码文件"></a>完整代码文件</h4><h5 id="CSS样式文件内容"><a href="#CSS样式文件内容" class="headerlink" title="CSS样式文件内容"></a>CSS样式文件内容</h5><p><strong>文件：</strong> <code>themes/anzhiyu/source/css/code-runner.css</code></p><figure class="highlight css"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br><span class="line">142</span><br><span class="line">143</span><br><span class="line">144</span><br><span class="line">145</span><br><span class="line">146</span><br><span class="line">147</span><br><span class="line">148</span><br><span class="line">149</span><br><span class="line">150</span><br><span class="line">151</span><br><span class="line">152</span><br><span class="line">153</span><br><span class="line">154</span><br><span class="line">155</span><br><span class="line">156</span><br><span class="line">157</span><br><span class="line">158</span><br><span class="line">159</span><br><span class="line">160</span><br><span class="line">161</span><br><span class="line">162</span><br><span class="line">163</span><br><span class="line">164</span><br><span class="line">165</span><br><span class="line">166</span><br><span class="line">167</span><br><span class="line">168</span><br><span class="line">169</span><br><span class="line">170</span><br><span class="line">171</span><br><span class="line">172</span><br><span class="line">173</span><br><span class="line">174</span><br><span class="line">175</span><br><span class="line">176</span><br><span class="line">177</span><br><span class="line">178</span><br><span class="line">179</span><br><span class="line">180</span><br><span class="line">181</span><br><span class="line">182</span><br><span class="line">183</span><br><span class="line">184</span><br><span class="line">185</span><br><span class="line">186</span><br><span class="line">187</span><br><span class="line">188</span><br><span class="line">189</span><br><span class="line">190</span><br><span class="line">191</span><br><span class="line">192</span><br><span class="line">193</span><br><span class="line">194</span><br><span class="line">195</span><br><span class="line">196</span><br><span class="line">197</span><br><span class="line">198</span><br><span class="line">199</span><br><span class="line">200</span><br><span class="line">201</span><br><span class="line">202</span><br><span class="line">203</span><br><span class="line">204</span><br><span class="line">205</span><br><span class="line">206</span><br><span class="line">207</span><br><span class="line">208</span><br><span class="line">209</span><br><span class="line">210</span><br><span class="line">211</span><br><span class="line">212</span><br><span class="line">213</span><br><span class="line">214</span><br><span class="line">215</span><br><span class="line">216</span><br><span class="line">217</span><br><span class="line">218</span><br><span class="line">219</span><br><span class="line">220</span><br><span class="line">221</span><br><span class="line">222</span><br><span class="line">223</span><br><span class="line">224</span><br><span class="line">225</span><br><span class="line">226</span><br><span class="line">227</span><br><span class="line">228</span><br><span class="line">229</span><br><span class="line">230</span><br><span class="line">231</span><br><span class="line">232</span><br><span class="line">233</span><br><span class="line">234</span><br><span class="line">235</span><br><span class="line">236</span><br><span class="line">237</span><br><span class="line">238</span><br><span class="line">239</span><br><span class="line">240</span><br><span class="line">241</span><br><span class="line">242</span><br><span class="line">243</span><br><span class="line">244</span><br><span class="line">245</span><br><span class="line">246</span><br><span class="line">247</span><br><span class="line">248</span><br><span class="line">249</span><br><span class="line">250</span><br><span class="line">251</span><br><span class="line">252</span><br><span class="line">253</span><br><span class="line">254</span><br><span class="line">255</span><br><span class="line">256</span><br><span class="line">257</span><br><span class="line">258</span><br><span class="line">259</span><br><span class="line">260</span><br><span class="line">261</span><br><span class="line">262</span><br><span class="line">263</span><br><span class="line">264</span><br><span class="line">265</span><br><span class="line">266</span><br><span class="line">267</span><br><span class="line">268</span><br><span class="line">269</span><br><span class="line">270</span><br><span class="line">271</span><br><span class="line">272</span><br><span class="line">273</span><br><span class="line">274</span><br><span class="line">275</span><br><span class="line">276</span><br><span class="line">277</span><br><span class="line">278</span><br><span class="line">279</span><br><span class="line">280</span><br><span class="line">281</span><br><span class="line">282</span><br><span class="line">283</span><br><span class="line">284</span><br><span class="line">285</span><br><span class="line">286</span><br><span class="line">287</span><br><span class="line">288</span><br><span class="line">289</span><br><span class="line">290</span><br><span class="line">291</span><br><span class="line">292</span><br><span class="line">293</span><br><span class="line">294</span><br><span class="line">295</span><br><span class="line">296</span><br><span class="line">297</span><br><span class="line">298</span><br><span class="line">299</span><br><span class="line">300</span><br><span class="line">301</span><br><span class="line">302</span><br><span class="line">303</span><br><span class="line">304</span><br><span class="line">305</span><br><span class="line">306</span><br><span class="line">307</span><br><span class="line">308</span><br><span class="line">309</span><br><span class="line">310</span><br><span class="line">311</span><br><span class="line">312</span><br><span class="line">313</span><br><span class="line">314</span><br><span class="line">315</span><br><span class="line">316</span><br><span class="line">317</span><br><span class="line">318</span><br><span class="line">319</span><br><span class="line">320</span><br><span class="line">321</span><br><span class="line">322</span><br><span class="line">323</span><br><span class="line">324</span><br><span class="line">325</span><br><span class="line">326</span><br><span class="line">327</span><br><span class="line">328</span><br><span class="line">329</span><br><span class="line">330</span><br><span class="line">331</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">/* 代码运行器面板样式 */</span></span><br><span class="line"><span class="selector-id">#code-runner-panel</span> {</span><br><span class="line">  <span class="attribute">position</span>: fixed;</span><br><span class="line">  <span class="attribute">top</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">right</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">width</span>: <span class="number">600px</span>;</span><br><span class="line">  <span class="attribute">height</span>: <span class="number">100vh</span>;</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-card-bg);</span><br><span class="line">  <span class="attribute">backdrop-filter</span>: <span class="built_in">blur</span>(<span class="number">20px</span>);</span><br><span class="line">  <span class="attribute">border-left</span>: <span class="built_in">var</span>(--style-border-always);</span><br><span class="line">  <span class="attribute">box-shadow</span>: <span class="built_in">var</span>(--anzhiyu-shadow-lightblack);</span><br><span class="line">  <span class="attribute">z-index</span>: <span class="number">1001</span>;</span><br><span class="line">  <span class="attribute">transform</span>: <span class="built_in">translateX</span>(<span class="number">100%</span>);</span><br><span class="line">  <span class="attribute">transition</span>: transform <span class="number">0.3s</span> <span class="built_in">cubic-bezier</span>(<span class="number">0.4</span>, <span class="number">0</span>, <span class="number">0.2</span>, <span class="number">1</span>);</span><br><span class="line">  <span class="attribute">display</span>: flex;</span><br><span class="line">  <span class="attribute">flex-direction</span>: column;</span><br><span class="line">  <span class="attribute">overflow</span>: hidden;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-id">#code-runner-panel</span><span class="selector-class">.active</span> {</span><br><span class="line">  <span class="attribute">transform</span>: <span class="built_in">translateX</span>(<span class="number">0</span>);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 面板头部 */</span></span><br><span class="line"><span class="selector-class">.panel-header</span> {</span><br><span class="line">  <span class="attribute">display</span>: flex;</span><br><span class="line">  <span class="attribute">align-items</span>: center;</span><br><span class="line">  <span class="attribute">justify-content</span>: space-between;</span><br><span class="line">  <span class="attribute">padding</span>: <span class="number">1rem</span> <span class="number">1.5rem</span>;</span><br><span class="line">  <span class="attribute">border-bottom</span>: <span class="built_in">var</span>(--style-border-always);</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-card-bg);</span><br><span class="line">  <span class="attribute">backdrop-filter</span>: <span class="built_in">blur</span>(<span class="number">20px</span>);</span><br><span class="line">  <span class="attribute">position</span>: relative;</span><br><span class="line">  <span class="attribute">z-index</span>: <span class="number">1</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.panel-title</span> {</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">1.1rem</span>;</span><br><span class="line">  <span class="attribute">font-weight</span>: <span class="number">600</span>;</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-fontcolor);</span><br><span class="line">  <span class="attribute">margin</span>: <span class="number">0</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.panel-close-btn</span> {</span><br><span class="line">  <span class="attribute">width</span>: <span class="number">2rem</span>;</span><br><span class="line">  <span class="attribute">height</span>: <span class="number">2rem</span>;</span><br><span class="line">  <span class="attribute">border</span>: none;</span><br><span class="line">  <span class="attribute">background</span>: transparent;</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-fontcolor);</span><br><span class="line">  <span class="attribute">cursor</span>: pointer;</span><br><span class="line">  <span class="attribute">border-radius</span>: <span class="built_in">var</span>(--anzhiyu-border-radius);</span><br><span class="line">  <span class="attribute">display</span>: flex;</span><br><span class="line">  <span class="attribute">align-items</span>: center;</span><br><span class="line">  <span class="attribute">justify-content</span>: center;</span><br><span class="line">  <span class="attribute">transition</span>: all <span class="number">0.3s</span> ease;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.panel-close-btn</span><span class="selector-pseudo">:hover</span> {</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-secondbg);</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-red);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 面板主体 */</span></span><br><span class="line"><span class="selector-class">.panel-body</span> {</span><br><span class="line">  <span class="attribute">flex</span>: <span class="number">1</span>;</span><br><span class="line">  <span class="attribute">display</span>: flex;</span><br><span class="line">  <span class="attribute">overflow</span>: hidden;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 左侧导航 */</span></span><br><span class="line"><span class="selector-class">.panel-nav</span> {</span><br><span class="line">  <span class="attribute">width</span>: <span class="number">200px</span>;</span><br><span class="line">  <span class="attribute">flex-shrink</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">border-right</span>: <span class="built_in">var</span>(--style-border-always);</span><br><span class="line">  <span class="attribute">padding</span>: <span class="number">1rem</span>;</span><br><span class="line">  <span class="attribute">overflow-y</span>: auto;</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-card-bg);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.nav-category</span> {</span><br><span class="line">  <span class="attribute">margin-bottom</span>: <span class="number">0.5rem</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.category-header</span> {</span><br><span class="line">  <span class="attribute">display</span>: flex;</span><br><span class="line">  <span class="attribute">align-items</span>: center;</span><br><span class="line">  <span class="attribute">padding</span>: <span class="number">0.75rem</span>;</span><br><span class="line">  <span class="attribute">cursor</span>: pointer;</span><br><span class="line">  <span class="attribute">border-radius</span>: <span class="built_in">var</span>(--anzhiyu-border-radius);</span><br><span class="line">  <span class="attribute">transition</span>: all <span class="number">0.3s</span> ease;</span><br><span class="line">  <span class="attribute">position</span>: relative;</span><br><span class="line">  <span class="attribute">user-select</span>: none;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.category-header</span><span class="selector-pseudo">:hover</span> {</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-secondbg);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.category-header</span> <span class="selector-tag">i</span><span class="selector-pseudo">:first-child</span> {</span><br><span class="line">  <span class="attribute">margin-right</span>: <span class="number">0.5rem</span>;</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.category-name</span> {</span><br><span class="line">  <span class="attribute">flex</span>: <span class="number">1</span>;</span><br><span class="line">  <span class="attribute">font-weight</span>: <span class="number">500</span>;</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-fontcolor);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.expand-icon</span> {</span><br><span class="line">  <span class="attribute">margin-left</span>: <span class="number">0.5rem</span>;</span><br><span class="line">  <span class="attribute">transition</span>: transform <span class="number">0.3s</span> ease;</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-fontcolor);</span><br><span class="line">  <span class="attribute">opacity</span>: <span class="number">0.7</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.nav-category</span><span class="selector-class">.expanded</span> <span class="selector-class">.expand-icon</span> {</span><br><span class="line">  <span class="attribute">transform</span>: <span class="built_in">rotate</span>(<span class="number">180deg</span>);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 实例列表 */</span></span><br><span class="line"><span class="selector-class">.instance-list</span> {</span><br><span class="line">  <span class="attribute">list-style</span>: none;</span><br><span class="line">  <span class="attribute">padding</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">margin</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">max-height</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">overflow</span>: hidden;</span><br><span class="line">  <span class="attribute">transition</span>: max-height <span class="number">0.3s</span> ease;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.nav-category</span><span class="selector-class">.expanded</span> <span class="selector-class">.instance-list</span> {</span><br><span class="line">  <span class="attribute">max-height</span>: <span class="number">300px</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.instance-item</span> {</span><br><span class="line">  <span class="attribute">margin</span>: <span class="number">0</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.instance-link</span> {</span><br><span class="line">  <span class="attribute">display</span>: block;</span><br><span class="line">  <span class="attribute">padding</span>: <span class="number">0.5rem</span> <span class="number">0.75rem</span>;</span><br><span class="line">  <span class="attribute">margin-left</span>: <span class="number">1.5rem</span>;</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-fontcolor);</span><br><span class="line">  <span class="attribute">text-decoration</span>: none;</span><br><span class="line">  <span class="attribute">border-radius</span>: <span class="built_in">var</span>(--anzhiyu-border-radius);</span><br><span class="line">  <span class="attribute">transition</span>: all <span class="number">0.3s</span> ease;</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">0.9rem</span>;</span><br><span class="line">  <span class="attribute">border-left</span>: <span class="number">2px</span> solid transparent;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.instance-link</span><span class="selector-pseudo">:hover</span> {</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-secondbg);</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">  <span class="attribute">border-left-color</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.instance-link</span><span class="selector-class">.active</span> {</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-white);</span><br><span class="line">  <span class="attribute">font-weight</span>: <span class="number">500</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 右侧内容区 */</span></span><br><span class="line"><span class="selector-class">.panel-content</span> {</span><br><span class="line">  <span class="attribute">flex</span>: <span class="number">1</span>;</span><br><span class="line">  <span class="attribute">position</span>: relative;</span><br><span class="line">  <span class="attribute">display</span>: flex;</span><br><span class="line">  <span class="attribute">flex-direction</span>: column;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.welcome-message</span> {</span><br><span class="line">  <span class="attribute">flex</span>: <span class="number">1</span>;</span><br><span class="line">  <span class="attribute">display</span>: flex;</span><br><span class="line">  <span class="attribute">flex-direction</span>: column;</span><br><span class="line">  <span class="attribute">align-items</span>: center;</span><br><span class="line">  <span class="attribute">justify-content</span>: center;</span><br><span class="line">  <span class="attribute">text-align</span>: center;</span><br><span class="line">  <span class="attribute">padding</span>: <span class="number">2rem</span>;</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-fontcolor);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.welcome-icon</span> <span class="selector-tag">i</span> {</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">3rem</span>;</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">  <span class="attribute">margin-bottom</span>: <span class="number">1rem</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.welcome-text</span> <span class="selector-tag">h3</span> {</span><br><span class="line">  <span class="attribute">margin</span>: <span class="number">0</span> <span class="number">0</span> <span class="number">0.5rem</span> <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">1.2rem</span>;</span><br><span class="line">  <span class="attribute">font-weight</span>: <span class="number">600</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.welcome-text</span> <span class="selector-tag">p</span> {</span><br><span class="line">  <span class="attribute">margin</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">opacity</span>: <span class="number">0.7</span>;</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">0.9rem</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.iframe-container</span> {</span><br><span class="line">  <span class="attribute">flex</span>: <span class="number">1</span>;</span><br><span class="line">  <span class="attribute">position</span>: relative;</span><br><span class="line">  <span class="attribute">display</span>: none;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.iframe-container</span><span class="selector-class">.active</span> {</span><br><span class="line">  <span class="attribute">display</span>: block;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.iframe-container</span> <span class="selector-tag">iframe</span> {</span><br><span class="line">  <span class="attribute">width</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">height</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">border</span>: none;</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-white);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.loading-indicator</span> {</span><br><span class="line">  <span class="attribute">position</span>: absolute;</span><br><span class="line">  <span class="attribute">top</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">left</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">right</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">bottom</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">display</span>: none;</span><br><span class="line">  <span class="attribute">flex-direction</span>: column;</span><br><span class="line">  <span class="attribute">align-items</span>: center;</span><br><span class="line">  <span class="attribute">justify-content</span>: center;</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-card-bg);</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-fontcolor);</span><br><span class="line">  <span class="attribute">z-index</span>: <span class="number">2</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.loading-indicator</span><span class="selector-class">.active</span> {</span><br><span class="line">  <span class="attribute">display</span>: flex;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.loading-spinner</span> <span class="selector-tag">i</span> {</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">2rem</span>;</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">  <span class="attribute">margin-bottom</span>: <span class="number">1rem</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.loading-text</span> {</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">0.9rem</span>;</span><br><span class="line">  <span class="attribute">opacity</span>: <span class="number">0.8</span>;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 全局状态 */</span></span><br><span class="line"><span class="selector-tag">body</span><span class="selector-class">.code-runner-open</span> {</span><br><span class="line">  <span class="attribute">overflow</span>: hidden;</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 响应式设计 */</span></span><br><span class="line"><span class="keyword">@media</span> (<span class="attribute">max-width</span>: <span class="number">768px</span>) {</span><br><span class="line">  <span class="selector-id">#code-runner-panel</span> {</span><br><span class="line">    <span class="attribute">width</span>: <span class="number">100vw</span>;</span><br><span class="line">    <span class="attribute">transform</span>: <span class="built_in">translateX</span>(<span class="number">100%</span>);</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-id">#code-runner-panel</span><span class="selector-class">.active</span> {</span><br><span class="line">    <span class="attribute">transform</span>: <span class="built_in">translateX</span>(<span class="number">0</span>);</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-class">.panel-nav</span> {</span><br><span class="line">    <span class="attribute">width</span>: <span class="number">150px</span>;</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-class">.panel-header</span> {</span><br><span class="line">    <span class="attribute">padding</span>: <span class="number">0.75rem</span> <span class="number">1rem</span>;</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-class">.panel-title</span> {</span><br><span class="line">    <span class="attribute">font-size</span>: <span class="number">1rem</span>;</span><br><span class="line">  }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="keyword">@media</span> (<span class="attribute">max-width</span>: <span class="number">480px</span>) {</span><br><span class="line">  <span class="selector-class">.panel-nav</span> {</span><br><span class="line">    <span class="attribute">width</span>: <span class="number">120px</span>;</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-class">.category-header</span> {</span><br><span class="line">    <span class="attribute">padding</span>: <span class="number">0.5rem</span>;</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-class">.category-name</span> {</span><br><span class="line">    <span class="attribute">font-size</span>: <span class="number">0.85rem</span>;</span><br><span class="line">  }</span><br><span class="line">  </span><br><span class="line">  <span class="selector-class">.instance-link</span> {</span><br><span class="line">    <span class="attribute">font-size</span>: <span class="number">0.8rem</span>;</span><br><span class="line">    <span class="attribute">padding</span>: <span class="number">0.4rem</span> <span class="number">0.5rem</span>;</span><br><span class="line">  }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 暗色模式适配 */</span></span><br><span class="line"><span class="selector-attr">[data-theme=<span class="string">"dark"</span>]</span> <span class="selector-id">#code-runner-panel</span> <span class="selector-class">.iframe-container</span> <span class="selector-tag">iframe</span> {</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-card-bg);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* 按钮激活状态 */</span></span><br><span class="line"><span class="selector-id">#code-runner-btn</span><span class="selector-class">.active</span> {</span><br><span class="line">  <span class="attribute">background-color</span>: <span class="built_in">var</span>(--anzhiyu-main);</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-white);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">/* Tooltip样式 */</span></span><br><span class="line"><span class="selector-class">.category-header</span><span class="selector-pseudo">::after</span> {</span><br><span class="line">  <span class="attribute">content</span>: <span class="built_in">attr</span>(data-description);</span><br><span class="line">  <span class="attribute">position</span>: absolute;</span><br><span class="line">  <span class="attribute">left</span>: <span class="number">100%</span>;</span><br><span class="line">  <span class="attribute">top</span>: <span class="number">50%</span>;</span><br><span class="line">  <span class="attribute">transform</span>: <span class="built_in">translateY</span>(-<span class="number">50%</span>);</span><br><span class="line">  <span class="attribute">margin-left</span>: <span class="number">10px</span>;</span><br><span class="line">  <span class="attribute">background</span>: <span class="built_in">var</span>(--anzhiyu-card-bg);</span><br><span class="line">  <span class="attribute">color</span>: <span class="built_in">var</span>(--anzhiyu-fontcolor);</span><br><span class="line">  <span class="attribute">padding</span>: <span class="number">0.5rem</span> <span class="number">0.75rem</span>;</span><br><span class="line">  <span class="attribute">border-radius</span>: <span class="built_in">var</span>(--anzhiyu-border-radius);</span><br><span class="line">  <span class="attribute">font-size</span>: <span class="number">0.8rem</span>;</span><br><span class="line">  <span class="attribute">white-space</span>: nowrap;</span><br><span class="line">  <span class="attribute">box-shadow</span>: <span class="built_in">var</span>(--anzhiyu-shadow-lightblack);</span><br><span class="line">  <span class="attribute">opacity</span>: <span class="number">0</span>;</span><br><span class="line">  <span class="attribute">visibility</span>: hidden;</span><br><span class="line">  <span class="attribute">transition</span>: all <span class="number">0.3s</span> ease;</span><br><span class="line">  <span class="attribute">z-index</span>: <span class="number">1002</span>;</span><br><span class="line">  <span class="attribute">border</span>: <span class="built_in">var</span>(--style-border-always);</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="selector-class">.category-header</span><span class="selector-pseudo">:hover</span><span class="selector-pseudo">::after</span> {</span><br><span class="line">  <span class="attribute">opacity</span>: <span class="number">1</span>;</span><br><span class="line">  <span class="attribute">visibility</span>: visible;</span><br><span class="line">}</span><br></pre></td></tr></tbody></table></figure><h5 id="JavaScript功能文件内容"><a href="#JavaScript功能文件内容" class="headerlink" title="JavaScript功能文件内容"></a>JavaScript功能文件内容</h5><p><strong>文件：</strong> <code>themes/anzhiyu/source/js/code-runner.js</code></p><figure class="highlight javascript"><table><tbody><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br><span class="line">142</span><br><span class="line">143</span><br><span class="line">144</span><br><span class="line">145</span><br><span class="line">146</span><br><span class="line">147</span><br><span class="line">148</span><br><span class="line">149</span><br><span class="line">150</span><br><span class="line">151</span><br><span class="line">152</span><br><span class="line">153</span><br><span class="line">154</span><br><span class="line">155</span><br><span class="line">156</span><br><span class="line">157</span><br><span class="line">158</span><br><span class="line">159</span><br><span class="line">160</span><br><span class="line">161</span><br><span class="line">162</span><br><span class="line">163</span><br><span class="line">164</span><br><span class="line">165</span><br><span class="line">166</span><br><span class="line">167</span><br><span class="line">168</span><br><span class="line">169</span><br><span class="line">170</span><br><span class="line">171</span><br><span class="line">172</span><br><span class="line">173</span><br><span class="line">174</span><br><span class="line">175</span><br><span class="line">176</span><br><span class="line">177</span><br><span class="line">178</span><br><span class="line">179</span><br><span class="line">180</span><br><span class="line">181</span><br><span class="line">182</span><br><span class="line">183</span><br><span class="line">184</span><br><span class="line">185</span><br><span class="line">186</span><br><span class="line">187</span><br><span class="line">188</span><br><span class="line">189</span><br><span class="line">190</span><br><span class="line">191</span><br><span class="line">192</span><br><span class="line">193</span><br><span class="line">194</span><br><span class="line">195</span><br><span class="line">196</span><br><span class="line">197</span><br><span class="line">198</span><br><span class="line">199</span><br><span class="line">200</span><br><span class="line">201</span><br><span class="line">202</span><br><span class="line">203</span><br><span class="line">204</span><br><span class="line">205</span><br><span class="line">206</span><br><span class="line">207</span><br><span class="line">208</span><br><span class="line">209</span><br><span class="line">210</span><br><span class="line">211</span><br><span class="line">212</span><br><span class="line">213</span><br><span class="line">214</span><br><span class="line">215</span><br><span class="line">216</span><br><span class="line">217</span><br><span class="line">218</span><br><span class="line">219</span><br><span class="line">220</span><br><span class="line">221</span><br><span class="line">222</span><br><span class="line">223</span><br><span class="line">224</span><br><span class="line">225</span><br><span class="line">226</span><br><span class="line">227</span><br><span class="line">228</span><br><span class="line">229</span><br><span class="line">230</span><br><span class="line">231</span><br><span class="line">232</span><br><span class="line">233</span><br><span class="line">234</span><br><span class="line">235</span><br><span class="line">236</span><br><span class="line">237</span><br><span class="line">238</span><br><span class="line">239</span><br><span class="line">240</span><br><span class="line">241</span><br><span class="line">242</span><br><span class="line">243</span><br><span class="line">244</span><br><span class="line">245</span><br><span class="line">246</span><br><span class="line">247</span><br><span class="line">248</span><br><span class="line">249</span><br><span class="line">250</span><br><span class="line">251</span><br><span class="line">252</span><br><span class="line">253</span><br><span class="line">254</span><br><span class="line">255</span><br><span class="line">256</span><br><span class="line">257</span><br><span class="line">258</span><br><span class="line">259</span><br><span class="line">260</span><br><span class="line">261</span><br><span class="line">262</span><br><span class="line">263</span><br><span class="line">264</span><br><span class="line">265</span><br><span class="line">266</span><br><span class="line">267</span><br><span class="line">268</span><br><span class="line">269</span><br><span class="line">270</span><br><span class="line">271</span><br><span class="line">272</span><br><span class="line">273</span><br><span class="line">274</span><br><span class="line">275</span><br><span class="line">276</span><br><span class="line">277</span><br><span class="line">278</span><br><span class="line">279</span><br><span class="line">280</span><br><span class="line">281</span><br><span class="line">282</span><br><span class="line">283</span><br><span class="line">284</span><br><span class="line">285</span><br><span class="line">286</span><br><span class="line">287</span><br><span class="line">288</span><br><span class="line">289</span><br><span class="line">290</span><br><span class="line">291</span><br><span class="line">292</span><br><span class="line">293</span><br><span class="line">294</span><br><span class="line">295</span><br><span class="line">296</span><br><span class="line">297</span><br><span class="line">298</span><br><span class="line">299</span><br><span class="line">300</span><br><span class="line">301</span><br><span class="line">302</span><br><span class="line">303</span><br><span class="line">304</span><br><span class="line">305</span><br><span class="line">306</span><br><span class="line">307</span><br><span class="line">308</span><br><span class="line">309</span><br><span class="line">310</span><br><span class="line">311</span><br><span class="line">312</span><br><span class="line">313</span><br><span class="line">314</span><br><span class="line">315</span><br><span class="line">316</span><br><span class="line">317</span><br><span class="line">318</span><br><span class="line">319</span><br><span class="line">320</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">/**</span></span><br><span class="line"><span class="comment"> * 代码运行器功能</span></span><br><span class="line"><span class="comment"> * Code Runner functionality for AnZhiYu theme</span></span><br><span class="line"><span class="comment"> * 基于doc-sidebar的实现模式</span></span><br><span class="line"><span class="comment"> */</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 初始化函数，支持PJAX</span></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">initCodeRunner</span>(<span class="params"></span>) {</span><br><span class="line">  <span class="keyword">const</span> codeRunnerPanel = <span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'code-runner-panel'</span>);</span><br><span class="line">  <span class="keyword">const</span> codeRunnerButton = <span class="variable language_">document</span>.<span class="title function_">getElementById</span>(<span class="string">'code-runner-btn'</span>);</span><br><span class="line"></span><br><span class="line">  <span class="keyword">if</span> (!codeRunnerPanel || !codeRunnerButton) {</span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'Code Runner: Panel or button not found'</span>);</span><br><span class="line">    <span class="keyword">return</span>;</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'Code Runner: Initializing...'</span>);</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 初始化代码运行器功能</span></span><br><span class="line">  <span class="title function_">initCodeRunnerPanel</span>();</span><br><span class="line"></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">initCodeRunnerPanel</span>(<span class="params"></span>) {</span><br><span class="line">    <span class="comment">// 设置初始状态</span></span><br><span class="line">    <span class="title function_">setupInitialState</span>();</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 设置按钮功能</span></span><br><span class="line">    <span class="title function_">setupToggleButton</span>();</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 设置面板内部交互</span></span><br><span class="line">    <span class="title function_">setupPanelInteractions</span>();</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 恢复用户选择</span></span><br><span class="line">    <span class="title function_">restoreUserSelection</span>();</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 设置初始状态</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">setupInitialState</span>(<span class="params"></span>) {</span><br><span class="line">    <span class="comment">// 确保面板初始状态为隐藏</span></span><br><span class="line">    codeRunnerPanel.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'active'</span>);</span><br><span class="line">    <span class="variable language_">document</span>.<span class="property">body</span>.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'code-runner-open'</span>);</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 设置iframe和加载指示器初始状态</span></span><br><span class="line">    <span class="keyword">const</span> iframeContainer = codeRunnerPanel.<span class="title function_">querySelector</span>(<span class="string">'.iframe-container'</span>);</span><br><span class="line">    <span class="keyword">const</span> loadingIndicator = codeRunnerPanel.<span class="title function_">querySelector</span>(<span class="string">'.loading-indicator'</span>);</span><br><span class="line">    <span class="keyword">const</span> welcomeMessage = codeRunnerPanel.<span class="title function_">querySelector</span>(<span class="string">'.welcome-message'</span>);</span><br><span class="line">    <span class="keyword">const</span> iframe = codeRunnerPanel.<span class="title function_">querySelector</span>(<span class="string">'#code-runner-iframe'</span>);</span><br><span class="line"></span><br><span class="line">    <span class="keyword">if</span> (iframeContainer) iframeContainer.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'active'</span>);</span><br><span class="line">    <span class="keyword">if</span> (loadingIndicator) loadingIndicator.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'active'</span>);</span><br><span class="line">    <span class="keyword">if</span> (welcomeMessage) welcomeMessage.<span class="property">style</span>.<span class="property">display</span> = <span class="string">'flex'</span>;</span><br><span class="line">    <span class="keyword">if</span> (iframe) iframe.<span class="property">src</span> = <span class="string">''</span>;</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 设置切换按钮功能</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">setupToggleButton</span>(<span class="params"></span>) {</span><br><span class="line">    codeRunnerButton.<span class="title function_">addEventListener</span>(<span class="string">'click'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">      <span class="keyword">const</span> isOpen = codeRunnerPanel.<span class="property">classList</span>.<span class="title function_">contains</span>(<span class="string">'active'</span>);</span><br><span class="line"></span><br><span class="line">      <span class="keyword">if</span> (isOpen) {</span><br><span class="line">        <span class="title function_">closePanel</span>();</span><br><span class="line">      } <span class="keyword">else</span> {</span><br><span class="line">        <span class="title function_">openPanel</span>();</span><br><span class="line">      }</span><br><span class="line">    });</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 打开面板</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">openPanel</span>(<span class="params"></span>) {</span><br><span class="line">    codeRunnerPanel.<span class="property">classList</span>.<span class="title function_">add</span>(<span class="string">'active'</span>);</span><br><span class="line">    <span class="variable language_">document</span>.<span class="property">body</span>.<span class="property">classList</span>.<span class="title function_">add</span>(<span class="string">'code-runner-open'</span>);</span><br><span class="line">    codeRunnerButton.<span class="property">classList</span>.<span class="title function_">add</span>(<span class="string">'active'</span>);</span><br><span class="line"></span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'Code Runner: Panel opened'</span>);</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 延迟自动加载第一个实例（如果配置允许）</span></span><br><span class="line">    <span class="keyword">const</span> config = <span class="variable language_">window</span>.<span class="property">GLOBAL_CONFIG</span>?.<span class="property">code_runner</span> || {};</span><br><span class="line">    <span class="keyword">if</span> (config.<span class="property">auto_load_first</span> !== <span class="literal">false</span>) {</span><br><span class="line">      <span class="comment">// 延迟加载，避免PJAX切换时立即加载导致失败</span></span><br><span class="line">      <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> {</span><br><span class="line">        <span class="title function_">autoLoadFirstInstance</span>();</span><br><span class="line">      }, <span class="number">1000</span>); <span class="comment">// 延迟1秒</span></span><br><span class="line">    }</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 关闭面板</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">closePanel</span>(<span class="params"></span>) {</span><br><span class="line">    codeRunnerPanel.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'active'</span>);</span><br><span class="line">    <span class="variable language_">document</span>.<span class="property">body</span>.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'code-runner-open'</span>);</span><br><span class="line">    codeRunnerButton.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'active'</span>);</span><br><span class="line"></span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'Code Runner: Panel closed'</span>);</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 设置面板内部交互</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">setupPanelInteractions</span>(<span class="params"></span>) {</span><br><span class="line">    <span class="comment">// 关闭按钮</span></span><br><span class="line">    <span class="keyword">const</span> closeBtn = codeRunnerPanel.<span class="title function_">querySelector</span>(<span class="string">'.panel-close-btn'</span>);</span><br><span class="line">    <span class="keyword">if</span> (closeBtn) {</span><br><span class="line">      closeBtn.<span class="title function_">addEventListener</span>(<span class="string">'click'</span>, closePanel);</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 分类展开/收缩事件</span></span><br><span class="line">    <span class="keyword">const</span> categoryHeaders = codeRunnerPanel.<span class="title function_">querySelectorAll</span>(<span class="string">'.category-header'</span>);</span><br><span class="line">    categoryHeaders.<span class="title function_">forEach</span>(<span class="function"><span class="params">header</span> =&gt;</span> {</span><br><span class="line">      header.<span class="title function_">addEventListener</span>(<span class="string">'click'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">        <span class="keyword">const</span> category = header.<span class="title function_">closest</span>(<span class="string">'.nav-category'</span>);</span><br><span class="line">        <span class="title function_">toggleCategory</span>(category);</span><br><span class="line">      });</span><br><span class="line">    });</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 实例选择事件</span></span><br><span class="line">    <span class="keyword">const</span> instanceLinks = codeRunnerPanel.<span class="title function_">querySelectorAll</span>(<span class="string">'.instance-link'</span>);</span><br><span class="line">    instanceLinks.<span class="title function_">forEach</span>(<span class="function"><span class="params">link</span> =&gt;</span> {</span><br><span class="line">      link.<span class="title function_">addEventListener</span>(<span class="string">'click'</span>, <span class="function">(<span class="params">e</span>) =&gt;</span> {</span><br><span class="line">        e.<span class="title function_">preventDefault</span>();</span><br><span class="line">        <span class="title function_">selectInstance</span>(link);</span><br><span class="line">      });</span><br><span class="line">    });</span><br><span class="line"></span><br><span class="line">    <span class="comment">// ESC键关闭</span></span><br><span class="line">    <span class="keyword">const</span> config = <span class="variable language_">window</span>.<span class="property">GLOBAL_CONFIG</span>?.<span class="property">code_runner</span> || {};</span><br><span class="line">    <span class="keyword">if</span> (config.<span class="property">close_on_escape</span> !== <span class="literal">false</span>) {</span><br><span class="line">      <span class="variable language_">document</span>.<span class="title function_">addEventListener</span>(<span class="string">'keydown'</span>, <span class="function">(<span class="params">e</span>) =&gt;</span> {</span><br><span class="line">        <span class="keyword">if</span> (e.<span class="property">key</span> === <span class="string">'Escape'</span> &amp;&amp; codeRunnerPanel.<span class="property">classList</span>.<span class="title function_">contains</span>(<span class="string">'active'</span>)) {</span><br><span class="line">          <span class="title function_">closePanel</span>();</span><br><span class="line">        }</span><br><span class="line">      });</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 点击面板外部关闭</span></span><br><span class="line">    <span class="variable language_">document</span>.<span class="title function_">addEventListener</span>(<span class="string">'click'</span>, <span class="function">(<span class="params">e</span>) =&gt;</span> {</span><br><span class="line">      <span class="keyword">if</span> (codeRunnerPanel.<span class="property">classList</span>.<span class="title function_">contains</span>(<span class="string">'active'</span>) &amp;&amp;</span><br><span class="line">        !codeRunnerPanel.<span class="title function_">contains</span>(e.<span class="property">target</span>) &amp;&amp;</span><br><span class="line">        !codeRunnerButton.<span class="title function_">contains</span>(e.<span class="property">target</span>)) {</span><br><span class="line">        <span class="title function_">closePanel</span>();</span><br><span class="line">      }</span><br><span class="line">    });</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 切换分类展开/收缩</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">toggleCategory</span>(<span class="params">category</span>) {</span><br><span class="line">    <span class="keyword">if</span> (!category) <span class="keyword">return</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 收缩所有其他分类</span></span><br><span class="line">    <span class="keyword">const</span> allCategories = codeRunnerPanel.<span class="title function_">querySelectorAll</span>(<span class="string">'.nav-category'</span>);</span><br><span class="line">    allCategories.<span class="title function_">forEach</span>(<span class="function"><span class="params">cat</span> =&gt;</span> {</span><br><span class="line">      <span class="keyword">if</span> (cat !== category) {</span><br><span class="line">        cat.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'expanded'</span>);</span><br><span class="line">      }</span><br><span class="line">    });</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 切换当前分类</span></span><br><span class="line">    category.<span class="property">classList</span>.<span class="title function_">toggle</span>(<span class="string">'expanded'</span>);</span><br><span class="line"></span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'Code Runner: Category toggled'</span>, category.<span class="property">dataset</span>.<span class="property">category</span>);</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 选择实例</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">selectInstance</span>(<span class="params">link</span>) {</span><br><span class="line">    <span class="keyword">const</span> url = link.<span class="property">dataset</span>.<span class="property">url</span>;</span><br><span class="line">    <span class="keyword">const</span> name = link.<span class="property">dataset</span>.<span class="property">name</span>;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">if</span> (!url) {</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">warn</span>(<span class="string">'Code Runner: No URL found for instance'</span>, name);</span><br><span class="line">      <span class="keyword">return</span>;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 更新选中状态</span></span><br><span class="line">    <span class="keyword">const</span> allLinks = codeRunnerPanel.<span class="title function_">querySelectorAll</span>(<span class="string">'.instance-link'</span>);</span><br><span class="line">    allLinks.<span class="title function_">forEach</span>(<span class="function"><span class="params">l</span> =&gt;</span> l.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'active'</span>));</span><br><span class="line">    link.<span class="property">classList</span>.<span class="title function_">add</span>(<span class="string">'active'</span>);</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 加载iframe</span></span><br><span class="line">    <span class="title function_">loadIframe</span>(url);</span><br><span class="line"></span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'Code Runner: Instance selected'</span>, name, url);</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 加载iframe</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">loadIframe</span>(<span class="params">url, retryCount = <span class="number">0</span></span>) {</span><br><span class="line">    <span class="keyword">const</span> iframe = codeRunnerPanel.<span class="title function_">querySelector</span>(<span class="string">'#code-runner-iframe'</span>);</span><br><span class="line">    <span class="keyword">const</span> loadingIndicator = codeRunnerPanel.<span class="title function_">querySelector</span>(<span class="string">'.loading-indicator'</span>);</span><br><span class="line">    <span class="keyword">const</span> welcomeMessage = codeRunnerPanel.<span class="title function_">querySelector</span>(<span class="string">'.welcome-message'</span>);</span><br><span class="line">    <span class="keyword">const</span> iframeContainer = codeRunnerPanel.<span class="title function_">querySelector</span>(<span class="string">'.iframe-container'</span>);</span><br><span class="line"></span><br><span class="line">    <span class="keyword">if</span> (!iframe) {</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">error</span>(<span class="string">'Code Runner: iframe not found'</span>);</span><br><span class="line">      <span class="keyword">return</span>;</span><br><span class="line">    }</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 显示加载指示器</span></span><br><span class="line">    <span class="keyword">if</span> (welcomeMessage) welcomeMessage.<span class="property">style</span>.<span class="property">display</span> = <span class="string">'none'</span>;</span><br><span class="line">    <span class="keyword">if</span> (iframeContainer) iframeContainer.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'active'</span>);</span><br><span class="line">    <span class="keyword">if</span> (loadingIndicator) loadingIndicator.<span class="property">classList</span>.<span class="title function_">add</span>(<span class="string">'active'</span>);</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 清除之前的事件监听器</span></span><br><span class="line">    iframe.<span class="property">onload</span> = <span class="literal">null</span>;</span><br><span class="line">    iframe.<span class="property">onerror</span> = <span class="literal">null</span>;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 设置加载超时</span></span><br><span class="line">    <span class="keyword">const</span> loadingTimeout = <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> {</span><br><span class="line">      <span class="title function_">onIframeError</span>();</span><br><span class="line">    }, <span class="number">20000</span>); <span class="comment">// 增加到20秒超时</span></span><br><span class="line"></span><br><span class="line">    <span class="comment">// iframe加载完成处理</span></span><br><span class="line">    <span class="keyword">const</span> <span class="title function_">onIframeLoad</span> = (<span class="params"></span>) =&gt; {</span><br><span class="line">      <span class="built_in">clearTimeout</span>(loadingTimeout);</span><br><span class="line">      <span class="keyword">if</span> (loadingIndicator) loadingIndicator.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'active'</span>);</span><br><span class="line">      <span class="keyword">if</span> (iframeContainer) iframeContainer.<span class="property">classList</span>.<span class="title function_">add</span>(<span class="string">'active'</span>);</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">'Code Runner: iframe loaded successfully'</span>);</span><br><span class="line">    };</span><br><span class="line"></span><br><span class="line">    <span class="comment">// iframe加载错误处理</span></span><br><span class="line">    <span class="keyword">const</span> <span class="title function_">onIframeError</span> = (<span class="params"></span>) =&gt; {</span><br><span class="line">      <span class="built_in">clearTimeout</span>(loadingTimeout);</span><br><span class="line"></span><br><span class="line">      <span class="comment">// 如果重试次数少于2次，则重试</span></span><br><span class="line">      <span class="keyword">if</span> (retryCount &lt; <span class="number">2</span>) {</span><br><span class="line">        <span class="variable language_">console</span>.<span class="title function_">warn</span>(<span class="string">`Code Runner: iframe load failed, retrying... (<span class="subst">${retryCount + <span class="number">1</span>}</span>/2)`</span>);</span><br><span class="line">        <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> {</span><br><span class="line">          <span class="title function_">loadIframe</span>(url, retryCount + <span class="number">1</span>);</span><br><span class="line">        }, <span class="number">2000</span>); <span class="comment">// 延迟2秒重试</span></span><br><span class="line">        <span class="keyword">return</span>;</span><br><span class="line">      }</span><br><span class="line"></span><br><span class="line">      <span class="comment">// 重试失败，显示错误信息</span></span><br><span class="line">      <span class="keyword">if</span> (loadingIndicator) loadingIndicator.<span class="property">classList</span>.<span class="title function_">remove</span>(<span class="string">'active'</span>);</span><br><span class="line">      <span class="keyword">if</span> (welcomeMessage) {</span><br><span class="line">        welcomeMessage.<span class="property">style</span>.<span class="property">display</span> = <span class="string">'flex'</span>;</span><br><span class="line">        <span class="keyword">const</span> welcomeText = welcomeMessage.<span class="title function_">querySelector</span>(<span class="string">'.welcome-text'</span>);</span><br><span class="line">        <span class="keyword">if</span> (welcomeText) {</span><br><span class="line">          welcomeText.<span class="property">innerHTML</span> = <span class="string">`</span></span><br><span class="line"><span class="string">            &lt;h3&gt;加载失败&lt;/h3&gt;</span></span><br><span class="line"><span class="string">            &lt;p&gt;无法加载编程环境，请检查网络连接或尝试其他选项&lt;/p&gt;</span></span><br><span class="line"><span class="string">            &lt;button onclick="location.reload()" style="margin-top: 10px; padding: 5px 10px; background: var(--anzhiyu-main); color: white; border: none; border-radius: 4px; cursor: pointer;"&gt;刷新页面重试&lt;/button&gt;</span></span><br><span class="line"><span class="string">          `</span>;</span><br><span class="line">        }</span><br><span class="line">      }</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">error</span>(<span class="string">'Code Runner: iframe failed to load after retries'</span>, url);</span><br><span class="line">    };</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 绑定事件</span></span><br><span class="line">    iframe.<span class="property">onload</span> = onIframeLoad;</span><br><span class="line">    iframe.<span class="property">onerror</span> = onIframeError;</span><br><span class="line"></span><br><span class="line">    <span class="comment">// 延迟设置iframe源，避免过快加载</span></span><br><span class="line">    <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> {</span><br><span class="line">      iframe.<span class="property">src</span> = url;</span><br><span class="line">    }, <span class="number">500</span>);</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 自动加载第一个实例</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">autoLoadFirstInstance</span>(<span class="params"></span>) {</span><br><span class="line">    <span class="keyword">const</span> firstCategory = codeRunnerPanel.<span class="title function_">querySelector</span>(<span class="string">'.nav-category'</span>);</span><br><span class="line">    <span class="keyword">const</span> firstInstance = firstCategory?.<span class="title function_">querySelector</span>(<span class="string">'.instance-link'</span>);</span><br><span class="line"></span><br><span class="line">    <span class="keyword">if</span> (firstCategory &amp;&amp; firstInstance) {</span><br><span class="line">      <span class="comment">// 展开第一个分类</span></span><br><span class="line">      firstCategory.<span class="property">classList</span>.<span class="title function_">add</span>(<span class="string">'expanded'</span>);</span><br><span class="line"></span><br><span class="line">      <span class="comment">// 选择第一个实例</span></span><br><span class="line">      <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> {</span><br><span class="line">        firstInstance.<span class="title function_">click</span>();</span><br><span class="line">      }, <span class="number">300</span>); <span class="comment">// 等待展开动画完成</span></span><br><span class="line">    }</span><br><span class="line">  }</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 恢复用户选择</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">restoreUserSelection</span>(<span class="params"></span>) {</span><br><span class="line">    <span class="keyword">const</span> config = <span class="variable language_">window</span>.<span class="property">GLOBAL_CONFIG</span>?.<span class="property">code_runner</span> || {};</span><br><span class="line">    <span class="keyword">if</span> (config.<span class="property">remember_selection</span> === <span class="literal">false</span>) <span class="keyword">return</span>;</span><br><span class="line"></span><br><span class="line">    <span class="keyword">try</span> {</span><br><span class="line">      <span class="keyword">const</span> saved = <span class="variable language_">localStorage</span>.<span class="title function_">getItem</span>(<span class="string">'code-runner-selection'</span>);</span><br><span class="line">      <span class="keyword">if</span> (!saved) <span class="keyword">return</span>;</span><br><span class="line"></span><br><span class="line">      <span class="keyword">const</span> selection = <span class="title class_">JSON</span>.<span class="title function_">parse</span>(saved);</span><br><span class="line">      <span class="keyword">if</span> (!selection.<span class="property">category</span> || !selection.<span class="property">instance</span>) <span class="keyword">return</span>;</span><br><span class="line"></span><br><span class="line">      <span class="comment">// 查找并恢复分类</span></span><br><span class="line">      <span class="keyword">const</span> category = codeRunnerPanel.<span class="title function_">querySelector</span>(<span class="string">`[data-category="<span class="subst">${selection.category}</span>"]`</span>);</span><br><span class="line">      <span class="keyword">if</span> (category) {</span><br><span class="line">        category.<span class="property">classList</span>.<span class="title function_">add</span>(<span class="string">'expanded'</span>);</span><br><span class="line"></span><br><span class="line">        <span class="comment">// 查找并恢复实例</span></span><br><span class="line">        <span class="keyword">const</span> instance = category.<span class="title function_">querySelector</span>(<span class="string">`[data-name="<span class="subst">${selection.instance}</span>"]`</span>);</span><br><span class="line">        <span class="keyword">if</span> (instance) {</span><br><span class="line">          instance.<span class="property">classList</span>.<span class="title function_">add</span>(<span class="string">'active'</span>);</span><br><span class="line">        }</span><br><span class="line">      }</span><br><span class="line">    } <span class="keyword">catch</span> (error) {</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">warn</span>(<span class="string">'Code Runner: Failed to restore selection'</span>, error);</span><br><span class="line">    }</span><br><span class="line">  }</span><br><span class="line">}</span><br><span class="line"></span><br><span class="line"><span class="comment">// 防止重复初始化的标志</span></span><br><span class="line"><span class="keyword">let</span> codeRunnerInitialized = <span class="literal">false</span>;</span><br><span class="line"></span><br><span class="line"><span class="comment">// 页面加载完成时初始化</span></span><br><span class="line"><span class="variable language_">document</span>.<span class="title function_">addEventListener</span>(<span class="string">'DOMContentLoaded'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">  <span class="keyword">if</span> (!codeRunnerInitialized) {</span><br><span class="line">    <span class="title function_">initCodeRunner</span>();</span><br><span class="line">    codeRunnerInitialized = <span class="literal">true</span>;</span><br><span class="line">  }</span><br><span class="line">});</span><br><span class="line"></span><br><span class="line"><span class="comment">// 为 PJAX 提供支持</span></span><br><span class="line"><span class="variable language_">document</span>.<span class="title function_">addEventListener</span>(<span class="string">'pjax:complete'</span>, <span class="function">() =&gt;</span> {</span><br><span class="line">  <span class="comment">// 重置初始化标志，因为DOM可能已经改变</span></span><br><span class="line">  codeRunnerInitialized = <span class="literal">false</span>;</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 延迟执行以确保 DOM 完全加载</span></span><br><span class="line">  <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> {</span><br><span class="line">    <span class="keyword">if</span> (!codeRunnerInitialized) {</span><br><span class="line">      <span class="title function_">initCodeRunner</span>();</span><br><span class="line">      codeRunnerInitialized = <span class="literal">true</span>;</span><br><span class="line">    }</span><br><span class="line">  }, <span class="number">500</span>); <span class="comment">// 增加延迟时间</span></span><br><span class="line">});</span><br></pre></td></tr></tbody></table></figure></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/56426.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/56426.html&quot;)">23.内容拓展：代码运行器功能实现指南</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/56426.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/56426.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>框架技术<span class="categoryesPageCount">31</span></a><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>Hexo<span class="categoryesPageCount">31</span></a><a class="post-meta__box__categoryes" href="/categories/%E6%A1%86%E6%9E%B6%E6%8A%80%E6%9C%AF/Hexo/%E9%AD%94%E6%94%B9/"><span class="categoryes-punctuation"> <i class="anzhiyufont anzhiyu-icon-inbox"></i></span>魔改<span class="categoryesPageCount">23</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>博客搭建教程<span class="tagsPageCount">31</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/49291.html"><img class="prev-cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">22.内容扩展：创建“实用网站”导航页</div></div></a></div><div class="next-post pull-right"><a href="/posts/30401.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">1️⃣ 内容产品模型实战</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/24286.html" title="10.内容扩展：添加“安全跳转”中间页"><img class="cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">10.内容扩展：添加“安全跳转”中间页</div></div></a></div><div><a href="/posts/65188.html" title="11.Twikoo 美化：添加自定义表情包"><img class="cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">11.Twikoo 美化：添加自定义表情包</div></div></a></div><div><a href="/posts/57565.html" title="12.Twikoo 美化：自定义评论回复邮件模板"><img class="cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">12.Twikoo 美化：自定义评论回复邮件模板</div></div></a></div><div><a href="/posts/20246.html" title="13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）"><img class="cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">13.Twikoo 美化：评论框体验双重增强（输入提示 + 表情放大）</div></div></a></div><div><a href="/posts/43263.html" title="14.主题魔改：添加“背景切换”弹窗面板"><img class="cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">14.主题魔改：添加“背景切换”弹窗面板</div></div></a></div><div><a href="/posts/34091.html" title="15.主题魔改：自定义全站字体"><img class="cover" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/Canvas-Ruom%20(2).webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-11</div><div class="title">15.主题魔改：自定义全站字体</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData = {
  title: "23.内容拓展：代码运行器功能实现指南",
  date: "2025-07-11 15:13:45",
  updated: "2025-07-19 19:25:44",
  tags: ["博客搭建教程"],
  categories: ["框架技术","Hexo","魔改"],
  content: "\n### 23.内容拓展：代码运行器功能实现指南\n\n本指南提供了在AnZhiYu主题中集成代码运行器功能的完整实现步骤，包括两级导航菜单、多服务商支持、响应式设计等功能。\n\n#### 步骤1：修改主题配置文件\n\n**文件路径：** `_config.anzhiyu.yml`\n\n在配置文件中添加以下内容：\n\n```yaml\n# 代码运行器配置\ncode_runner:\n  enable: true                    # 是否启用代码运行器功能\n  title: \"代码运行器\"             # 面板标题\n  button_title: \"代码运行器\"      # 按钮提示文字\n  panel_width: \"600px\"            # 面板宽度\n  auto_load_first: false          # 是否自动加载第一个实例\n  close_on_escape: true           # 是否支持ESC键关闭\n  remember_selection: true        # 是否记住用户选择\n\n  # 服务商分类配置\n  categories:\n    # 第一个分类：Trinket\n    - name: \"Trinket\"\n      icon: \"fas fa-leaf\"\n      description: \"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用\"\n      instances:\n        - name: \"Python 3\"\n          url: \"https://trinket.io/embed/python3/f417f7026885\"\n          description: \"Python 3 在线编程环境\"\n        - name: \"HTML/CSS/JS\"\n          url: \"https://trinket.io/embed/html/1aac0e8640a7\"\n          description: \"前端三件套在线编辑器\"\n        - name: \"Java\"\n          url: \"https://trinket.io/embed/java/33cfa8ec292c\"\n          description: \"Java 在线编程环境\"\n\n    # 第二个分类：JDoodle\n    - name: \"JDoodle\"\n      icon: \"fas fa-terminal\"\n      description: \"支持70+种编程语言，功能强大的在线编译器\"\n      instances:\n        - name: \"C++ Compiler\"\n          url: \"https://www.jdoodle.com/online-compiler-c++/\"\n          description: \"C++ 在线编译器\"\n        - name: \"Java Compiler\"\n          url: \"https://www.jdoodle.com/online-java-compiler/\"\n          description: \"Java 在线编译器\"\n        - name: \"Python 3\"\n          url: \"https://www.jdoodle.com/python3-programming-online/\"\n          description: \"Python 3 在线编程\"\n        - name: \"Go Playground\"\n          url: \"https://www.jdoodle.com/compile-go-online/\"\n          description: \"Go 语言在线编程\"\n\n    # 第三个分类：CodePen\n    - name: \"CodePen\"\n      icon: \"fab fa-codepen\"\n      description: \"前端开发者的在线代码编辑器和社区\"\n      instances:\n        - name: \"HTML/CSS/JS\"\n          url: \"https://codepen.io/pen/\"\n          description: \"CodePen 在线编辑器\"\n        - name: \"React Playground\"\n          url: \"https://codepen.io/pen/?template=react\"\n          description: \"React 在线开发环境\"\n```\n\n修改rightside按钮配置：\n\n```yaml\nrightside_item_order: # 右下角按钮顺序和显示控制\n  enable: true # 是否启用自定义右下角按钮顺序\n  hide: readmode,translate,darkmode,hideAside # 要隐藏的按钮列表\n  show: toc,chat,comment,downloadMd,docToc,codeRunner # 要显示的按钮列表 (添加codeRunner)\n```\n\n修改inject配置：\n\n```yaml\ninject:\n  head:\n    # 其他现有配置...\n    # 代码运行器样式\n    - '<link rel=\"stylesheet\" href=\"/css/code-runner.css\">'\n\n  bottom:\n    # 其他现有配置...\n    # 代码运行器功能脚本\n    - '<script src=\"/js/code-runner.js\"></script>'\n```\n\n#### 步骤2：修改rightside.pug文件\n\n**文件路径：** `themes/anzhiyu/layout/includes/rightside.pug`\n\n在rightside.pug文件的case语句中添加codeRunner分支：\n\n```pug\nwhen 'codeRunner'\n if theme.code_runner &amp;&amp; theme.code_runner.enable\n button#code-runner-btn(type=\"button\" title=theme.code_runner.button_title || \"代码运行器\")\n i.fas.fa-code\n```\n\n#### 步骤3：修改layout.pug文件\n\n**文件路径：** `themes/anzhiyu/layout/includes/layout.pug`\n\n在rightside.pug之后添加面板HTML结构：\n\n```pug\n//- 代码运行器面板\nif theme.code_runner &amp;&amp; theme.code_runner.enable\n #code-runner-panel.code-runner-panel\n .panel-header\n .panel-title= theme.code_runner.title || \"代码运行器\"\n button.panel-close-btn(type=\"button\" title=\"关闭\")\n i.fas.fa-times\n \n .panel-body\n //- 左侧导航菜单\n nav.panel-nav\n each category in theme.code_runner.categories\n .nav-category(data-category=category.name)\n .category-header(\n data-description=category.description\n title=category.description\n )\n if category.icon\n i(class=category.icon)\n span.category-name= category.name\n i.expand-icon.fas.fa-chevron-down\n \n ul.instance-list\n each instance in category.instances\n li.instance-item\n a.instance-link(\n href=\"javascript:void(0);\"\n data-url=instance.url\n data-name=instance.name\n title=instance.description || instance.name\n )= instance.name\n \n //- 右侧内容区\n .panel-content\n .welcome-message\n .welcome-icon\n i.fas.fa-code\n .welcome-text\n h3 欢迎使用代码运行器\n p 请从左侧菜单选择一个编程环境开始编码\n \n .iframe-container\n iframe#code-runner-iframe(\n frameborder=\"0\"\n width=\"100%\"\n height=\"100%\"\n sandbox=\"allow-scripts allow-same-origin allow-forms allow-popups allow-modals\"\n )\n \n .loading-indicator\n .loading-spinner\n i.fas.fa-spinner.fa-spin\n .loading-text 正在加载编程环境...\n```\n\n#### 步骤4：修改config.pug文件\n\n**文件路径：** `themes/anzhiyu/layout/includes/head/config.pug`\n\n在GLOBAL_CONFIG对象中添加code_runner配置：\n\n```pug\ncode_runner: !{theme.code_runner ? JSON.stringify(theme.code_runner) : 'null'}\n```\n\n将此行添加到GLOBAL_CONFIG对象的其他配置项中。\n\n#### 步骤5：创建CSS样式文件\n\n**文件路径：** `themes/anzhiyu/source/css/code-runner.css`\n\n创建完整的CSS文件（内容较长，见下一部分）。\n\n#### 步骤6：创建JavaScript功能文件\n\n**文件路径：** `themes/anzhiyu/source/js/code-runner.js`\n\n创建完整的JavaScript文件（内容较长，见下一部分）。\n\n#### 完整代码文件\n\n##### CSS样式文件内容\n\n**文件：** `themes/anzhiyu/source/css/code-runner.css`\n\n```css\n/* 代码运行器面板样式 */\n#code-runner-panel {\n position: fixed;\n top: 0;\n right: 0;\n width: 600px;\n height: 100vh;\n background: var(--anzhiyu-card-bg);\n backdrop-filter: blur(20px);\n border-left: var(--style-border-always);\n box-shadow: var(--anzhiyu-shadow-lightblack);\n z-index: 1001;\n transform: translateX(100%);\n transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n display: flex;\n flex-direction: column;\n overflow: hidden;\n}\n\n#code-runner-panel.active {\n transform: translateX(0);\n}\n\n/* 面板头部 */\n.panel-header {\n display: flex;\n align-items: center;\n justify-content: space-between;\n padding: 1rem 1.5rem;\n border-bottom: var(--style-border-always);\n background: var(--anzhiyu-card-bg);\n backdrop-filter: blur(20px);\n position: relative;\n z-index: 1;\n}\n\n.panel-title {\n font-size: 1.1rem;\n font-weight: 600;\n color: var(--anzhiyu-fontcolor);\n margin: 0;\n}\n\n.panel-close-btn {\n width: 2rem;\n height: 2rem;\n border: none;\n background: transparent;\n color: var(--anzhiyu-fontcolor);\n cursor: pointer;\n border-radius: var(--anzhiyu-border-radius);\n display: flex;\n align-items: center;\n justify-content: center;\n transition: all 0.3s ease;\n}\n\n.panel-close-btn:hover {\n background: var(--anzhiyu-secondbg);\n color: var(--anzhiyu-red);\n}\n\n/* 面板主体 */\n.panel-body {\n flex: 1;\n display: flex;\n overflow: hidden;\n}\n\n/* 左侧导航 */\n.panel-nav {\n width: 200px;\n flex-shrink: 0;\n border-right: var(--style-border-always);\n padding: 1rem;\n overflow-y: auto;\n background: var(--anzhiyu-card-bg);\n}\n\n.nav-category {\n margin-bottom: 0.5rem;\n}\n\n.category-header {\n display: flex;\n align-items: center;\n padding: 0.75rem;\n cursor: pointer;\n border-radius: var(--anzhiyu-border-radius);\n transition: all 0.3s ease;\n position: relative;\n user-select: none;\n}\n\n.category-header:hover {\n background: var(--anzhiyu-secondbg);\n}\n\n.category-header i:first-child {\n margin-right: 0.5rem;\n color: var(--anzhiyu-main);\n}\n\n.category-name {\n flex: 1;\n font-weight: 500;\n color: var(--anzhiyu-fontcolor);\n}\n\n.expand-icon {\n margin-left: 0.5rem;\n transition: transform 0.3s ease;\n color: var(--anzhiyu-fontcolor);\n opacity: 0.7;\n}\n\n.nav-category.expanded .expand-icon {\n transform: rotate(180deg);\n}\n\n/* 实例列表 */\n.instance-list {\n list-style: none;\n padding: 0;\n margin: 0;\n max-height: 0;\n overflow: hidden;\n transition: max-height 0.3s ease;\n}\n\n.nav-category.expanded .instance-list {\n max-height: 300px;\n}\n\n.instance-item {\n margin: 0;\n}\n\n.instance-link {\n display: block;\n padding: 0.5rem 0.75rem;\n margin-left: 1.5rem;\n color: var(--anzhiyu-fontcolor);\n text-decoration: none;\n border-radius: var(--anzhiyu-border-radius);\n transition: all 0.3s ease;\n font-size: 0.9rem;\n border-left: 2px solid transparent;\n}\n\n.instance-link:hover {\n background: var(--anzhiyu-secondbg);\n color: var(--anzhiyu-main);\n border-left-color: var(--anzhiyu-main);\n}\n\n.instance-link.active {\n background: var(--anzhiyu-main);\n color: var(--anzhiyu-white);\n font-weight: 500;\n}\n\n/* 右侧内容区 */\n.panel-content {\n flex: 1;\n position: relative;\n display: flex;\n flex-direction: column;\n}\n\n.welcome-message {\n flex: 1;\n display: flex;\n flex-direction: column;\n align-items: center;\n justify-content: center;\n text-align: center;\n padding: 2rem;\n color: var(--anzhiyu-fontcolor);\n}\n\n.welcome-icon i {\n font-size: 3rem;\n color: var(--anzhiyu-main);\n margin-bottom: 1rem;\n}\n\n.welcome-text h3 {\n margin: 0 0 0.5rem 0;\n font-size: 1.2rem;\n font-weight: 600;\n}\n\n.welcome-text p {\n margin: 0;\n opacity: 0.7;\n font-size: 0.9rem;\n}\n\n.iframe-container {\n flex: 1;\n position: relative;\n display: none;\n}\n\n.iframe-container.active {\n display: block;\n}\n\n.iframe-container iframe {\n width: 100%;\n height: 100%;\n border: none;\n background: var(--anzhiyu-white);\n}\n\n.loading-indicator {\n position: absolute;\n top: 0;\n left: 0;\n right: 0;\n bottom: 0;\n display: none;\n flex-direction: column;\n align-items: center;\n justify-content: center;\n background: var(--anzhiyu-card-bg);\n color: var(--anzhiyu-fontcolor);\n z-index: 2;\n}\n\n.loading-indicator.active {\n display: flex;\n}\n\n.loading-spinner i {\n font-size: 2rem;\n color: var(--anzhiyu-main);\n margin-bottom: 1rem;\n}\n\n.loading-text {\n font-size: 0.9rem;\n opacity: 0.8;\n}\n\n/* 全局状态 */\nbody.code-runner-open {\n overflow: hidden;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n #code-runner-panel {\n width: 100vw;\n transform: translateX(100%);\n }\n \n #code-runner-panel.active {\n transform: translateX(0);\n }\n \n .panel-nav {\n width: 150px;\n }\n \n .panel-header {\n padding: 0.75rem 1rem;\n }\n \n .panel-title {\n font-size: 1rem;\n }\n}\n\n@media (max-width: 480px) {\n .panel-nav {\n width: 120px;\n }\n \n .category-header {\n padding: 0.5rem;\n }\n \n .category-name {\n font-size: 0.85rem;\n }\n \n .instance-link {\n font-size: 0.8rem;\n padding: 0.4rem 0.5rem;\n }\n}\n\n/* 暗色模式适配 */\n[data-theme=\"dark\"] #code-runner-panel .iframe-container iframe {\n background: var(--anzhiyu-card-bg);\n}\n\n/* 按钮激活状态 */\n#code-runner-btn.active {\n background-color: var(--anzhiyu-main);\n color: var(--anzhiyu-white);\n}\n\n/* Tooltip样式 */\n.category-header::after {\n content: attr(data-description);\n position: absolute;\n left: 100%;\n top: 50%;\n transform: translateY(-50%);\n margin-left: 10px;\n background: var(--anzhiyu-card-bg);\n color: var(--anzhiyu-fontcolor);\n padding: 0.5rem 0.75rem;\n border-radius: var(--anzhiyu-border-radius);\n font-size: 0.8rem;\n white-space: nowrap;\n box-shadow: var(--anzhiyu-shadow-lightblack);\n opacity: 0;\n visibility: hidden;\n transition: all 0.3s ease;\n z-index: 1002;\n border: var(--style-border-always);\n}\n\n.category-header:hover::after {\n opacity: 1;\n visibility: visible;\n}\n```\n\n##### JavaScript功能文件内容\n\n**文件：** `themes/anzhiyu/source/js/code-runner.js`\n\n```javascript\n/**\n * 代码运行器功能\n * Code Runner functionality for AnZhiYu theme\n * 基于doc-sidebar的实现模式\n */\n\n// 初始化函数，支持PJAX\nfunction initCodeRunner() {\n const codeRunnerPanel = document.getElementById('code-runner-panel');\n const codeRunnerButton = document.getElementById('code-runner-btn');\n\n if (!codeRunnerPanel || !codeRunnerButton) {\n console.log('Code Runner: Panel or button not found');\n return;\n }\n\n console.log('Code Runner: Initializing...');\n\n // 初始化代码运行器功能\n initCodeRunnerPanel();\n\n function initCodeRunnerPanel() {\n // 设置初始状态\n setupInitialState();\n\n // 设置按钮功能\n setupToggleButton();\n\n // 设置面板内部交互\n setupPanelInteractions();\n\n // 恢复用户选择\n restoreUserSelection();\n }\n\n // 设置初始状态\n function setupInitialState() {\n // 确保面板初始状态为隐藏\n codeRunnerPanel.classList.remove('active');\n document.body.classList.remove('code-runner-open');\n\n // 设置iframe和加载指示器初始状态\n const iframeContainer = codeRunnerPanel.querySelector('.iframe-container');\n const loadingIndicator = codeRunnerPanel.querySelector('.loading-indicator');\n const welcomeMessage = codeRunnerPanel.querySelector('.welcome-message');\n const iframe = codeRunnerPanel.querySelector('#code-runner-iframe');\n\n if (iframeContainer) iframeContainer.classList.remove('active');\n if (loadingIndicator) loadingIndicator.classList.remove('active');\n if (welcomeMessage) welcomeMessage.style.display = 'flex';\n if (iframe) iframe.src = '';\n }\n\n // 设置切换按钮功能\n function setupToggleButton() {\n codeRunnerButton.addEventListener('click', () =&gt; {\n const isOpen = codeRunnerPanel.classList.contains('active');\n\n if (isOpen) {\n closePanel();\n } else {\n openPanel();\n }\n });\n }\n\n // 打开面板\n function openPanel() {\n codeRunnerPanel.classList.add('active');\n document.body.classList.add('code-runner-open');\n codeRunnerButton.classList.add('active');\n\n console.log('Code Runner: Panel opened');\n\n // 延迟自动加载第一个实例（如果配置允许）\n const config = window.GLOBAL_CONFIG?.code_runner || {};\n if (config.auto_load_first !== false) {\n // 延迟加载，避免PJAX切换时立即加载导致失败\n setTimeout(() =&gt; {\n autoLoadFirstInstance();\n }, 1000); // 延迟1秒\n }\n }\n\n // 关闭面板\n function closePanel() {\n codeRunnerPanel.classList.remove('active');\n document.body.classList.remove('code-runner-open');\n codeRunnerButton.classList.remove('active');\n\n console.log('Code Runner: Panel closed');\n }\n\n // 设置面板内部交互\n function setupPanelInteractions() {\n // 关闭按钮\n const closeBtn = codeRunnerPanel.querySelector('.panel-close-btn');\n if (closeBtn) {\n closeBtn.addEventListener('click', closePanel);\n }\n\n // 分类展开/收缩事件\n const categoryHeaders = codeRunnerPanel.querySelectorAll('.category-header');\n categoryHeaders.forEach(header =&gt; {\n header.addEventListener('click', () =&gt; {\n const category = header.closest('.nav-category');\n toggleCategory(category);\n });\n });\n\n // 实例选择事件\n const instanceLinks = codeRunnerPanel.querySelectorAll('.instance-link');\n instanceLinks.forEach(link =&gt; {\n link.addEventListener('click', (e) =&gt; {\n e.preventDefault();\n selectInstance(link);\n });\n });\n\n // ESC键关闭\n const config = window.GLOBAL_CONFIG?.code_runner || {};\n if (config.close_on_escape !== false) {\n document.addEventListener('keydown', (e) =&gt; {\n if (e.key === 'Escape' &amp;&amp; codeRunnerPanel.classList.contains('active')) {\n closePanel();\n }\n });\n }\n\n // 点击面板外部关闭\n document.addEventListener('click', (e) =&gt; {\n if (codeRunnerPanel.classList.contains('active') &amp;&amp;\n !codeRunnerPanel.contains(e.target) &amp;&amp;\n !codeRunnerButton.contains(e.target)) {\n closePanel();\n }\n });\n }\n\n // 切换分类展开/收缩\n function toggleCategory(category) {\n if (!category) return;\n\n // 收缩所有其他分类\n const allCategories = codeRunnerPanel.querySelectorAll('.nav-category');\n allCategories.forEach(cat =&gt; {\n if (cat !== category) {\n cat.classList.remove('expanded');\n }\n });\n\n // 切换当前分类\n category.classList.toggle('expanded');\n\n console.log('Code Runner: Category toggled', category.dataset.category);\n }\n\n // 选择实例\n function selectInstance(link) {\n const url = link.dataset.url;\n const name = link.dataset.name;\n\n if (!url) {\n console.warn('Code Runner: No URL found for instance', name);\n return;\n }\n\n // 更新选中状态\n const allLinks = codeRunnerPanel.querySelectorAll('.instance-link');\n allLinks.forEach(l =&gt; l.classList.remove('active'));\n link.classList.add('active');\n\n // 加载iframe\n loadIframe(url);\n\n console.log('Code Runner: Instance selected', name, url);\n }\n\n // 加载iframe\n function loadIframe(url, retryCount = 0) {\n const iframe = codeRunnerPanel.querySelector('#code-runner-iframe');\n const loadingIndicator = codeRunnerPanel.querySelector('.loading-indicator');\n const welcomeMessage = codeRunnerPanel.querySelector('.welcome-message');\n const iframeContainer = codeRunnerPanel.querySelector('.iframe-container');\n\n if (!iframe) {\n console.error('Code Runner: iframe not found');\n return;\n }\n\n // 显示加载指示器\n if (welcomeMessage) welcomeMessage.style.display = 'none';\n if (iframeContainer) iframeContainer.classList.remove('active');\n if (loadingIndicator) loadingIndicator.classList.add('active');\n\n // 清除之前的事件监听器\n iframe.onload = null;\n iframe.onerror = null;\n\n // 设置加载超时\n const loadingTimeout = setTimeout(() =&gt; {\n onIframeError();\n }, 20000); // 增加到20秒超时\n\n // iframe加载完成处理\n const onIframeLoad = () =&gt; {\n clearTimeout(loadingTimeout);\n if (loadingIndicator) loadingIndicator.classList.remove('active');\n if (iframeContainer) iframeContainer.classList.add('active');\n console.log('Code Runner: iframe loaded successfully');\n };\n\n // iframe加载错误处理\n const onIframeError = () =&gt; {\n clearTimeout(loadingTimeout);\n\n // 如果重试次数少于2次，则重试\n if (retryCount &lt; 2) {\n console.warn(`Code Runner: iframe load failed, retrying... (${retryCount + 1}/2)`);\n setTimeout(() =&gt; {\n loadIframe(url, retryCount + 1);\n }, 2000); // 延迟2秒重试\n return;\n }\n\n // 重试失败，显示错误信息\n if (loadingIndicator) loadingIndicator.classList.remove('active');\n if (welcomeMessage) {\n welcomeMessage.style.display = 'flex';\n const welcomeText = welcomeMessage.querySelector('.welcome-text');\n if (welcomeText) {\n welcomeText.innerHTML = `\n<h3>加载失败</h3>\n<p>无法加载编程环境，请检查网络连接或尝试其他选项</p>\n <button onclick="\&quot;location.reload()\&quot;" style="\&quot;margin-top:" 10px;="" padding:="" 5px="" background:="" var(--anzhiyu-main);="" color:="" white;="" border:="" none;="" border-radius:="" 4px;="" cursor:="" pointer;\"="">刷新页面重试\n `;\n }\n }\n console.error('Code Runner: iframe failed to load after retries', url);\n };\n\n // 绑定事件\n iframe.onload = onIframeLoad;\n iframe.onerror = onIframeError;\n\n // 延迟设置iframe源，避免过快加载\n setTimeout(() =&gt; {\n iframe.src = url;\n }, 500);\n }\n\n // 自动加载第一个实例\n function autoLoadFirstInstance() {\n const firstCategory = codeRunnerPanel.querySelector('.nav-category');\n const firstInstance = firstCategory?.querySelector('.instance-link');\n\n if (firstCategory &amp;&amp; firstInstance) {\n // 展开第一个分类\n firstCategory.classList.add('expanded');\n\n // 选择第一个实例\n setTimeout(() =&gt; {\n firstInstance.click();\n }, 300); // 等待展开动画完成\n }\n }\n\n // 恢复用户选择\n function restoreUserSelection() {\n const config = window.GLOBAL_CONFIG?.code_runner || {};\n if (config.remember_selection === false) return;\n\n try {\n const saved = localStorage.getItem('code-runner-selection');\n if (!saved) return;\n\n const selection = JSON.parse(saved);\n if (!selection.category || !selection.instance) return;\n\n // 查找并恢复分类\n const category = codeRunnerPanel.querySelector(`[data-category=\"${selection.category}\"]`);\n if (category) {\n category.classList.add('expanded');\n\n // 查找并恢复实例\n const instance = category.querySelector(`[data-name=\"${selection.instance}\"]`);\n if (instance) {\n instance.classList.add('active');\n }\n }\n } catch (error) {\n console.warn('Code Runner: Failed to restore selection', error);\n }\n }\n}\n\n// 防止重复初始化的标志\nlet codeRunnerInitialized = false;\n\n// 页面加载完成时初始化\ndocument.addEventListener('DOMContentLoaded', () =&gt; {\n if (!codeRunnerInitialized) {\n initCodeRunner();\n codeRunnerInitialized = true;\n }\n});\n\n// 为 PJAX 提供支持\ndocument.addEventListener('pjax:complete', () =&gt; {\n // 重置初始化标志，因为DOM可能已经改变\n codeRunnerInitialized = false;\n\n // 延迟执行以确保 DOM 完全加载\n setTimeout(() =&gt; {\n if (!codeRunnerInitialized) {\n initCodeRunner();\n codeRunnerInitialized = true;\n }\n }, 500); // 增加延迟时间\n});\n```" };</button></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-3"><a class="toc-link" href="#23-%E5%86%85%E5%AE%B9%E6%8B%93%E5%B1%95%EF%BC%9A%E4%BB%A3%E7%A0%81%E8%BF%90%E8%A1%8C%E5%99%A8%E5%8A%9F%E8%83%BD%E5%AE%9E%E7%8E%B0%E6%8C%87%E5%8D%97"><span class="toc-number">1.</span> <span class="toc-text">23.内容拓展：代码运行器功能实现指南</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A41%EF%BC%9A%E4%BF%AE%E6%94%B9%E4%B8%BB%E9%A2%98%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6"><span class="toc-number">1.1.</span> <span class="toc-text">步骤1：修改主题配置文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A42%EF%BC%9A%E4%BF%AE%E6%94%B9rightside-pug%E6%96%87%E4%BB%B6"><span class="toc-number">1.2.</span> <span class="toc-text">步骤2：修改rightside.pug文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A43%EF%BC%9A%E4%BF%AE%E6%94%B9layout-pug%E6%96%87%E4%BB%B6"><span class="toc-number">1.3.</span> <span class="toc-text">步骤3：修改layout.pug文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A44%EF%BC%9A%E4%BF%AE%E6%94%B9config-pug%E6%96%87%E4%BB%B6"><span class="toc-number">1.4.</span> <span class="toc-text">步骤4：修改config.pug文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A45%EF%BC%9A%E5%88%9B%E5%BB%BACSS%E6%A0%B7%E5%BC%8F%E6%96%87%E4%BB%B6"><span class="toc-number">1.5.</span> <span class="toc-text">步骤5：创建CSS样式文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%AD%A5%E9%AA%A46%EF%BC%9A%E5%88%9B%E5%BB%BAJavaScript%E5%8A%9F%E8%83%BD%E6%96%87%E4%BB%B6"><span class="toc-number">1.6.</span> <span class="toc-text">步骤6：创建JavaScript功能文件</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%AE%8C%E6%95%B4%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6"><span class="toc-number">1.7.</span> <span class="toc-text">完整代码文件</span></a><ol class="toc-child"><li class="toc-item toc-level-5"><a class="toc-link" href="#CSS%E6%A0%B7%E5%BC%8F%E6%96%87%E4%BB%B6%E5%86%85%E5%AE%B9"><span class="toc-number">1.7.1.</span> <span class="toc-text">CSS样式文件内容</span></a></li><li class="toc-item toc-level-5"><a class="toc-link" href="#JavaScript%E5%8A%9F%E8%83%BD%E6%96%87%E4%BB%B6%E5%86%85%E5%AE%B9"><span class="toc-number">1.7.2.</span> <span class="toc-text">JavaScript功能文件内容</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>