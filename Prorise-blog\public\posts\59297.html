<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><title>产品经理入门（三）：第三章：需求分析 | Prorise - 分享技术与实战经验</title><meta name="keywords" content="产品经理教程"><meta name="author" content="Prorise"><meta name="copyright" content="Prorise"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#f0f5f9"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-touch-fullscreen" content="yes"><meta name="apple-mobile-web-app-title" content="产品经理入门（三）：第三章：需求分析"><meta name="application-name" content="产品经理入门（三）：第三章：需求分析"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="#f0f5f9"><meta property="og:type" content="article"><meta property="og:title" content="产品经理入门（三）：第三章：需求分析"><meta property="og:url" content="https://prorise666.site/posts/59297.html"><meta property="og:site_name" content="Prorise - 分享技术与实战经验"><meta property="og:description" content="第三章：需求分析在上一章，我们学会了如何像一名侦探一样，通过各种手段去“收集”需求的线索。但这些线索往往是零散的、模糊的，甚至带有误导性。如果我们不加处理就直接采纳，很可能会做出南辕北辙的产品。 因此，需求分析就是我们作为产品经理，对这些原始线索进行“勘察、推理、定案”的关键过程。这是整个产品工作中"><meta property="og:locale" content="zh-CN"><meta property="og:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta property="article:author" content="Prorise"><meta property="article:tag" content="全栈, Full Stack, 前端, 后端, Node.js, Vue, React, 数据库, Linux, Docker, 个人博客, 技术分享"><meta name="twitter:card" content="summary"><meta name="twitter:image" content="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp"><meta name="description" content="第三章：需求分析在上一章，我们学会了如何像一名侦探一样，通过各种手段去“收集”需求的线索。但这些线索往往是零散的、模糊的，甚至带有误导性。如果我们不加处理就直接采纳，很可能会做出南辕北辙的产品。 因此，需求分析就是我们作为产品经理，对这些原始线索进行“勘察、推理、定案”的关键过程。这是整个产品工作中"><link rel="shortcut icon" href="https://bu.dusays.com/2025/07/19/687b394cb439b.ico"><link rel="canonical" href="https://prorise666.site/posts/59297.html"><link rel="preconnect" href="//cdn.cbd.int"><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""><link rel="preconnect" href="//busuanzi.ibruce.info"><meta name="google-site-verification" content="xxx"><meta name="baidu-site-verification" content="code-xxx"><meta name="msvalidate.01" content="xxx"><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/node-snackbar/0.1.16/snackbar.min.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.css" media="print" onload="this.media=&quot;all&quot;"><link rel="stylesheet" href="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/swiper/swiper.min.css" media="print" onload="this.media=&quot;all&quot;"><script async="" src="https://www.googletagmanager.com/gtag/js?id=[object Object]"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","[object Object]")</script><script>const GLOBAL_CONFIG = {
  linkPageTop: {"enable":true,"title":"与数百名博主无限进步","addFriendPlaceholder":"昵称（请勿包含博客等字样）：\n网站地址（要求博客地址，请勿提交个人主页）：\n头像图片url（请提供尽可能清晰的图片，我会上传到我自己的图床）：\n描述：\n站点截图（可选）：\n"},
  peoplecanvas: undefined,
  postHeadAiDescription: {"enable":true,"gptName":"Prorise","mode":"tianli","switchBtn":false,"btnLink":"https://afdian.net/item/886a79d4db6711eda42a52540025c377","randomNum":3,"basicWordCount":1000,"key":"S-DNA1JM95BX2F9L0N","Referer":"https://xx.xx/"},
  diytitle: undefined,
  LA51: undefined,
  greetingBox: {"enable":true,"default":"晚上好👋","list":[{"greeting":"晚安😴","startTime":0,"endTime":5},{"greeting":"早上好鸭👋, 祝你一天好心情！","startTime":6,"endTime":9},{"greeting":"上午好👋, 状态很好，鼓励一下～","startTime":10,"endTime":10},{"greeting":"11点多啦, 在坚持一下就吃饭啦～","startTime":11,"endTime":11},{"greeting":"午安👋, 宝贝","startTime":12,"endTime":14},{"greeting":"🌈充实的一天辛苦啦！","startTime":14,"endTime":18},{"greeting":"19点喽, 奖励一顿丰盛的大餐吧🍔。","startTime":19,"endTime":19},{"greeting":"晚上好👋, 在属于自己的时间好好放松😌~","startTime":20,"endTime":24}]},
  twikooEnvId: 'https://twikoo.prorise666.site/',
  commentBarrageConfig:{"enable":true,"maxBarrage":1,"barrageTime":10000,"accessToken":"b39753735ed0ed5ffeb771bc108e3158","mailMd5":"ec3291d59a8d7d3675df8a0537fcbc979f0fa611c8df55841fb7f4fd162bd07a"},
  music_page_default: "nav_music",
  root: '/',
  preloader: {"source":3},
  friends_vue_info: undefined,
  navMusic: true,
  mainTone: {"mode":"both","api":"https://img2color-go.vercel.app/api?img=","cover_change":true},
  authorStatus: {"skills":["🤣 全栈开发工程师","🙂 前端架构设计师","🙃 后端服务构建者","🤩 移动端应用开发","🤯 数据库设计专家","🥳 DevOps运维实践者","🤭 技术栈多面手","😎 性能优化达人"],"witty_words":["你可以的","你一定可以的","祝你好运，陌生人","保持热爱，奔赴山海","愿你历尽千帆，归来仍是少年","纵然世事无常，也要保持内心的光亮","时间会证明一切，也会治愈一切","做自己的太阳，无需凭借谁的光"],"states":{"morning":"✨ 早上好，新的一天开始了","noon":"🍲 午餐时间","afternoon":"🌞 下午好","night":"早点休息","goodnight":"晚安 😴"}},
  algolia: {"appId":"K8Y7M0RMXQ","apiKey":"********************************","indexName":"prorise_blog","hits":{"per_page":6},"languages":{"input_placeholder":"输入关键词后按下回车查找","hits_empty":"找不到您查询的内容：${query}","hits_stats":"找到 ${hits} 条结果，用时 ${time} 毫秒"}},
  localSearch: undefined,
  translate: {"defaultEncoding":2,"translateDelay":0,"msgToTraditionalChinese":"繁","msgToSimplifiedChinese":"简","rightMenuMsgToTraditionalChinese":"转为繁体","rightMenuMsgToSimplifiedChinese":"转为简体"},
  noticeOutdate: {"limitDay":360,"position":"top","messagePrev":"距离上次更新已经过了","messageNext":"天，文章内容可能已经过时。"},
  highlight: {"plugin":"highlight.js","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":330},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: true,
    simplehomepage: false,
    post: true
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"copy":true,"copyrightEbable":false,"limitCount":50,"languages":{"author":"作者: Prorise","link":"链接: ","source":"来源: Prorise - 分享技术与实战经验","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。","copySuccess":"复制成功，复制和转载请标注本文地址"}},
  lightbox: 'fancybox',
  Snackbar: {"chs_to_cht":"你已切换为繁体","cht_to_chs":"你已切换为简体","day_to_night":"你已切换为深色模式","night_to_day":"你已切换为浅色模式","bgLight":"#425AEF","bgDark":"#1f1f1f","position":"top-center"},
  source: {
    justifiedGallery: {
      js: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.min.js',
      css: 'https://cdn.cbd.int/flickr-justified-gallery@2.1.2/dist/fjGallery.css'
    }
  },
  isPhotoFigcaption: true,
  islazyload: true,
  isAnchor: true,
  shortcutKey: undefined,
  autoDarkmode: true,
  code_runner: {"enable":true,"title":"代码运行器","button_title":"代码运行器","panel_width":"600px","auto_load_first":true,"close_on_escape":true,"remember_selection":true,"categories":[{"name":"Trinket","icon":"fas fa-leaf","description":"适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用","instances":[{"name":"Python 3","url":"https://trinket.io/embed/python3/f417f7026885","description":"Python 3 在线编程环境"},{"name":"HTML/CSS/JS","url":"https://trinket.io/embed/html/1aac0e8640a7","description":"前端三件套在线编辑器"},{"name":"Java","url":"https://trinket.io/embed/java/33cfa8ec292c","description":"Java 在线编程环境"}]},{"name":"JDoodle","icon":"fas fa-terminal","description":"支持70+种编程语言，功能强大的在线编译器","instances":[{"name":"C++ Compiler","url":"https://www.jdoodle.com/online-compiler-c++/","description":"C++ 在线编译器"},{"name":"Java Compiler","url":"https://www.jdoodle.com/online-java-compiler/","description":"Java 在线编译器"},{"name":"Python 3","url":"https://www.jdoodle.com/python3-programming-online/","description":"Python 3 在线编程"},{"name":"TypeScript 在线编译器","url":"https://www.jdoodle.com/embed/v1/f40f7676c55c09b","description":"TypeScript"},{"name":"SQL 在线编译器","url":"https://www.jdoodle.com/embed/v1/510d607e22ee9e7c","description":"SQL 在线编译器"},{"name":"PHP 在线编译器","url":"https://www.jdoodle.com/embed/v1/ca85d333af57a5fc","description":"PHP 在线编译器"},{"name":"C语言 在线编译器","url":"https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248","description":"C语言 在线编译器"}]}]}
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE={configTitle:"Prorise - 分享技术与实战经验",title:"产品经理入门（三）：第三章：需求分析",postAI:"true",pageFillDescription:"第三章：需求分析, 3.1 需求分析的定义, 3.1.1 什么是需求分析, 1. 需求分析的本质, 2. 原始需求与产品需求的转换, 3.2 需求分析的时机, 3.2.1 收集需求时的需求分析, 1. 直接沟通进行需求分析, 2. 案例：物流公司时效需求分析, 3.2.2 收集需求后的需求分析, 1. 集中分析需求, 2. 案例：用户反馈需求分析, 3.3 需求分析的步骤, 3.3.1 需求澄清, 1. WHY（为什么）, 2. WHO（用户是谁）, 3. WHAT（什么问题）, 4. HOW（现状如何）, 3.3.2 需求甄别, 1. 需求真伪判定的三个标准, 2. 伪需求判断案例分析, 3.3.3 需求的优先级, 1. 四象限法则, 2. 优先级确定因素, 3. 确定需求优先级的注意事项第三章需求分析在上一章我们学会了如何像一名侦探一样通过各种手段去收集需求的线索但这些线索往往是零散的模糊的甚至带有误导性如果我们不加处理就直接采纳很可能会做出南辕北辙的产品因此需求分析就是我们作为产品经理对这些原始线索进行勘察推理定案的关键过程这是整个产品工作中最能体现我们逻辑思辨和深度思考能力的核心环节需求分析的定义我们先从最根本的问题开始到底什么才叫需求分析我们可以来看一个案例上面这个案例我们不难看出当我们接到需求后没有去了解需求的背景深挖需求很容易导致我们做出来的方案是不符合要求导致大量的人力时间资源的浪费我们应当去拆解用户的需求如下什么是需求分析在我看来要理解需求分析我们需要抓住它的本质和过程需求分析的本质我理解的需求分析其本质是一个解构与重构的过程解构是把用户提出的原始需求无论是问题目的还是方案打碎拆解深入挖掘其背后真正的动机和未被满足的痛点重构是在我们完全理解了本质痛点之后重新组合信息设计出一个真正能有效解决该问题的合理的可落地的产品解决方案简单来说就是先想为什么再想怎么办原始需求与产品需求的转换基于这个本质我给需求分析一个最直接的定义所谓需求分析就是将用户的原始需求转化为可执行的产品需求的全过程我们再来回顾一下这两个概念原始需求是用户直接给我们的东西是用户想要什么它可能是我想要个一键下单按钮也可能是我希望能快速找到便宜的外卖它是我们工作的输入产品需求是我们经过分析甄别权衡之后最终决定要做的东西是我们应该为用户做什么它是一个包含了用户场景核心问题解决方案和验收标准的完整方案它是我们工作的输出所以需求分析就是连接这两者的桥梁是那个至关重要的转化步骤没有这个转化过程我们就只是一个需求的传声筒而不是一个创造价值的产品经理为了让这个区别更清晰我总结了下面的对比表对比维度原始需求产品需求来源用户直接表达产品经理分析转化形式通常是模糊零散未经验证的是清晰结构化经过验证的关注点我想要一个功能为了解决用户问题我们需要方案我的角色聆听者记录员分析师决策者方案设计师需求分析的时机在我看来需求分析并不是一个孤立的只在特定阶段才进行的仪式它应该像呼吸一样贯穿我们产品工作的始终不过从实践上我主要会在两个关键的时间点以不同的方式来开展这项工作收集需求时和收集需求后收集需求时的需求分析直接沟通进行需求分析我把这个过程称为实时分析当我在进行用户访谈或是与业务方开会时我绝不只做一个被动的记录员我的大脑会高速运转对接收到的每一个信息点当场进行第一轮的分析澄清和追问这种方式的好处是我可以在信息最新鲜上下文最完整的时刻抓住机会深挖下去及时地探究用户为什么这么想而不是等会议结束记忆模糊后再自己去猜测案例物流公司时效需求分析这个案例能很好地说明实时分析的价值场景我作为一家物流平台的正在访谈一位重要的企业客户原始需求客户告诉我我希望你们的平台能提供一个功能让我能在地图上看到我们货物的运输车辆的实时位置我的实时分析与追问听到这个方案后我没有立刻记下来就完事而是当场追问这个想法很有意思可以和我聊聊吗您为什么需要看到车辆的实时位置这个功能能帮您解决什么具体问题呢挖掘出的本质目的通过追问我发现客户并不真的关心车辆在哪条路上他关心的是货物到底什么时候能到因为他的下游客户总是在催问他送达时间他需要一个准确的预期来安抚客户安排接货转化后的产品需求因此我将这个需求从提供车辆实时定位转化为了在订单详情页提供一个精准动态的预计送达时间并支持在临近送达时向收货人发送提醒后者显然是价值高得多也更贴近问题本质的解决方案收集需求后的需求分析集中分析需求当然光有实时分析是远远不够的在完成了当周的用户访谈回收了所有的调查问卷或者整理完用户反馈后我会专门安排时间进行一次集中分析在这个阶段我会把所有零散的原始的需求信息汇总到一起像一个侦探把所有线索都钉在白板上一样我会开始寻找它们之间的关联共性试图发现那个隐藏在多个表象之下的更宏观的系统性的问题或机会案例用户反馈需求分析场景我作为一款社交的每周都会定期整理来自应用商店社区的用户反馈原始需求集合我看到了大量看似独立的用户抱怨用户说你们的视频加载太慢了用户说视频看着看着就卡住了用户说上传一个视频要等半天我的集中分析如果我只看单条反馈可能会分别给技术团队提加载慢播放卡上传慢这三个独立的零散的问题但当我把它们放在一起集中分析时我发现了一个共性我们产品的整体视频处理和分发链路可能存在系统性的性能瓶颈转化后的产品需求基于这个判断我最终定义的产品需求就不是一个个小修小补而是一个系统性的优化项目优化视频处理架构提升视频在不同网络环境下的加载和播放流畅度将平均起播时间缩短这个需求显然比解决单个用户的抱怨要有价值得多总而言之这两种时机的分析各有侧重缺一不可我将它们的区别总结如下分析时机核心特点我的目标收集时实时分析互动性强有上下文聚焦于个体快速探究单个原始需求背后的为什么收集后集中分析宏观全面寻找关联发现多个原始需求背后共同指向的系统性问题或机会需求分析的步骤到目前为止我们已经定义了需求分析也明确了进行分析的时机那具体到一项原始需求我究竟是如何一步步把它解剖清楚的呢在我的工作流中这个过程被严格地划分为三个步骤第一步需求澄清第二步需求甄别第三步需求的优先级排序这三个步骤层层递进缺一不可我们先来看第一步也是所有分析的基础需求澄清需求澄清每当一个原始需求摆在我面前时我从不急于判断它的好坏真伪我做的第一件事永远是把它弄清楚需求澄清对就我像一名侦探在审视案发现场我需要通过反复地询问和探究把这个需求的所有模糊不确定的信息都变得清晰明确为了系统化地做到这一点我随身携带着一个强大的思维工具框架我们以外卖平台的案例来逐一拆解这四个关键问题假设我收到的一个原始需求是我想要一个智能营养餐功能为什么这是我首先要问的问题我们为什么要做这个需求它能为用户带来什么核心价值它又能为我们的公司带来什么商业价值对于智能营养餐这个需求可能是用户价值帮助对健康有追求的用户解决不知道怎么吃才健康以及计算热量和营养成分很麻烦的痛点商业价值通过差异化的健康服务吸引高价值用户提升平台的品牌形象和用户粘性如果一个需求的我都答不出来那它基本上就可以被直接否决了用户是谁第二个问题这个需求我们是为谁而做的我需要清晰地描绘出目标用户的画像智能营养餐的绝不是所有用户它的核心用户画像可能是一线城市的年轻白领有健身习惯或正在减脂的人群关注生活品质愿意为健康付出一定溢价的用户明确能帮助我在后续的设计中始终围绕着这群核心用户的审美和习惯来进行什么问题第三个问题我们具体要解决一个什么问题我需要把用户的痛点用清晰无歧义的语言描述出来对于智能营养餐不是用户想要一个功能而是要解决用户的本质问题用户因缺乏专业知识难以判断不同外卖的营养成分和热量是否满足自己的健康需求用户因工作繁忙没有时间去自己计算和搭配每日的营养摄入现状如何最后一个问题用户现在是如何解决这个问题的了解用户当前的野生解决方案能帮我判断痛点的强度并为我的设计提供灵感对于智能营养餐这个需求用户当下的可能是自己去网上搜索食物热量表估算着点餐下载专门的健康手动记录自己点的外卖再查看营养分析干脆放弃点外卖选择自己做饭或吃价格昂贵的成品健康餐这些笨拙耗时昂贵的现状恰恰反证了我们这个新功能潜在的巨大价值我将这个澄清框架总结为一张表它是我分析任何需求前的必填清单澄清问题我的核心关注点案例应用智能营养餐为什么做探究需求的商业与用户价值满足健康需求提升用户粘性与品牌价值为谁而做定义精准的目标用户画像追求健康的都市白领健身人群解决什么问题识别并定义用户的本质痛点解决用户不懂如何健康搭配和没时间计算营养的问题现状如何了解用户当前的解决方案或替代方案用户目前通过手动查询使用其他等方式过程繁琐且不准确只有把这四个问题都回答清楚了我才会认为这个需求已经被我澄清了接下来我才会进入分析的第二步需求甄别需求甄别我把需求分析的第二步称为需求甄别或者叫真伪需求判定我的核心任务是判断这个被澄清后的需求到底是一个能为多数用户创造巨大价值的真需求还是一个看似有理实则虚幻的伪需求投入资源去做一个伪需求是我认为对团队最大的浪费需求真伪判定的三个标准为了避免凭感觉做判断我建立了一套自己的甄别滤网它由三个核心标准构成一个有价值的真需求通常需要至少满足其中两个甚至是全部三个标准普遍性这个问题是否具有广泛的代表性在我的目标用户群体中遇到这个问题的用户规模有多大是只有一小撮人有这个特殊的毛病还是绝大多数用户共同的困扰我追求的是能让尽可能多的目标用户受益的需求痛点性这个问题给用户带来的痛感有多强如果这个问题不被解决用户是否会感到非常沮丧烦躁甚至愿意付费来解决它我常用是痒点还是痛点来区分挠痒痒的需求可做可不做但治病止痛的需求用户才会真正买单高频性用户遇到这个问题的频率有多高是每天每周都会遇到还是每年甚至几年才会遇到一次高频的需求意味着我们的解决方案能被用户频繁使用这对于培养用户习惯提升产品粘性至关重要伪需求判断案例分析我们来看一个经典的案例学习如何运用这三个标准来甄别伪需求场景我是一家母婴社区的产品经理我们的核心业务是为新手爸妈提供育儿知识交流和社交的平台原始需求社区里有一位非常活跃且有影响力的用户她强烈建议我们增加一个儿童防走丢手表的功能她的设想是用户可以在我们的里购买一款儿童手表并随时查看孩子的位置我的甄别过程这个需求听起来非常刚需因为儿童安全是天大的事但我们必须冷静地用三个标准来审视它普遍性分析所有家长都关心孩子安全这是一个普遍的情感但是需要通过一个内嵌的硬件功能来随时追踪孩子位置这还是一个普遍的需求吗我的判断是只有其中一部分极度焦虑的家长才会有此强需求因此需求的普遍性较低痛点性分析孩子走丢这个场景痛不痛当然痛这是天塌下来的痛所以痛点性极高高频性分析一个正常的孩子在父母的看护下走丢这件事发生的频率有多高谢天谢地这是一个极低极低的概率所以需求的高频性极低我的最终结论这是一个低频非普适的超强痛点需求更重要的是它涉及到硬件供应链地图服务等这与我们社区内容的核心能力相去甚远因此尽管它听起来很有吸引力但我会判定对于我们这个母婴社区而言这是一个伪需求它是一个真实存在的问题但它不应该由我们这个产品来解决我将这三个标准总结为一张自检表每当我分析需求时都会在心里为它打分甄别标准我问自己的问题强需求特征普遍性我的目标用户中有多大比例的人会遇到这个问题广大目标用户都存在痛点性如果不解决用户会有多痛他们愿意为此做什么痛感强烈用户愿意付费或付出代价解决高频性用户多久会遇到一次这个问题每天每周还是几乎遇不到每日或每周多次遇到只有通过了这道严格的安检门一个需求才有资格进入我分析流程的最后一步优先级排序需求的优先级经过了澄清和甄别我们现在手上拿到了一份真需求清单但现实是我们的研发资源人力时间金钱永远是有限的我们不可能同时满足所有人的所有需求因此优先级排序就是决定我们下一步应该先做什么再做什么的艺术和科学在我看来这是产品经理最重要的决策没有之一一个正确的优先级决策能让我们的产品价值最大化四象限法则当我面对一大堆需求感到千头万绪时我用来理清思路的第一个工具就是经典的四象限法则也叫艾森豪威尔矩阵我通过重要性和紧急性这两个维度快速地对需求进行一次粗分类重要且紧急这是最高优先级是那些着火了的需求比如线上支付功能出现重大服务器宕机我的原则是马上做调动一切资源立刻解决重要不紧急这是最能体现我们产品经理价值的区域这些需求关系到产品的长期发展和核心战略比如架构优化新功能探索用户体验的系统性提升它们没有明确的最容易被我们拖延我的原则是计划做必须主动地有计划地把它们排入我们的产品路线图紧急不重要这些是日常工作中最大的干扰比如某个领导临时想要一个不重要的数据某个非核心客户的一些小报怨它们看起来很急但对我们的核心目标贡献不大我的原则是授权做或快速应付看能否让团队其他人帮忙或者用最小的代价快速解决不重要不紧急这些是价值最低的需求我的原则是尽量不做或直接拒绝我们必须学会对这类需求说不优先级确定因素四象限法则帮我做了初步的划分尤其区分出了重要不紧急这个价值区域但当我有多个重要不紧急的需求时应该先做哪一个呢这时我需要引入更多的因素来综合判断性价比成本与价值这是我最看重的因素我会粗略地估算每个需求的投入产出比即这个需求需要耗费多少开发资源成本它又能带来多大的用户价值和商业价值我总是在寻找那些四两拨千斤的低成本高价值的需求符合战略规划这个需求是否与我们公司本季度或本年度的战略目标相符一个再酷的功能如果脱离了公司的战略主航道那它就是一个漂亮的干扰项我必须确保我们的开发资源始终服务于公司的战略大方向长短期价值我需要在短期见效和长期投资之间做出平衡有时为了提振士气或达成某个我会选择一个能快速上线马上看到效果的需求短期价值有时我也会选择一个用户完全感知不到的后台架构重构项目因为它能为我们未来几年的开发效率打下坚实的基础长期价值确定需求优先级的注意事项最后我想分享几点我在无数次优先级中总结出的经验和教训需求变动性我必须承认优先级不是一成不变的市场环境在变用户需求在变我们必须保持敏锐和灵活定期比如每两周或每月回顾和调整我们的产品路线图全局主动性我不能只做一个被动接收和排序需求的人我需要站在整个产品的视角主动地去规划那些能建立长期壁垒的系统性的项目而不是被各个业务方的紧急需求牵着鼻子走真实需求基础我的所有优先级判断都必须建立在我们在和节所做的澄清和甄别工作之上即必须是真实的有价值的需求绝不能因为某个领导声音大或者某个客户会吵就轻易提高他的需求的优先级需求选择搭配原则一个健康的版本迭代就像一顿营养均衡的饭我通常会搭配着来比如一个大的新功能几个重要的体验优化一些历史修复这样的版本既能给用户带来惊喜又能提升产品的稳定性还能让团队有成就感我将优先级排序的要点总结为下面这张表核心方法我的实践要点四象限法则快速分类重点投入重要不紧急的价值型需求综合因素判断在价值型需求中进一步权衡性价比战略价值和长短期收益注意事项保持灵活性立足真实需求主动规划并合理搭配版本内容到这里我们需求分析的三个步骤就全部完成了一个需求只有经过了澄清甄别和优先级排序这三堂会审才有资格最终出现在我们的产品路线图上最后我们附上产品需求文件模板供产品设计师快速完成需求分析的任务引用站外地址产品需求文档模板",isPost:!0,isHome:!1,isHighlightShrink:!1,isToc:!0,postUpdate:"2025-07-21 14:52:10",postMainColor:""}</script><noscript><style type="text/css">#nav{opacity:1}.justified-gallery img{opacity:1}#post-meta time,#recent-posts time{display:inline!important}</style></noscript><script>(win=>{
      win.saveToLocal = {
        set: (key, value, ttl) => {
          if (ttl === 0) return
          const now = Date.now()
          const expiry = now + ttl * 86400000
          const item = {
            value,
            expiry
          }
          localStorage.setItem(key, JSON.stringify(item))
        },
      
        get: key => {
          const itemStr = localStorage.getItem(key)
      
          if (!itemStr) {
            return undefined
          }
          const item = JSON.parse(itemStr)
          const now = Date.now()
      
          if (now > item.expiry) {
            localStorage.removeItem(key)
            return undefined
          }
          return item.value
        }
      }
    
      win.getScript = (url, attr = {}) => new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = url
        script.async = true
        script.onerror = reject
        script.onload = script.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          script.onload = script.onreadystatechange = null
          resolve()
        }

        Object.keys(attr).forEach(key => {
          script.setAttribute(key, attr[key])
        })

        document.head.appendChild(script)
      })
    
      win.getCSS = (url, id = false) => new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = url
        if (id) link.id = id
        link.onerror = reject
        link.onload = link.onreadystatechange = function() {
          const loadState = this.readyState
          if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
          link.onload = link.onreadystatechange = null
          resolve()
        }
        document.head.appendChild(link)
      })
    
      win.activateDarkMode = () => {
        document.documentElement.setAttribute('data-theme', 'dark')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#1e2022')
        }
      }
      win.activateLightMode = () => {
        document.documentElement.setAttribute('data-theme', 'light')
        if (document.querySelector('meta[name="theme-color"]') !== null) {
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#f0f5f9')
        }
      }
      const t = saveToLocal.get('theme')
    
          const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
          const isLightMode = window.matchMedia('(prefers-color-scheme: light)').matches
          const isNotSpecified = window.matchMedia('(prefers-color-scheme: no-preference)').matches
          const hasNoSupport = !isDarkMode && !isLightMode && !isNotSpecified

          if (t === undefined) {
            if (isLightMode) activateLightMode()
            else if (isDarkMode) activateDarkMode()
            else if (isNotSpecified || hasNoSupport) {
              const now = new Date()
              const hour = now.getHours()
              const isNight = hour <= 6 || hour >= 18
              isNight ? activateDarkMode() : activateLightMode()
            }
            window.matchMedia('(prefers-color-scheme: dark)').addListener(e => {
              if (saveToLocal.get('theme') === undefined) {
                e.matches ? activateDarkMode() : activateLightMode()
              }
            })
          } else if (t === 'light') activateLightMode()
          else activateDarkMode()
        
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
      const detectApple = () => {
        if(/iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
          document.documentElement.classList.add('apple')
        }
      }
      detectApple()
    })(window)</script><link rel="stylesheet" href="/css/font.css"><meta name="generator" content="Hexo 7.3.0"><link rel="alternate" href="/atom.xml" title="Prorise - 分享技术与实战经验" type="application/atom+xml"></head><body data-type="anzhiyu"><div id="web_bg"></div><div id="an_music_bg"></div><div id="loading-box" onclick="document.getElementById(&quot;loading-box&quot;).classList.add(&quot;loaded&quot;)"><div class="loading-bg"><img class="loading-img nolazyload" alt="加载头像" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png"><div class="loading-image-dot"></div></div></div><script>const preloader = {
  endLoading: () => {
    document.getElementById('loading-box').classList.add("loaded");
  },
  initLoading: () => {
    document.getElementById('loading-box').classList.remove("loaded")
  }
}
window.addEventListener('load',()=> { preloader.endLoading() })
setTimeout(function(){preloader.endLoading();},10000)

if (true) {
  document.addEventListener('pjax:send', () => { preloader.initLoading() })
  document.addEventListener('pjax:complete', () => { preloader.endLoading() })
}</script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.10/progress_bar/progress_bar.css"><script async="" src="https://cdn.cbd.int/pace-js@1.2.4/pace.min.js" data-pace-options="{ &quot;restartOnRequestAfter&quot;:false,&quot;eventLag&quot;:false}"></script><div class="post" id="body-wrap"><div class="doc-sidebar" id="doc-sidebar"><nav class="doc-sidebar-nav"><div class="doc-toc-content"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E7%AB%A0%EF%BC%9A%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-text">第三章：需求分析</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#3-1-%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%E7%9A%84%E5%AE%9A%E4%B9%89"><span class="toc-text">3.1 需求分析的定义</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-1-%E4%BB%80%E4%B9%88%E6%98%AF%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-text">3.1.1 什么是需求分析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%E7%9A%84%E6%9C%AC%E8%B4%A8"><span class="toc-text">1. 需求分析的本质</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%8E%9F%E5%A7%8B%E9%9C%80%E6%B1%82%E4%B8%8E%E4%BA%A7%E5%93%81%E9%9C%80%E6%B1%82%E7%9A%84%E8%BD%AC%E6%8D%A2"><span class="toc-text">2. 原始需求与产品需求的转换</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-2-%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%E7%9A%84%E6%97%B6%E6%9C%BA"><span class="toc-text">3.2 需求分析的时机</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-1-%E6%94%B6%E9%9B%86%E9%9C%80%E6%B1%82%E6%97%B6%E7%9A%84%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-text">3.2.1 收集需求时的需求分析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%9B%B4%E6%8E%A5%E6%B2%9F%E9%80%9A%E8%BF%9B%E8%A1%8C%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-text">1. 直接沟通进行需求分析</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%A1%88%E4%BE%8B%EF%BC%9A%E7%89%A9%E6%B5%81%E5%85%AC%E5%8F%B8%E6%97%B6%E6%95%88%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-text">2. 案例：物流公司时效需求分析</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-2-%E6%94%B6%E9%9B%86%E9%9C%80%E6%B1%82%E5%90%8E%E7%9A%84%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-text">3.2.2 收集需求后的需求分析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%9B%86%E4%B8%AD%E5%88%86%E6%9E%90%E9%9C%80%E6%B1%82"><span class="toc-text">1. 集中分析需求</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%A1%88%E4%BE%8B%EF%BC%9A%E7%94%A8%E6%88%B7%E5%8F%8D%E9%A6%88%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-text">2. 案例：用户反馈需求分析</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-3-%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%E7%9A%84%E6%AD%A5%E9%AA%A4"><span class="toc-text">3.3 需求分析的步骤</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-1-%E9%9C%80%E6%B1%82%E6%BE%84%E6%B8%85"><span class="toc-text">3.3.1 需求澄清</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-WHY%EF%BC%88%E4%B8%BA%E4%BB%80%E4%B9%88%EF%BC%89"><span class="toc-text">1. WHY（为什么）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-WHO%EF%BC%88%E7%94%A8%E6%88%B7%E6%98%AF%E8%B0%81%EF%BC%89"><span class="toc-text">2. WHO（用户是谁）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-WHAT%EF%BC%88%E4%BB%80%E4%B9%88%E9%97%AE%E9%A2%98%EF%BC%89"><span class="toc-text">3. WHAT（什么问题）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-HOW%EF%BC%88%E7%8E%B0%E7%8A%B6%E5%A6%82%E4%BD%95%EF%BC%89"><span class="toc-text">4. HOW（现状如何）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-2-%E9%9C%80%E6%B1%82%E7%94%84%E5%88%AB"><span class="toc-text">3.3.2 需求甄别</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%9C%80%E6%B1%82%E7%9C%9F%E4%BC%AA%E5%88%A4%E5%AE%9A%E7%9A%84%E4%B8%89%E4%B8%AA%E6%A0%87%E5%87%86"><span class="toc-text">1. 需求真伪判定的三个标准</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E4%BC%AA%E9%9C%80%E6%B1%82%E5%88%A4%E6%96%AD%E6%A1%88%E4%BE%8B%E5%88%86%E6%9E%90"><span class="toc-text">2. 伪需求判断案例分析</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-3-%E9%9C%80%E6%B1%82%E7%9A%84%E4%BC%98%E5%85%88%E7%BA%A7"><span class="toc-text">3.3.3 需求的优先级</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%9B%9B%E8%B1%A1%E9%99%90%E6%B3%95%E5%88%99"><span class="toc-text">1. 四象限法则</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E4%BC%98%E5%85%88%E7%BA%A7%E7%A1%AE%E5%AE%9A%E5%9B%A0%E7%B4%A0"><span class="toc-text">2. 优先级确定因素</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%A1%AE%E5%AE%9A%E9%9C%80%E6%B1%82%E4%BC%98%E5%85%88%E7%BA%A7%E7%9A%84%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9"><span class="toc-text">3. 确定需求优先级的注意事项</span></a></li></ol></li></ol></li></ol></li></ol></div></nav></div><header class="not-top-img" id="page-header"><nav id="nav"><div id="nav-group"><span id="blog_name"><div class="back-home-button"><i class="anzhiyufont anzhiyu-icon-grip-vertical"></i><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div></div><a id="site-name" href="/" accesskey="h"><div class="title">Prorise - 分享技术与实战经验</div><i class="anzhiyufont anzhiyu-icon-house-chimney"></i></a></span><div class="mask-name-container"><div id="name-container"><a id="page-name" href="javascript:anzhiyu.scrollToDest(0, 500)" rel="external nofollow noreferrer">PAGE_NAME</a></div></div><div id="menus"><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div></div><div id="nav-right"><div class="nav-button" id="randomPost_button"><a class="site-page" onclick="toRandomPost()" title="随机前往一个文章" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-dice"></i></a></div><div class="nav-button" id="background-change-button"><a class="site-page" onclick="toggleWinbox()" title="切换背景" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fas fa-palette"></i></a></div><div class="nav-button" id="search-button"><a class="site-page social-icon search" href="javascript:void(0);" rel="external nofollow noreferrer" title="搜索🔍" accesskey="s"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span> 搜索</span></a></div><input id="center-console" type="checkbox"><label class="widget" for="center-console" title="中控台" onclick="anzhiyu.switchConsole()"><i class="left"></i><i class="widget center"></i><i class="widget right"></i></label><div id="console"><div class="console-card-group-reward"><ul class="reward-all console-card"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="微信" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" alt="支付宝" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png"></a><div class="post-qr-code-desc">支付宝</div></li></ul></div><div class="console-card-group"><div class="console-card-group-left"><div class="console-card" id="card-newest-comments"><div class="card-content"><div class="author-content-item-tips">互动</div><span class="author-content-item-title">最新评论</span></div><div class="aside-list"><span>正在加载中...</span></div></div></div><div class="console-card-group-right"><div class="console-card tags"><div class="card-content"><div class="author-content-item-tips">兴趣点</div><span class="author-content-item-title">寻找你感兴趣的领域</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#500336">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:1.05rem;color:#3d87bb">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:1.05rem;color:#5c8a2b">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:1.05rem;color:#988467">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:1.05rem;color:#2ab75e">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#4d011d">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:1.05rem;color:#be5f01">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div class="console-card history"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-box-archiv"></i><span>文章</span></div><div class="card-archives"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-archive"></i><span>归档</span></div><ul class="card-archive-list"><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/07/"><span class="card-archive-list-date">七月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">58</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/06/"><span class="card-archive-list-date">六月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">4</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/05/"><span class="card-archive-list-date">五月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">9</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/04/"><span class="card-archive-list-date">四月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">22</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/03/"><span class="card-archive-list-date">三月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">11</span><span>篇</span></div></a></li><li class="card-archive-list-item"><a class="card-archive-list-link" href="/archives/2025/01/"><span class="card-archive-list-date">一月 2025</span><div class="card-archive-list-count-group"><span class="card-archive-list-count">5</span><span>篇</span></div></a></li></ul></div></div></div></div><div class="button-group"><div class="console-btn-item"><a class="darkmode_switchbutton" title="显示模式切换" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-moon"></i></a></div><div class="console-btn-item" id="consoleHideAside" onclick="anzhiyu.hideAsideBtn()" title="边栏显示控制"><a class="asideSwitch"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></a></div><div class="console-btn-item on" id="consoleCommentBarrage" onclick="anzhiyu.switchCommentBarrage()" title="热评开关"><a class="commentBarrage"><i class="anzhiyufont anzhiyu-icon-message"></i></a></div><div class="console-btn-item" id="consoleMusic" onclick="anzhiyu.musicToggle()" title="音乐开关"><a class="music-switch"><i class="anzhiyufont anzhiyu-icon-music"></i></a></div><div class="console-btn-item" id="consoleAskAIMode" onclick="rm.toggleAskAIMode()" title="AI询问模式切换"><a class="askAIMode-switch"><i class="fa-solid fa-robot"></i></a></div></div><div class="console-mask" onclick="anzhiyu.hideConsole()" href="javascript:void(0);" rel="external nofollow noreferrer"></div></div><div class="nav-button" id="nav-totop"><a class="totopbtn" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i><span id="percent" onclick="anzhiyu.scrollToDest(0,500)">0</span></a></div><div id="toggle-menu"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer" title="切换"><i class="anzhiyufont anzhiyu-icon-bars"></i></a></div></div></div></nav></header><main id="blog-container"><div class="layout" id="content-inner"><div id="post"><div id="post-info"><div id="post-firstinfo"><div class="meta-firstline"><a class="post-meta-original">原创</a><span class="post-meta-categories"><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-inbox post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a></span><span class="article-meta tags"><a class="article-meta__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url"> <span><i class="anzhiyufont anzhiyu-icon-hashtag"></i>产品经理教程</span></a></span></div></div><h1 class="post-title" itemprop="name headline">产品经理入门（三）：第三章：需求分析</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="anzhiyufont anzhiyu-icon-calendar-days post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" itemprop="dateCreated datePublished" datetime="2025-07-20T10:13:45.000Z" title="发表于 2025-07-20 18:13:45">2025-07-20</time><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-history post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" itemprop="dateCreated datePublished" datetime="2025-07-21T06:52:10.977Z" title="更新于 2025-07-21 14:52:10">2025-07-21</time></span></div><div class="meta-secondline"><span class="post-meta-separator"></span><span class="post-meta-wordcount"><i class="anzhiyufont anzhiyu-icon-file-word post-meta-icon" title="文章字数"></i><span class="post-meta-label" title="文章字数">字数总计:</span><span class="word-count" title="文章字数">5.9k</span><span class="post-meta-separator"></span><i class="anzhiyufont anzhiyu-icon-clock post-meta-icon" title="阅读时长"></i><span class="post-meta-label" title="阅读时长">阅读时长:</span><span>17分钟</span></span><span class="post-meta-separator"></span><span data-flag-title="产品经理入门（三）：第三章：需求分析"><i class="anzhiyufont anzhiyu-icon-fw-eye post-meta-icon"></i><span class="post-meta-label" title="阅读量">阅读量:</span><span id="twikoo_visitors" title="访问量"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></span><span class="post-meta-separator"> </span><span class="post-meta-position" title="作者IP属地为广东"><i class="anzhiyufont anzhiyu-icon-location-dot"></i>广东</span><span class="post-meta-separator"></span><span class="post-meta-commentcount"><i class="anzhiyufont anzhiyu-icon-comments post-meta-icon"></i><span class="post-meta-label">评论数:</span><a href="/posts/59297.html#post-comment" tabindex="-1"><span id="twikoo-count"><i class="anzhiyufont anzhiyu-icon-spinner anzhiyu-spin"></i></span></a></span></div></div></div><div class="post-ai-description"><div class="ai-title"><i class="anzhiyufont anzhiyu-icon-bilibili"></i><div class="ai-title-text">AI-摘要</div><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i><i class="anzhiyufont anzhiyu-icon-circle-dot" title="朗读摘要"></i><div id="ai-tag">Tianli GPT</div></div><div class="ai-explanation">AI初始化中...</div><div class="ai-btn-box"><div class="ai-btn-item">介绍自己 🙈</div><div class="ai-btn-item">生成本文简介 👋</div><div class="ai-btn-item">推荐相关文章 📖</div><div class="ai-btn-item">前往主页 🏠</div><div class="ai-btn-item" id="go-tianli-blog">前往爱发电购买</div></div><script data-pjax="" src="/js/anzhiyu/ai_abstract.js"></script></div><article class="post-content" id="article-container" itemscope="" itemtype="https://prorise666.site/posts/59297.html"><header><a class="post-meta-categories" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/" itemprop="url">产品经理</a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" tabindex="-1" itemprop="url">产品经理教程</a><h1 id="CrawlerTitle" itemprop="name headline">产品经理入门（三）：第三章：需求分析</h1><span itemprop="author" itemscope="" itemtype="http://schema.org/Person">Prorise</span><time itemprop="dateCreated datePublished" datetime="2025-07-20T10:13:45.000Z" title="发表于 2025-07-20 18:13:45">2025-07-20</time><time itemprop="dateCreated datePublished" datetime="2025-07-21T06:52:10.977Z" title="更新于 2025-07-21 14:52:10">2025-07-21</time></header><div id="postchat_postcontent"><h1 id="第三章：需求分析"><a href="#第三章：需求分析" class="headerlink" title="第三章：需求分析"></a>第三章：需求分析</h1><p>在上一章，我们学会了如何像一名侦探一样，通过各种手段去“收集”需求的线索。但这些线索往往是零散的、模糊的，甚至带有误导性。如果我们不加处理就直接采纳，很可能会做出南辕北辙的产品。</p><p>因此，<strong>需求分析</strong>就是我们作为产品经理，对这些原始线索进行“勘察、推理、定案”的关键过程。这是整个产品工作中，最能体现我们逻辑思辨和深度思考能力的核心环节。</p><h2 id="3-1-需求分析的定义"><a href="#3-1-需求分析的定义" class="headerlink" title="3.1 需求分析的定义"></a>3.1 需求分析的定义</h2><p>我们先从最根本的问题开始：到底什么才叫“需求分析”,我们可以来看一个案例：</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719112130263.png" alt="image-20250719112130263"></p><p>上面这个案例，我们不难看出，当我们接到需求后，没有去了解需求的背景，深挖需求。很容易导致我们做出来的方案是不符合要求，导致大量的人力、时间、资源的浪费，我们应当去拆解用户的需求，如下：</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719112244196.png" alt="image-20250719112244196"></p><h3 id="3-1-1-什么是需求分析"><a href="#3-1-1-什么是需求分析" class="headerlink" title="3.1.1 什么是需求分析"></a>3.1.1 什么是需求分析</h3><p>在我看来，要理解需求分析，我们需要抓住它的“本质”和“过程”。</p><h4 id="1-需求分析的本质"><a href="#1-需求分析的本质" class="headerlink" title="1. 需求分析的本质"></a>1. 需求分析的本质</h4><p>我理解的需求分析，其本质是一个**“解构”与“重构”**的过程。</p><ul><li><strong>解构</strong>：是把用户提出的原始需求（无论是问题、目的还是方案）打碎、拆解，深入挖掘其背后真正的动机和未被满足的痛点。</li><li><strong>重构</strong>：是在我们完全理解了本质痛点之后，重新组合信息，设计出一个真正能有效解决该问题的、合理的、可落地的产品解决方案。</li></ul><p>简单来说，就是<strong>先想“为什么”，再想“怎么办”</strong>。</p><h4 id="2-原始需求与产品需求的转换"><a href="#2-原始需求与产品需求的转换" class="headerlink" title="2. 原始需求与产品需求的转换"></a>2. 原始需求与产品需求的转换</h4><p>基于这个本质，我给需求分析一个最直接的定义：<strong>所谓需求分析，就是将“用户的原始需求”，转化为“可执行的产品需求”的全过程。</strong></p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719112050347.png" alt="image-20250719112050347"></p><p>我们再来回顾一下这两个概念：</p><ul><li><strong>原始需求</strong>：是用户直接给我们的东西，是“用户想要什么”。它可能是“我想要个一键下单按钮”，也可能是“我希望能快速找到便宜的外卖”。它是我们工作的<strong>输入</strong>。</li><li><strong>产品需求</strong>：是我们经过分析、甄别、权衡之后，最终决定要做的东西，是“我们应该为用户做什么”。它是一个包含了用户场景、核心问题、解决方案和验收标准的完整方案。它是我们工作的<strong>输出</strong>。</li></ul><p>所以，需求分析就是连接这两者的桥梁，是那个至关重要的“转化”步骤。没有这个转化过程，我们就只是一个需求的“传声筒”，而不是一个创造价值的“产品经理”。</p><p>为了让这个区别更清晰，我总结了下面的对比表：</p><table><thead><tr><th align="left"><strong>对比维度</strong></th><th align="left"><strong>原始需求</strong></th><th align="left"><strong>产品需求</strong></th></tr></thead><tbody><tr><td align="left"><strong>来源</strong></td><td align="left">用户直接表达</td><td align="left">产品经理分析转化</td></tr><tr><td align="left"><strong>形式</strong></td><td align="left">通常是模糊、零散、未经验证的</td><td align="left">是清晰、结构化、经过验证的</td></tr><tr><td align="left"><strong>关注点</strong></td><td align="left">“我想要一个XX功能”（what）</td><td align="left">“为了解决用户XX问题，我们需要XX方案”（why &amp; how）</td></tr><tr><td align="left"><strong>我的角色</strong></td><td align="left">聆听者、记录员</td><td align="left">分析师、决策者、方案设计师</td></tr></tbody></table><hr><h2 id="3-2-需求分析的时机"><a href="#3-2-需求分析的时机" class="headerlink" title="3.2 需求分析的时机"></a>3.2 需求分析的时机</h2><p>在我看来，需求分析并不是一个孤立的、只在特定阶段才进行的“仪式”。它应该像呼吸一样，贯穿我们产品工作的始终。不过，从实践上，我主要会在两个关键的时间点，以不同的方式来开展这项工作：<strong>1️⃣收集需求时</strong> 和 <strong>2️⃣收集需求后</strong>。</p><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719113814345.png" alt="image-20250719113814345"></p><h3 id="3-2-1-收集需求时的需求分析"><a href="#3-2-1-收集需求时的需求分析" class="headerlink" title="3.2.1 收集需求时的需求分析"></a>3.2.1 收集需求时的需求分析</h3><h4 id="1-直接沟通进行需求分析"><a href="#1-直接沟通进行需求分析" class="headerlink" title="1. 直接沟通进行需求分析"></a>1. 直接沟通进行需求分析</h4><p>我把这个过程称为“实时分析”。当我在进行用户访谈、或是与业务方开会时，我绝不只做一个被动的记录员。我的大脑会高速运转，对接收到的每一个信息点，当场进行第一轮的分析、澄清和追问。</p><p>这种方式的好处是，我可以在信息最新鲜、上下文最完整的时刻，抓住机会深挖下去，及时地探究用户“为什么”这么想，而不是等会议结束、记忆模糊后，再自己去猜测。</p><h4 id="2-案例：物流公司时效需求分析"><a href="#2-案例：物流公司时效需求分析" class="headerlink" title="2. 案例：物流公司时效需求分析"></a>2. 案例：物流公司时效需求分析</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719113104681.png" alt="image-20250719113104681"></p><p>这个案例能很好地说明实时分析的价值。</p><ul><li><strong>场景</strong>：我作为一家物流平台的PM，正在访谈一位重要的企业客户。</li><li><strong>原始需求</strong>：客户告诉我：“我希望你们的平台能提供一个功能，让我能在地图上看到我们货物的运输车辆的实时GPS位置。”</li><li><strong>我的实时分析与追问</strong>：听到这个“方案”后，我没有立刻记下来就完事，而是当场追问：“这个想法很有意思。可以和我聊聊吗，您为什么需要看到车辆的实时位置？这个功能能帮您解决什么具体问题呢？”</li><li><strong>挖掘出的本质目的</strong>：通过追问，我发现，客户并不真的关心车辆在哪条路上，他关心的是“货物到底什么时候能到”。因为他的下游客户总是在催问他送达时间，他需要一个准确的预期，来安抚客户、安排接货。</li><li><strong>转化后的产品需求</strong>：因此，我将这个需求从“提供车辆实时定位”，转化为了“在订单详情页提供一个精准、动态的<strong>预计送达时间（ETA）</strong>，并支持在临近送达时，向收货人发送提醒”。后者显然是价值高得多、也更贴近问题本质的解决方案。</li></ul><h3 id="3-2-2-收集需求后的需求分析"><a href="#3-2-2-收集需求后的需求分析" class="headerlink" title="3.2.2 收集需求后的需求分析"></a>3.2.2 收集需求后的需求分析</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719113442021.png" alt="image-20250719113442021"></p><h4 id="1-集中分析需求"><a href="#1-集中分析需求" class="headerlink" title="1. 集中分析需求"></a>1. 集中分析需求</h4><p>当然，光有实时分析是远远不够的。在完成了当周的用户访谈、回收了所有的调查问卷、或者整理完用户反馈后，我会专门安排时间，进行一次“集中分析”。</p><p>在这个阶段，我会把所有零散的、原始的需求信息汇总到一起，像一个侦探把所有线索都钉在白板上一样。我会开始寻找它们之间的关联、共性，试图发现那个隐藏在多个表象之下的、更宏观的、系统性的问题或机会。</p><h4 id="2-案例：用户反馈需求分析"><a href="#2-案例：用户反馈需求分析" class="headerlink" title="2. 案例：用户反馈需求分析"></a>2. 案例：用户反馈需求分析</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719113602157.png" alt="image-20250719113602157"></p><ul><li><strong>场景</strong>：我作为一款社交App的PM，每周都会定期整理来自应用商店、社区的用户反馈。</li><li><strong>原始需求（集合）</strong>：我看到了大量看似独立的用户抱怨。<ul><li>用户A说：“你们的视频加载太慢了！”；</li><li>用户B说：“视频看着看着就卡住了”；</li><li>用户C说：“上传一个视频要等半天”。</li></ul></li><li><strong>我的集中分析</strong>：如果我只看单条反馈，可能会分别给技术团队提“加载慢”、“播放卡”、“上传慢”这三个独立的、零散的问题。但当我把它们放在一起集中分析时，我发现了一个共性——<strong>我们产品的整体视频处理和分发链路，可能存在系统性的性能瓶颈</strong>。</li><li><strong>转化后的产品需求</strong>：基于这个判断，我最终定义的产品需求，就不是一个个小修小补，而是一个系统性的优化项目：“<strong>优化视频处理架构，提升视频在不同网络环境下的加载和播放流畅度，将平均起播时间缩短30%</strong>”。这个需求，显然比解决单个用户的抱怨要有价值得多。</li></ul><hr><p>总而言之，这两种时机的分析，各有侧重，缺一不可。我将它们的区别总结如下：</p><table><thead><tr><th align="left"><strong>分析时机</strong></th><th align="left"><strong>核心特点</strong></th><th align="left"><strong>我的目标</strong></th></tr></thead><tbody><tr><td align="left"><strong>收集时（实时分析）</strong></td><td align="left">互动性强、有上下文、聚焦于个体</td><td align="left">快速探究单个原始需求背后的“<strong>为什么</strong>”。</td></tr><tr><td align="left"><strong>收集后（集中分析）</strong></td><td align="left">宏观、全面、寻找关联</td><td align="left">发现多个原始需求背后共同指向的“<strong>系统性问题或机会</strong>”。</td></tr></tbody></table><hr><h2 id="3-3-需求分析的步骤"><a href="#3-3-需求分析的步骤" class="headerlink" title="3.3 需求分析的步骤"></a>3.3 需求分析的步骤</h2><p>到目前为止，我们已经定义了需求分析，也明确了进行分析的时机。那具体到一项原始需求，我究竟是如何一步步把它“解剖”清楚的呢？</p><p>在我的工作流中，这个过程被严格地划分为三个步骤：</p><ul><li><strong>第一步：需求澄清；</strong></li><li><strong>第二步 ：需求甄别；</strong></li><li><strong>第三步：需求的优先级排序</strong>。这三个步骤，层层递进，缺一不可。</li></ul><p>我们先来看第一步，也是所有分析的基础——需求澄清。</p><h3 id="3-3-1-需求澄清"><a href="#3-3-1-需求澄清" class="headerlink" title="3.3.1 需求澄清"></a>3.3.1 需求澄清</h3><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719130637070.png" alt="image-20250719130637070"></p><p>每当一个原始需求摆在我面前时，我从不急于判断它的好坏、真伪，我做的第一件事，永远是：<strong>把它弄清楚</strong>。</p><p>需求澄清，对就我像一名侦探在审视案发现场，我需要通过反复地询问和探究，把这个需求的所有模糊、不确定的信息，都变得清晰、明确。为了系统化地做到这一点，我随身携带着一个强大的思维工具——<strong>“WHY-WHO-WHAT-HOW”框架</strong>。</p><p>我们以外卖平台的案例来逐一拆解这四个关键问题：假设我收到的一个原始需求是“我想要一个‘智能营养餐’功能”。</p><h4 id="1-WHY（为什么）"><a href="#1-WHY（为什么）" class="headerlink" title="1. WHY（为什么）"></a>1. WHY（为什么）</h4><p>这是我首先要问的问题：<strong>我们为什么要做这个需求？</strong> 它能为用户带来什么核心价值？它又能为我们的公司带来什么商业价值？</p><ul><li>对于“智能营养餐”这个需求，WHY可能是：<ul><li><strong>用户价值</strong>：帮助对健康有追求的用户，解决“不知道怎么吃才健康”以及“计算热量和营养成分很麻烦”的痛点。</li><li><strong>商业价值</strong>：通过差异化的健康服务，吸引高价值用户，提升平台的品牌形象和用户粘性。</li></ul></li></ul><p>如果一个需求的“WHY”我都答不出来，那它基本上就可以被直接否决了。</p><h4 id="2-WHO（用户是谁）"><a href="#2-WHO（用户是谁）" class="headerlink" title="2. WHO（用户是谁）"></a>2. WHO（用户是谁）</h4><p>第二个问题：<strong>这个需求我们是为谁而做的？</strong> 我需要清晰地描绘出目标用户的画像。</p><ul><li>“智能营养餐”的WHO，绝不是“所有用户”。它的核心用户画像可能是：<ul><li>一线城市的年轻白领；</li><li>有健身习惯或正在减脂的人群；</li><li>关注生活品质、愿意为健康付出一定溢价的用户。</li></ul></li><li>明确WHO，能帮助我在后续的设计中，始终围绕着这群核心用户的审美和习惯来进行。</li></ul><h4 id="3-WHAT（什么问题）"><a href="#3-WHAT（什么问题）" class="headerlink" title="3. WHAT（什么问题）"></a>3. WHAT（什么问题）</h4><p>第三个问题：<strong>我们具体要解决一个什么问题？</strong> 我需要把用户的痛点用清晰、无歧义的语言描述出来。</p><ul><li>对于“智能营养餐”，WHAT不是“用户想要一个功能”，而是要解决用户的本质问题：<ul><li>“用户因缺乏专业知识，<strong>难以判断</strong>不同外卖的营养成分和热量是否满足自己的健康需求。”</li><li>“用户因工作繁忙，<strong>没有时间</strong>去自己计算和搭配每日的营养摄入。”</li></ul></li></ul><h4 id="4-HOW（现状如何）"><a href="#4-HOW（现状如何）" class="headerlink" title="4. HOW（现状如何）"></a>4. HOW（现状如何）</h4><p>最后一个问题：<strong>用户现在是如何解决这个问题的？</strong> 了解用户当前的“野生”解决方案，能帮我判断痛点的强度，并为我的设计提供灵感。</p><ul><li>对于“智能营养餐”这个需求，用户当下的HOW可能是：<ul><li>自己去网上搜索食物热量表，估算着点餐。</li><li>下载专门的健康App，手动记录自己点的外卖，再查看营养分析。</li><li>干脆放弃点外卖，选择自己做饭或吃价格昂贵的成品健康餐。</li></ul></li><li>这些笨拙、耗时、昂贵的现状，恰恰反证了我们这个新功能潜在的巨大价值。</li></ul><hr><p>我将这个澄清框架总结为一张表，它是我分析任何需求前的“必填清单”：</p><table><thead><tr><th align="left"><strong>澄清问题</strong></th><th align="left"><strong>我的核心关注点</strong></th><th align="left"><strong>案例应用（智能营养餐）</strong></th></tr></thead><tbody><tr><td align="left"><strong>WHY (为什么做)</strong></td><td align="left">探究需求的<strong>商业与用户价值</strong></td><td align="left">满足健康需求，提升用户粘性与品牌价值。</td></tr><tr><td align="left"><strong>WHO (为谁而做)</strong></td><td align="left">定义精准的<strong>目标用户画像</strong></td><td align="left">追求健康的都市白领、健身人群。</td></tr><tr><td align="left"><strong>WHAT (解决什么问题)</strong></td><td align="left">识别并定义用户的<strong>本质痛点</strong></td><td align="left">解决用户“不懂如何健康搭配”和“没时间计算营养”的问题。</td></tr><tr><td align="left"><strong>HOW (现状如何)</strong></td><td align="left">了解用户当前的<strong>解决方案或替代方案</strong></td><td align="left">用户目前通过手动查询、使用其他App等方式，过程繁琐且不准确。</td></tr></tbody></table><p>只有把这四个问题都回答清楚了，我才会认为，这个需求已经被我“澄清”了。接下来，我才会进入分析的第二步：需求甄别。</p><h3 id="3-3-2-需求甄别"><a href="#3-3-2-需求甄别" class="headerlink" title="3.3.2 需求甄别"></a>3.3.2 需求甄别</h3><p>我把需求分析的第二步称为**“需求甄别”**，或者叫“真伪需求判定”。我的核心任务，是判断这个被澄清后的需求，到底是一个能为多数用户创造巨大价值的“真需求”，还是一个看似有理、实则虚幻的“伪需求”。</p><p>投入资源去做一个伪需求，是我认为对团队最大的浪费。</p><h4 id="1-需求真伪判定的三个标准"><a href="#1-需求真伪判定的三个标准" class="headerlink" title="1. 需求真伪判定的三个标准"></a>1. 需求真伪判定的三个标准</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719131258692.png" alt="image-20250719131258692"></p><p>为了避免凭感觉做判断，我建立了一套自己的“甄别滤网”，它由三个核心标准构成。一个有价值的真需求，通常需要至少满足其中两个，甚至是全部三个标准。</p><ul><li><p><strong>普遍性 (Universality)</strong><br>这个问题是否具有广泛的代表性？在我的目标用户群体中，遇到这个问题的用户规模有多大？是只有一小撮人有这个特殊的毛病，还是绝大多数用户共同的困扰？我追求的，是能让尽可能多的目标用户受益的需求。</p></li><li><p><strong>痛点性 (Painfulness)</strong><br>这个问题给用户带来的“痛感”有多强？如果这个问题不被解决，用户是否会感到非常沮丧、烦躁，甚至愿意付费来解决它？我常用“是‘痒点’还是‘痛点’”来区分。挠痒痒的需求可做可不做，但治病止痛的需求，用户才会真正买单。</p></li><li><p><strong>高频性 (High Frequency)</strong><br>用户遇到这个问题的频率有多高？是每天、每周都会遇到，还是每年甚至几年才会遇到一次？高频的需求，意味着我们的解决方案能被用户频繁使用，这对于培养用户习惯、提升产品粘性至关重要。</p></li></ul><hr><h4 id="2-伪需求判断案例分析"><a href="#2-伪需求判断案例分析" class="headerlink" title="2. 伪需求判断案例分析"></a>2. 伪需求判断案例分析</h4><p>我们来看一个经典的案例，学习如何运用这三个标准来甄别伪需求。</p><ul><li><p><strong>场景</strong>：我是一家“母婴社区”App的产品经理，我们的核心业务是为新手爸妈提供育儿知识交流和社交的平台。</p></li><li><p><strong>原始需求</strong>：社区里有一位非常活跃且有影响力的用户，她强烈建议我们增加一个“儿童防走丢手表”的功能。她的设想是，用户可以在我们的App里购买一款儿童手表，并随时查看孩子的位置。</p></li><li><p><strong>我的甄别过程</strong>：这个需求听起来非常“刚需”，因为儿童安全是天大的事。但我们必须冷静地用三个标准来审视它。</p><ol><li><strong>普遍性分析</strong>：所有家长都关心孩子安全，这是一个普遍的情感。但是，“需要通过一个App内嵌的硬件功能来随时追踪孩子位置”，这还是一个普遍的需求吗？我的判断是，只有其中一部分极度焦虑的家长才会有此强需求。因此，需求的<strong>普遍性较低</strong>。</li><li><strong>痛点性分析</strong>：“孩子走丢”这个场景，痛不痛？当然痛，这是天塌下来的痛。所以，<strong>痛点性极高</strong>。</li><li><strong>高频性分析</strong>：一个正常的孩子，在父母的看护下，“走丢”这件事发生的频率有多高？谢天谢地，这是一个极低极低的概率。所以，需求的<strong>高频性极低</strong>。</li></ol></li><li><p><strong>我的最终结论</strong>：这是一个“<strong>低频、非普适的超强痛点</strong>”需求。更重要的是，它涉及到硬件、供应链、地图服务等，这与我们“社区内容”的核心能力相去甚远。因此，尽管它听起来很有吸引力，但我会判定，对于我们这个母婴社区App而言，这是一个“<strong>伪需求</strong>”。它是一个真实存在的问题，但它不应该由我们这个产品来解决。</p></li></ul><hr><p>我将这三个标准总结为一张自检表，每当我分析需求时，都会在心里为它打分：</p><table><thead><tr><th align="left"><strong>甄别标准</strong></th><th align="left"><strong>我问自己的问题</strong></th><th align="left"><strong>强需求特征</strong></th></tr></thead><tbody><tr><td align="left"><strong>普遍性</strong></td><td align="left">我的目标用户中，有多大比例的人会遇到这个问题？</td><td align="left">广大目标用户都存在</td></tr><tr><td align="left"><strong>痛点性</strong></td><td align="left">如果不解决，用户会有多“痛”？他们愿意为此做什么？</td><td align="left">痛感强烈，用户愿意付费或付出代价解决</td></tr><tr><td align="left"><strong>高频性</strong></td><td align="left">用户多久会遇到一次这个问题？每天？每周？还是几乎遇不到？</td><td align="left">每日或每周多次遇到</td></tr></tbody></table><p>只有通过了这道严格的“安检门”，一个需求才有资格进入我分析流程的最后一步：优先级排序。</p><hr><h3 id="3-3-3-需求的优先级"><a href="#3-3-3-需求的优先级" class="headerlink" title="3.3.3 需求的优先级"></a>3.3.3 需求的优先级</h3><p>经过了“澄清”和“甄别”，我们现在手上拿到了一份“真需求”清单。但现实是，我们的研发资源（人力、时间、金钱）永远是有限的。我们不可能同时满足所有人的所有需求。</p><p>因此，<strong>优先级排序</strong>，就是决定“<strong>我们下一步应该先做什么，再做什么</strong>”的艺术和科学。在我看来，这是产品经理最重要的决策，没有之一。一个正确的优先级决策，能让我们的产品价值最大化。</p><h4 id="1-四象限法则"><a href="#1-四象限法则" class="headerlink" title="1. 四象限法则"></a>1. 四象限法则</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719132049714.png" alt="image-20250719132049714"></p><p>当我面对一大堆需求，感到千头万绪时，我用来理清思路的第一个工具，就是经典的**“四象限法则”**（也叫艾森豪威尔矩阵）。我通过“重要性”和“紧急性”这两个维度，快速地对需求进行一次粗分类。</p><ul><li><strong>重要且紧急</strong>：这是最高优先级，是那些“着火了”的需求。比如：线上支付功能出现重大Bug、服务器宕机。我的原则是：<strong>马上做</strong>，调动一切资源，立刻解决。</li><li><strong>重要不紧急</strong>：这是最能体现我们产品经理价值的区域。这些需求关系到产品的长期发展和核心战略，比如：架构优化、新功能探索、用户体验的系统性提升。它们没有明确的deadline，最容易被我们拖延。我的原则是：<strong>计划做</strong>，必须主动地、有计划地把它们排入我们的产品路线图。</li><li><strong>紧急不重要</strong>：这些是日常工作中最大的干扰。比如：某个领导临时想要一个不重要的数据、某个非核心客户的一些小报怨。它们看起来很急，但对我们的核心目标贡献不大。我的原则是：<strong>授权做或快速应付</strong>，看能否让团队其他人帮忙，或者用最小的代价快速解决。</li><li><strong>不重要不紧急</strong>：这些是价值最低的需求。我的原则是：<strong>尽量不做或直接拒绝</strong>。我们必须学会对这类需求说“不”。</li></ul><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719132245046.png" alt="image-20250719132245046"></p><h4 id="2-优先级确定因素"><a href="#2-优先级确定因素" class="headerlink" title="2. 优先级确定因素"></a>2. 优先级确定因素</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719132330097.png" alt="image-20250719132330097"></p><p>四象限法则帮我做了初步的划分，尤其区分出了“重要不紧急”这个价值区域。但当我有多个“重要不紧急”的需求时，应该先做哪一个呢？这时，我需要引入更多的因素来综合判断。</p><ul><li><strong>性价比（成本与价值）</strong>：这是我最看重的因素。我会粗略地估算每个需求的“投入产出比（ROI）”。即，这个需求需要耗费多少开发资源（成本）？它又能带来多大的用户价值和商业价值？我总是在寻找那些“四两拨千斤”的、低成本高价值的需求。</li><li><strong>符合战略规划</strong>：这个需求是否与我们公司本季度或本年度的战略目标相符？一个再酷的功能，如果脱离了公司的战略主航道，那它就是一个“漂亮的干扰项”。我必须确保我们的开发资源，始终服务于公司的战略大方向。</li><li><strong>长短期价值</strong>：我需要在“短期见效”和“长期投资”之间做出平衡。有时为了提振士气或达成某个KPI，我会选择一个能快速上线、马上看到效果的需求（短期价值）。有时我也会选择一个用户完全感知不到的“后台架构重构”项目，因为它能为我们未来几年的开发效率打下坚实的基础（长期价值）。</li></ul><h4 id="3-确定需求优先级的注意事项"><a href="#3-确定需求优先级的注意事项" class="headerlink" title="3. 确定需求优先级的注意事项"></a>3. 确定需求优先级的注意事项</h4><p><img src="https://bu.dusays.com/2025/07/15/68763f14f04fb.gif" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" data-lazy-src="https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719132422433.png" alt="image-20250719132422433"></p><p>最后，我想分享几点我在无数次优先级PK中，总结出的经验和教训。</p><ul><li><strong>需求变动性</strong>：我必须承认，优先级不是一成不变的。市场环境在变，用户需求在变，我们必须保持敏锐和灵活，定期（比如每两周或每月）回顾和调整我们的产品路线图。</li><li><strong>全局主动性</strong>：我不能只做一个被动接收和排序需求的人。我需要站在整个产品的视角，主动地去规划那些能建立长期壁垒的、系统性的项目，而不是被各个业务方的“紧急”需求牵着鼻子走。</li><li><strong>真实需求基础</strong>：我的所有优先级判断，都必须建立在我们在3.3.1和3.3.2节所做的“澄清”和“甄别”工作之上，即必须是<strong>真实的、有价值的需求</strong>。绝不能因为某个领导声音大、或者某个客户会吵，就轻易提高他的需求的优先级。</li><li><strong>需求选择搭配原则</strong>：一个健康的版本迭代，就像一顿营养均衡的饭。我通常会搭配着来，比如“<strong>一个大的新功能 + 几个重要的体验优化 + 一些历史Bug修复</strong>”。这样的版本，既能给用户带来惊喜，又能提升产品的稳定性，还能让团队有成就感。</li></ul><hr><p>我将优先级排序的要点，总结为下面这张表：</p><table><thead><tr><th align="left"><strong>核心方法</strong></th><th align="left"><strong>我的实践要点</strong></th></tr></thead><tbody><tr><td align="left"><strong>四象限法则</strong></td><td align="left">快速分类，重点投入<strong>重要不紧急</strong>的价值型需求。</td></tr><tr><td align="left"><strong>综合因素判断</strong></td><td align="left">在价值型需求中，进一步权衡<strong>性价比</strong>、<strong>战略价值</strong>和<strong>长短期收益</strong>。</td></tr><tr><td align="left"><strong>注意事项</strong></td><td align="left">保持<strong>灵活性</strong>，立足<strong>真实需求</strong>，<strong>主动规划</strong>，并<strong>合理搭配</strong>版本内容。</td></tr></tbody></table><p>到这里，我们需求分析的三个步骤就全部完成了。一个需求，只有经过了澄清、甄别和优先级排序这“三堂会审”，才有资格最终出现在我们的产品路线图上。</p><p>最后，我们附上产品需求文件模板供产品设计师快速完成需求分析的任务</p><div calss="anzhiyu-tag-link"><a class="tag-Link" target="_blank" href="/go.html?u=aHR0cHM6Ly9wcm9yaXNlLWJsb2cub3NzLWNuLWd1YW5nemhvdS5hbGl5dW5jcy5jb20vQUklRTQlQkElQTclRTUlOTMlODElRTclQkIlOEYlRTclOTAlODYvMDMlRTQlQkElQTclRTUlOTMlODElRTklOUMlODAlRTYlQjElODIlRTYlOTYlODclRTYlQTElQTMlRTYlQTglQTElRTYlOUQlQkYuZG9jeA" rel="external nofollow noopener noreferrer"><div class="tag-link-tips">引用站外地址</div><div class="tag-link-bottom"><div class="tag-link-left" style="background-image:url(https://bu.dusays.com/2025/07/19/687b2cf24c5db.png)"><i class="anzhiyufont anzhiyu-icon-link" style="display:none"></i></div><div class="tag-link-right"><div class="tag-link-title">产品需求文档模板.docx</div><div class="tag-link-sitename">Prorise</div></div><i class="anzhiyufont anzhiyu-icon-angle-right"></i></div></a></div><hr></div></article><div class="post-copyright"><div class="copyright-cc-box"><i class="anzhiyufont anzhiyu-icon-copyright"></i></div><div class="post-copyright__author_box"><a class="post-copyright__author_img" href="/" title="头像"><img class="post-copyright__author_img_back" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" title="头像" alt="头像"><img class="post-copyright__author_img_front" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png# 版权信息区域作者头像前景图片" title="头像" alt="头像"></a><div class="post-copyright__author_name">Prorise</div><div class="post-copyright__author_desc"></div></div><div class="post-copyright__post__info"><a class="post-copyright__original" title="该文章为原创文章，注意版权协议" href="https://prorise666.site/posts/59297.html">原创</a><a class="post-copyright-title"><span onclick="rm.copyPageUrl(&quot;https://prorise666.site/posts/59297.html&quot;)">产品经理入门（三）：第三章：需求分析</span></a></div><div class="post-tools" id="post-tools"><div class="post-tools-left"><div class="rewardLeftButton"><div class="post-reward" onclick="anzhiyu.addRewardMask()"><div class="reward-button button--animated" title="赞赏作者"><i class="anzhiyufont anzhiyu-icon-hand-heart-fill"></i>打赏作者</div><div class="reward-main"><div class="reward-all"><span class="reward-title">感谢你赐予我前进的力量</span><ul class="reward-group"><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/bYMH/418X445/ef9a3f68-d36a-441a-95fe-8179b1a25992.png" alt="微信"></a><div class="post-qr-code-desc">微信</div></li><li class="reward-item"><a href="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" rel="external nofollow noreferrer" target="_blank"><img class="post-qr-code-img" src="https://tc.z.wiki/autoupload/iXoPwUD80CPTvQUyITTBFOykMT9FcWW5SasRoXZEs3Wyl5f0KlZfm6UsKj-HyTuv/20250609/cYu3/347X390/zfb.png" alt="支付宝"></a><div class="post-qr-code-desc">支付宝</div></li></ul><a class="reward-main-btn" href="/about/#about-reward" target="_blank"><div class="reward-text">赞赏者名单</div><div class="reward-dec">因为你们的支持让我意识到写文章的价值🙏</div></a></div></div></div><div id="quit-box" onclick="anzhiyu.removeRewardMask()" style="display:none"></div></div><div class="shareRight"><div class="share-link mobile"><div class="share-qrcode"><div class="share-button" title="使用手机访问这篇文章"><i class="anzhiyufont anzhiyu-icon-qrcode"></i></div><div class="share-main"><div class="share-main-all"><div id="qrcode" title="https://prorise666.site/posts/59297.html"></div><div class="reward-dec">使用手机访问这篇文章</div></div></div></div></div><div class="share-link weibo"><a class="share-button" target="_blank" href="https://service.weibo.com/share/share.php?title=undefined&amp;url=https://prorise666.site/posts/59297.html&amp;pic=undefined" rel="external nofollow noreferrer noopener"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a></div><script>function copyCurrentPageUrl(){var e=window.location.href,t=document.createElement("input");t.setAttribute("value",e),document.body.appendChild(t),t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(t)}</script><div class="share-link copyurl"><div class="share-button" id="post-share-url" title="复制链接" onclick="copyCurrentPageUrl()"><i class="anzhiyufont anzhiyu-icon-link"></i></div></div></div></div></div><div class="post-copyright__notice"><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="https://prorise666.site" target="_blank">Prorise - 分享技术与实战经验</a>！</span></div></div><div class="post-tools-right"><div class="tag_share"><div class="post-meta__box"><div class="post-meta__box__category-list"><a class="post-meta__box__categoryes" href="/categories/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/"><span class="categoryes-punctuation"><i class="anzhiyufont anzhiyu-icon-inbox"></i></span>产品经理<span class="categoryesPageCount">26</span></a></div><div class="post-meta__box__tag-list"><a class="post-meta__box__tags" href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/"><span class="tags-punctuation"><i class="anzhiyufont anzhiyu-icon-tag"></i></span>产品经理教程<span class="tagsPageCount">26</span></a></div></div><div class="post_share"><div class="social-share" data-image="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.cbd.int/butterfly-extsrc@1.1.3/sharejs/dist/css/share.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lib.baomitu.com/social-share.js/1.0.16/js/social-share.min.js" defer=""></script></div></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/posts/56262.html"><img class="prev-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div class="next-post pull-right"><a href="/posts/13237.html"><img class="next-cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" onerror="onerror=null,src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">产品经理入门（四）：第四章：流程图与结构图</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="anzhiyufont anzhiyu-icon-thumbs-up fa-fw" style="font-size:1.5rem;margin-right:4px"></i><span>喜欢这篇文章的人也看了</span></div><div class="relatedPosts-list"><div><a href="/posts/10477.html" title="产品经理入门（一）：第一章：内容产品模型"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（一）：第一章：内容产品模型</div></div></a></div><div><a href="/posts/51587.html" title="产品经理入门（七）：第七章：用户端设计"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（七）：第七章：用户端设计</div></div></a></div><div><a href="/posts/38041.html" title="产品经理入门（九）：第九章：平台端设计（用户-内容-运营）"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-21</div><div class="title">产品经理入门（九）：第九章：平台端设计（用户-内容-运营）</div></div></a></div><div><a href="/posts/56262.html" title="产品经理入门（二）：第二章：需求收集与管理"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（二）：第二章：需求收集与管理</div></div></a></div><div><a href="/posts/23264.html" title="产品经理入门（五）：第五章：产品设计与原型制作"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（五）：第五章：产品设计与原型制作</div></div></a></div><div><a href="/posts/11780.html" title="产品经理入门（八）：第八章：内容产品自媒体端设计"><img class="cover" src="https://i1.wp.com/dev.ruom.top/i/2025/07/19/606082.webp" alt="cover"><div class="content is-center"><div class="date"><i class="anzhiyufont anzhiyu-icon-calendar-days fa-fw"></i> 2025-07-20</div><div class="title">产品经理入门（八）：第八章：内容产品自媒体端设计</div></div></a></div></div></div><hr><div id="post-comment"><div class="comment-head"><div class="comment-headline"><i class="anzhiyufont anzhiyu-icon-comments"></i><span> 评论</span></div><div class="comment-randomInfo"><a onclick="anzhiyu.addRandomCommentInfo()" href="javascript:void(0)" rel="external nofollow noreferrer" style="display:none">匿名评论</a><a href="/privacy" style="margin-left:4px">隐私政策</a></div><div class="comment-switch"><span class="first-comment">Twikoo</span><span id="switch-btn"></span><span class="second-comment">Waline</span></div><div class="comment-tips" id="comment-tips"><span>✅ 你无需删除空行，直接评论以获取最佳展示效果</span></div></div><div class="comment-wrap"><div><div id="twikoo-wrap"></div></div><div><div id="waline-wrap"></div></div></div></div><div class="comment-barrage"></div><script>window.postData={title:"产品经理入门（三）：第三章：需求分析",date:"2025-07-20 18:13:45",updated:"2025-07-21 14:52:10",tags:["产品经理教程"],categories:["产品经理"],content:"\n# 第三章：需求分析\n\n在上一章，我们学会了如何像一名侦探一样，通过各种手段去“收集”需求的线索。但这些线索往往是零散的、模糊的，甚至带有误导性。如果我们不加处理就直接采纳，很可能会做出南辕北辙的产品。\n\n因此，**需求分析**就是我们作为产品经理，对这些原始线索进行“勘察、推理、定案”的关键过程。这是整个产品工作中，最能体现我们逻辑思辨和深度思考能力的核心环节。\n\n## 3.1 需求分析的定义\n\n我们先从最根本的问题开始：到底什么才叫“需求分析”,我们可以来看一个案例：\n\n![image-20250719112130263](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719112130263.png)\n\n上面这个案例，我们不难看出，当我们接到需求后，没有去了解需求的背景，深挖需求。很容易导致我们做出来的方案是不符合要求，导致大量的人力、时间、资源的浪费，我们应当去拆解用户的需求，如下：\n\n![image-20250719112244196](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719112244196.png)\n\n### 3.1.1 什么是需求分析\n\n在我看来，要理解需求分析，我们需要抓住它的“本质”和“过程”。\n\n#### 1. 需求分析的本质\n\n我理解的需求分析，其本质是一个**“解构”与“重构”**的过程。\n* **解构**：是把用户提出的原始需求（无论是问题、目的还是方案）打碎、拆解，深入挖掘其背后真正的动机和未被满足的痛点。\n* **重构**：是在我们完全理解了本质痛点之后，重新组合信息，设计出一个真正能有效解决该问题的、合理的、可落地的产品解决方案。\n\n简单来说，就是**先想“为什么”，再想“怎么办”**。\n\n#### 2. 原始需求与产品需求的转换\n\n基于这个本质，我给需求分析一个最直接的定义：**所谓需求分析，就是将“用户的原始需求”，转化为“可执行的产品需求”的全过程。**\n\n![image-20250719112050347](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719112050347.png)\n\n我们再来回顾一下这两个概念：\n* **原始需求**：是用户直接给我们的东西，是“用户想要什么”。它可能是“我想要个一键下单按钮”，也可能是“我希望能快速找到便宜的外卖”。它是我们工作的**输入**。\n* **产品需求**：是我们经过分析、甄别、权衡之后，最终决定要做的东西，是“我们应该为用户做什么”。它是一个包含了用户场景、核心问题、解决方案和验收标准的完整方案。它是我们工作的**输出**。\n\n所以，需求分析就是连接这两者的桥梁，是那个至关重要的“转化”步骤。没有这个转化过程，我们就只是一个需求的“传声筒”，而不是一个创造价值的“产品经理”。\n\n为了让这个区别更清晰，我总结了下面的对比表：\n\n| **对比维度** | **原始需求** | **产品需求** |\n| :--- | :--- | :--- |\n| **来源** | 用户直接表达 | 产品经理分析转化 |\n| **形式** | 通常是模糊、零散、未经验证的 | 是清晰、结构化、经过验证的 |\n| **关注点** | “我想要一个XX功能”（what） | “为了解决用户XX问题，我们需要XX方案”（why & how）|\n| **我的角色** | 聆听者、记录员 | 分析师、决策者、方案设计师 |\n\n\n\n\n---\n\n## 3.2 需求分析的时机\n\n在我看来，需求分析并不是一个孤立的、只在特定阶段才进行的“仪式”。它应该像呼吸一样，贯穿我们产品工作的始终。不过，从实践上，我主要会在两个关键的时间点，以不同的方式来开展这项工作：**1️⃣收集需求时** 和 **2️⃣收集需求后**。\n\n![image-20250719113814345](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719113814345.png)\n\n### 3.2.1 收集需求时的需求分析\n\n#### 1. 直接沟通进行需求分析\n\n我把这个过程称为“实时分析”。当我在进行用户访谈、或是与业务方开会时，我绝不只做一个被动的记录员。我的大脑会高速运转，对接收到的每一个信息点，当场进行第一轮的分析、澄清和追问。\n\n这种方式的好处是，我可以在信息最新鲜、上下文最完整的时刻，抓住机会深挖下去，及时地探究用户“为什么”这么想，而不是等会议结束、记忆模糊后，再自己去猜测。\n\n#### 2. 案例：物流公司时效需求分析\n\n![image-20250719113104681](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719113104681.png)\n\n这个案例能很好地说明实时分析的价值。\n\n* **场景**：我作为一家物流平台的PM，正在访谈一位重要的企业客户。\n* **原始需求**：客户告诉我：“我希望你们的平台能提供一个功能，让我能在地图上看到我们货物的运输车辆的实时GPS位置。”\n* **我的实时分析与追问**：听到这个“方案”后，我没有立刻记下来就完事，而是当场追问：“这个想法很有意思。可以和我聊聊吗，您为什么需要看到车辆的实时位置？这个功能能帮您解决什么具体问题呢？”\n* **挖掘出的本质目的**：通过追问，我发现，客户并不真的关心车辆在哪条路上，他关心的是“货物到底什么时候能到”。因为他的下游客户总是在催问他送达时间，他需要一个准确的预期，来安抚客户、安排接货。\n* **转化后的产品需求**：因此，我将这个需求从“提供车辆实时定位”，转化为了“在订单详情页提供一个精准、动态的**预计送达时间（ETA）**，并支持在临近送达时，向收货人发送提醒”。后者显然是价值高得多、也更贴近问题本质的解决方案。\n\n### 3.2.2 收集需求后的需求分析\n\n![image-20250719113442021](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719113442021.png)\n\n#### 1. 集中分析需求\n\n当然，光有实时分析是远远不够的。在完成了当周的用户访谈、回收了所有的调查问卷、或者整理完用户反馈后，我会专门安排时间，进行一次“集中分析”。\n\n在这个阶段，我会把所有零散的、原始的需求信息汇总到一起，像一个侦探把所有线索都钉在白板上一样。我会开始寻找它们之间的关联、共性，试图发现那个隐藏在多个表象之下的、更宏观的、系统性的问题或机会。\n\n#### 2. 案例：用户反馈需求分析\n\n![image-20250719113602157](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719113602157.png)\n\n* **场景**：我作为一款社交App的PM，每周都会定期整理来自应用商店、社区的用户反馈。\n* **原始需求（集合）**：我看到了大量看似独立的用户抱怨。\n\t* 用户A说：“你们的视频加载太慢了！”；\n\t* 用户B说：“视频看着看着就卡住了”；\n\t* 用户C说：“上传一个视频要等半天”。\n* **我的集中分析**：如果我只看单条反馈，可能会分别给技术团队提“加载慢”、“播放卡”、“上传慢”这三个独立的、零散的问题。但当我把它们放在一起集中分析时，我发现了一个共性——**我们产品的整体视频处理和分发链路，可能存在系统性的性能瓶颈**。\n* **转化后的产品需求**：基于这个判断，我最终定义的产品需求，就不是一个个小修小补，而是一个系统性的优化项目：“**优化视频处理架构，提升视频在不同网络环境下的加载和播放流畅度，将平均起播时间缩短30%**”。这个需求，显然比解决单个用户的抱怨要有价值得多。\n\n---\n总而言之，这两种时机的分析，各有侧重，缺一不可。我将它们的区别总结如下：\n\n| **分析时机** | **核心特点** | **我的目标** |\n| :--- | :--- | :--- |\n| **收集时（实时分析）** | 互动性强、有上下文、聚焦于个体 | 快速探究单个原始需求背后的“**为什么**”。 |\n| **收集后（集中分析）** | 宏观、全面、寻找关联 | 发现多个原始需求背后共同指向的“**系统性问题或机会**”。 |\n\n\n\n\n---\n\n## 3.3 需求分析的步骤\n\n到目前为止，我们已经定义了需求分析，也明确了进行分析的时机。那具体到一项原始需求，我究竟是如何一步步把它“解剖”清楚的呢？\n\n在我的工作流中，这个过程被严格地划分为三个步骤：\n\n- **第一步：需求澄清；**\n- **第二步 ：需求甄别；**\n- **第三步：需求的优先级排序**。这三个步骤，层层递进，缺一不可。\n\n我们先来看第一步，也是所有分析的基础——需求澄清。\n\n### 3.3.1 需求澄清\n\n![image-20250719130637070](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719130637070.png)\n\n每当一个原始需求摆在我面前时，我从不急于判断它的好坏、真伪，我做的第一件事，永远是：**把它弄清楚**。\n\n需求澄清，对就我像一名侦探在审视案发现场，我需要通过反复地询问和探究，把这个需求的所有模糊、不确定的信息，都变得清晰、明确。为了系统化地做到这一点，我随身携带着一个强大的思维工具——**“WHY-WHO-WHAT-HOW”框架**。\n\n我们以外卖平台的案例来逐一拆解这四个关键问题：假设我收到的一个原始需求是“我想要一个‘智能营养餐’功能”。\n\n#### 1. WHY（为什么）\n\n这是我首先要问的问题：**我们为什么要做这个需求？** 它能为用户带来什么核心价值？它又能为我们的公司带来什么商业价值？\n\n* 对于“智能营养餐”这个需求，WHY可能是：\n    * **用户价值**：帮助对健康有追求的用户，解决“不知道怎么吃才健康”以及“计算热量和营养成分很麻烦”的痛点。\n    * **商业价值**：通过差异化的健康服务，吸引高价值用户，提升平台的品牌形象和用户粘性。\n\n如果一个需求的“WHY”我都答不出来，那它基本上就可以被直接否决了。\n\n#### 2. WHO（用户是谁）\n\n第二个问题：**这个需求我们是为谁而做的？** 我需要清晰地描绘出目标用户的画像。\n\n* “智能营养餐”的WHO，绝不是“所有用户”。它的核心用户画像可能是：\n    * 一线城市的年轻白领；\n    * 有健身习惯或正在减脂的人群；\n    * 关注生活品质、愿意为健康付出一定溢价的用户。\n* 明确WHO，能帮助我在后续的设计中，始终围绕着这群核心用户的审美和习惯来进行。\n\n#### 3. WHAT（什么问题）\n\n第三个问题：**我们具体要解决一个什么问题？** 我需要把用户的痛点用清晰、无歧义的语言描述出来。\n\n* 对于“智能营养餐”，WHAT不是“用户想要一个功能”，而是要解决用户的本质问题：\n    * “用户因缺乏专业知识，**难以判断**不同外卖的营养成分和热量是否满足自己的健康需求。”\n    * “用户因工作繁忙，**没有时间**去自己计算和搭配每日的营养摄入。”\n\n#### 4. HOW（现状如何）\n\n最后一个问题：**用户现在是如何解决这个问题的？** 了解用户当前的“野生”解决方案，能帮我判断痛点的强度，并为我的设计提供灵感。\n\n* 对于“智能营养餐”这个需求，用户当下的HOW可能是：\n    * 自己去网上搜索食物热量表，估算着点餐。\n    * 下载专门的健康App，手动记录自己点的外卖，再查看营养分析。\n    * 干脆放弃点外卖，选择自己做饭或吃价格昂贵的成品健康餐。\n* 这些笨拙、耗时、昂贵的现状，恰恰反证了我们这个新功能潜在的巨大价值。\n\n---\n我将这个澄清框架总结为一张表，它是我分析任何需求前的“必填清单”：\n\n| **澄清问题** | **我的核心关注点** | **案例应用（智能营养餐）** |\n| :--- | :--- | :--- |\n| **WHY (为什么做)** | 探究需求的**商业与用户价值** | 满足健康需求，提升用户粘性与品牌价值。 |\n| **WHO (为谁而做)** | 定义精准的**目标用户画像** | 追求健康的都市白领、健身人群。 |\n| **WHAT (解决什么问题)** | 识别并定义用户的**本质痛点** | 解决用户“不懂如何健康搭配”和“没时间计算营养”的问题。 |\n| **HOW (现状如何)** | 了解用户当前的**解决方案或替代方案** | 用户目前通过手动查询、使用其他App等方式，过程繁琐且不准确。 |\n\n只有把这四个问题都回答清楚了，我才会认为，这个需求已经被我“澄清”了。接下来，我才会进入分析的第二步：需求甄别。\n\n\n\n### 3.3.2 需求甄别\n\n我把需求分析的第二步称为**“需求甄别”**，或者叫“真伪需求判定”。我的核心任务，是判断这个被澄清后的需求，到底是一个能为多数用户创造巨大价值的“真需求”，还是一个看似有理、实则虚幻的“伪需求”。\n\n投入资源去做一个伪需求，是我认为对团队最大的浪费。\n\n#### 1. 需求真伪判定的三个标准\n\n\n\n![image-20250719131258692](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719131258692.png)\n\n为了避免凭感觉做判断，我建立了一套自己的“甄别滤网”，它由三个核心标准构成。一个有价值的真需求，通常需要至少满足其中两个，甚至是全部三个标准。\n\n* **普遍性 (Universality)**\n    这个问题是否具有广泛的代表性？在我的目标用户群体中，遇到这个问题的用户规模有多大？是只有一小撮人有这个特殊的毛病，还是绝大多数用户共同的困扰？我追求的，是能让尽可能多的目标用户受益的需求。\n\n* **痛点性 (Painfulness)**\n    这个问题给用户带来的“痛感”有多强？如果这个问题不被解决，用户是否会感到非常沮丧、烦躁，甚至愿意付费来解决它？我常用“是‘痒点’还是‘痛点’”来区分。挠痒痒的需求可做可不做，但治病止痛的需求，用户才会真正买单。\n\n* **高频性 (High Frequency)**\n    用户遇到这个问题的频率有多高？是每天、每周都会遇到，还是每年甚至几年才会遇到一次？高频的需求，意味着我们的解决方案能被用户频繁使用，这对于培养用户习惯、提升产品粘性至关重要。\n\n---\n\n#### 2. 伪需求判断案例分析\n\n我们来看一个经典的案例，学习如何运用这三个标准来甄别伪需求。\n\n* **场景**：我是一家“母婴社区”App的产品经理，我们的核心业务是为新手爸妈提供育儿知识交流和社交的平台。\n* **原始需求**：社区里有一位非常活跃且有影响力的用户，她强烈建议我们增加一个“儿童防走丢手表”的功能。她的设想是，用户可以在我们的App里购买一款儿童手表，并随时查看孩子的位置。\n* **我的甄别过程**：这个需求听起来非常“刚需”，因为儿童安全是天大的事。但我们必须冷静地用三个标准来审视它。\n    1.  **普遍性分析**：所有家长都关心孩子安全，这是一个普遍的情感。但是，“需要通过一个App内嵌的硬件功能来随时追踪孩子位置”，这还是一个普遍的需求吗？我的判断是，只有其中一部分极度焦虑的家长才会有此强需求。因此，需求的**普遍性较低**。\n    2.  **痛点性分析**：“孩子走丢”这个场景，痛不痛？当然痛，这是天塌下来的痛。所以，**痛点性极高**。\n    3.  **高频性分析**：一个正常的孩子，在父母的看护下，“走丢”这件事发生的频率有多高？谢天谢地，这是一个极低极低的概率。所以，需求的**高频性极低**。\n\n* **我的最终结论**：这是一个“**低频、非普适的超强痛点**”需求。更重要的是，它涉及到硬件、供应链、地图服务等，这与我们“社区内容”的核心能力相去甚远。因此，尽管它听起来很有吸引力，但我会判定，对于我们这个母婴社区App而言，这是一个“**伪需求**”。它是一个真实存在的问题，但它不应该由我们这个产品来解决。\n\n---\n我将这三个标准总结为一张自检表，每当我分析需求时，都会在心里为它打分：\n\n| **甄别标准** | **我问自己的问题** | **强需求特征** |\n| :--- | :--- | :--- |\n| **普遍性** | 我的目标用户中，有多大比例的人会遇到这个问题？ | 广大目标用户都存在 |\n| **痛点性** | 如果不解决，用户会有多“痛”？他们愿意为此做什么？ | 痛感强烈，用户愿意付费或付出代价解决 |\n| **高频性** | 用户多久会遇到一次这个问题？每天？每周？还是几乎遇不到？ | 每日或每周多次遇到 |\n\n只有通过了这道严格的“安检门”，一个需求才有资格进入我分析流程的最后一步：优先级排序。\n\n\n\n\n---\n\n### 3.3.3 需求的优先级\n\n经过了“澄清”和“甄别”，我们现在手上拿到了一份“真需求”清单。但现实是，我们的研发资源（人力、时间、金钱）永远是有限的。我们不可能同时满足所有人的所有需求。\n\n因此，**优先级排序**，就是决定“**我们下一步应该先做什么，再做什么**”的艺术和科学。在我看来，这是产品经理最重要的决策，没有之一。一个正确的优先级决策，能让我们的产品价值最大化。\n\n#### 1. 四象限法则\n\n![image-20250719132049714](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719132049714.png)\n\n当我面对一大堆需求，感到千头万绪时，我用来理清思路的第一个工具，就是经典的**“四象限法则”**（也叫艾森豪威尔矩阵）。我通过“重要性”和“紧急性”这两个维度，快速地对需求进行一次粗分类。\n\n* **重要且紧急**：这是最高优先级，是那些“着火了”的需求。比如：线上支付功能出现重大Bug、服务器宕机。我的原则是：**马上做**，调动一切资源，立刻解决。\n* **重要不紧急**：这是最能体现我们产品经理价值的区域。这些需求关系到产品的长期发展和核心战略，比如：架构优化、新功能探索、用户体验的系统性提升。它们没有明确的deadline，最容易被我们拖延。我的原则是：**计划做**，必须主动地、有计划地把它们排入我们的产品路线图。\n* **紧急不重要**：这些是日常工作中最大的干扰。比如：某个领导临时想要一个不重要的数据、某个非核心客户的一些小报怨。它们看起来很急，但对我们的核心目标贡献不大。我的原则是：**授权做或快速应付**，看能否让团队其他人帮忙，或者用最小的代价快速解决。\n* **不重要不紧急**：这些是价值最低的需求。我的原则是：**尽量不做或直接拒绝**。我们必须学会对这类需求说“不”。\n\n![image-20250719132245046](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719132245046.png)\n\n#### 2. 优先级确定因素\n\n![image-20250719132330097](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719132330097.png)\n\n四象限法则帮我做了初步的划分，尤其区分出了“重要不紧急”这个价值区域。但当我有多个“重要不紧急”的需求时，应该先做哪一个呢？这时，我需要引入更多的因素来综合判断。\n\n* **性价比（成本与价值）**：这是我最看重的因素。我会粗略地估算每个需求的“投入产出比（ROI）”。即，这个需求需要耗费多少开发资源（成本）？它又能带来多大的用户价值和商业价值？我总是在寻找那些“四两拨千斤”的、低成本高价值的需求。\n* **符合战略规划**：这个需求是否与我们公司本季度或本年度的战略目标相符？一个再酷的功能，如果脱离了公司的战略主航道，那它就是一个“漂亮的干扰项”。我必须确保我们的开发资源，始终服务于公司的战略大方向。\n* **长短期价值**：我需要在“短期见效”和“长期投资”之间做出平衡。有时为了提振士气或达成某个KPI，我会选择一个能快速上线、马上看到效果的需求（短期价值）。有时我也会选择一个用户完全感知不到的“后台架构重构”项目，因为它能为我们未来几年的开发效率打下坚实的基础（长期价值）。\n\n#### 3. 确定需求优先级的注意事项\n\n![image-20250719132422433](https://cdn.jsdmirror.com/gh/Prorise-cool/prorise-blog-assets@main/img/image-20250719132422433.png)\n\n最后，我想分享几点我在无数次优先级PK中，总结出的经验和教训。\n\n* **需求变动性**：我必须承认，优先级不是一成不变的。市场环境在变，用户需求在变，我们必须保持敏锐和灵活，定期（比如每两周或每月）回顾和调整我们的产品路线图。\n* **全局主动性**：我不能只做一个被动接收和排序需求的人。我需要站在整个产品的视角，主动地去规划那些能建立长期壁垒的、系统性的项目，而不是被各个业务方的“紧急”需求牵着鼻子走。\n* **真实需求基础**：我的所有优先级判断，都必须建立在我们在3.3.1和3.3.2节所做的“澄清”和“甄别”工作之上，即必须是**真实的、有价值的需求**。绝不能因为某个领导声音大、或者某个客户会吵，就轻易提高他的需求的优先级。\n* **需求选择搭配原则**：一个健康的版本迭代，就像一顿营养均衡的饭。我通常会搭配着来，比如“**一个大的新功能 + 几个重要的体验优化 + 一些历史Bug修复**”。这样的版本，既能给用户带来惊喜，又能提升产品的稳定性，还能让团队有成就感。\n\n---\n我将优先级排序的要点，总结为下面这张表：\n\n| **核心方法** | **我的实践要点** |\n| :--- | :--- |\n| **四象限法则** | 快速分类，重点投入**重要不紧急**的价值型需求。 |\n| **综合因素判断** | 在价值型需求中，进一步权衡**性价比**、**战略价值**和**长短期收益**。 |\n| **注意事项** | 保持**灵活性**，立足**真实需求**，**主动规划**，并**合理搭配**版本内容。 |\n\n到这里，我们需求分析的三个步骤就全部完成了。一个需求，只有经过了澄清、甄别和优先级排序这“三堂会审”，才有资格最终出现在我们的产品路线图上。\n\n最后，我们附上产品需求文件模板供产品设计师快速完成需求分析的任务\n\n{% link 产品需求文档模板.docx,Prorise,https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/AI%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86/03%E4%BA%A7%E5%93%81%E9%9C%80%E6%B1%82%E6%96%87%E6%A1%A3%E6%A8%A1%E6%9D%BF.docx,https://bu.dusays.com/2025/07/19/687b2cf24c5db.png %}\n\n\n\n\n---"}</script></div><div class="aside-content" id="aside-content"><div class="card-widget card-info card-author-solitude"><div class="card-top-section"><div class="sayhi" id="author-info__sayhi" onclick="anzhiyu.changeWittyWord()"></div><div class="avatar"><img alt="avatar" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2024/06/20/6673caa5bca18.gif&quot;"><div class="sticker"><img class="sticker-img" src="https://bu.dusays.com/2025/07/19/687b396f89934.png" alt="status"></div></div></div><div class="card-bottom-section"><div class="author-info"><div class="name">Prorise</div><div class="desc">这是我的博客，分享技术与生活的点点滴滴</div></div><div class="site-stats"><div class="stat-item"><a href="/archives/"><div class="stat-number">109</div><div class="stat-label">文章</div></a></div><div class="stat-item"><a href="/tags/"><div class="stat-number">7</div><div class="stat-label">标签</div></a></div><div class="stat-item"><a href="/categories/"><div class="stat-number">9</div><div class="stat-label">分类</div></a></div></div><div class="social-icons"><a class="social-icon" href="https://github.com/Prorise-cool" rel="external nofollow noreferrer" title="Github" target="_blank"><i class="fab fa-github"></i></a><a class="social-icon" href="https://space.bilibili.com/361040115?spm_id_from=333.1387.0.0" rel="external nofollow noreferrer" title="BiliBili" target="_blank"><i class="fab fa-bilibili"></i></a></div></div></div><div class="card-widget card-latest-comments"><div class="item-headline"><i class="fas fa-comments"></i><span>最新评论</span></div><div class="item-content"><a href="/messages/" class="headline-right" title="查看更多"><i class="fas fa-angle-right"></i></a><div class="aside-list" id="latest-comments"></div></div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-bars"></i><span>文章目录</span><span class="toc-percentage"></span></div><div class="toc-content is-expand"><ol class="toc"><li class="toc-item toc-level-1"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E7%AB%A0%EF%BC%9A%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-number">1.</span> <span class="toc-text">第三章：需求分析</span></a><ol class="toc-child"><li class="toc-item toc-level-2"><a class="toc-link" href="#3-1-%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%E7%9A%84%E5%AE%9A%E4%B9%89"><span class="toc-number">1.1.</span> <span class="toc-text">3.1 需求分析的定义</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-1-1-%E4%BB%80%E4%B9%88%E6%98%AF%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-number">1.1.1.</span> <span class="toc-text">3.1.1 什么是需求分析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%E7%9A%84%E6%9C%AC%E8%B4%A8"><span class="toc-number">1.1.1.1.</span> <span class="toc-text">1. 需求分析的本质</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E5%8E%9F%E5%A7%8B%E9%9C%80%E6%B1%82%E4%B8%8E%E4%BA%A7%E5%93%81%E9%9C%80%E6%B1%82%E7%9A%84%E8%BD%AC%E6%8D%A2"><span class="toc-number">1.1.1.2.</span> <span class="toc-text">2. 原始需求与产品需求的转换</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-2-%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%E7%9A%84%E6%97%B6%E6%9C%BA"><span class="toc-number">1.2.</span> <span class="toc-text">3.2 需求分析的时机</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-1-%E6%94%B6%E9%9B%86%E9%9C%80%E6%B1%82%E6%97%B6%E7%9A%84%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-number">1.2.1.</span> <span class="toc-text">3.2.1 收集需求时的需求分析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E7%9B%B4%E6%8E%A5%E6%B2%9F%E9%80%9A%E8%BF%9B%E8%A1%8C%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-number">1.2.1.1.</span> <span class="toc-text">1. 直接沟通进行需求分析</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%A1%88%E4%BE%8B%EF%BC%9A%E7%89%A9%E6%B5%81%E5%85%AC%E5%8F%B8%E6%97%B6%E6%95%88%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-number">1.2.1.2.</span> <span class="toc-text">2. 案例：物流公司时效需求分析</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-2-2-%E6%94%B6%E9%9B%86%E9%9C%80%E6%B1%82%E5%90%8E%E7%9A%84%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-number">1.2.2.</span> <span class="toc-text">3.2.2 收集需求后的需求分析</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%9B%86%E4%B8%AD%E5%88%86%E6%9E%90%E9%9C%80%E6%B1%82"><span class="toc-number">1.2.2.1.</span> <span class="toc-text">1. 集中分析需求</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E6%A1%88%E4%BE%8B%EF%BC%9A%E7%94%A8%E6%88%B7%E5%8F%8D%E9%A6%88%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90"><span class="toc-number">1.2.2.2.</span> <span class="toc-text">2. 案例：用户反馈需求分析</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#3-3-%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90%E7%9A%84%E6%AD%A5%E9%AA%A4"><span class="toc-number">1.3.</span> <span class="toc-text">3.3 需求分析的步骤</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-1-%E9%9C%80%E6%B1%82%E6%BE%84%E6%B8%85"><span class="toc-number">1.3.1.</span> <span class="toc-text">3.3.1 需求澄清</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-WHY%EF%BC%88%E4%B8%BA%E4%BB%80%E4%B9%88%EF%BC%89"><span class="toc-number">1.3.1.1.</span> <span class="toc-text">1. WHY（为什么）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-WHO%EF%BC%88%E7%94%A8%E6%88%B7%E6%98%AF%E8%B0%81%EF%BC%89"><span class="toc-number">1.3.1.2.</span> <span class="toc-text">2. WHO（用户是谁）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-WHAT%EF%BC%88%E4%BB%80%E4%B9%88%E9%97%AE%E9%A2%98%EF%BC%89"><span class="toc-number">1.3.1.3.</span> <span class="toc-text">3. WHAT（什么问题）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#4-HOW%EF%BC%88%E7%8E%B0%E7%8A%B6%E5%A6%82%E4%BD%95%EF%BC%89"><span class="toc-number">1.3.1.4.</span> <span class="toc-text">4. HOW（现状如何）</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-2-%E9%9C%80%E6%B1%82%E7%94%84%E5%88%AB"><span class="toc-number">1.3.2.</span> <span class="toc-text">3.3.2 需求甄别</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E9%9C%80%E6%B1%82%E7%9C%9F%E4%BC%AA%E5%88%A4%E5%AE%9A%E7%9A%84%E4%B8%89%E4%B8%AA%E6%A0%87%E5%87%86"><span class="toc-number">1.3.2.1.</span> <span class="toc-text">1. 需求真伪判定的三个标准</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E4%BC%AA%E9%9C%80%E6%B1%82%E5%88%A4%E6%96%AD%E6%A1%88%E4%BE%8B%E5%88%86%E6%9E%90"><span class="toc-number">1.3.2.2.</span> <span class="toc-text">2. 伪需求判断案例分析</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#3-3-3-%E9%9C%80%E6%B1%82%E7%9A%84%E4%BC%98%E5%85%88%E7%BA%A7"><span class="toc-number">1.3.3.</span> <span class="toc-text">3.3.3 需求的优先级</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#1-%E5%9B%9B%E8%B1%A1%E9%99%90%E6%B3%95%E5%88%99"><span class="toc-number">1.3.3.1.</span> <span class="toc-text">1. 四象限法则</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2-%E4%BC%98%E5%85%88%E7%BA%A7%E7%A1%AE%E5%AE%9A%E5%9B%A0%E7%B4%A0"><span class="toc-number">1.3.3.2.</span> <span class="toc-text">2. 优先级确定因素</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#3-%E7%A1%AE%E5%AE%9A%E9%9C%80%E6%B1%82%E4%BC%98%E5%85%88%E7%BA%A7%E7%9A%84%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9"><span class="toc-number">1.3.3.3.</span> <span class="toc-text">3. 确定需求优先级的注意事项</span></a></li></ol></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="anzhiyufont anzhiyu-icon-history"></i><span>最近发布</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/26/266618.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="4️⃣ 商业B端产品经理实战"></a><div class="content"><a class="title" href="/posts/30592.html" title="4️⃣ 商业B端产品经理实战">4️⃣ 商业B端产品经理实战</a><time datetime="2025-07-26T07:09:45.000Z" title="发表于 2025-07-26 15:09:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/51707.html" title="第五章：用户运营"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第五章：用户运营"></a><div class="content"><a class="title" href="/posts/51707.html" title="第五章：用户运营">第五章：用户运营</a><time datetime="2025-07-26T05:13:45.000Z" title="发表于 2025-07-26 13:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/19658.html" title="第四章：内容管理"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第四章：内容管理"></a><div class="content"><a class="title" href="/posts/19658.html" title="第四章：内容管理">第四章：内容管理</a><time datetime="2025-07-26T04:13:45.000Z" title="发表于 2025-07-26 12:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/27803.html" title="第三章：活动管理-总价活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第三章：活动管理-总价活动"></a><div class="content"><a class="title" href="/posts/27803.html" title="第三章：活动管理-总价活动">第三章：活动管理-总价活动</a><time datetime="2025-07-26T03:13:45.000Z" title="发表于 2025-07-26 11:13:45">2025-07-26</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/posts/10822.html" title="第二章：活动管理-单品活动"><img src="https://i1.wp.com/dev.ruom.top/i/2025/07/25/884971.webp" onerror="this.onerror=null,this.src=&quot;https://bu.dusays.com/2025/07/19/687b3ab77aed8.jpg&quot;" alt="第二章：活动管理-单品活动"></a><div class="content"><a class="title" href="/posts/10822.html" title="第二章：活动管理-单品活动">第二章：活动管理-单品活动</a><time datetime="2025-07-26T02:13:45.000Z" title="发表于 2025-07-26 10:13:45">2025-07-26</time></div></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div id="footer_deal"><a class="deal_link" href="mailto:<EMAIL>" rel="external nofollow noreferrer" title="email"><i class="anzhiyufont anzhiyu-icon-envelope"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://weibo.com/u/7484736298?tabtype=home" title="微博"><i class="anzhiyufont anzhiyu-icon-weibo"></i></a><a class="deal_link" href="/atom.xml" title="RSS"><i class="anzhiyufont anzhiyu-icon-rss"></i></a><img class="footer_mini_logo" title="返回顶部" alt="返回顶部" onclick="anzhiyu.scrollToDest(0,500)" src="https://prorise-blog.oss-cn-guangzhou.aliyuncs.com/cover/avatar.png" size="50px"><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool" title="Github"><i class="anzhiyufont anzhiyu-icon-github"></i></a><a class="deal_link" target="_blank" rel="noopener external nofollow noreferrer" href="https://space.bilibili.com/361040115?spm_id_from=333.788.0.0" title="Bilibili"><i class="anzhiyufont anzhiyu-icon-bilibili"></i></a><a class="deal_link" href="/copyright" title="CC"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i></a></div><div id="workboard"><div id="runtimeTextTip"></div></div><div class="footer_custom_text">这是我的个人博客，分享技术与生活点滴。</div><div id="anzhiyu-footer"><div class="footer-group"><div class="footer-title">服务</div><div class="footer-links"><a class="footer-item" title="51la统计" target="_blank" rel="noopener external nofollow noreferrer" href="https://v6.51.la/">51la统计</a><a class="footer-item" title="十年之约" target="_blank" rel="noopener external nofollow noreferrer" href="https://www.foreverblog.cn/">十年之约</a></div></div><div class="footer-group"><div class="footer-title">主题</div><div class="footer-links"><a class="footer-item" title="文档" href="/docs/">文档</a><a class="footer-item" title="源码" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool/Prorise-cool.github.io/tree/main">源码</a></div></div><div class="footer-group"><div class="footer-title">导航</div><div class="footer-links"><a class="footer-item" title="即刻短文" href="/essay/">即刻短文</a><a class="footer-item" title="留言板" href="/comments/">留言板</a></div></div><div class="footer-group"><div class="footer-title">协议</div><div class="footer-links"><a class="footer-item" title="隐私协议" href="/privacy/">隐私协议</a><a class="footer-item" title="版权协议" href="/copyright/">版权协议</a></div></div><div class="footer-group"><div class="footer-title-group"><div class="footer-title">友链</div><a class="random-friends-btn" id="footer-random-friends-btn" href="javascript:addFriendLinksInFooter();" rel="external nofollow noreferrer" title="换一批友情链接"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right"></i></a></div><div class="footer-links" id="friend-links-in-footer"></div></div></div><p id="ghbdages"><a class="github-badge" target="_blank" href="https://hexo.io/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="博客框架为Hexo7.0" title="博客框架为Hexo7.0"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Frame-Hexo.svg" alt="博客框架为Hexo7.0"></a><a class="github-badge" target="_blank" href="https://www.dogecloud.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站使用多吉云为静态资源提供CDN加速" title="本站使用多吉云为静态资源提供CDN加速"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/CDN-多吉云-3693F3.svg" alt="本站使用多吉云为静态资源提供CDN加速"></a><a class="github-badge" target="_blank" href="https://github.com/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站项目由Github托管" title="本站项目由Github托管"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.1.5/img/badge/Source-Github.svg" alt="本站项目由Github托管"></a><a class="github-badge" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" style="margin-inline:5px" data-title="本站采用CC BY-NC-SA 4.0协议" title="本站采用CC BY-NC-SA 4.0协议"><img src="https://npm.elemecdn.com/anzhiyu-blog@2.2.0/img/badge/Copyright-BY-NC-SA.svg" alt="本站采用CC BY-NC-SA 4.0协议"></a></p></div><div id="footer-bar"><div class="footer-bar-links"><div class="footer-bar-left"><div id="footer-bar-tips"><div class="copyright">©2025 By <a class="footer-bar-link" href="/about/" title="Prorise" target="_blank">Prorise</a></div></div><div id="footer-type-tips"></div><div class="js-pjax"><script>function subtitleType () {
  fetch('https://v1.hitokoto.cn')
    .then(response => response.json())
    .then(data => {
      if (true) {
        const from = '出自 ' + data.from
        const sub = ["生活明朗, 万物可爱, 人间值得, 未来可期.","愿你眼里的星星，永远亮晶晶。","愿我们每个人都能被世界温柔以待。"]
        sub.unshift(data.hitokoto, from)
        window.typed = new Typed('#footer-type-tips', {
          strings: sub,
          startDelay: 300,
          typeSpeed: 150,
          loop: true,
          backSpeed: 50,
        })
      } else {
        document.getElementById('footer-type-tips').innerHTML = data.hitokoto
      }
    })
}

if (true) {
  if (typeof Typed === 'function') {
    subtitleType()
  } else {
    getScript('https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/typed.js/2.0.12/typed.min.js').then(subtitleType)
  }
} else {
  subtitleType()
}</script></div></div><div class="footer-bar-right"><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/Prorise-cool?tab=repositories" title="网站源码">网站源码</a><a class="footer-bar-link" target="_blank" rel="noopener external nofollow noreferrer" href="https://beian.miit.gov.cn/" title="湘ICP备23041-302号">湘ICP备23041-302号</a><a class="footer-bar-link cc" href="/copyright" title="cc协议"><i class="anzhiyufont anzhiyu-icon-copyright-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-by-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nc-line"></i><i class="anzhiyufont anzhiyu-icon-creative-commons-nd-line"></i></a></div></div></div></footer></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="sidebar-site-data site-data is-center"><a href="/archives/" title="archive"><div class="headline">文章</div><div class="length-num">109</div></a><a href="/tags/" title="tag"><div class="headline">标签</div><div class="length-num">7</div></a><a href="/categories/" title="category"><div class="headline">分类</div><div class="length-num">9</div></a></div><span class="sidebar-menu-item-title">功能</span><div class="sidebar-menu-item"><a class="darkmode_switchbutton menu-child" href="javascript:void(0);" rel="external nofollow noreferrer" title="显示模式"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span>显示模式</span></a></div><div class="back-menu-list-groups"><div class="back-menu-list-group"><div class="back-menu-list-title">网页</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="博客"><img class="back-menu-item-icon" src="https://bu.dusays.com/2025/07/19/687b394cb439b.ico" alt="博客"><span class="back-menu-item-text">博客</span></a></div></div><div class="back-menu-list-group"><div class="back-menu-list-title">项目</div><div class="back-menu-list"><a class="back-menu-item" href="/" title="后续项目..."><img class="back-menu-item-icon" src="https://image.anheyu.com/favicon.ico" alt="后续项目..."><span class="back-menu-item-text">后续项目...</span></a></div></div></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>文章</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/archives/"><i class="anzhiyufont anzhiyu-icon-box-archive faa-tada" style="font-size:.9em"></i><span> 归档</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/categories/"><i class="anzhiyufont anzhiyu-icon-shapes faa-tada" style="font-size:.9em"></i><span> 分类</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags faa-tada" style="font-size:.9em"></i><span> 标签</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>个人</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/link/"><i class="anzhiyufont anzhiyu-icon-link faa-tada" style="font-size:.9em"></i><span> 友人帐</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/todolist/"><i class="fas fa-check-double faa-tada"></i><span> 留言板</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/awesome-links/"><i class="fas fa-link faa-tada"></i><span> 实用网站</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>预览</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/music/?server=netease&amp;id=3117791189"><i class="anzhiyufont anzhiyu-icon-music faa-tada" style="font-size:.9em"></i><span> 音乐馆</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/album/"><i class="anzhiyufont anzhiyu-icon-images faa-tada" style="font-size:.9em"></i><span> 相册集</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/messages/"><i class="fas fa-comments faa-tada"></i><span> 评论总览</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><span>关于</span></a><ul class="menus_item_child"><li><a class="site-page child faa-parent animated-hover" href="/about/"><i class="anzhiyufont anzhiyu-icon-paper-plane faa-tada" style="font-size:.9em"></i><span> 关于本人</span></a></li><li><a class="site-page child faa-parent animated-hover" href="/essay/"><i class="anzhiyufont anzhiyu-icon-lightbulb faa-tada" style="font-size:.9em"></i><span> 即刻短文</span></a></li><li><a class="site-page child faa-parent animated-hover" href="javascript:toRandomPost()" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-shoe-prints1 faa-tada" style="font-size:.9em"></i><span> 随便逛逛</span></a></li></ul></div></div><span class="sidebar-menu-item-title">标签</span><div class="card-tags"><div class="item-headline"></div><div class="card-tag-cloud"><a href="/tags/Java%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Java基础知识总汇<sup>9</sup></a><a href="/tags/Java%E5%BE%AE%E6%9C%8D%E5%8A%A1%E7%AF%87/" style="font-size:.88rem">Java微服务篇<sup>11</sup></a><a href="/tags/Python%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86%E6%80%BB%E6%B1%87/" style="font-size:.88rem">Python基础知识总汇<sup>22</sup></a><a href="/tags/%E4%B8%AA%E4%BA%BA/" style="font-size:.88rem">个人<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E5%AE%9E%E6%88%98/" style="font-size:.88rem">产品经理实战<sup>5</sup></a><a href="/tags/%E4%BA%A7%E5%93%81%E7%BB%8F%E7%90%86%E6%95%99%E7%A8%8B/" style="font-size:.88rem">产品经理教程<sup>26</sup></a><a href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E6%95%99%E7%A8%8B/" style="font-size:.88rem">博客搭建教程<sup>31</sup></a></div></div><hr></div></div><div id="rightside"><div id="rightside-config-hide"><button id="translateLink" type="button" title="简繁转换">繁</button><button id="darkmode" type="button" title="浅色和深色模式转换"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i></button><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="anzhiyufont anzhiyu-icon-arrows-left-right"></i></button></div><div id="rightside-config-show"><button id="go-down" type="button" title="直达底部" onclick="anzhiyu.scrollToDest(document.body.scrollHeight,500)"><i class="anzhiyufont anzhiyu-icon-arrow-down"></i></button><button id="rightside-config" type="button" title="设置"><i class="anzhiyufont anzhiyu-icon-gear"></i></button><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="anzhiyufont anzhiyu-icon-list-ul"></i></button><button id="chat-btn" type="button" title="聊天"><i class="anzhiyufont anzhiyu-icon-comment-sms"></i></button><button id="to_comment" type="button" title="直达评论" onclick="FixedCommentBtn()"><i class="anzhiyufont anzhiyu-icon-comments"></i></button><a id="switch-commentBarrage" href="javascript:anzhiyu.switchCommentBarrage();" rel="external nofollow noreferrer" title="开关弹幕"><i class="anzhiyufont anzhiyu-icon-danmu"></i></a><button id="download-md-btn" type="button" title="下载文章源文件"><i class="anzhiyufont anzhiyu-icon-download"></i></button><button id="doc-toc-button" type="button" title="文档目录"><i class="anzhiyufont anzhiyu-icon-bars"></i></button><button id="code-runner-btn" type="button" title="代码运行器"><i class="fas fa-code"></i></button><button id="go-up" type="button" title="回到顶部"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></button></div></div><div class="code-runner-panel" id="code-runner-panel"><div class="panel-header"><div class="panel-title">代码运行器</div><button class="panel-close-btn" type="button" title="关闭"><i class="fas fa-times"></i></button></div><div class="panel-body"><nav class="panel-nav"><div class="nav-category" data-category="Trinket"><div class="category-header" data-description="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用" title="适合Python/HTML/CSS/JS等基础代码运行，界面简洁易用"><i class="fas fa-leaf"></i><span class="category-name">Trinket</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/python3/f417f7026885" data-name="Python 3" title="Python 3 在线编程环境">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/html/1aac0e8640a7" data-name="HTML/CSS/JS" title="前端三件套在线编辑器">HTML/CSS/JS</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://trinket.io/embed/java/33cfa8ec292c" data-name="Java" title="Java 在线编程环境">Java</a></li></ul></div><div class="nav-category" data-category="JDoodle"><div class="category-header" data-description="支持70+种编程语言，功能强大的在线编译器" title="支持70+种编程语言，功能强大的在线编译器"><i class="fas fa-terminal"></i><span class="category-name">JDoodle</span><i class="expand-icon fas fa-chevron-down"></i></div><ul class="instance-list"><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-compiler-c++/" data-name="C++ Compiler" title="C++ 在线编译器">C++ Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/online-java-compiler/" data-name="Java Compiler" title="Java 在线编译器">Java Compiler</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/python3-programming-online/" data-name="Python 3" title="Python 3 在线编程">Python 3</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/f40f7676c55c09b" data-name="TypeScript 在线编译器" title="TypeScript">TypeScript 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/510d607e22ee9e7c" data-name="SQL 在线编译器" title="SQL 在线编译器">SQL 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/ca85d333af57a5fc" data-name="PHP 在线编译器" title="PHP 在线编译器">PHP 在线编译器</a></li><li class="instance-item"><a class="instance-link" href="javascript:void(0);" rel="external nofollow noreferrer" data-url="https://www.jdoodle.com/embed/v1/8e18f0fd58e4a248" data-name="C语言 在线编译器" title="C语言 在线编译器">C语言 在线编译器</a></li></ul></div></nav><div class="panel-content"><div class="welcome-message"><div class="welcome-icon"><i class="fas fa-code"></i></div><div class="welcome-text"><h3>欢迎使用代码运行器</h3><p>请从左侧菜单选择一个编程环境开始编码</p></div></div><div class="iframe-container"><iframe id="code-runner-iframe" frameborder="0" width="100%" height="100%" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"></iframe></div><div class="loading-indicator"><div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div><div class="loading-text">正在加载编程环境...</div></div></div></div></div><div id="nav-music"><a id="nav-music-hoverTips" onclick="anzhiyu.musicToggle()" accesskey="m">播放音乐</a><div id="console-music-bg"></div><meting-js id="3117791189" server="netease" type="playlist" mutex="true" preload="none" theme="var(--anzhiyu-main)" data-lrctype="0" order="random" volume="0.7"></meting-js></div><div id="algolia-search"><div class="search-dialog"><nav class="search-nav"><span class="search-dialog-title">搜索</span><button class="search-close-button"><i class="anzhiyufont anzhiyu-icon-xmark"></i></button></nav><div class="search-wrap"><div id="algolia-search-input"></div><hr><div id="algolia-search-results"><div id="algolia-hits"></div><div id="algolia-pagination"></div><div id="algolia-info"><div class="algolia-stats"></div><div class="algolia-poweredBy"></div></div></div></div></div><div id="search-mask"></div></div><div id="rightMenu"><div class="rightMenu-group rightMenu-small"><div class="rightMenu-item" id="menu-backward"><i class="anzhiyufont anzhiyu-icon-arrow-left"></i></div><div class="rightMenu-item" id="menu-forward"><i class="anzhiyufont anzhiyu-icon-arrow-right"></i></div><div class="rightMenu-item" id="menu-refresh"><i class="anzhiyufont anzhiyu-icon-arrow-rotate-right" style="font-size:1rem"></i></div><div class="rightMenu-item" id="menu-top"><i class="anzhiyufont anzhiyu-icon-arrow-up"></i></div></div><div class="rightMenu-group rightMenu-line rightMenuPlugin"><div class="rightMenu-item" id="menu-copytext"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制选中文本</span></div><div class="rightMenu-item" id="menu-pastetext"><i class="anzhiyufont anzhiyu-icon-paste"></i><span>粘贴文本</span></div><a class="rightMenu-item" id="menu-commenttext"><i class="anzhiyufont anzhiyu-icon-comment-medical"></i><span>引用到评论</span></a><div class="rightMenu-item" id="menu-askAI"><i class="fa-solid fa-robot"></i><span>询问智能客服</span></div><div class="rightMenu-item" id="menu-newwindow"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开</span></div><div class="rightMenu-item" id="menu-copylink"><i class="anzhiyufont anzhiyu-icon-link"></i><span>复制链接地址</span></div><div class="rightMenu-item" id="menu-copyimg"><i class="anzhiyufont anzhiyu-icon-images"></i><span>复制此图片</span></div><div class="rightMenu-item" id="menu-downloadimg"><i class="anzhiyufont anzhiyu-icon-download"></i><span>下载此图片</span></div><div class="rightMenu-item" id="menu-newwindowimg"><i class="anzhiyufont anzhiyu-icon-window-restore"></i><span>新窗口打开图片</span></div><div class="rightMenu-item" id="menu-search"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>站内搜索</span></div><div class="rightMenu-item" id="menu-searchBaidu"><i class="anzhiyufont anzhiyu-icon-magnifying-glass"></i><span>百度搜索</span></div><div class="rightMenu-item" id="menu-music-toggle"><i class="anzhiyufont anzhiyu-icon-play"></i><span>播放音乐</span></div><div class="rightMenu-item" id="menu-music-back"><i class="anzhiyufont anzhiyu-icon-backward"></i><span>切换到上一首</span></div><div class="rightMenu-item" id="menu-music-forward"><i class="anzhiyufont anzhiyu-icon-forward"></i><span>切换到下一首</span></div><div class="rightMenu-item" id="menu-music-playlist" onclick="window.open(&quot;https://music.163.com/#/playlist?id=3117791189&quot;, &quot;_blank&quot;);" style="display:none"><i class="anzhiyufont anzhiyu-icon-radio"></i><span>查看所有歌曲</span></div><div class="rightMenu-item" id="menu-music-copyMusicName"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制歌名</span></div></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item menu-link" id="menu-randomPost"><i class="anzhiyufont anzhiyu-icon-shuffle"></i><span>随便逛逛</span></a><a class="rightMenu-item menu-link" href="/categories/"><i class="anzhiyufont anzhiyu-icon-cube"></i><span>博客分类</span></a><a class="rightMenu-item menu-link" href="/tags/"><i class="anzhiyufont anzhiyu-icon-tags"></i><span>文章标签</span></a></div><div class="rightMenu-group rightMenu-line rightMenuOther"><a class="rightMenu-item" id="menu-copy" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-copy"></i><span>复制地址</span></a><a class="rightMenu-item" id="menu-commentBarrage" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-message"></i><span class="menu-commentBarrage-text">关闭热评</span></a><a class="rightMenu-item" id="menu-darkmode" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-circle-half-stroke"></i><span class="menu-darkmode-text">深色模式</span></a><a class="rightMenu-item" id="menu-translate" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="anzhiyufont anzhiyu-icon-language"></i><span>轉為繁體</span></a></div></div><div id="rightmenu-mask"></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="/js/tw_cn.js"></script><script src="https://cdn.cbd.int/@fancyapps/ui@5.0.28/dist/fancybox/fancybox.umd.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/instant.page/5.1.0/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.5/dist/lazyload.min.js"></script><script src="https://cdn.jsdelivr.net/npm/node-snackbar@0.1.16/dist/snackbar.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/pangu/4.0.7/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><canvas id="universe"></canvas><script async="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.0/dark/dark.js"></script><script>//- 从这里开始的所有JS代码，都必须有缩进（例如2个空格）
// 消除控制台打印
var HoldLog = console.log;
console.log = function () {};
let now1 = new Date();
queueMicrotask(() => {
  const Log = function () {
    HoldLog.apply(console, arguments);
  }; //在恢复前输出日志
  const grt = new Date("06/10/2025 00:00:00"); // 此处读取您配置中的建站时间
  now1.setTime(now1.getTime() + 250);
  const days = (now1 - grt) / 1000 / 60 / 60 / 24;
  const dnum = Math.floor(days);
  
  // --- 已将您的专属LOGO和个人信息集成到这里 ---
  const ascll = [
    `欢迎访问 Prorise 的数字空间!`,
    `代码构建世界，思想驱动未来`,
    "已稳定运行",
    dnum,
    "天",
    `©2025 By Prorise 1.0.0`,
  ];
  
  const ascll2 = [`SYS-INFO`, `前置摄像头拍照成功`, `您的帅气脸庞为：`, `😎`];

  setTimeout(
    Log.bind(
      console,
      `\n%c${ascll[0]} %c ${ascll[1]} \n%c${ascll[2]} ${ascll[3]} ${ascll[4]}\n\n%c ${ascll[5]}\n`,
      "color:#425AEF",
      "",
      "color:#425AEF",
      "color:#425AEF"
    )
  );

  setTimeout(
    Log.bind(
      console,
      `%c ${ascll2[0]} %c ${ascll2[1]} %c \n${ascll2[2]} %c\n${ascll2[3]}\n`,
      "color:white; background-color:#4fd953",
      "",
      "",
      'background:url("https://npm.elemecdn.com/anzhiyu-blog@1.1.6/img/post/common/tinggge.gif") no-repeat;font-size:450%'
    )
  );

  setTimeout(Log.bind(console, "%c WELCOME %c 你好，朋友.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(
      console,
      "%c ⚡ Created by Prorise %c 你正在访问 Prorise 的博客.",
      "color:white; background-color:#f0ad4e",
      ""
    )
  );

  setTimeout(Log.bind(console, "%c W23-12 %c 你已打开控制台.", "color:white; background-color:#4f90d9", ""));

  setTimeout(
    console.warn.bind(console, "%c S013-782 %c 注意，你的所有操作已被记录.", "color:white; background-color:#d9534f", "")
  );
});</script><script async="" src="/anzhiyu/random.js"></script><script async="">(function () {
  var grt = new Date("06/10/2025 00:00:00"); //设置网站上线时间
  var now = new Date();
  var dnum;
  var hnum;
  var mnum;
  var snum;
  var nowHour;

  // 计算并更新天数、小时数、分钟数和秒数
  function updateTime() {
    now = new Date(); // 更新 now 的值
    nowHour = now.getHours(); // 更新 nowHour 的值
    var days = (now - grt) / 1000 / 60 / 60 / 24;
    dnum = Math.floor(days);
    var hours = (now - grt) / 1000 / 60 / 60 - 24 * dnum;
    hnum = Math.floor(hours);
    if (String(hnum).length == 1) {
      hnum = "0" + hnum;
    }
    var minutes = (now - grt) / 1000 / 60 - 24 * 60 * dnum - 60 * hnum;
    mnum = Math.floor(minutes);
    if (String(mnum).length == 1) {
      mnum = "0" + mnum;
    }
    var seconds = (now - grt) / 1000 - 24 * 60 * 60 * dnum - 60 * 60 * hnum - 60 * mnum;
    snum = Math.round(seconds);
    if (String(snum).length == 1) {
      snum = "0" + snum;
    }
  }

  // 更新网页中显示的网站运行时间
  function updateHtml() {
    const footer = document.getElementById("footer");
    if (!footer) return
    let currentTimeHtml = "";
    if (nowHour < 18 && nowHour >= 9) {
      // 如果是上班时间，默认就是"安知鱼-上班摸鱼中.svg"图片，不需要更改
      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    } else {
      // 如果是下班时间，插入"安知鱼-下班啦.svg"图片
      let img = document.querySelector("#workboard .workSituationImg");
      if (img != null) {
        img.src = "";
        img.title = "下班了就该开开心心的玩耍，嘿嘿~";
        img.alt = "下班了就该开开心心的玩耍，嘿嘿~";
      }

      currentTimeHtml = `本站居然运行了 ${dnum} 天<span id='runtime'> ${hnum} 小时 ${mnum} 分 ${snum} 秒 </span><i class='anzhiyufont anzhiyu-icon-heartbeat' style='color:red'></i>`;
    }

    if (document.getElementById("runtimeTextTip")) {
      document.getElementById("runtimeTextTip").innerHTML = currentTimeHtml;
    }
  }

  setInterval(() => {
    updateTime();
    updateHtml();
  }, 1000);
})();</script><script src="https://cdn.cbd.int/algoliasearch@4.18.0/dist/algoliasearch-lite.umd.js"></script><script src="https://cdn.cbd.int/instantsearch.js@4.60.0/dist/instantsearch.production.min.js"></script><script src="/js/search/algolia.js"></script><div class="js-pjax"><script>(() => {
  const $mermaid = document.querySelectorAll('#article-container .mermaid-wrap')
  if ($mermaid.length === 0) return
  const runMermaid = () => {
    window.loadMermaid = true
    const theme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default'

    Array.from($mermaid).forEach((item, index) => {
      const mermaidSrc = item.firstElementChild
      const mermaidThemeConfig = '%%{init:{ \'theme\':\'' + theme + '\'}}%%\n'
      const mermaidID = 'mermaid-' + index
      const mermaidDefinition = mermaidThemeConfig + mermaidSrc.textContent

      const renderFn = mermaid.render(mermaidID, mermaidDefinition)

      const renderV10 = () => {
        renderFn.then(({svg}) => {
          mermaidSrc.insertAdjacentHTML('afterend', svg)
        })
      }

      const renderV9 = svg => {
        mermaidSrc.insertAdjacentHTML('afterend', svg)
      }

      typeof renderFn === 'string' ? renderV9(renderFn) : renderV10()
    })
  }

  const loadMermaid = () => {
    window.loadMermaid ? runMermaid() : getScript('https://cdn.cbd.int/mermaid@10.2.4/dist/mermaid.min.js').then(runMermaid)
  }

  anzhiyu.addGlobalFn('themeChange', runMermaid, 'mermaid')

  window.pjax ? loadMermaid() : document.addEventListener('DOMContentLoaded', loadMermaid)
})()</script><script>(() => {
  const init = () => {
    twikoo.init(Object.assign({
      el: '#twikoo-wrap',
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      onCommentLoaded: () => {
        anzhiyu.loadLightbox(document.querySelectorAll('#twikoo .tk-content img:not(.tk-owo-emotion)'))
      }
    }, null))
  }

  const loadTwikoo = () => {
    if (typeof twikoo === 'object') setTimeout(runFn,0)
    else getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runFn)
  }

  const getCount = () => {
    const countELement = document.getElementById('twikoo-count')
    if(!countELement) return
    twikoo.getCommentsCount({
      envId: 'https://twikoo.prorise666.site/',
      region: 'ap-shanghai',
      urls: [window.location.pathname],
      includeReply: false
    }).then(res => {
      countELement.textContent = res[0].count
    }).catch(err => {
      console.error(err)
    })
  }

  const runFn = () => {
    init();
    GLOBAL_CONFIG_SITE.isPost && getCount()
  }

  if ('Twikoo' === 'Twikoo' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('twikoo-wrap'), loadTwikoo)
    else {
      loadTwikoo()
    }
  } else {
    window.loadOtherComment = loadTwikoo
  }
})()</script><script>(() => {
  let initFn = window.walineFn || null

  const initWaline = (Fn) => {
    const waline = Fn(Object.assign({
      el: '#waline-wrap',
      serverURL: 'https://waline.prorise666.site',
      pageview: true,
      dark: 'html[data-theme="dark"]',
      path: window.location.pathname,
      comment: true,
      imageUploader: true,
    }, null))

    const destroyWaline = () => {
      waline.destroy()
    }

    anzhiyu.addGlobalFn('pjax', destroyWaline, 'destroyWaline')
  }

  const loadWaline = async () => {
    if (initFn) initWaline(initFn)
    else {
      await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.css')
      if (true) await getCSS('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline-meta.css')
      const { init } = await import('https://cdn.cbd.int/@waline/client@3.1.3/dist/waline.js')
      initFn = init || Waline.init
      initWaline(initFn)
      window.walineFn = initFn
    }
  }

  if ('Twikoo' === 'Waline' || !false) {
    if (false) anzhiyu.loadComment(document.getElementById('waline-wrap'),loadWaline)
    else setTimeout(loadWaline, 0)
  } else {
    window.loadOtherComment = loadWaline
  }
})()</script><input type="hidden" name="page-type" id="page-type" value="post"><script async="" src="/js/anzhiyu/comment_barrage.js"></script></div><script>window.addEventListener('load', () => {
  const changeContent = (content) => {
    if (content === '') return content

    content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[图片]') // replace image link
    content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[链接]') // replace url
    content = content.replace(/<pre><code>.*?<\/pre>/gi, '[代码]') // replace code
    content = content.replace(/<[^>]+>/g,"") // remove html tag

    if (content.length > 150) {
      content = content.substring(0,150) + '...'
    }
    return content
  }

  const getComment = () => {
    const runTwikoo = () => {
      twikoo.getRecentComments({
        envId: 'https://twikoo.prorise666.site/',
        region: 'ap-shanghai',
        pageSize: 6,
        includeReply: true
      }).then(function (res) {
        const twikooArray = res.map(e => {
          return {
            'content': changeContent(e.comment),
            'avatar': e.avatar,
            'nick': e.nick,
            'url': e.url + '#' + e.id,
            'date': new Date(e.created).toISOString()
          }
        })

        saveToLocal.set('twikoo-newest-comments', JSON.stringify(twikooArray), 10/(60*24))
        generateHtml(twikooArray)
      }).catch(function (err) {
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.textContent= "无法获取评论，请确认相关配置是否正确"
      })
    }

    if (typeof twikoo === 'object') {
      runTwikoo()
    } else {
      getScript('https://cdn.cbd.int/twikoo@1.6.39/dist/twikoo.all.min.js').then(runTwikoo)
    }
  }

  const generateHtml = array => {
    let result = ''

    if (array.length) {
      for (let i = 0; i < array.length; i++) {
        result += '<div class=\'aside-list-item\'>'

        if (true) {
          const name = 'data-lazy-src'
          result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'><div class='name'><span>${array[i].nick} </span></div></a>`
        }
        
        result += `<div class='content'>
        <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
        <time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
        </div>`
      }
    } else {
      result += '没有评论'
    }

    let $dom = document.querySelector('#card-newest-comments .aside-list')
    $dom && ($dom.innerHTML= result)
    window.lazyLoadInstance && window.lazyLoadInstance.update()
    window.pjax && window.pjax.refresh($dom)
  }

  const newestCommentInit = () => {
    if (document.querySelector('#card-newest-comments .aside-list')) {
      const data = saveToLocal.get('twikoo-newest-comments')
      if (data) {
        generateHtml(JSON.parse(data))
      } else {
        getComment()
      }
    }
  }

  newestCommentInit()
  document.addEventListener('pjax:complete', newestCommentInit)
})</script><script async="" data-pjax="" src="https://npm.elemecdn.com/anzhiyu-theme-static@1.0.1/bubble/bubble.js"></script><script async="" data-pjax="" src="https://cdn.cbd.int/anzhiyu-theme-static@1.0.0/waterfall/waterfall.js"></script><script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script><script src="/js/anzhiyu/right_click_menu.js"></script><link rel="stylesheet" href="https://cdn.cbd.int/anzhiyu-theme-static@1.1.9/icon/ali_iconfont_css.css"><script src="/js/doc-sidebar.js"></script><script src="/js/load-on-demand.js"></script><script src="//code.tidio.co//tk571vck0ua2wjoalfbp8wyrlsdaunmz.js" async=""></script><script>(() => {
  const isChatBtn = true
  const isChatHideShow = true

  if (isChatBtn) {
    let isShow = false
    const close = () => {
      window.tidioChatApi.hide()
      isShow = false
      document.body.style.position = 'relative';
      document.documentElement.style.overflow = 'auto'
    }
    
    const open = () => {
      window.tidioChatApi.open()
      window.tidioChatApi.show()
      isShow = true
    }

    const onTidioChatApiReady = () => {
      window.tidioChatApi.hide()
      window.tidioChatApi.on("close", close)
    }
    if (window.tidioChatApi) {
      window.tidioChatApi.on("ready", onTidioChatApiReady)
    } else {
      document.addEventListener("tidioChat-ready", onTidioChatApiReady)
    }

    window.chatBtnFn = () => {
      if (!window.tidioChatApi) return
      isShow ? close() : open()
    }
  } else if (isChatHideShow) {
    window.chatBtn = {
      hide: () => {
        window.tidioChatApi && window.tidioChatApi.hide()
      },
      show: () => {
        window.tidioChatApi && window.tidioChatApi.show()
      }
    }
  }
})()</script><link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.css" media="print" onload="this.media=&quot;all&quot;"><script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aplayer/1.10.1/APlayer.min.js"></script><script src="https://npm.elemecdn.com/meting@2.0.1/dist/Meting.min.js"></script><script src="https://lib.baomitu.com/pjax/0.2.8/pjax.min.js"></script><script>let pjaxSelectors = ["meta[property=\"og:image\"]","meta[property=\"og:title\"]","meta[property=\"og:url\"]","meta[property=\"og:type\"]","meta[property=\"og:site_name\"]","meta[property=\"og:description\"]","head > title","#config-diff","#body-wrap","#rightside-config-hide","#rightside-config-show",".js-pjax"]
var pjax = new Pjax({
  elements: 'a:not([target="_blank"])',
  selectors: pjaxSelectors,
  cacheBust: false,
  analytics: true,
  scrollRestoration: false
})

document.addEventListener('pjax:send', function () {
  // removeEventListener scroll 
  anzhiyu.removeGlobalFnEvent('pjax')
  anzhiyu.removeGlobalFnEvent('themeChange')

  document.getElementById('rightside').classList.remove('rightside-show')
  
  if (window.aplayers) {
    for (let i = 0; i < window.aplayers.length; i++) {
      if (!window.aplayers[i].options.fixed) {
        window.aplayers[i].destroy()
      }
    }
  }

  typeof typed === 'object' && typed.destroy()

  //reset readmode
  const $bodyClassList = document.body.classList
  $bodyClassList.contains('read-mode') && $bodyClassList.remove('read-mode')
})

document.addEventListener('pjax:complete', function () {
  window.refreshFn()

  document.querySelectorAll('script[data-pjax]').forEach(item => {
    const newScript = document.createElement('script')
    const content = item.text || item.textContent || item.innerHTML || ""
    Array.from(item.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value))
    newScript.appendChild(document.createTextNode(content))
    item.parentNode.replaceChild(newScript, item)
  })

  GLOBAL_CONFIG.islazyload && window.lazyLoadInstance.update()

  typeof panguInit === 'function' && panguInit()

  // google analytics
  typeof gtag === 'function' && gtag('config', '[object Object]', {'page_path': window.location.pathname});

  // baidu analytics
  typeof _hmt === 'object' && _hmt.push(['_trackPageview',window.location.pathname]);

  typeof loadMeting === 'function' && document.getElementsByClassName('aplayer').length && loadMeting()

  // prismjs
  typeof Prism === 'object' && Prism.highlightAll()
})

document.addEventListener('pjax:error', e => {
  if (e.request.status === 404) {
    pjax.loadUrl('/404.html')
  }
})</script><script async="" data-pjax="" src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div><div id="popup-window"><div class="popup-window-title">通知</div><div class="popup-window-divider"></div><div class="popup-window-content"><div class="popup-tip">你好呀</div><div class="popup-link"><i class="anzhiyufont anzhiyu-icon-arrow-circle-right"></i></div></div></div>
    <link rel="stylesheet" href="https://ai.tianli0.top/static/public/postChatUser_summary.min.css">
    <script>
        let tianliGPT_key = 'S-DNA1JM95BX2F9L0N';
        let tianliGPT_postSelector = '#article-container';
        let tianliGPT_Title = '✨ AI 文章摘要';
        let tianliGPT_postURL = '';
        let tianliGPT_blacklist = '';
        let tianliGPT_wordLimit = '1000';
        let tianliGPT_typingAnimate = true;
        let tianliGPT_theme = 'default';
        var postChatConfig = {
          backgroundColor: "#1e2022",
          bottom: "76px",
          left: "16px",
          fill: "#FFFFFF",
          width: "50px",
          frameWidth: "380px",
          frameHeight: "600px",
          defaultInput: true,
          upLoadWeb: true,
          showInviteLink: false,
          userTitle: "💬 Prorise",
          userDesc: "我是 Prorise 博客的 AI 助手，有任何关于网站内容的问题都可以问我哦～",
          addButton: true,
          beginningText: "这篇文章介绍了",
          userIcon: "",
          userMode: "magic",
          defaultChatQuestions: ["你好","你是谁"],
          defaultSearchQuestions: ["视频压缩","设计"]
        };
    </script>
    <script data-postchat_key="S-DNA1JM95BX2F9L0N" src="https://ai.tianli0.top/static/public/postChatUser_summary.min.js"></script>
  </body></html>